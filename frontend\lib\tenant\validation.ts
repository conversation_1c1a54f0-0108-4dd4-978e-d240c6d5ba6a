/**
 * Tenant Validation Service
 * 
 * Provides comprehensive tenant data isolation enforcement
 * and validation at all application layers.
 */

import { Pool, PoolClient } from 'pg';
import { databaseService } from '@/lib/services/database-service';
import { TenantContext } from '@/lib/types';
import { AuthSession } from '@/lib/api/auth-middleware';

export interface TenantValidationError {
  code: string;
  message: string;
  details?: any;
}

export interface TenantValidationResult {
  isValid: boolean;
  error?: TenantValidationError;
  warnings?: string[];
}

/**
 * Validate that a user has access to a specific tenant
 */
export async function validateUserTenantAccess(
  userId: string,
  tenantId: string
): Promise<TenantValidationResult> {
  let client: PoolClient | null = null;
  
  try {
    await databaseService.initialize();
    client = await databaseService.getClient();

    // Check if user exists and get their tenant associations
    const userQuery = `
      SELECT 
        u.id,
        u.email,
        u.tenant_id,
        u.roles,
        u.is_active,
        c.client_id,
        c.name,
        c.is_active as tenant_active
      FROM users u
      LEFT JOIN clients c ON u.tenant_id = c.tenant_id
      WHERE u.id = $1
    `;

    const userResult = await client.query(userQuery, [userId]);

    if (userResult.rows.length === 0) {
      return {
        isValid: false,
        error: {
          code: 'USER_NOT_FOUND',
          message: 'User not found'
        }
      };
    }

    const user = userResult.rows[0];

    // Check if user is active
    if (!user.is_active) {
      return {
        isValid: false,
        error: {
          code: 'USER_INACTIVE',
          message: 'User account is inactive'
        }
      };
    }

    // Check if user's tenant matches the requested tenant
    if (user.client_id !== tenantId && user.tenant_id !== tenantId) {
      return {
        isValid: false,
        error: {
          code: 'TENANT_ACCESS_DENIED',
          message: 'User does not have access to this tenant'
        }
      };
    }

    // Check if tenant is active
    if (!user.tenant_active) {
      return {
        isValid: false,
        error: {
          code: 'TENANT_INACTIVE',
          message: 'Tenant is inactive'
        }
      };
    }

    return { isValid: true };

  } catch (error) {
    console.error('Error validating user tenant access:', error);
    return {
      isValid: false,
      error: {
        code: 'VALIDATION_ERROR',
        message: 'Error validating tenant access',
        details: error
      }
    };
  } finally {
    if (client) {
      client.release();
    }
  }
}

/**
 * Validate that a database schema exists and is properly configured
 */
export async function validateTenantSchema(
  tenantSchema: string
): Promise<TenantValidationResult> {
  let client: PoolClient | null = null;
  
  try {
    await databaseService.initialize();
    client = await databaseService.getClient();

    // Check if schema exists
    const schemaQuery = `
      SELECT schema_name 
      FROM information_schema.schemata 
      WHERE schema_name = $1
    `;

    const schemaResult = await client.query(schemaQuery, [tenantSchema]);

    if (schemaResult.rows.length === 0) {
      return {
        isValid: false,
        error: {
          code: 'SCHEMA_NOT_FOUND',
          message: `Tenant schema '${tenantSchema}' does not exist`
        }
      };
    }

    // Check if required tables exist in the schema
    const requiredTables = ['tenant_renewals', 'tenant_alerts', 'tenant_users']; // Add more as needed
    const warnings: string[] = [];

    for (const table of requiredTables) {
      const tableQuery = `
        SELECT table_name
        FROM information_schema.tables
        WHERE table_schema = $1 AND table_name = $2
      `;

      const tableResult = await client.query(tableQuery, [tenantSchema, table]);

      if (tableResult.rows.length === 0) {
        warnings.push(`Table '${table}' not found in schema '${tenantSchema}'`);
      }
    }

    return {
      isValid: true,
      warnings: warnings.length > 0 ? warnings : undefined
    };

  } catch (error) {
    console.error('Error validating tenant schema:', error);
    return {
      isValid: false,
      error: {
        code: 'SCHEMA_VALIDATION_ERROR',
        message: 'Error validating tenant schema',
        details: error
      }
    };
  } finally {
    if (client) {
      client.release();
    }
  }
}

/**
 * Validate that a query is safe for tenant isolation
 * Checks for potential cross-tenant data access
 */
export function validateTenantQuery(
  query: string,
  allowedSchema: string
): TenantValidationResult {
  const normalizedQuery = query.toLowerCase().trim();
  
  // Check for dangerous patterns
  const dangerousPatterns = [
    /set\s+search_path/i,
    /information_schema/i,
    /pg_catalog/i,
    /\bpublic\./i,
    /\bmetadata\./i // Allow metadata schema but flag for review
  ];

  const warnings: string[] = [];

  for (const pattern of dangerousPatterns) {
    if (pattern.test(normalizedQuery)) {
      if (pattern.source.includes('metadata')) {
        warnings.push('Query accesses metadata schema - ensure this is intentional');
      } else {
        return {
          isValid: false,
          error: {
            code: 'UNSAFE_QUERY',
            message: `Query contains potentially unsafe pattern: ${pattern.source}`
          }
        };
      }
    }
  }

  // Check if query explicitly references the allowed schema
  const schemaPattern = new RegExp(`"${allowedSchema}"`, 'i');
  if (!schemaPattern.test(query) && !normalizedQuery.includes('set search_path')) {
    warnings.push(`Query does not explicitly reference tenant schema '${allowedSchema}'`);
  }

  return {
    isValid: true,
    warnings: warnings.length > 0 ? warnings : undefined
  };
}

/**
 * Comprehensive tenant validation for API requests
 */
export async function validateTenantRequest(
  session: AuthSession,
  tenant: TenantContext,
  operation: string,
  resource: string
): Promise<TenantValidationResult> {
  const validations = await Promise.all([
    validateUserTenantAccess(session.userId, tenant.clientId),
    validateTenantSchema(tenant.tenantSchema)
  ]);

  const userValidation = validations[0];
  const schemaValidation = validations[1];

  if (!userValidation.isValid) {
    return userValidation;
  }

  if (!schemaValidation.isValid) {
    return schemaValidation;
  }

  // Combine warnings from both validations
  const allWarnings = [
    ...(userValidation.warnings || []),
    ...(schemaValidation.warnings || [])
  ];

  return {
    isValid: true,
    warnings: allWarnings.length > 0 ? allWarnings : undefined
  };
}

/**
 * Create a tenant-safe database connection with enforced schema
 */
export async function createTenantConnection(
  tenantSchema: string
): Promise<{ client: PoolClient; release: () => void }> {
  await databaseService.initialize();
  const client = await databaseService.getClient();

  try {
    // Validate schema first
    const validation = await validateTenantSchema(tenantSchema);
    if (!validation.isValid) {
      client.release();
      throw new Error(`Invalid tenant schema: ${validation.error?.message}`);
    }

    // Set search path to tenant schema only (no fallback to public)
    await client.query(`SET search_path TO "${tenantSchema}"`);

    // Log warnings if any
    if (validation.warnings) {
      console.warn('Tenant schema warnings:', validation.warnings);
    }

    let isReleased = false;
    return {
      client,
      release: () => {
        if (!isReleased) {
          isReleased = true;
          client.release();
        }
      }
    };

  } catch (error) {
    try {
      client.release();
    } catch (releaseError) {
      // Ignore release errors in catch block
    }
    throw error;
  }
}

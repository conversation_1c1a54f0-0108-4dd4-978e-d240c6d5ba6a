/**
 * Simple Toast Notification System
 */

'use client';

import React, { ReactNode } from 'react';

export type ToastType = 'success' | 'error' | 'warning' | 'info';

export interface ToastProps {
  id: string;
  type: ToastType;
  title?: string;
  message: string;
  duration?: number;
  onDismiss?: () => void;
}

// Simple toast provider that just renders children
export function ToastProvider({ children }: { children: ReactNode }) {
  return <>{children}</>;
}

// Simple toast component
export function Toast({ type, title, message, onDismiss }: ToastProps) {
  const getToastStyles = (type: ToastType) => {
    const baseStyles = {
      padding: '12px 16px',
      borderRadius: '6px',
      marginBottom: '8px',
      border: '1px solid',
    };

    const typeStyles = {
      success: {
        backgroundColor: '#f0f9ff',
        borderColor: '#22c55e',
        color: '#166534',
      },
      error: {
        backgroundColor: '#fef2f2',
        borderColor: '#ef4444',
        color: '#991b1b',
      },
      warning: {
        backgroundColor: '#fffbeb',
        borderColor: '#f59e0b',
        color: '#92400e',
      },
      info: {
        backgroundColor: '#f0f9ff',
        borderColor: '#3b82f6',
        color: '#1e40af',
      },
    };

    return { ...baseStyles, ...typeStyles[type] };
  };

  return (
    <div style={getToastStyles(type)}>
      {title && <strong>{title}: </strong>}
      {message}
      {onDismiss && (
        <button
          onClick={onDismiss}
          style={{
            marginLeft: '8px',
            background: 'none',
            border: 'none',
            cursor: 'pointer'
          }}
        >
          ×
        </button>
      )}
    </div>
  );
}
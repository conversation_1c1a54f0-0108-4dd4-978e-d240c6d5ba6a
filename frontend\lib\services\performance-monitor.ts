/**
 * Performance Monitoring Service
 * 
 * Provides comprehensive performance monitoring including:
 * - API response time tracking
 * - Component render performance
 * - Memory usage monitoring
 * - User interaction metrics
 * - Performance alerts and recommendations
 */

interface PerformanceMetric {
  name: string
  value: number
  timestamp: number
  tags?: Record<string, string>
}

interface ComponentMetric {
  componentName: string
  renderTime: number
  propsSize: number
  reRenderCount: number
  timestamp: number
}

interface APIMetric {
  endpoint: string
  method: string
  responseTime: number
  statusCode: number
  timestamp: number
  tenantId?: string
}

interface PerformanceAlert {
  type: 'warning' | 'error'
  message: string
  metric: string
  value: number
  threshold: number
  timestamp: number
}

class PerformanceMonitoringService {
  private metrics: PerformanceMetric[] = []
  private componentMetrics: ComponentMetric[] = []
  private apiMetrics: APIMetric[] = []
  private alerts: PerformanceAlert[] = []
  private observers: PerformanceObserver[] = []
  
  // Performance thresholds
  private readonly THRESHOLDS = {
    API_RESPONSE_TIME: 2000, // 2 seconds
    COMPONENT_RENDER_TIME: 100, // 100ms
    MEMORY_USAGE: 100 * 1024 * 1024, // 100MB
    FCP: 2000, // First Contentful Paint - 2 seconds
    LCP: 4000, // Largest Contentful Paint - 4 seconds
    FID: 100, // First Input Delay - 100ms
    CLS: 0.1 // Cumulative Layout Shift
  }

  constructor() {
    this.initializePerformanceObservers()
    this.startMemoryMonitoring()
  }

  /**
   * Track API performance
   */
  trackAPICall(
    endpoint: string,
    method: string,
    responseTime: number,
    statusCode: number,
    tenantId?: string
  ): void {
    const metric: APIMetric = {
      endpoint,
      method,
      responseTime,
      statusCode,
      timestamp: Date.now(),
      tenantId
    }

    this.apiMetrics.push(metric)
    
    // Check for performance issues
    if (responseTime > this.THRESHOLDS.API_RESPONSE_TIME) {
      this.createAlert('warning', 
        `Slow API response: ${endpoint} took ${responseTime}ms`,
        'api_response_time',
        responseTime,
        this.THRESHOLDS.API_RESPONSE_TIME
      )
    }

    // Keep only last 1000 metrics
    if (this.apiMetrics.length > 1000) {
      this.apiMetrics = this.apiMetrics.slice(-1000)
    }
  }

  /**
   * Track component render performance
   */
  trackComponentRender(
    componentName: string,
    renderTime: number,
    propsSize: number = 0,
    reRenderCount: number = 1
  ): void {
    const metric: ComponentMetric = {
      componentName,
      renderTime,
      propsSize,
      reRenderCount,
      timestamp: Date.now()
    }

    this.componentMetrics.push(metric)

    // Check for slow renders
    if (renderTime > this.THRESHOLDS.COMPONENT_RENDER_TIME) {
      this.createAlert('warning',
        `Slow component render: ${componentName} took ${renderTime}ms`,
        'component_render_time',
        renderTime,
        this.THRESHOLDS.COMPONENT_RENDER_TIME
      )
    }

    // Keep only last 500 component metrics
    if (this.componentMetrics.length > 500) {
      this.componentMetrics = this.componentMetrics.slice(-500)
    }
  }

  /**
   * Track custom performance metric
   */
  trackMetric(name: string, value: number, tags?: Record<string, string>): void {
    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: Date.now(),
      tags
    }

    this.metrics.push(metric)

    // Keep only last 1000 custom metrics
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000)
    }
  }

  /**
   * Get performance summary
   */
  getPerformanceSummary(): {
    api: {
      avgResponseTime: number
      slowestEndpoints: Array<{ endpoint: string; avgTime: number }>
      errorRate: number
    }
    components: {
      slowestComponents: Array<{ name: string; avgRenderTime: number }>
      totalRenders: number
    }
    vitals: {
      fcp?: number
      lcp?: number
      fid?: number
      cls?: number
    }
    alerts: PerformanceAlert[]
  } {
    return {
      api: this.getAPIMetricsSummary(),
      components: this.getComponentMetricsSummary(),
      vitals: this.getWebVitals(),
      alerts: this.getRecentAlerts()
    }
  }

  /**
   * Get API metrics summary
   */
  private getAPIMetricsSummary() {
    if (this.apiMetrics.length === 0) {
      return {
        avgResponseTime: 0,
        slowestEndpoints: [],
        errorRate: 0
      }
    }

    const totalResponseTime = this.apiMetrics.reduce((sum, m) => sum + m.responseTime, 0)
    const avgResponseTime = totalResponseTime / this.apiMetrics.length

    // Group by endpoint and calculate averages
    const endpointGroups = this.apiMetrics.reduce((groups, metric) => {
      const key = `${metric.method} ${metric.endpoint}`
      if (!groups[key]) {
        groups[key] = { times: [], errors: 0 }
      }
      groups[key].times.push(metric.responseTime)
      if (metric.statusCode >= 400) {
        groups[key].errors++
      }
      return groups
    }, {} as Record<string, { times: number[], errors: number }>)

    const slowestEndpoints = Object.entries(endpointGroups)
      .map(([endpoint, data]) => ({
        endpoint,
        avgTime: data.times.reduce((sum, time) => sum + time, 0) / data.times.length
      }))
      .sort((a, b) => b.avgTime - a.avgTime)
      .slice(0, 5)

    const totalErrors = this.apiMetrics.filter(m => m.statusCode >= 400).length
    const errorRate = (totalErrors / this.apiMetrics.length) * 100

    return {
      avgResponseTime,
      slowestEndpoints,
      errorRate
    }
  }

  /**
   * Get component metrics summary
   */
  private getComponentMetricsSummary() {
    if (this.componentMetrics.length === 0) {
      return {
        slowestComponents: [],
        totalRenders: 0
      }
    }

    // Group by component name
    const componentGroups = this.componentMetrics.reduce((groups, metric) => {
      if (!groups[metric.componentName]) {
        groups[metric.componentName] = { times: [], renders: 0 }
      }
      groups[metric.componentName].times.push(metric.renderTime)
      groups[metric.componentName].renders += metric.reRenderCount
      return groups
    }, {} as Record<string, { times: number[], renders: number }>)

    const slowestComponents = Object.entries(componentGroups)
      .map(([name, data]) => ({
        name,
        avgRenderTime: data.times.reduce((sum, time) => sum + time, 0) / data.times.length
      }))
      .sort((a, b) => b.avgRenderTime - a.avgRenderTime)
      .slice(0, 5)

    const totalRenders = Object.values(componentGroups)
      .reduce((sum, data) => sum + data.renders, 0)

    return {
      slowestComponents,
      totalRenders
    }
  }

  /**
   * Get Web Vitals metrics
   */
  private getWebVitals() {
    const vitals: Record<string, number> = {}

    // Get performance entries
    if (typeof window !== 'undefined' && 'performance' in window) {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
      if (navigation) {
        // First Contentful Paint approximation
        vitals.fcp = navigation.responseEnd - navigation.fetchStart
      }

      // Get paint entries
      const paintEntries = performance.getEntriesByType('paint')
      paintEntries.forEach(entry => {
        if (entry.name === 'first-contentful-paint') {
          vitals.fcp = entry.startTime
        }
      })
    }

    return vitals
  }

  /**
   * Get recent alerts
   */
  private getRecentAlerts(limit: number = 10): PerformanceAlert[] {
    return this.alerts
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, limit)
  }

  /**
   * Create performance alert
   */
  private createAlert(
    type: 'warning' | 'error',
    message: string,
    metric: string,
    value: number,
    threshold: number
  ): void {
    const alert: PerformanceAlert = {
      type,
      message,
      metric,
      value,
      threshold,
      timestamp: Date.now()
    }

    this.alerts.push(alert)

    // Keep only last 100 alerts
    if (this.alerts.length > 100) {
      this.alerts = this.alerts.slice(-100)
    }

    // Log critical alerts
    if (type === 'error') {
      console.error('Performance Alert:', message)
    } else {
      console.warn('Performance Warning:', message)
    }
  }

  /**
   * Initialize performance observers
   */
  private initializePerformanceObservers(): void {
    if (typeof window === 'undefined' || !('PerformanceObserver' in window)) {
      return
    }

    try {
      // Observe Long Tasks
      const longTaskObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          this.trackMetric('long_task', entry.duration, {
            type: 'long-task'
          })
        })
      })
      longTaskObserver.observe({ entryTypes: ['longtask'] })
      this.observers.push(longTaskObserver)

      // Observe Layout Shifts
      const layoutShiftObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry: any) => {
          if (entry.value > this.THRESHOLDS.CLS) {
            this.createAlert('warning',
              `High Cumulative Layout Shift: ${entry.value}`,
              'cls',
              entry.value,
              this.THRESHOLDS.CLS
            )
          }
        })
      })
      layoutShiftObserver.observe({ entryTypes: ['layout-shift'] })
      this.observers.push(layoutShiftObserver)

    } catch (error) {
      console.warn('Failed to initialize performance observers:', error)
    }
  }

  /**
   * Start memory monitoring
   */
  private startMemoryMonitoring(): void {
    if (typeof window === 'undefined' || !('performance' in window) || !(performance as any).memory) {
      return
    }

    setInterval(() => {
      const memory = (performance as any).memory
      if (memory) {
        this.trackMetric('memory_used', memory.usedJSHeapSize, {
          type: 'memory'
        })

        if (memory.usedJSHeapSize > this.THRESHOLDS.MEMORY_USAGE) {
          this.createAlert('warning',
            `High memory usage: ${Math.round(memory.usedJSHeapSize / 1024 / 1024)}MB`,
            'memory_usage',
            memory.usedJSHeapSize,
            this.THRESHOLDS.MEMORY_USAGE
          )
        }
      }
    }, 30000) // Check every 30 seconds
  }

  /**
   * Cleanup observers
   */
  cleanup(): void {
    this.observers.forEach(observer => observer.disconnect())
    this.observers = []
  }
}

// Export singleton instance
export const performanceMonitor = new PerformanceMonitoringService()
export default performanceMonitor

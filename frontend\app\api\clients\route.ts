import { createClient, updateClient, getAllClients } from '@/lib/tenant/clients';
import { createApiRoute } from '@/lib/api/route-factory';
import { z } from 'zod';

// Define schema for client creation
const createClientSchema = z.object({
  name: z.string().min(2).max(255),
  domain: z.string().min(3).max(255).regex(
    /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z]{2,})+$/,
    'Invalid domain format'
  ),
  status: z.enum(['active', 'inactive', 'pending']).optional(),
  settings: z.record(z.unknown()).optional()
});

// Define schema for client updates
const updateClientSchema = z.object({
  name: z.string().min(2).max(255).optional(),
  domain: z.string().min(3).max(255).regex(
    /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z]{2,})+$/,
    'Invalid domain format'
  ).optional(),
  status: z.enum(['active', 'inactive', 'pending']).optional(),
  settings: z.record(z.unknown()).optional()
});

/**
 * GET /api/clients
 * Get all clients (super-admin only)
 */
export const GET = createApiRoute('GET', {
  requireAuth: true,
  requireRoles: ['super_admin'],
  handler: async (context) => {
    // Get all clients
    const clients = await getAllClients();
    return clients;
  }
})

export const POST = createApiRoute('POST', {
  requireAuth: true,
  requireRoles: ['admin'],
  bodySchema: createClientSchema,
  handler: async (context) => {
    const { body } = context;
    if (!body) {
      throw new Error('Request body is required');
    }

    const client = await createClient({
      name: body.name,
      domain: body.domain,
      status: body.status,
      settings: body.settings
    });

    return client;
  }
})
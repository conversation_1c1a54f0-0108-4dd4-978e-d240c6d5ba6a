/**
 * Add Alert Modal Component
 * 
 * Modal for creating new renewal alerts
 */

'use client'

import React, { useState } from 'react'
import { Form } from '@/components/ui/Form'

interface AddAlertModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (alertData: {
    alert_name: string
    days_before_renewal: number
    email_recipients: string[]
  }) => Promise<void>
}

export default function AddAlertModal({ isOpen, onClose, onSubmit }: AddAlertModalProps) {
  const [formData, setFormData] = useState({
    alert_name: '',
    days_before_renewal: 30,
    email_recipients: ['']
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validate form
    const newErrors: Record<string, string> = {}
    
    if (!formData.alert_name.trim()) {
      newErrors.alert_name = 'Alert name is required'
    }
    
    if (formData.days_before_renewal < 1 || formData.days_before_renewal > 365) {
      newErrors.days_before_renewal = 'Days must be between 1 and 365'
    }
    
    const validEmails = formData.email_recipients.filter(email => email.trim())
    if (validEmails.length === 0) {
      newErrors.email_recipients = 'At least one email recipient is required'
    } else {
      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      const invalidEmails = validEmails.filter(email => !emailRegex.test(email.trim()))
      if (invalidEmails.length > 0) {
        newErrors.email_recipients = 'Please enter valid email addresses'
      }
    }
    
    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors)
      return
    }
    
    setIsSubmitting(true)
    setErrors({})
    
    try {
      await onSubmit({
        alert_name: formData.alert_name.trim(),
        days_before_renewal: formData.days_before_renewal,
        email_recipients: validEmails.map(email => email.trim())
      })
      
      // Reset form
      setFormData({
        alert_name: '',
        days_before_renewal: 30,
        email_recipients: ['']
      })
    } catch (error) {
      console.error('Error creating alert:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleEmailChange = (index: number, value: string) => {
    const newEmails = [...formData.email_recipients]
    newEmails[index] = value
    setFormData({ ...formData, email_recipients: newEmails })
  }

  const addEmailField = () => {
    setFormData({
      ...formData,
      email_recipients: [...formData.email_recipients, '']
    })
  }

  const removeEmailField = (index: number) => {
    if (formData.email_recipients.length > 1) {
      const newEmails = formData.email_recipients.filter((_, i) => i !== index)
      setFormData({ ...formData, email_recipients: newEmails })
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div className="flex items-center justify-between p-6 border-b">
          <h3 className="text-lg font-semibold text-gray-900">Add New Alert</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6">
          <div className="space-y-4">
            {/* Alert Name */}
            <div>
              <Form.Label htmlFor="alert_name" required>
                Alert Name
              </Form.Label>
              <Form.Input
                id="alert_name"
                type="text"
                value={formData.alert_name}
                onChange={(e) => setFormData({ ...formData, alert_name: e.target.value })}
                placeholder="e.g., 30-day renewal reminder"
                error={errors.alert_name}
              />
            </div>

            {/* Days Before Renewal */}
            <div>
              <Form.Label htmlFor="days_before_renewal" required>
                Days Before Renewal
              </Form.Label>
              <Form.Input
                id="days_before_renewal"
                type="number"
                min="1"
                max="365"
                value={formData.days_before_renewal}
                onChange={(e) => setFormData({ ...formData, days_before_renewal: parseInt(e.target.value) || 1 })}
                error={errors.days_before_renewal}
              />
            </div>

            {/* Email Recipients */}
            <div>
              <Form.Label required>
                Email Recipients
              </Form.Label>
              <div className="space-y-2">
                {formData.email_recipients.map((email, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <Form.Input
                      type="email"
                      value={email}
                      onChange={(e) => handleEmailChange(index, e.target.value)}
                      placeholder="<EMAIL>"
                      className="flex-1"
                    />
                    {formData.email_recipients.length > 1 && (
                      <button
                        type="button"
                        onClick={() => removeEmailField(index)}
                        className="text-red-500 hover:text-red-700 transition-colors"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    )}
                  </div>
                ))}
                <button
                  type="button"
                  onClick={addEmailField}
                  className="text-sm text-blue-600 hover:text-blue-700 transition-colors"
                >
                  + Add another email
                </button>
              </div>
              {errors.email_recipients && (
                <p className="text-red-500 text-sm mt-1">{errors.email_recipients}</p>
              )}
            </div>
          </div>

          <div className="flex items-center justify-end space-x-3 mt-6 pt-4 border-t">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {isSubmitting ? 'Creating...' : 'Create Alert'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

/**
 * License Terms API
 * 
 * Endpoints for managing license terms configuration
 */

import { NextRequest, NextResponse } from 'next/server'
import { databaseService } from '@/lib/services/database-service'
import { requireSuperAdmin } from '@/lib/api/auth-middleware'
import { createSuccessResponse, createErrorResponse, ApiErrorCode, HttpStatus } from '@/lib/api/response'
import { STATUS } from '@/lib/constants/app-constants'

/**
 * GET /api/admin/license-terms
 * Get all license terms
 */
export async function GET(request: NextRequest) {
  try {
    // Require super admin authentication
    const authResult = await requireSuperAdmin(request)
    if (!authResult.success) {
      return authResult.response
    }

    const query = `
      SELECT 
        id,
        term_name,
        term_months,
        discount_percentage,
        status
      FROM metadata.admin_license_terms
      WHERE status = '${STATUS.ACTIVE}'
      ORDER BY term_months ASC
    `

    const result = await databaseService.query(query)

    return createSuccessResponse(result.rows)

  } catch (error) {
    console.error('Error fetching license terms:', error)
    return createErrorResponse('Failed to fetch license terms', ApiErrorCode.INTERNAL_ERROR, HttpStatus.INTERNAL_SERVER_ERROR)
  }
}

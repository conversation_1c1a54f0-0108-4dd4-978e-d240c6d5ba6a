/**
 * Addon Packages API - Manage client addon packages
 * 
 * This endpoint manages addon packages that can be assigned to clients
 * for controlling page access. Only super-admin can manage packages.
 */

import { NextRequest } from 'next/server'
import { fetchAuthSession } from 'aws-amplify/auth'
import { createSuccessResponse, createErrorResponse, ApiErrorCode, HttpStatus } from '@/lib/api/response'
import { databaseService } from '@/lib/services/database-service'

export interface AddonPackage {
  id: number
  name: string
  display_name: string
  description: string | null
  is_default: boolean
  status: string
  created_on: string
  changed_on: string
}

/**
 * GET /api/addon-packages
 * Get all addon packages (super-admin only)
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await fetchAuthSession()
    if (!session?.tokens?.idToken) {
      return createErrorResponse('Authentication required', ApiErrorCode.UNAUTHORIZED, HttpStatus.UNAUTHORIZED)
    }

    // Extract user groups from token
    const idToken = session.tokens.idToken
    const payload = idToken.payload as any
    
    let userGroups: string[] = []
    
    // Check various possible locations for groups in the token
    if (payload['cognito:groups']) {
      userGroups = Array.isArray(payload['cognito:groups']) 
        ? payload['cognito:groups'] 
        : [payload['cognito:groups']]
    } else if (payload.groups) {
      userGroups = Array.isArray(payload.groups) 
        ? payload.groups 
        : [payload.groups]
    }

    // Normalize groups to lowercase
    const normalizedGroups = userGroups
      .filter(group => typeof group === 'string')
      .map(group => group.toLowerCase().trim())

    console.log('[ADDON-PACKAGES] User groups:', normalizedGroups)

    // Only super-admin can view addon packages
    if (!normalizedGroups.includes('super-admin')) {
      return createErrorResponse('Super admin access required', ApiErrorCode.FORBIDDEN, HttpStatus.FORBIDDEN)
    }

    // Get database connection
    const db = databaseService

    // Query all addon packages
    const query = `
      SELECT 
        id,
        name,
        display_name,
        description,
        is_default,
        status,
        created_on,
        changed_on
      FROM metadata.addon_packages
      ORDER BY display_name ASC
    `

    const result = await db.query(query)
    
    const packages: AddonPackage[] = result.rows.map((row: any) => ({
      id: row.id,
      name: row.name,
      display_name: row.display_name,
      description: row.description,
      is_default: row.is_default,
      status: row.status,
      created_on: row.created_on,
      changed_on: row.changed_on
    }))

    console.log(`[ADDON-PACKAGES] Found ${packages.length} addon packages`)

    return createSuccessResponse(packages)

  } catch (error) {
    console.error('[ADDON-PACKAGES] Error fetching addon packages:', error)
    return createErrorResponse('Failed to fetch addon packages', ApiErrorCode.INTERNAL_ERROR, HttpStatus.INTERNAL_SERVER_ERROR)
  }
}

/**
 * POST /api/addon-packages
 * Create a new addon package (super-admin only)
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await fetchAuthSession()
    if (!session?.tokens?.idToken) {
      return createErrorResponse('Authentication required', ApiErrorCode.UNAUTHORIZED, HttpStatus.UNAUTHORIZED)
    }

    // Extract user groups from token
    const idToken = session.tokens.idToken
    const payload = idToken.payload as any
    
    let userGroups: string[] = []
    
    if (payload['cognito:groups']) {
      userGroups = Array.isArray(payload['cognito:groups']) 
        ? payload['cognito:groups'] 
        : [payload['cognito:groups']]
    } else if (payload.groups) {
      userGroups = Array.isArray(payload.groups) 
        ? payload.groups 
        : [payload.groups]
    }

    const normalizedGroups = userGroups
      .filter(group => typeof group === 'string')
      .map(group => group.toLowerCase().trim())

    // Only super-admin can create addon packages
    if (!normalizedGroups.includes('super-admin')) {
      return createErrorResponse('Super admin access required', ApiErrorCode.FORBIDDEN, HttpStatus.FORBIDDEN)
    }

    const body = await request.json()
    const { name, display_name, description, is_default, status } = body

    // Validate required fields
    if (!name || !display_name) {
      return createErrorResponse('Name and display name are required', ApiErrorCode.VALIDATION_ERROR, HttpStatus.BAD_REQUEST)
    }

    // Get database connection
    const db = databaseService

    // Insert new addon package
    const insertQuery = `
      INSERT INTO metadata.addon_packages (name, display_name, description, is_default, status)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING *
    `

    const result = await db.query(insertQuery, [
      name,
      display_name,
      description || null,
      is_default || false,
      status || 'A'
    ])

    const newPackage = result.rows[0]

    console.log('[ADDON-PACKAGES] Created new addon package:', newPackage.name)

    return createSuccessResponse(newPackage)

  } catch (error) {
    console.error('[ADDON-PACKAGES] Error creating addon package:', error)
    
    // Handle unique constraint violations
    if (error instanceof Error && error.message.includes('unique')) {
      return createErrorResponse('Package name already exists', ApiErrorCode.DATABASE_ERROR, HttpStatus.CONFLICT)
    }
    
    return createErrorResponse('Failed to create addon package', ApiErrorCode.INTERNAL_ERROR, HttpStatus.INTERNAL_SERVER_ERROR)
  }
}

/**
 * License Management API
 * 
 * Super-admin endpoints for license key generation and management
 * POST /api/admin/licenses - Generate new license key
 * GET /api/admin/licenses - List all license keys
 */

import { NextRequest } from 'next/server';
import { fetchAuthSession, fetchUserAttributes } from 'aws-amplify/auth';
import { executeQuery, executeQuerySingle } from '@/lib/database';
import {
  createSuccessResponse,
  createErrorResponse,
  ApiErrorCode,
  HttpStatus,
  withErrorHandling
} from '@/lib/api/response';

interface LicenseKeyRequest {
  licenseType: 'client' | 'trial' | 'enterprise';
  maxUsers?: number;
  maxTenants?: number;
  validityDays?: number;
  features?: string[];
  notes?: string;
}

interface LicenseKey {
  id: number;
  licenseKey: string;
  licenseType: string;
  maxUsers: number;
  maxTenants: number;
  features: any;
  validFrom: string;
  validUntil: string | null;
  isActive: boolean;
  usageCount: number;
  maxUsage: number;
  generatedBy: string;
  generatedAt: string;
  notes: string | null;
}

/**
 * Generate new license key (Super-admin only)
 */
export const POST = withErrorHandling(async (request: NextRequest) => {
  try {
    // Verify authentication
    const session = await fetchAuthSession();
    if (!session?.tokens?.idToken) {
      return createErrorResponse('Authentication required', ApiErrorCode.UNAUTHORIZED, HttpStatus.UNAUTHORIZED);
    }

    // Get user attributes to check super-admin status
    const attributes = await fetchUserAttributes();
    const userGroups = attributes['cognito:groups']?.split(',') || [];
    
    if (!userGroups.includes('super-admin')) {
      return createErrorResponse('Super-admin access required', ApiErrorCode.FORBIDDEN, HttpStatus.FORBIDDEN);
    }

    // Parse request body
    const body: LicenseKeyRequest = await request.json();
    const {
      licenseType = 'client',
      maxUsers = 10,
      maxTenants = 1,
      validityDays,
      features = ['renewals', 'vendors', 'reports', 'notifications'],
      notes
    } = body;

    // Validate input
    if (!['client', 'trial', 'enterprise'].includes(licenseType)) {
      return createErrorResponse('Invalid license type', ApiErrorCode.VALIDATION_ERROR, HttpStatus.BAD_REQUEST);
    }

    // Calculate expiration date
    const validUntil = validityDays 
      ? new Date(Date.now() + validityDays * 24 * 60 * 60 * 1000).toISOString()
      : null;

    // Generate license key
    const generateKeyQuery = `
      INSERT INTO licensing.license_keys (
        license_key,
        license_type,
        max_users,
        max_tenants,
        features,
        valid_to,
        generated_by,
        notes
      ) VALUES (
        licensing.generate_license_key(),
        $1, $2, $3, $4, $5, $6, $7
      )
      RETURNING *
    `;

    const result = await executeQuerySingle<LicenseKey>(generateKeyQuery, [
      licenseType,
      maxUsers,
      maxTenants,
      JSON.stringify(features),
      validUntil,
      attributes.email,
      notes
    ]);

    if (!result.success || !result.data) {
      return createErrorResponse('Failed to generate license key', ApiErrorCode.INTERNAL_ERROR, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    return createSuccessResponse({
      licenseKey: result.data,
      message: 'License key generated successfully'
    });

  } catch (error) {
    console.error('License generation error:', error);
    return createErrorResponse('Failed to generate license key', ApiErrorCode.INTERNAL_ERROR, HttpStatus.INTERNAL_SERVER_ERROR);
  }
});

/**
 * List all license keys (Super-admin only)
 */
export const GET = withErrorHandling(async (request: NextRequest) => {
  try {
    // Verify authentication
    const session = await fetchAuthSession();
    if (!session?.tokens?.idToken) {
      return createErrorResponse('Authentication required', ApiErrorCode.UNAUTHORIZED, HttpStatus.UNAUTHORIZED);
    }

    // Get user attributes to check super-admin status
    const attributes = await fetchUserAttributes();
    const userGroups = attributes['cognito:groups']?.split(',') || [];

    if (!userGroups.some(group => group.toLowerCase() === 'super-admin')) {
      return createErrorResponse('Super-admin access required', ApiErrorCode.FORBIDDEN, HttpStatus.FORBIDDEN);
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100);
    const licenseType = searchParams.get('type');
    const status = searchParams.get('status'); // 'active', 'expired', 'all'
    const offset = (page - 1) * limit;

    // Build query conditions
    let whereConditions = ['1=1'];
    let queryParams: any[] = [];
    let paramIndex = 1;

    if (licenseType) {
      whereConditions.push(`license_type = $${paramIndex}`);
      queryParams.push(licenseType);
      paramIndex++;
    }

    if (status === 'active') {
      whereConditions.push(`is_active = true AND (valid_to IS NULL OR valid_to > CURRENT_TIMESTAMP)`);
    } else if (status === 'expired') {
      whereConditions.push(`is_active = false OR (valid_to IS NOT NULL AND valid_to <= CURRENT_TIMESTAMP)`);
    }

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total
      FROM licensing.license_keys
      WHERE ${whereConditions.join(' AND ')}
    `;

    const countResult = await executeQuerySingle<{ total: number }>(countQuery, queryParams);
    const totalCount = countResult.data?.total || 0;

    // Get license keys with pagination
    const licenseQuery = `
      SELECT 
        lk.*,
        COALESCE(ca.client_count, 0) as activated_clients
      FROM licensing.license_keys lk
      LEFT JOIN (
        SELECT 
          license_key_id,
          COUNT(*) as client_count
        FROM licensing.client_activations
        WHERE activation_status = 'active'
        GROUP BY license_key_id
      ) ca ON lk.id = ca.license_key_id
      WHERE ${whereConditions.join(' AND ')}
      ORDER BY lk.generated_at DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    queryParams.push(limit, offset);

    const result = await executeQuery<LicenseKey & { activated_clients: number }>(
      licenseQuery, 
      queryParams
    );

    if (!result.success) {
      return createErrorResponse('Failed to fetch license keys', ApiErrorCode.INTERNAL_ERROR, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    return createSuccessResponse({
      licenses: result.data || [],
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        hasNext: page * limit < totalCount,
        hasPrev: page > 1
      }
    });

  } catch (error) {
    console.error('License listing error:', error);
    return createErrorResponse('Failed to fetch license keys', ApiErrorCode.INTERNAL_ERROR, HttpStatus.INTERNAL_SERVER_ERROR);
  }
});

/**
 * Amplify Service - Singleton Configuration Manager
 * 
 * Eliminates redundant Amplify configurations by providing a centralized,
 * singleton service that configures Amplify once and reuses the configuration.
 */

import { Amplify } from 'aws-amplify'
import { publicConfig } from '@/lib/config'
import { handleError, getUserFriendlyMessage } from '@/lib/utils/error-handler'

class AmplifyService {
  private static instance: AmplifyService
  private isConfigured = false
  private configurationPromise: Promise<void> | null = null

  private constructor() {
    // Private constructor to enforce singleton pattern


  }

  /**
   * Get singleton instance
   */
  public static getInstance(): AmplifyService {
    // Check for existing instance in development (Fast Refresh persistence)
    if (typeof window !== 'undefined' && publicConfig.app.isDevelopment) {
      // @ts-ignore - Development only
      if (window.__amplifyService && !AmplifyService.instance) {
        // @ts-ignore
        AmplifyService.instance = window.__amplifyService
        return AmplifyService.instance
      }
    }

    if (!AmplifyService.instance) {
      AmplifyService.instance = new AmplifyService()
    }
    return AmplifyService.instance
  }

  /**
   * Configure Amplify (idempotent - safe to call multiple times)
   */
  public async configure(): Promise<void> {
    // Check global state first (for Fast Refresh persistence)
    if (typeof window !== 'undefined' && publicConfig.app.isDevelopment) {
      // @ts-ignore - Development only
      if (window.__amplifyConfigured) {
        this.isConfigured = true
        return
      }
    }

    // If already configured, return immediately
    if (this.isConfigured) {
      return
    }

    // If configuration is in progress, wait for it
    if (this.configurationPromise) {
      return this.configurationPromise
    }

    // Start configuration
    this.configurationPromise = this.performConfiguration()

    try {
      await this.configurationPromise
    } finally {
      this.configurationPromise = null
    }
  }

  /**
   * Perform the actual Amplify configuration
   */
  private async performConfiguration(): Promise<void> {
    try {
      // Validate required configuration values
      const requiredConfig = {
        userPoolId: publicConfig.aws.userPoolId,
        userPoolClientId: publicConfig.aws.userPoolClientId,
        cognitoDomain: publicConfig.aws.cognitoDomain,
        redirectSignIn: publicConfig.auth.redirectSignIn,
        redirectSignOut: publicConfig.auth.redirectSignOut
      };

      // Check for missing configuration
      const missingConfig = Object.entries(requiredConfig)
        .filter(([key, value]) => !value || value.trim() === '')
        .map(([key]) => key);

      if (missingConfig.length > 0) {
        const error = new Error(`Missing required Amplify configuration: ${missingConfig.join(', ')}`);
        handleError(error, { component: 'AmplifyService', operation: 'configure' });
        // Don't throw error - allow app to continue and handle auth errors gracefully
        return;
      }

      // Configure Amplify with proper UserPool configuration for v6 and secure storage
      const amplifyConfig = {
        Auth: {
          Cognito: {
            userPoolId: requiredConfig.userPoolId,
            userPoolClientId: requiredConfig.userPoolClientId,
            loginWith: {
              oauth: {
                domain: requiredConfig.cognitoDomain,
                scopes: ['openid', 'email', 'profile'],
                redirectSignIn: [requiredConfig.redirectSignIn],
                redirectSignOut: [requiredConfig.redirectSignOut],
                responseType: 'code' as const
              }
            }
          }
        }
      };

      Amplify.configure(amplifyConfig, {
        ssr: true, // Enable SSR for better security
        storage: {
          // Use localStorage for web apps with persistent sessions
          // This is more secure than sessionStorage for user experience
          // while still being cleared on logout
          localStorage: typeof window !== 'undefined' ? window.localStorage : undefined
        }
      });

      this.isConfigured = true

      // Set global flag for Fast Refresh persistence
      if (typeof window !== 'undefined' && publicConfig.app.isDevelopment) {
        // @ts-ignore - Development only
        window.__amplifyConfigured = true
      }

    } catch (error) {
      handleError(error as Error, { component: 'AmplifyService', operation: 'configure' });
      throw new Error('Authentication service initialization failed. Please try again or contact support.');
    }
  }

  /**
   * Check if Amplify is configured
   */
  public isAmplifyConfigured(): boolean {
    return this.isConfigured
  }

  /**
   * Get current configuration (for debugging)
   */
  public getConfiguration() {
    return {
      isConfigured: this.isConfigured,
      config: {
        region: publicConfig.aws.region,
        userPoolId: publicConfig.aws.userPoolId,
        userPoolClientId: publicConfig.aws.userPoolClientId,
        cognitoDomain: publicConfig.aws.cognitoDomain,
        redirectSignIn: publicConfig.auth.redirectSignIn,
        redirectSignOut: publicConfig.auth.redirectSignOut
      }
    }
  }

  /**
   * Reset configuration (for testing purposes)
   */
  public reset(): void {
    this.isConfigured = false
    this.configurationPromise = null
  }
}

// Export singleton instance
export const amplifyService = AmplifyService.getInstance()

// Convenience function for easy import
export async function ensureAmplifyConfigured(): Promise<void> {
  await amplifyService.configure()
}

// Export the class for testing
export { AmplifyService }


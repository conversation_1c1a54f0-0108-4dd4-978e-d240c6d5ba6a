/**
 * Performance Monitoring React Hook
 * 
 * Provides easy integration of performance monitoring into React components
 */

import React, { useEffect, useRef, useCallback } from 'react'
import { performanceMonitor } from '@/lib/services/performance-monitor'

interface UsePerformanceMonitoringOptions {
  componentName?: string
  trackRenders?: boolean
  trackProps?: boolean
  trackInteractions?: boolean
}

interface PerformanceHookReturn {
  trackRender: (renderTime?: number) => void
  trackInteraction: (interactionName: string, duration?: number) => void
  trackCustomMetric: (name: string, value: number, tags?: Record<string, string>) => void
  startTimer: (name: string) => () => number
}

/**
 * Hook for monitoring component performance
 */
export function usePerformanceMonitoring(
  options: UsePerformanceMonitoringOptions = {}
): PerformanceHookReturn {
  const {
    componentName = 'UnknownComponent',
    trackRenders = true,
    trackProps = false,
    trackInteractions = false
  } = options

  const renderStartTime = useRef<number>(0)
  const renderCount = useRef<number>(0)
  const timers = useRef<Map<string, number>>(new Map())

  // Track component mount/unmount
  useEffect(() => {
    const mountTime = Date.now()
    
    return () => {
      const unmountTime = Date.now()
      const lifetimeMs = unmountTime - mountTime
      
      performanceMonitor.trackMetric('component_lifetime', lifetimeMs, {
        component: componentName,
        type: 'lifetime'
      })
    }
  }, [componentName])

  // Track renders if enabled
  useEffect(() => {
    if (trackRenders) {
      const renderEndTime = Date.now()
      const renderTime = renderStartTime.current > 0 
        ? renderEndTime - renderStartTime.current 
        : 0

      renderCount.current++

      if (renderTime > 0) {
        performanceMonitor.trackComponentRender(
          componentName,
          renderTime,
          0, // Props size would need to be calculated separately
          renderCount.current
        )
      }
    }
  })

  // Set render start time before each render
  if (trackRenders) {
    renderStartTime.current = Date.now()
  }

  /**
   * Manually track a render with optional custom render time
   */
  const trackRender = useCallback((renderTime?: number) => {
    const actualRenderTime = renderTime ?? (Date.now() - renderStartTime.current)
    renderCount.current++
    
    performanceMonitor.trackComponentRender(
      componentName,
      actualRenderTime,
      0,
      renderCount.current
    )
  }, [componentName])

  /**
   * Track user interactions
   */
  const trackInteraction = useCallback((interactionName: string, duration?: number) => {
    const actualDuration = duration ?? 0
    
    performanceMonitor.trackMetric('user_interaction', actualDuration, {
      component: componentName,
      interaction: interactionName,
      type: 'interaction'
    })
  }, [componentName])

  /**
   * Track custom metrics
   */
  const trackCustomMetric = useCallback((
    name: string, 
    value: number, 
    tags?: Record<string, string>
  ) => {
    performanceMonitor.trackMetric(name, value, {
      component: componentName,
      ...tags
    })
  }, [componentName])

  /**
   * Start a timer and return a function to stop it
   */
  const startTimer = useCallback((name: string) => {
    const startTime = Date.now()
    timers.current.set(name, startTime)
    
    return () => {
      const endTime = Date.now()
      const duration = endTime - startTime
      timers.current.delete(name)
      
      performanceMonitor.trackMetric(`timer_${name}`, duration, {
        component: componentName,
        type: 'timer'
      })
      
      return duration
    }
  }, [componentName])

  return {
    trackRender,
    trackInteraction,
    trackCustomMetric,
    startTimer
  }
}

/**
 * Hook for monitoring API calls
 */
export function useAPIPerformanceMonitoring() {
  const trackAPICall = useCallback((
    endpoint: string,
    method: string,
    responseTime: number,
    statusCode: number,
    tenantId?: string
  ) => {
    performanceMonitor.trackAPICall(endpoint, method, responseTime, statusCode, tenantId)
  }, [])

  const wrapAPICall = useCallback(async (
    apiCall: () => Promise<any>,
    endpoint: string,
    method: string = 'GET',
    tenantId?: string
  ): Promise<any> => {
    const startTime = Date.now()
    let statusCode = 200

    try {
      const result = await apiCall()
      return result
    } catch (error: any) {
      statusCode = error.status || error.statusCode || 500
      throw error
    } finally {
      const responseTime = Date.now() - startTime
      trackAPICall(endpoint, method, responseTime, statusCode, tenantId)
    }
  }, [trackAPICall])

  return {
    trackAPICall,
    wrapAPICall
  }
}

/**
 * Hook for getting performance metrics
 */
export function usePerformanceMetrics() {
  const getMetrics = useCallback(() => {
    return performanceMonitor.getPerformanceSummary()
  }, [])

  return {
    getMetrics
  }
}

/**
 * Higher-order component for automatic performance monitoring
 */
export function withPerformanceMonitoring<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  componentName?: string
) {
  const displayName = componentName || WrappedComponent.displayName || WrappedComponent.name || 'Component'
  
  const PerformanceMonitoredComponent = (props: P) => {
    const { trackRender, trackCustomMetric } = usePerformanceMonitoring({
      componentName: displayName,
      trackRenders: true
    })

    // Track props changes
    const prevPropsRef = useRef<P>()
    useEffect(() => {
      if (prevPropsRef.current) {
        const propsChanged = JSON.stringify(prevPropsRef.current) !== JSON.stringify(props)
        if (propsChanged) {
          trackCustomMetric('props_change', 1, { type: 'props-change' })
        }
      }
      prevPropsRef.current = props
    })

    return <WrappedComponent {...props} />
  }

  PerformanceMonitoredComponent.displayName = `withPerformanceMonitoring(${displayName})`
  
  return PerformanceMonitoredComponent
}

/**
 * Hook for monitoring form performance
 */
export function useFormPerformanceMonitoring(formName: string) {
  const { trackInteraction, trackCustomMetric, startTimer } = usePerformanceMonitoring({
    componentName: `Form_${formName}`,
    trackInteractions: true
  })

  const trackFieldChange = useCallback((fieldName: string) => {
    trackInteraction('field_change', 0)
    trackCustomMetric('field_change', 1, { field: fieldName })
  }, [trackInteraction, trackCustomMetric])

  const trackFormSubmission = useCallback((success: boolean, validationErrors?: number) => {
    trackInteraction('form_submit', 0)
    trackCustomMetric('form_submit', success ? 1 : 0, { 
      success: success.toString(),
      errors: validationErrors?.toString() || '0'
    })
  }, [trackInteraction, trackCustomMetric])

  const trackValidation = useCallback((fieldName: string, isValid: boolean) => {
    trackCustomMetric('field_validation', isValid ? 1 : 0, { 
      field: fieldName,
      valid: isValid.toString()
    })
  }, [trackCustomMetric])

  return {
    trackFieldChange,
    trackFormSubmission,
    trackValidation,
    startFormTimer: (timerName: string) => startTimer(`form_${timerName}`)
  }
}

/**
 * Hook for monitoring data loading performance
 */
export function useDataLoadingPerformance(dataSource: string) {
  const { trackCustomMetric, startTimer } = usePerformanceMonitoring({
    componentName: `DataLoader_${dataSource}`
  })

  const trackDataLoad = useCallback((
    success: boolean,
    recordCount?: number,
    cacheHit?: boolean
  ) => {
    trackCustomMetric('data_load', success ? 1 : 0, {
      source: dataSource,
      success: success.toString(),
      records: recordCount?.toString() || '0',
      cache_hit: cacheHit?.toString() || 'false'
    })
  }, [trackCustomMetric, dataSource])

  const trackCachePerformance = useCallback((hit: boolean, size?: number) => {
    trackCustomMetric('cache_performance', hit ? 1 : 0, {
      source: dataSource,
      hit: hit.toString(),
      size: size?.toString() || '0'
    })
  }, [trackCustomMetric, dataSource])

  return {
    trackDataLoad,
    trackCachePerformance,
    startLoadTimer: (timerName: string) => startTimer(`load_${timerName}`)
  }
}

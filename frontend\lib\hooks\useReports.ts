/**
 * Reports Hook
 *
 * Custom hook for fetching and managing reports data
 */

import { useState, useCallback } from 'react'
import { useData } from './useData'
import { API_BASE } from '@/lib/constants/api-endpoints'
import { Renewal } from '@/lib/types'

export interface ReportsStats {
  totalRenewals: number
  totalValue: number
  averageValue: number
  upcomingRenewals: number
  vendorCount: number
  typeBreakdown: Record<string, number>
}

export interface ReportsFilters {
  vendor?: string
  renewalType?: string
  renewalFrom?: string
  renewalTo?: string
  includeArchived?: boolean
}

export interface ReportsData {
  renewals: Renewal[]
  stats: ReportsStats
  filters: ReportsFilters
}

export interface UseReportsOptions {
  filters?: ReportsFilters
  enabled?: boolean
}

export function useReports(options: UseReportsOptions = {}) {
  const { filters = {}, enabled = true } = options

  // Build query parameters
  const queryParams = new URLSearchParams()
  
  if (filters.vendor) queryParams.set('vendor', filters.vendor)
  if (filters.renewalType) queryParams.set('renewalType', filters.renewalType)
  if (filters.renewalFrom) queryParams.set('renewalFrom', filters.renewalFrom)
  if (filters.renewalTo) queryParams.set('renewalTo', filters.renewalTo)
  if (filters.includeArchived) queryParams.set('includeArchived', 'true')

  const queryString = queryParams.toString()
  const endpoint = `${API_BASE}/reports${queryString ? `?${queryString}` : ''}`

  const {
    data,
    loading,
    error,
    refetch,
    mutate
  } = useData<ReportsData>({
    endpoint,
    cache: {
      key: `reports-${queryString}`,
      ttl: 5 * 60 * 1000, // 5 minutes cache
    },
    options: {
      enabled,
      retryAttempts: 2,
      retryDelay: 1000
    }
  })

  return {
    // Data
    renewals: data?.renewals || [],
    stats: data?.stats || {
      totalRenewals: 0,
      totalValue: 0,
      averageValue: 0,
      upcomingRenewals: 0,
      vendorCount: 0,
      typeBreakdown: {}
    },
    appliedFilters: data?.filters || {},
    
    // State
    isLoading: loading,
    error,
    
    // Actions
    refetch,
    mutate,
    
    // Convenience methods
    refresh: () => refetch(),
    invalidateCache: () => mutate(null)
  }
}

/**
 * Hook for reports with real-time filtering
 */
export function useReportsWithFilters(initialFilters: ReportsFilters = {}) {
  const [filters, setFilters] = useState<ReportsFilters>(initialFilters)
  
  const reports = useReports({ filters })
  
  const updateFilter = useCallback((key: keyof ReportsFilters, value: string | boolean | undefined) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }))
  }, [])
  
  const clearFilters = useCallback(() => {
    setFilters({})
  }, [])
  
  const resetFilters = useCallback(() => {
    setFilters(initialFilters)
  }, [initialFilters])
  
  return {
    ...reports,
    filters,
    updateFilter,
    clearFilters,
    resetFilters
  }
}

// Re-export for convenience
export type { Renewal } from '@/lib/types'

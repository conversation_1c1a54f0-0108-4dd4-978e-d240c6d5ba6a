import { NextRequest, NextResponse } from 'next/server';
import { updateClient } from '@/lib/tenant/clients';
import { fetchAuthSession } from 'aws-amplify/auth';
import { z } from 'zod';

// Define schema for client updates
const updateClientSchema = z.object({
  name: z.string().min(2).max(255).optional(),
  domain: z.string().min(3).max(255).regex(
    /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\.[a-zA-Z]{2,})+$/,
    'Invalid domain format'
  ).optional(),
  status: z.enum(['active', 'inactive', 'pending']).optional(),
  settings: z.record(z.unknown()).optional()
});

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Verify authentication using Amplify
    const session = await fetchAuthSession();
    if (!session?.tokens?.idToken) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // For admin operations, we should check user groups/roles
    // This would need to be implemented based on Cognito groups
    // For now, allowing authenticated users
  } catch (error) {
    return NextResponse.json({ error: 'Authentication failed' }, { status: 401 });
  }

  const resolvedParams = await params;
  const clientId = resolvedParams.id;
  if (!clientId) {
    return NextResponse.json({ error: 'Client ID is required' }, { status: 400 });
  }
  
  try {
    const body = await request.json();
    
    // Validate input
    try {
      updateClientSchema.parse(body);
    } catch (error) {
      return NextResponse.json({ 
        error: 'Invalid client data',
        details: error instanceof Error ? error.message : 'Validation error'
      }, { status: 400 });
    }
    
    const updatedClient = await updateClient(clientId, {
      name: body.name,
      domain: body.domain,
      status: body.status,
      settings: body.settings
    });
    
    if (!updatedClient) {
      return NextResponse.json({ error: 'Client not found' }, { status: 404 });
    }
    
    return NextResponse.json(updatedClient);
  } catch (error) {
    console.error('Error updating client:', error);
    return NextResponse.json({ error: 'Failed to update client' }, { status: 500 });
  }
}
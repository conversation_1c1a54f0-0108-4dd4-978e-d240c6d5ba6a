# RenewTrack Production Deployment Guide

This guide provides comprehensive instructions for deploying RenewTrack to AWS production environment with enterprise-grade security, monitoring, and scalability.

## Architecture Overview

The production deployment includes:

- **Multi-AZ VPC** with public, private, and database subnets
- **Application Load Balancer** with SSL termination
- **Auto Scaling Group** with EC2 instances in private subnets
- **RDS PostgreSQL** with Multi-AZ, encryption, and automated backups
- **Comprehensive monitoring** with CloudWatch, SNS alerts, and dashboards
- **Secrets management** using AWS Parameter Store and Secrets Manager
- **Enhanced security** with IAM roles, security groups, and encryption

## Prerequisites

### Required Tools
- AWS CLI v2.x configured with appropriate permissions
- jq (for JSON processing)
- OpenSSL (for generating secrets)
- Git

### Required AWS Permissions
The deployment user/role needs permissions for:
- CloudFormation (full access)
- EC2 (full access)
- RDS (full access)
- IAM (full access)
- S3 (full access)
- CloudWatch (full access)
- SNS (full access)
- Lambda (full access)
- Parameter Store (full access)
- Secrets Manager (full access)

### Required Environment Variables

```bash
# Required
export KEY_PAIR_NAME="your-ec2-keypair"
export DATABASE_PASSWORD="your-secure-db-password"
export JWT_SECRET="your-jwt-secret-32-chars-min"
export ENCRYPTION_KEY="your-encryption-key-32-chars-min"

# Optional (with defaults)
export ENVIRONMENT="prod"
export APPLICATION_NAME="renewtrack"
export AWS_REGION="ca-central-1"
export INSTANCE_TYPE="t3.medium"
export MIN_SIZE="2"
export MAX_SIZE="10"
export DESIRED_CAPACITY="2"
export CERTIFICATE_ARN="arn:aws:acm:region:account:certificate/cert-id"
export DOMAIN_NAME="renewtrack.com"
export ALERT_EMAIL="<EMAIL>"
```

## Deployment Steps

### 1. Generate Secrets (if not provided)

```bash
# Generate JWT secret (64 characters)
export JWT_SECRET=$(openssl rand -base64 48)

# Generate encryption key (64 characters)
export ENCRYPTION_KEY=$(openssl rand -base64 48)

# Generate strong database password
export DATABASE_PASSWORD=$(openssl rand -base64 32)
```

### 2. Create EC2 Key Pair

```bash
# Create key pair for SSH access
aws ec2 create-key-pair \
    --key-name renewtrack-prod \
    --query 'KeyMaterial' \
    --output text > ~/.ssh/renewtrack-prod.pem

chmod 400 ~/.ssh/renewtrack-prod.pem
export KEY_PAIR_NAME="renewtrack-prod"
```

### 3. Request SSL Certificate (Optional but Recommended)

```bash
# Request certificate for your domain
aws acm request-certificate \
    --domain-name renewtrack.com \
    --subject-alternative-names "*.renewtrack.com" \
    --validation-method DNS \
    --region ca-central-1

# Note the certificate ARN and export it
export CERTIFICATE_ARN="arn:aws:acm:ca-central-1:123456789012:certificate/12345678-1234-1234-1234-123456789012"
```

### 4. Run Deployment Script

```bash
# Make script executable
chmod +x scripts/deploy-production.sh

# Run deployment
./scripts/deploy-production.sh
```

### 5. Manual Steps After Deployment

#### Configure DNS
Point your domain to the load balancer:
```bash
# Get load balancer DNS name
aws cloudformation describe-stacks \
    --stack-name renewtrack-application-prod \
    --query 'Stacks[0].Outputs[?OutputKey==`LoadBalancerDNS`].OutputValue' \
    --output text
```

Create CNAME record: `renewtrack.com` → `your-alb-dns-name`

#### Deploy Application Code
```bash
# SSH to instances and deploy code
ssh -i ~/.ssh/renewtrack-prod.pem ec2-user@instance-ip

# Clone repository
sudo -u renewtrack git clone https://github.com/your-org/renewtrack.git /opt/renewtrack

# Install dependencies
cd /opt/renewtrack
sudo -u renewtrack npm install --production

# Start application
sudo systemctl start renewtrack
sudo systemctl enable renewtrack
```

## Stack Components

### 1. Secrets Infrastructure (`secrets-infrastructure.yaml`)
- KMS key for encryption
- Parameter Store parameters for configuration
- Secrets Manager secrets for sensitive data
- IAM roles for secrets access

### 2. Core Infrastructure (`production-infrastructure.yaml`)
- VPC with public, private, and database subnets
- Internet Gateway and NAT Gateways
- Security Groups for different tiers
- Route tables and network ACLs

### 3. Application Deployment (`application-deployment.yaml`)
- Application Load Balancer with SSL
- Auto Scaling Group with launch template
- CloudWatch alarms for scaling
- IAM roles for EC2 instances

### 4. Database Infrastructure (`database-infrastructure.yaml`)
- RDS PostgreSQL with Multi-AZ
- Read replica for production
- Parameter and option groups
- Enhanced monitoring and Performance Insights
- Automated backups and snapshots

### 5. Monitoring Infrastructure (`monitoring-infrastructure.yaml`)
- CloudWatch dashboards
- SNS topics for alerts
- Lambda functions for Slack notifications
- Custom metrics collection
- Log groups and retention policies

## Security Features

### Network Security
- Private subnets for application and database tiers
- Security groups with least privilege access
- NACLs for additional network protection
- VPC Flow Logs for network monitoring

### Data Security
- Encryption at rest for RDS and EBS volumes
- Encryption in transit with SSL/TLS
- Secrets stored in AWS Secrets Manager
- KMS keys for encryption key management

### Access Control
- IAM roles with minimal required permissions
- No hardcoded credentials in code
- Session-based authentication with JWT
- Multi-factor authentication support

### Monitoring & Auditing
- Comprehensive audit logging
- CloudTrail for API call logging
- CloudWatch for application monitoring
- SNS alerts for security events

## Monitoring & Alerting

### CloudWatch Dashboards
- Application overview dashboard
- Database performance dashboard
- Infrastructure metrics dashboard

### Alerts Configuration
- **Critical Alerts**: Database failures, application errors, security events
- **Warning Alerts**: High CPU, slow response times, storage issues
- **Info Alerts**: Scaling events, deployments, routine operations

### Log Management
- Application logs in CloudWatch Logs
- System logs with retention policies
- Centralized log aggregation
- Log-based metrics and alarms

## Backup & Disaster Recovery

### Database Backups
- Automated daily backups with 30-day retention
- Point-in-time recovery capability
- Cross-region backup replication (optional)
- Manual snapshot before major changes

### Application Backups
- AMI snapshots of configured instances
- Code repository backups
- Configuration backups in S3
- Secrets backup in Secrets Manager

### Disaster Recovery Plan
1. **RTO (Recovery Time Objective)**: 4 hours
2. **RPO (Recovery Point Objective)**: 1 hour
3. **Multi-AZ deployment** for automatic failover
4. **Cross-region replication** for major disasters

## Scaling Configuration

### Auto Scaling Policies
- **Scale Up**: CPU > 70% for 10 minutes
- **Scale Down**: CPU < 30% for 10 minutes
- **Minimum Instances**: 2
- **Maximum Instances**: 10

### Database Scaling
- **Storage Auto Scaling**: Enabled up to 1TB
- **Read Replicas**: Available for read scaling
- **Connection Pooling**: Configured in application

## Maintenance Procedures

### Regular Maintenance
- **Weekly**: Review CloudWatch dashboards and alerts
- **Monthly**: Update security patches and dependencies
- **Quarterly**: Review and update backup procedures
- **Annually**: Disaster recovery testing

### Update Procedures
1. Test changes in staging environment
2. Create database snapshot before updates
3. Deploy during maintenance window
4. Monitor application health post-deployment
5. Rollback plan if issues occur

## Troubleshooting

### Common Issues

#### Application Not Responding
1. Check load balancer health checks
2. Verify security group rules
3. Check application logs in CloudWatch
4. Verify database connectivity

#### Database Connection Issues
1. Check security group rules
2. Verify database status in RDS console
3. Check connection pool settings
4. Review database logs

#### High CPU Usage
1. Check CloudWatch metrics
2. Review application performance
3. Consider scaling up instance types
4. Optimize database queries

### Log Locations
- **Application Logs**: `/aws/ec2/renewtrack/application`
- **System Logs**: `/aws/ec2/renewtrack/system`
- **Database Logs**: RDS console → Logs and events

## Cost Optimization

### Cost Monitoring
- Set up billing alerts
- Use AWS Cost Explorer
- Review Reserved Instance opportunities
- Monitor unused resources

### Optimization Strategies
- Use appropriate instance types
- Implement lifecycle policies for logs
- Optimize database storage
- Use Spot Instances for non-critical workloads

## Support & Contacts

### Emergency Contacts
- **Primary**: <EMAIL>
- **Secondary**: <EMAIL>
- **Slack**: #renewtrack-alerts

### AWS Support
- **Support Plan**: Business or Enterprise
- **Support Cases**: AWS Console → Support
- **Documentation**: AWS Documentation Portal

## Appendix

### Useful Commands

```bash
# Check stack status
aws cloudformation describe-stacks --stack-name renewtrack-application-prod

# View stack events
aws cloudformation describe-stack-events --stack-name renewtrack-application-prod

# Get stack outputs
aws cloudformation describe-stacks \
    --stack-name renewtrack-application-prod \
    --query 'Stacks[0].Outputs'

# Check application health
curl -f http://your-load-balancer-dns/api/health

# View application logs
aws logs tail /aws/ec2/renewtrack/application --follow

# Connect to instance
aws ssm start-session --target i-1234567890abcdef0
```

### Configuration Files
- CloudFormation templates: `cloudformation/`
- Deployment scripts: `scripts/`
- Application configuration: `.env.prod`
- Database migrations: `database/migrations/`

---

For additional support or questions, please contact the DevOps team or create an issue in the project repository.

# Master Data Synchronization System

## Overview

The Master Data Synchronization System implements a sophisticated lazy synchronization pattern for managing vendor, product, and product version data across multiple tenants. The system maintains global canonical tables while providing tenant-specific views and seamless CRUD operations.

## Architecture

### Core Components

1. **SyncEngine** - Main orchestrator for synchronization operations
2. **Processors** - Entity-specific synchronization logic (Vendor, Product, ProductVersion)
3. **Matchers** - Sophisticated matching algorithms for entity deduplication
4. **TenantSyncService** - Tenant-facing APIs with automatic sync integration
5. **AdminSyncService** - Administrative APIs for conflict resolution
6. **BackgroundJobService** - Async processing with retry logic

### Database Schema

#### Global Tables (metadata schema)
- `global_vendors` - Canonical vendor data
- `global_products` - Canonical product data  
- `global_product_versions` - Canonical version data
- `sync_batches` - Synchronization batch tracking
- `sync_conflicts` - Conflicts requiring manual resolution
- `sync_jobs` - Background job queue

#### Tenant Tables (tenant_* schemas)
- `tenant_vendors` - Tenant-specific vendor data
- `tenant_products` - Tenant-specific product data
- `tenant_product_versions` - Tenant-specific version data
- `tenant_vendor_sync` - Vendor synchronization status
- `tenant_product_sync` - Product synchronization status
- `tenant_product_version_sync` - Version synchronization status

## Matching Algorithms

### Vendor Matching
1. **Exact Tax ID match** (95% confidence)
   - Normalized tax ID comparison
   - Highest priority, definitive match

2. **Domain + Name match** (90% confidence)
   - Domain normalization (remove www, protocol)
   - Name similarity >= 70%

3. **Fuzzy Name + Address match** (70-85% confidence)
   - Levenshtein distance for name similarity
   - Address component comparison
   - Weighted scoring algorithm

### Product Matching
1. **GTIN match** (95% confidence)
   - Global Trade Item Number comparison
   - Definitive product identification

2. **Vendor + SKU match** (90% confidence)
   - Within same vendor context
   - Normalized SKU comparison

3. **Vendor + Fuzzy Name match** (70-85% confidence)
   - Name similarity with category boost
   - Within vendor context only

### Product Version Matching
1. **Exact version string** (95% confidence)
   - Normalized version comparison
   - Within product context

2. **Semantic version match** (90% confidence)
   - Major.minor.patch parsing
   - Structured version comparison

3. **Fuzzy version + date match** (70-85% confidence)
   - Version similarity + release date proximity
   - Weighted scoring algorithm

## Confidence Thresholds

- **Auto-match**: >85% confidence
- **Manual review**: 50-85% confidence  
- **Reject**: <50% confidence

## API Usage

### Tenant APIs

```typescript
// Create vendor with automatic sync
const result = await tenantSyncService.createVendor(tenantId, {
  name: "Acme Corp",
  taxId: "12-3456789",
  domain: "acme.com"
})

// Get vendor with sync status
const vendor = await tenantSyncService.getVendor(tenantId, vendorId)

// List vendors with filtering
const vendors = await tenantSyncService.listVendors(tenantId, {
  syncStatus: 'synced',
  search: 'acme'
})

// Manual sync trigger
const syncResult = await tenantSyncService.syncVendor(tenantId, vendorId)
```

### Admin APIs

```typescript
// Get pending conflicts
const conflicts = await adminSyncService.getPendingConflicts({
  entityType: 'vendor',
  limit: 50
})

// Resolve conflict
const resolution = await adminSyncService.resolveConflict(conflictId, {
  action: 'accept_match',
  selectedMatchId: 'global-vendor-123'
}, adminUserId)

// System-wide sync
const systemSync = await adminSyncService.triggerSystemSync({
  entityTypes: ['vendor', 'product'],
  dryRun: false
})

// Get system statistics
const stats = await adminSyncService.getSystemStats()
```

### Background Jobs

```typescript
// Queue sync job
const jobId = await backgroundJobService.queueSyncJob(tenantId, 'vendor_sync', {
  priority: 8,
  maxAttempts: 3
})

// Check job status
const job = await backgroundJobService.getJobStatus(jobId)

// Schedule recurring sync
await backgroundJobService.scheduleRecurringSync('full_sync', 24) // Every 24 hours
```

## Configuration

### Sync Options
```typescript
interface SyncOptions {
  batchSize?: number          // Default: 100
  maxRetries?: number         // Default: 3
  retryDelayMs?: number       // Default: exponential backoff
  autoResolveThreshold?: number // Default: 85
  dryRun?: boolean           // Default: false
}
```

### Job Priorities
- 1-3: Low priority (scheduled jobs)
- 4-6: Normal priority (user-triggered)
- 7-10: High priority (critical operations)

## Monitoring

### Key Metrics
- Sync success rate
- Average processing time
- Conflict rate by entity type
- Queue depth and processing lag
- Match confidence distribution

### Logging
All operations are comprehensively logged with:
- Tenant context
- Batch/job identifiers
- Performance metrics
- Error details
- Match confidence scores

## Error Handling

### Retry Logic
- Exponential backoff: 2^attempt seconds (max 5 minutes)
- Configurable max attempts per job
- Permanent failure for non-retryable errors

### Conflict Resolution
- Automatic resolution for high-confidence matches
- Manual review queue for ambiguous cases
- Administrative override capabilities
- Audit trail for all resolutions

## Performance Considerations

### Optimization Strategies
1. **Batch Processing** - Process entities in configurable batches
2. **Lazy Loading** - Only sync when needed
3. **Caching** - Cache global data for matching
4. **Indexing** - Optimized database indexes for matching queries
5. **Async Processing** - Background job queue for heavy operations

### Scaling
- Horizontal scaling via multiple job processors
- Database connection pooling
- Tenant-based sharding support
- Rate limiting for API operations

## Security

### Access Control
- Tenant isolation at database level
- Role-based access for admin functions
- Audit logging for all operations
- Secure conflict data handling

### Data Privacy
- Tenant data remains in tenant schemas
- Global data is anonymized/aggregated
- Configurable data retention policies
- GDPR compliance considerations

## Deployment

### Prerequisites
- PostgreSQL 12+ with UUID extension
- Node.js 18+ runtime
- Redis for job queue (optional)
- Monitoring infrastructure

### Migration Process
1. Run database migrations in order
2. Initialize global tables
3. Configure sync thresholds
4. Start background job processor
5. Monitor initial sync operations

### Health Checks
- Database connectivity
- Job processor status
- Queue depth monitoring
- Error rate tracking

/**
 * Client Addon Packages API - Manage client package assignments
 * 
 * This endpoint manages the assignment of addon packages to clients.
 * Only super-admin can manage client package assignments.
 */

import { NextRequest } from 'next/server'
import { fetchAuthSession } from 'aws-amplify/auth'
import { createSuccessResponse, createErrorResponse } from '@/lib/api'
import { ApiErrorCode, HttpStatus } from '@/lib/api/response'
import { databaseService } from '@/lib/services/database-service'

export interface ClientAddonPackage {
  id: number
  client_id: string
  name: string
  domain: string
  package_id: number
  package_name: string
  package_display_name: string
  assigned_on: string
  assigned_by: string | null
  status: string
}

/**
 * GET /api/client-addon-packages
 * Get all client addon package assignments (super-admin only)
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await fetchAuthSession()
    if (!session?.tokens?.idToken) {
      return createErrorResponse('Authentication required', ApiErrorCode.UNAUTHORIZED, HttpStatus.UNAUTHORIZED)
    }

    // Extract user groups from token
    const idToken = session.tokens.idToken
    const payload = idToken.payload as any
    
    let userGroups: string[] = []
    
    if (payload['cognito:groups']) {
      userGroups = Array.isArray(payload['cognito:groups']) 
        ? payload['cognito:groups'] 
        : [payload['cognito:groups']]
    } else if (payload.groups) {
      userGroups = Array.isArray(payload.groups) 
        ? payload.groups 
        : [payload.groups]
    }

    const normalizedGroups = userGroups
      .filter(group => typeof group === 'string')
      .map(group => group.toLowerCase().trim())

    // Only super-admin can view client package assignments
    if (!normalizedGroups.includes('super-admin')) {
      return createErrorResponse('Super admin access required', ApiErrorCode.FORBIDDEN, HttpStatus.FORBIDDEN)
    }

    // Get database connection
    const db = databaseService

    // Query client addon package assignments with client and package details
    const query = `
      SELECT
        cap.id,
        cap.client_id,
        c.name,
        c.domain[1] as domain,
        cap.package_id,
        ap.name as package_name,
        ap.display_name as package_display_name,
        cap.assigned_on,
        cap.assigned_by,
        cap.status
      FROM metadata.client_addon_packages cap
      INNER JOIN metadata.clients c ON cap.client_id = c.id
      INNER JOIN metadata.addon_packages ap ON cap.package_id = ap.id
      WHERE cap.status = 'A'
      ORDER BY c.name ASC, ap.display_name ASC
    `

    const result = await db.query(query)
    
    const assignments: ClientAddonPackage[] = result.rows.map((row: any) => ({
      id: row.id,
      client_id: row.client_id,
      name: row.name,
      domain: row.domain,
      package_id: row.package_id,
      package_name: row.package_name,
      package_display_name: row.package_display_name,
      assigned_on: row.assigned_on,
      assigned_by: row.assigned_by,
      status: row.status
    }))

    console.log(`[CLIENT-ADDON-PACKAGES] Found ${assignments.length} client package assignments`)

    return createSuccessResponse(assignments)

  } catch (error) {
    console.error('[CLIENT-ADDON-PACKAGES] Error fetching client package assignments:', error)
    return createErrorResponse('Failed to fetch client package assignments', ApiErrorCode.INTERNAL_ERROR, HttpStatus.INTERNAL_SERVER_ERROR)
  }
}

/**
 * POST /api/client-addon-packages
 * Assign an addon package to a client (super-admin only)
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await fetchAuthSession()
    if (!session?.tokens?.idToken) {
      return createErrorResponse('Authentication required', ApiErrorCode.UNAUTHORIZED, HttpStatus.UNAUTHORIZED)
    }

    // Extract user groups and email from token
    const idToken = session.tokens.idToken
    const payload = idToken.payload as any
    
    let userGroups: string[] = []
    const userEmail = payload.email || payload['cognito:username'] || ''
    
    if (payload['cognito:groups']) {
      userGroups = Array.isArray(payload['cognito:groups']) 
        ? payload['cognito:groups'] 
        : [payload['cognito:groups']]
    } else if (payload.groups) {
      userGroups = Array.isArray(payload.groups) 
        ? payload.groups 
        : [payload.groups]
    }

    const normalizedGroups = userGroups
      .filter(group => typeof group === 'string')
      .map(group => group.toLowerCase().trim())

    // Only super-admin can assign packages to clients
    if (!normalizedGroups.includes('super-admin')) {
      return createErrorResponse('Super admin access required', ApiErrorCode.FORBIDDEN, HttpStatus.FORBIDDEN)
    }

    const body = await request.json()
    const { client_id, package_id } = body

    // Validate required fields
    if (!client_id || !package_id) {
      return createErrorResponse('Client ID and package ID are required', ApiErrorCode.VALIDATION_ERROR, HttpStatus.BAD_REQUEST)
    }

    // Get database connection
    const db = databaseService

    // Insert new client addon package assignment
    const insertQuery = `
      INSERT INTO metadata.client_addon_packages (client_id, package_id, assigned_by, status)
      VALUES ($1, $2, $3, 'A')
      RETURNING *
    `

    const result = await db.query(insertQuery, [
      client_id,
      package_id,
      userEmail
    ])

    const newAssignment = result.rows[0]

    console.log('[CLIENT-ADDON-PACKAGES] Assigned package to client:', { client_id, package_id })

    return createSuccessResponse(newAssignment)

  } catch (error) {
    console.error('[CLIENT-ADDON-PACKAGES] Error assigning package to client:', error)
    
    // Handle unique constraint violations
    if (error instanceof Error && error.message.includes('unique')) {
      return createErrorResponse('Package already assigned to this client', ApiErrorCode.VALIDATION_ERROR, HttpStatus.CONFLICT)
    }
    
    return createErrorResponse('Failed to assign package to client', ApiErrorCode.INTERNAL_ERROR, HttpStatus.INTERNAL_SERVER_ERROR)
  }
}

/**
 * DELETE /api/client-addon-packages
 * Remove a package assignment from a client (super-admin only)
 */
export async function DELETE(request: NextRequest) {
  try {
    // Check authentication
    const session = await fetchAuthSession()
    if (!session?.tokens?.idToken) {
      return createErrorResponse('Authentication required', ApiErrorCode.UNAUTHORIZED, HttpStatus.UNAUTHORIZED)
    }

    // Extract user groups from token
    const idToken = session.tokens.idToken
    const payload = idToken.payload as any
    
    let userGroups: string[] = []
    
    if (payload['cognito:groups']) {
      userGroups = Array.isArray(payload['cognito:groups']) 
        ? payload['cognito:groups'] 
        : [payload['cognito:groups']]
    }

    const normalizedGroups = userGroups
      .filter(group => typeof group === 'string')
      .map(group => group.toLowerCase().trim())

    // Only super-admin can remove package assignments
    if (!normalizedGroups.includes('super-admin')) {
      return createErrorResponse('Super admin access required', ApiErrorCode.FORBIDDEN, HttpStatus.FORBIDDEN)
    }

    const body = await request.json()
    const { client_id, package_id } = body

    if (!client_id || !package_id) {
      return createErrorResponse('Client ID and package ID are required', ApiErrorCode.VALIDATION_ERROR, HttpStatus.BAD_REQUEST)
    }

    // Get database connection
    const db = databaseService

    // Remove the assignment
    const deleteQuery = `
      DELETE FROM metadata.client_addon_packages 
      WHERE client_id = $1 AND package_id = $2
      RETURNING *
    `

    const result = await db.query(deleteQuery, [client_id, package_id])

    if (result.rows.length === 0) {
      return createErrorResponse('Package assignment not found', ApiErrorCode.NOT_FOUND, HttpStatus.NOT_FOUND)
    }

    console.log('[CLIENT-ADDON-PACKAGES] Removed package assignment:', { client_id, package_id })

    return createSuccessResponse({ message: 'Package assignment removed successfully' })

  } catch (error) {
    console.error('[CLIENT-ADDON-PACKAGES] Error removing package assignment:', error)
    return createErrorResponse('Failed to remove package assignment', ApiErrorCode.INTERNAL_ERROR, HttpStatus.INTERNAL_SERVER_ERROR)
  }
}

AWSTemplateFormatVersion: '2010-09-09'
Description: 'RenewTrack Database Infrastructure - RDS PostgreSQL with Multi-AZ, Backup, and Monitoring'

Parameters:
  Environment:
    Type: String
    Default: 'prod'
    AllowedValues: ['dev', 'staging', 'prod']
    Description: 'Environment name'
  
  ApplicationName:
    Type: String
    Default: 'renewtrack'
    Description: 'Application name for resource naming'
  
  DBInstanceClass:
    Type: String
    Default: 'db.t3.medium'
    AllowedValues: 
      - 'db.t3.micro'
      - 'db.t3.small'
      - 'db.t3.medium'
      - 'db.t3.large'
      - 'db.t3.xlarge'
      - 'db.r5.large'
      - 'db.r5.xlarge'
      - 'db.r5.2xlarge'
    Description: 'RDS instance class'
  
  DBAllocatedStorage:
    Type: Number
    Default: 100
    MinValue: 20
    MaxValue: 1000
    Description: 'Database allocated storage in GB'
  
  DBMaxAllocatedStorage:
    Type: Number
    Default: 1000
    MinValue: 100
    MaxValue: 10000
    Description: 'Database maximum allocated storage for autoscaling in GB'
  
  DBName:
    Type: String
    Default: 'renewtrack'
    Description: 'Database name'
  
  DBUsername:
    Type: String
    Default: 'renewtrack_admin'
    Description: 'Database master username'
  
  DBPassword:
    Type: String
    NoEcho: true
    MinLength: 8
    MaxLength: 128
    Description: 'Database master password'
  
  MultiAZ:
    Type: String
    Default: 'true'
    AllowedValues: ['true', 'false']
    Description: 'Enable Multi-AZ deployment'
  
  BackupRetentionPeriod:
    Type: Number
    Default: 30
    MinValue: 1
    MaxValue: 35
    Description: 'Backup retention period in days'
  
  PreferredBackupWindow:
    Type: String
    Default: '03:00-04:00'
    Description: 'Preferred backup window (UTC)'
  
  PreferredMaintenanceWindow:
    Type: String
    Default: 'sun:04:00-sun:05:00'
    Description: 'Preferred maintenance window (UTC)'

Conditions:
  IsProduction: !Equals [!Ref Environment, 'prod']

Resources:
  # DB Subnet Group
  DBSubnetGroup:
    Type: AWS::RDS::DBSubnetGroup
    Properties:
      DBSubnetGroupName: !Sub '${ApplicationName}-db-subnet-group-${Environment}'
      DBSubnetGroupDescription: 'Subnet group for RDS database'
      SubnetIds:
        - Fn::ImportValue: !Sub '${ApplicationName}-db-subnet-1-${Environment}'
        - Fn::ImportValue: !Sub '${ApplicationName}-db-subnet-2-${Environment}'
      Tags:
        - Key: Name
          Value: !Sub '${ApplicationName}-db-subnet-group-${Environment}'
        - Key: Environment
          Value: !Ref Environment

  # DB Parameter Group
  DBParameterGroup:
    Type: AWS::RDS::DBParameterGroup
    Properties:
      Family: postgres14
      Description: !Sub 'Parameter group for ${ApplicationName} PostgreSQL database'
      Parameters:
        # Performance tuning
        shared_preload_libraries: 'pg_stat_statements'
        log_statement: 'all'
        log_min_duration_statement: 1000
        log_checkpoints: 1
        log_connections: 1
        log_disconnections: 1
        log_lock_waits: 1
        log_temp_files: 0
        
        # Memory settings
        effective_cache_size: '{DBInstanceClassMemory*3/4}'
        shared_buffers: '{DBInstanceClassMemory/4}'
        maintenance_work_mem: '{DBInstanceClassMemory/16}'
        work_mem: '4MB'
        
        # Checkpoint settings
        checkpoint_completion_target: 0.9
        wal_buffers: '16MB'
        
        # Connection settings
        max_connections: 200
        
        # Autovacuum settings
        autovacuum_max_workers: 3
        autovacuum_naptime: '1min'
        
        # Security settings
        ssl: 1
        ssl_ciphers: 'HIGH:MEDIUM:+3DES:!aNULL'
        
      Tags:
        - Key: Name
          Value: !Sub '${ApplicationName}-db-params-${Environment}'
        - Key: Environment
          Value: !Ref Environment

  # DB Option Group (for PostgreSQL extensions)
  DBOptionGroup:
    Type: AWS::RDS::OptionGroup
    Properties:
      EngineName: postgres
      MajorEngineVersion: '14'
      OptionGroupDescription: !Sub 'Option group for ${ApplicationName} PostgreSQL database'
      Tags:
        - Key: Name
          Value: !Sub '${ApplicationName}-db-options-${Environment}'
        - Key: Environment
          Value: !Ref Environment

  # Enhanced Monitoring Role
  EnhancedMonitoringRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Sid: ''
            Effect: Allow
            Principal:
              Service: monitoring.rds.amazonaws.com
            Action: 'sts:AssumeRole'
      ManagedPolicyArns:
        - 'arn:aws:iam::aws:policy/service-role/AmazonRDSEnhancedMonitoringRole'
      Path: /

  # RDS Instance
  DBInstance:
    Type: AWS::RDS::DBInstance
    DeletionPolicy: Snapshot
    Properties:
      DBInstanceIdentifier: !Sub '${ApplicationName}-db-${Environment}'
      DBName: !Ref DBName
      DBInstanceClass: !Ref DBInstanceClass
      Engine: postgres
      EngineVersion: '14.9'
      MasterUsername: !Ref DBUsername
      MasterUserPassword: !Ref DBPassword
      
      # Storage configuration
      AllocatedStorage: !Ref DBAllocatedStorage
      MaxAllocatedStorage: !Ref DBMaxAllocatedStorage
      StorageType: gp2
      StorageEncrypted: true
      
      # Network configuration
      DBSubnetGroupName: !Ref DBSubnetGroup
      VPCSecurityGroups:
        - Fn::ImportValue: !Sub '${ApplicationName}-db-sg-${Environment}'
      PubliclyAccessible: false
      
      # High availability
      MultiAZ: !Ref MultiAZ
      
      # Backup configuration
      BackupRetentionPeriod: !Ref BackupRetentionPeriod
      PreferredBackupWindow: !Ref PreferredBackupWindow
      PreferredMaintenanceWindow: !Ref PreferredMaintenanceWindow
      CopyTagsToSnapshot: true
      DeleteAutomatedBackups: false
      DeletionProtection: !If [IsProduction, true, false]
      
      # Monitoring
      MonitoringInterval: 60
      MonitoringRoleArn: !GetAtt EnhancedMonitoringRole.Arn
      EnablePerformanceInsights: true
      PerformanceInsightsRetentionPeriod: !If [IsProduction, 731, 7]
      
      # Parameter and option groups
      DBParameterGroupName: !Ref DBParameterGroup
      OptionGroupName: !Ref DBOptionGroup
      
      # Maintenance
      AutoMinorVersionUpgrade: true
      AllowMajorVersionUpgrade: false
      
      Tags:
        - Key: Name
          Value: !Sub '${ApplicationName}-db-${Environment}'
        - Key: Environment
          Value: !Ref Environment
        - Key: Backup
          Value: 'Required'

  # Read Replica (for production)
  DBReadReplica:
    Type: AWS::RDS::DBInstance
    Condition: IsProduction
    Properties:
      DBInstanceIdentifier: !Sub '${ApplicationName}-db-replica-${Environment}'
      SourceDBInstanceIdentifier: !Ref DBInstance
      DBInstanceClass: !Ref DBInstanceClass
      PubliclyAccessible: false
      
      # Monitoring
      MonitoringInterval: 60
      MonitoringRoleArn: !GetAtt EnhancedMonitoringRole.Arn
      EnablePerformanceInsights: true
      PerformanceInsightsRetentionPeriod: 7
      
      Tags:
        - Key: Name
          Value: !Sub '${ApplicationName}-db-replica-${Environment}'
        - Key: Environment
          Value: !Ref Environment
        - Key: Role
          Value: 'ReadReplica'

  # CloudWatch Alarms
  DBCPUAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub '${ApplicationName}-db-cpu-${Environment}'
      AlarmDescription: 'RDS instance high CPU'
      MetricName: CPUUtilization
      Namespace: AWS/RDS
      Statistic: Average
      Period: 300
      EvaluationPeriods: 2
      Threshold: 80
      ComparisonOperator: GreaterThanThreshold
      Dimensions:
        - Name: DBInstanceIdentifier
          Value: !Ref DBInstance
      TreatMissingData: notBreaching

  DBConnectionsAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub '${ApplicationName}-db-connections-${Environment}'
      AlarmDescription: 'RDS instance high connection count'
      MetricName: DatabaseConnections
      Namespace: AWS/RDS
      Statistic: Average
      Period: 300
      EvaluationPeriods: 2
      Threshold: 160  # 80% of max_connections (200)
      ComparisonOperator: GreaterThanThreshold
      Dimensions:
        - Name: DBInstanceIdentifier
          Value: !Ref DBInstance
      TreatMissingData: notBreaching

  DBFreeStorageSpaceAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub '${ApplicationName}-db-storage-${Environment}'
      AlarmDescription: 'RDS instance low free storage'
      MetricName: FreeStorageSpace
      Namespace: AWS/RDS
      Statistic: Average
      Period: 300
      EvaluationPeriods: 1
      Threshold: 2000000000  # 2GB in bytes
      ComparisonOperator: LessThanThreshold
      Dimensions:
        - Name: DBInstanceIdentifier
          Value: !Ref DBInstance
      TreatMissingData: notBreaching

  DBReadLatencyAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub '${ApplicationName}-db-read-latency-${Environment}'
      AlarmDescription: 'RDS instance high read latency'
      MetricName: ReadLatency
      Namespace: AWS/RDS
      Statistic: Average
      Period: 300
      EvaluationPeriods: 2
      Threshold: 0.2  # 200ms
      ComparisonOperator: GreaterThanThreshold
      Dimensions:
        - Name: DBInstanceIdentifier
          Value: !Ref DBInstance
      TreatMissingData: notBreaching

  DBWriteLatencyAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub '${ApplicationName}-db-write-latency-${Environment}'
      AlarmDescription: 'RDS instance high write latency'
      MetricName: WriteLatency
      Namespace: AWS/RDS
      Statistic: Average
      Period: 300
      EvaluationPeriods: 2
      Threshold: 0.2  # 200ms
      ComparisonOperator: GreaterThanThreshold
      Dimensions:
        - Name: DBInstanceIdentifier
          Value: !Ref DBInstance
      TreatMissingData: notBreaching

  # SNS Topic for Database Alerts
  DBAlertsTopic:
    Type: AWS::SNS::Topic
    Properties:
      TopicName: !Sub '${ApplicationName}-db-alerts-${Environment}'
      DisplayName: !Sub '${ApplicationName} Database Alerts'

  # CloudWatch Dashboard
  DatabaseDashboard:
    Type: AWS::CloudWatch::Dashboard
    Properties:
      DashboardName: !Sub '${ApplicationName}-database-${Environment}'
      DashboardBody: !Sub |
        {
          "widgets": [
            {
              "type": "metric",
              "x": 0,
              "y": 0,
              "width": 12,
              "height": 6,
              "properties": {
                "metrics": [
                  [ "AWS/RDS", "CPUUtilization", "DBInstanceIdentifier", "${DBInstance}" ],
                  [ ".", "DatabaseConnections", ".", "." ],
                  [ ".", "FreeStorageSpace", ".", "." ]
                ],
                "view": "timeSeries",
                "stacked": false,
                "region": "${AWS::Region}",
                "title": "Database Performance",
                "period": 300
              }
            },
            {
              "type": "metric",
              "x": 12,
              "y": 0,
              "width": 12,
              "height": 6,
              "properties": {
                "metrics": [
                  [ "AWS/RDS", "ReadLatency", "DBInstanceIdentifier", "${DBInstance}" ],
                  [ ".", "WriteLatency", ".", "." ],
                  [ ".", "ReadThroughput", ".", "." ],
                  [ ".", "WriteThroughput", ".", "." ]
                ],
                "view": "timeSeries",
                "stacked": false,
                "region": "${AWS::Region}",
                "title": "Database I/O",
                "period": 300
              }
            }
          ]
        }

Outputs:
  DBInstanceEndpoint:
    Description: 'RDS instance endpoint'
    Value: !GetAtt DBInstance.Endpoint.Address
    Export:
      Name: !Sub '${ApplicationName}-db-endpoint-${Environment}'

  DBInstancePort:
    Description: 'RDS instance port'
    Value: !GetAtt DBInstance.Endpoint.Port
    Export:
      Name: !Sub '${ApplicationName}-db-port-${Environment}'

  DBInstanceIdentifier:
    Description: 'RDS instance identifier'
    Value: !Ref DBInstance
    Export:
      Name: !Sub '${ApplicationName}-db-identifier-${Environment}'

  DBReadReplicaEndpoint:
    Condition: IsProduction
    Description: 'RDS read replica endpoint'
    Value: !GetAtt DBReadReplica.Endpoint.Address
    Export:
      Name: !Sub '${ApplicationName}-db-replica-endpoint-${Environment}'

  DBSubnetGroupName:
    Description: 'DB subnet group name'
    Value: !Ref DBSubnetGroup
    Export:
      Name: !Sub '${ApplicationName}-db-subnet-group-${Environment}'

  DBParameterGroupName:
    Description: 'DB parameter group name'
    Value: !Ref DBParameterGroup
    Export:
      Name: !Sub '${ApplicationName}-db-parameter-group-${Environment}'

  DatabaseDashboardURL:
    Description: 'CloudWatch Dashboard URL'
    Value: !Sub 'https://${AWS::Region}.console.aws.amazon.com/cloudwatch/home?region=${AWS::Region}#dashboards:name=${ApplicationName}-database-${Environment}'

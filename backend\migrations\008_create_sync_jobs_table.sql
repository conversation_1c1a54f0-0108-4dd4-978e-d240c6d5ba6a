-- Migration: Create sync jobs table for background processing
-- This table manages async synchronization jobs with retry logic

-- Create sync_jobs table in metadata schema
CREATE TABLE IF NOT EXISTS metadata.sync_jobs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid (),
    tenant_id VARCHAR(16) NOT NULL,
    job_type VARCHAR(50) NOT NULL CHECK (
        job_type IN (
            'vendor_sync',
            'product_sync',
            'version_sync',
            'full_sync'
        )
    ),
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (
        status IN (
            'pending',
            'processing',
            'completed',
            'failed',
            'retrying'
        )
    ),
    priority INTEGER NOT NULL DEFAULT 5 CHECK (
        priority >= 1
        AND priority <= 10
    ),
    attempts INTEGER NOT NULL DEFAULT 0,
    max_attempts INTEGER NOT NULL DEFAULT 3,
    scheduled_at TIMESTAMP
    WITH
        TIME ZONE NOT NULL DEFAULT NOW(),
        started_at TIMESTAMP
    WITH
        TIME ZONE,
        completed_at TIMESTAMP
    WITH
        TIME ZONE,
        error_message TEXT,
        job_data JSONB,
        created_on TIMESTAMP
    WITH
        TIME ZONE NOT NULL DEFAULT NOW(),
        changed_on TIMESTAMP
    WITH
        TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create indexes for efficient job processing
CREATE INDEX IF NOT EXISTS idx_sync_jobs_status_scheduled ON metadata.sync_jobs (status, scheduled_at)
WHERE
    status IN ('pending', 'retrying');

CREATE INDEX IF NOT EXISTS idx_sync_jobs_tenant_status ON metadata.sync_jobs (tenant_id, status);

CREATE INDEX IF NOT EXISTS idx_sync_jobs_priority_created ON metadata.sync_jobs (priority DESC, created_on ASC)
WHERE
    status IN ('pending', 'retrying');

CREATE INDEX IF NOT EXISTS idx_sync_jobs_created_at ON metadata.sync_jobs (created_on);

-- Add comments for documentation
COMMENT ON
TABLE metadata.sync_jobs IS 'Background job queue for synchronization operations';

COMMENT ON COLUMN metadata.sync_jobs.tenant_id IS 'ID of the tenant this job belongs to';

COMMENT ON COLUMN metadata.sync_jobs.job_type IS 'Type of synchronization job to execute';

COMMENT ON COLUMN metadata.sync_jobs.status IS 'Current status of the job';

COMMENT ON COLUMN metadata.sync_jobs.priority IS 'Job priority (1=lowest, 10=highest)';

COMMENT ON COLUMN metadata.sync_jobs.attempts IS 'Number of execution attempts';

COMMENT ON COLUMN metadata.sync_jobs.max_attempts IS 'Maximum number of retry attempts';

COMMENT ON COLUMN metadata.sync_jobs.scheduled_at IS 'When the job should be executed';

COMMENT ON COLUMN metadata.sync_jobs.job_data IS 'Additional data for job execution';

-- Create function to update changed_on timestamp
CREATE OR REPLACE FUNCTION metadata.update_sync_jobs_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.changed_on = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update changed_on
CREATE TRIGGER sync_jobs_updated_at_trigger
    BEFORE UPDATE ON metadata.sync_jobs
    FOR EACH ROW
    EXECUTE FUNCTION metadata.update_sync_jobs_updated_at();

-- Grant permissions
GRANT
SELECT,
INSERT
,
UPDATE,
DELETE ON metadata.sync_jobs TO renewtrack_app;

GRANT USAGE ON SCHEMA metadata TO renewtrack_app;
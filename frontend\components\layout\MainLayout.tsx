'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import DynamicSidebar from './DynamicSidebar'
import { LoadingPage } from '@/components/common/LoadingStates'
import { getLoginUrl } from '@/lib/auth'

export default function MainLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const [mounted, setMounted] = useState(false)
  const router = useRouter()

  useEffect(() => {
    setMounted(true)
  }, [])

  // Don't render anything until mounted (prevents hydration issues)
  if (!mounted) {
    return <LoadingPage title="Loading Application..." />
  }

  // For development, always show the authenticated layout
  console.log('🎉 [MainLayout] Rendering authenticated layout')
  return (
    <div className="flex h-screen">
      <DynamicSidebar />
      <main className="flex-1 overflow-auto" style={{ marginLeft: '250px' }}>
        {children}
      </main>
    </div>
  )
}






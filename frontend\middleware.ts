import { NextRequest, NextResponse } from 'next/server'

// Define API_BASE constant directly in middleware since imports may not work
const API_BASE = '/api'

/**
 * Extract license information from JWT token claims
 * This avoids making HTTP calls in middleware
 */
function extractLicenseFromToken(token: string): { hasValidLicense: boolean; reason?: string } {

  try {
    // Decode JWT payload (without verification since we already validated the token)
    const payload = JSON.parse(atob(token.split('.')[1]))

    // Check if license info is embedded in the token
    const licenseInfo = payload['custom:license_status']
    const clientId = payload['custom:client_id']

    if (licenseInfo === 'valid' && clientId) {
      return { hasValidLicense: true }
    }

    // For now, allow access and let page-level validation handle license checks
    // This prevents blocking the entire app due to license validation issues
    return { hasValidLicense: true, reason: 'License validation deferred to page level' }

  } catch (error) {
    console.warn('Could not extract license from token, allowing access:', error)
    return { hasValidLicense: true, reason: 'License validation deferred to page level' }
  }
}

/**
 * Extract user groups from JWT token
 */
function getUserGroupsFromToken(token?: string): string[] {
  if (!token) return []

  try {
    const payload = JSON.parse(atob(token.split('.')[1]))
    return payload['cognito:groups'] || []
  } catch (error) {
    console.error('Error extracting groups from token:', error)
    return []
  }
}

/**
 * Check if user should be exempt from license validation
 * This is a simplified check for middleware - detailed validation happens at page level
 */
function isLicenseExemptUser(groups: string[]): boolean {
  // Basic exemption check for middleware performance
  // Detailed database-driven checks happen at the application level
  return groups.some(group => ['super-admin', 'admin'].includes(group.toLowerCase()))
}

/**
 * Extract user email from JWT token
 */
function getUserEmailFromToken(token?: string): string | null {
  if (!token) return null

  try {
    const parts = token.split('.')
    if (parts.length !== 3) return null

    const payload = JSON.parse(atob(parts[1]))
    return payload.email || null
  } catch (error) {
    return null
  }
}

/**
 * Validate JWT token format and basic structure
 * Note: This is a simplified validation for middleware use only
 * We're being more lenient here and letting the app handle token refresh
 */
function isValidAuthToken(token?: string): boolean {
  if (!token || token.trim() === '') {
    return false
  }

  // Basic JWT format check (3 parts separated by dots)
  const parts = token.split('.')
  if (parts.length !== 3) {
    return false
  }

  // Just check if token has basic structure - let the app handle detailed validation
  try {
    const payload = JSON.parse(atob(parts[1]))

    // For now, just check if we have a valid JWT structure with required fields
    // Let the app handle the actual authentication validation
    return !!(payload.sub && payload.iss)
  } catch (error) {
    return false
  }
}

// Define which paths require authentication
const protectedPaths = [
  '/overview',
  '/profile',
  '/settings',
  '/clients',
  '/renewals',
  '/vendors',
  '/vendor-dashboard',
  '/vendor-management',
  '/admin'
]

// Define which paths should redirect if already authenticated
const authPaths = [
  '/login',
  '/signup'
]

// Define paths that don't require license validation
const licenseExemptPaths = [
  '/login',
  '/signup',
  '/license-error',
  '/auth'
]

// Define API paths that require tenant context
const tenantRequiredApiPaths = [
  `${API_BASE}/renewals`,
  `${API_BASE}/clients`,
  `${API_BASE}/metadata`,
  `${API_BASE}/dashboard`,
  `${API_BASE}/overview`,
  `${API_BASE}/tenant-logs`
]

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl

  // Skip middleware for callback processing to avoid interference
  if (pathname === '/callback') {
    return NextResponse.next()
  }

  // Check if the path is protected
  const isProtectedPath = protectedPaths.some(path =>
    pathname === path || pathname.startsWith(`${path}/`)
  )

  // Check if the path is an auth path
  const isAuthPath = authPaths.some(path =>
    pathname === path || pathname.startsWith(`${path}/`)
  )
  
  // Check authentication status from cookie
  // Note: Middleware runs at edge and cannot use Amplify directly
  // Only use our custom idToken cookie, ignore Cognito cookies to avoid stale token issues
  const idTokenCookie = request.cookies.get('idToken')?.value
  const isAuthenticated = isValidAuthToken(idTokenCookie)


  
  // Create the response
  const response = NextResponse.next()

  // Add security headers
  const securityHeaders = {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin'
  }

  Object.entries(securityHeaders).forEach(([key, value]) => {
    response.headers.set(key, value)
  })

  // Add correlation ID for request tracing
  const correlationId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  response.headers.set('X-Correlation-ID', correlationId)

  // For API requests that require tenant context, add tenant validation headers
  const isApiRequest = pathname.startsWith(`${API_BASE}/`)
  const requiresTenantContext = tenantRequiredApiPaths.some(path =>
    pathname === path || pathname.startsWith(`${path}/`)
  )

  if (isApiRequest && requiresTenantContext && isAuthenticated) {
    // Add headers to indicate tenant context is required
    response.headers.set('X-Tenant-Required', 'true')

    // In a full implementation, we could extract tenant info here
    // and add it to headers for downstream processing

  }
  
  // Handle protected routes - redirect to Cognito if not authenticated
  if (isProtectedPath && !isAuthenticated) {
    // Build Cognito login URL
    const cognitoDomain = process.env.NEXT_PUBLIC_AWS_COGNITO_DOMAIN
    const userPoolClientId = process.env.NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID
    const redirectSignIn = process.env.NEXT_PUBLIC_REDIRECT_SIGN_IN

    if (cognitoDomain && userPoolClientId && redirectSignIn) {
      const loginUrl = `https://${cognitoDomain}/login?client_id=${userPoolClientId}&response_type=code&scope=openid+email+profile&redirect_uri=${encodeURIComponent(redirectSignIn)}`

      // Clear any invalid or expired cookies before redirecting
      const response = NextResponse.redirect(new URL(loginUrl))

      // Clear our custom idToken cookie
      response.cookies.set('idToken', '', {
        expires: new Date(0),
        path: '/',
        httpOnly: false,
        secure: true,
        sameSite: 'lax'
      })

      return response
    }
  }

  // Handle license validation for authenticated users on protected routes
  const isLicenseExemptPath = licenseExemptPaths.some(path =>
    pathname === path || pathname.startsWith(`${path}/`)
  )

  if (isProtectedPath && isAuthenticated && !isLicenseExemptPath) {
    const userEmail = getUserEmailFromToken(request.cookies.get('idToken')?.value)
    const userGroups = getUserGroupsFromToken(request.cookies.get('idToken')?.value)

    // Check if user is accessing admin or super-admin pages
    const isAdminPath = pathname.startsWith('/admin')
    const isSuperAdminPath = pathname.startsWith('/super-admin')

    // Determine if user needs license validation based on their groups and the path they're accessing
    let requiresLicense = true

    // Simplified license exemption for middleware - detailed checks happen at application level
    if (isLicenseExemptUser(userGroups)) {
      // Exempt users don't need licenses for admin paths
      if (isAdminPath || isSuperAdminPath) {
        requiresLicense = false
      }
      // They still need licenses for regular user pages
    }

    // Simplified license validation in middleware
    // Detailed validation is handled at page level to keep middleware lightweight
    if (requiresLicense && userEmail) {
      const idToken = request.cookies.get('idToken')?.value
      if (idToken) {
        const licenseValidation = extractLicenseFromToken(idToken)

        // Only block if we have explicit license failure information
        if (!licenseValidation.hasValidLicense && licenseValidation.reason?.includes('expired')) {
          return NextResponse.redirect(new URL('/license-error', request.url))
        }
      }
    }
  }
  
  // Handle auth routes - redirect to root if already authenticated
  // The root page will handle determining the first accessible page
  if (isAuthPath && isAuthenticated) {
    return NextResponse.redirect(new URL('/', request.url))
  }
  
  return response
}

// Configure middleware to run only on specific paths
export const config = {
  matcher: [
    /*
     * Match all request paths except:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     * - api routes (except those that need auth checks)
     */
    '/((?!_next/static|_next/image|favicon.ico|public|api/public).*)',
  ],
}



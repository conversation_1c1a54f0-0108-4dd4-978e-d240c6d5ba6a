/**
 * AWS Amplify Mock for Jest
 *
 * Mocks AWS Amplify functionality for testing using the new auth API
 */

// Mock the new auth functions
export const fetchAuthSession = jest.fn().mockResolvedValue({
  tokens: {
    idToken: {
      toString: () => 'mock-id-token',
      payload: {
        sub: 'test-user-id',
        email: '<EMAIL>',
        'cognito:groups': ['users'],
      },
    },
    accessToken: {
      toString: () => 'mock-access-token',
    },
  },
});

export const getCurrentUser = jest.fn().mockResolvedValue({
  userId: 'test-user-id',
  username: 'test-user',
  signInDetails: {
    loginId: '<EMAIL>',
  },
});

export const fetchUserAttributes = jest.fn().mockResolvedValue({
  email: '<EMAIL>',
  name: 'Test User',
  given_name: 'Test',
  family_name: 'User',
  'custom:roles': 'user',
});

export const signOut = jest.fn().mockResolvedValue(undefined);

// Mock Amplify configure
const mockAmplify = {
  configure: jest.fn(),
  Hub: {
    listen: jest.fn(),
    dispatch: jest.fn(),
    remove: jest.fn(),
  },
  Cache: {
    configure: jest.fn(),
    setItem: jest.fn(),
    getItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
  },
}

export default mockAmplify

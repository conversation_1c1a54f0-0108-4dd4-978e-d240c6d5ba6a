-- Update existing renewals to have upcoming dates
UPDATE "tenant_0000000000000001".tenant_renewals
SET
    start_date = CURRENT_DATE + INTERVAL '5 days'
WHERE
    renewal_name = 'Alert Test';

UPDATE "tenant_0000000000000001".tenant_renewals
SET
    start_date = CURRENT_DATE + INTERVAL '10 days'
WHERE
    renewal_name = 'ASA400';

UPDATE "tenant_0000000000000001".tenant_renewals
SET
    start_date = CURRENT_DATE + INTERVAL '15 days'
WHERE
    renewal_name = 'Cloud Suite';

UPDATE "tenant_0000000000000001".tenant_renewals
SET
    start_date = CURRENT_DATE + INTERVAL '20 days'
WHERE
    renewal_name = 'M365';

UPDATE "tenant_0000000000000001".tenant_renewals
SET
    start_date = CURRENT_DATE + INTERVAL '25 days'
WHERE
    renewal_name = 'Mobile app';

-- Insert some new upcoming renewals
INSERT INTO
    "tenant_0000000000000001"."tenant_renewals" (
        "renewal_name",
        "start_date",
        "currency_id",
        "cost",
        "license_count",
        "description"
    )
VALUES (
        'Slack Pro',
        CURRENT_DATE + INTERVAL '3 days',
        'CAD',
        2500,
        50,
        'Team communication platform'
    ),
    (
        'Zoom Enterprise',
        CURRENT_DATE + INTERVAL '7 days',
        'CAD',
        1800,
        25,
        'Video conferencing platform'
    ),
    (
        'Figma Professional',
        CURRENT_DATE + INTERVAL '18 days',
        'CAD',
        1440,
        15,
        'Design and prototyping tool'
    ),
    (
        'Jira Software',
        CURRENT_DATE + INTERVAL '22 days',
        'CAD',
        3600,
        75,
        'Project management and issue tracking'
    )
ON CONFLICT DO NOTHING;

-- Verify the updates
-- Note: Replace :tenant_schema with actual tenant schema name before running
SELECT
    "renewal_name",
    "start_date",
    "cost"
FROM:tenant_schema."tenant_renewals"
WHERE
    "start_date" BETWEEN CURRENT_DATE AND CURRENT_DATE  + INTERVAL '30 days'
ORDER BY "start_date";
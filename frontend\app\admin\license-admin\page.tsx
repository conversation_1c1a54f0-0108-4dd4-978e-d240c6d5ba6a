'use client'

/**
 * License Administration Page
 * 
 * Super admin interface for generating and managing license keys
 */

import React, { useState, useEffect } from 'react'
import { Button, Form, Modal, useToast } from '@/components/ui'
import { useAuth } from '@/lib/hooks'

interface LicenseKey {
  license_key_id: number
  license_key: string
  license_type: string
  max_renewals: number
  type_description: string
  key_description: string
  is_assigned: boolean
  created_on: string
  created_by: string
  assigned_at?: string
  assigned_to_client_id?: number
  name?: string
  status: string
}

interface LicenseType {
  id: number
  type_name: string
  max_renewals: number
  description: string
  price_per_renewal: number
  features: any
}

interface LicenseTerm {
  id: number
  term_name: string
  term_months: number
  discount_percentage: number
}

interface Client {
  client_id: number
  name: string
  email_domain: string
}

export default function LicenseAdminPage() {
  const { user } = useAuth()
  const toast = useToast()

  const [licenseKeys, setLicenseKeys] = useState<LicenseKey[]>([])
  const [licenseTypes, setLicenseTypes] = useState<LicenseType[]>([])
  const [licenseTerms, setLicenseTerms] = useState<LicenseTerm[]>([])
  const [clients, setClients] = useState<Client[]>([])
  const [loading, setLoading] = useState(true)
  const [generating, setGenerating] = useState(false)
  const [assigning, setAssigning] = useState(false)
  const [showGenerateDialog, setShowGenerateDialog] = useState(false)
  const [showAssignDialog, setShowAssignDialog] = useState(false)
  const [selectedLicenseKey, setSelectedLicenseKey] = useState<LicenseKey | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState('')
  const [filterAssigned, setFilterAssigned] = useState('')

  const [generateForm, setGenerateForm] = useState({
    license_type_id: '',
    quantity: 1,
    description: ''
  })

  const [assignForm, setAssignForm] = useState({
    client_id: '',
    license_term_id: ''
  })

  // Check if user is super admin
  const isSuperAdmin = user?.groups?.includes('super-admin') ?? false

  useEffect(() => {
    if (!isSuperAdmin) {
      toast.error('You need super admin privileges to access this page.', 'Access Denied')
      return
    }

    fetchInitialData()
  }, [isSuperAdmin])

  const fetchInitialData = async () => {
    try {
      setLoading(true)
      await Promise.all([
        fetchLicenseKeys(),
        fetchLicenseTypes(),
        fetchLicenseTerms(),
        fetchClients()
      ])
    } catch (error) {
      console.error('Error fetching initial data:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchLicenseKeys = async () => {
    try {
      const params = new URLSearchParams()
      if (searchTerm) params.append('search', searchTerm)
      if (filterType) params.append('license_type', filterType)
      if (filterAssigned) params.append('is_assigned', filterAssigned)

      const response = await fetch(`/api/admin/license-keys?${params}`)
      const data = await response.json()

      if (data.success) {
        setLicenseKeys(data.data)
      } else {
        toast.error(data.message || 'Failed to fetch license keys', 'Error')
      }
    } catch (error) {
      console.error('Error fetching license keys:', error)
      toast.error('Failed to fetch license keys', 'Error')
    }
  }

  const fetchLicenseTypes = async () => {
    try {
      const response = await fetch('/api/admin/license-types')
      const data = await response.json()
      if (data.success) {
        setLicenseTypes(data.data)
      }
    } catch (error) {
      console.error('Error fetching license types:', error)
    }
  }

  const fetchLicenseTerms = async () => {
    try {
      const response = await fetch('/api/admin/license-terms')
      const data = await response.json()
      if (data.success) {
        setLicenseTerms(data.data)
      }
    } catch (error) {
      console.error('Error fetching license terms:', error)
    }
  }

  const fetchClients = async () => {
    try {
      const response = await fetch('/api/admin/clients')
      const data = await response.json()
      if (data.success) {
        setClients(data.data)
      }
    } catch (error) {
      console.error('Error fetching clients:', error)
    }
  }

  const handleGenerateLicenses = async () => {
    if (!generateForm.license_type_id) {
      toast.error('Please select a license type', 'Error')
      return
    }

    try {
      setGenerating(true)

      const response = await fetch('/api/admin/license-keys', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(generateForm)
      })

      const data = await response.json()

      if (data.success) {
        toast.success(`Generated ${generateForm.quantity} license key(s) successfully`, 'Success')

        setShowGenerateDialog(false)
        setGenerateForm({
          license_type_id: '',
          quantity: 1,
          description: ''
        })
        fetchLicenseKeys()
      } else {
        toast.error(data.message || 'Failed to generate license keys', 'Error')
      }
    } catch (error) {
      console.error('Error generating license keys:', error)
      toast.error('Failed to generate license keys', 'Error')
    } finally {
      setGenerating(false)
    }
  }

  const handleAssignLicense = async () => {
    if (!assignForm.client_id || !assignForm.license_term_id || !selectedLicenseKey) {
      toast.error('Please fill in all required fields', 'Error')
      return
    }

    try {
      setAssigning(true)

      const response = await fetch('/api/admin/license-assignment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          license_key: selectedLicenseKey.license_key,
          client_id: parseInt(assignForm.client_id),
          license_term_id: parseInt(assignForm.license_term_id)
        })
      })

      const data = await response.json()

      if (data.success) {
        toast.success('License assigned successfully', 'Success')

        setShowAssignDialog(false)
        setSelectedLicenseKey(null)
        setAssignForm({
          client_id: '',
          license_term_id: ''
        })
        fetchLicenseKeys()
      } else {
        toast.error(data.message || 'Failed to assign license', 'Error')
      }
    } catch (error) {
      console.error('Error assigning license:', error)
      toast.error('Failed to assign license', 'Error')
    } finally {
      setAssigning(false)
    }
  }

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      toast.success('License key copied to clipboard', 'Copied')
    } catch (error) {
      console.error('Failed to copy:', error)
      toast.error('Failed to copy to clipboard', 'Error')
    }
  }

  const exportLicenseKeys = () => {
    const csvContent = [
      ['License Key', 'Type', 'Max Renewals', 'Status', 'Created', 'Assigned To', 'Description'].join(','),
      ...licenseKeys.map(key => [
        key.license_key,
        key.license_type,
        key.max_renewals,
        key.is_assigned ? 'Assigned' : 'Available',
        new Date(key.created_on).toLocaleDateString(),
        key.name || '',
        key.key_description || ''
      ].join(','))
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `license-keys-${new Date().toISOString().split('T')[0]}.csv`
    a.click()
    window.URL.revokeObjectURL(url)
  }

  const openAssignDialog = (licenseKey: LicenseKey) => {
    setSelectedLicenseKey(licenseKey)
    setShowAssignDialog(true)
  }

  if (!isSuperAdmin) {
    return (
      <div className="container">
        <div className="card">
          <p>You need super admin privileges to access the License Administration page.</p>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="container">
        <div className="text-center py-8">Loading license keys...</div>
      </div>
    )
  }

  return (
    <div className="container">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">License Administration</h1>
          <p className="text-secondary">Generate and manage license keys</p>
        </div>
        
        <div className="flex gap-sm">
          <Button onClick={exportLicenseKeys} variant="outline">
            📥 Export
          </Button>
          
          <Button onClick={() => setShowGenerateDialog(true)}>
            ➕ Generate License Keys
          </Button>
        </div>
      </div>

      {/* Filters */}
      <div className="card mb-6">
        <div className="flex flex-wrap gap-md">
          <div className="flex-1" style={{ minWidth: '200px' }}>
            <Form.Input
              placeholder="🔍 Search license keys..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          <Form.Select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            placeholder="License Type"
          >
            <option value="">All Types</option>
            {licenseTypes.map(type => (
              <option key={type.id} value={type.type_name}>
                {type.type_name}
              </option>
            ))}
          </Form.Select>

          <Form.Select
            value={filterAssigned}
            onChange={(e) => setFilterAssigned(e.target.value)}
            placeholder="Status"
          >
            <option value="">All Status</option>
            <option value="false">Available</option>
            <option value="true">Assigned</option>
          </Form.Select>

          <Button
            variant="outline"
            onClick={() => {
              setSearchTerm('')
              setFilterType('')
              setFilterAssigned('')
              fetchLicenseKeys()
            }}
          >
            🔄 Clear
          </Button>

          <Button onClick={fetchLicenseKeys}>
            🔍 Search
          </Button>
        </div>
      </div>

      {/* License Keys Table */}
      <div className="card">
        <div className="card-header">
          <h2>License Keys ({licenseKeys.length})</h2>
        </div>
        <div className="card-content">
          {licenseKeys.length === 0 ? (
            <div className="text-center py-8 text-secondary">
              No license keys found. Generate your first license key to get started.
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-2">License Key</th>
                    <th className="text-left py-2">Type</th>
                    <th className="text-left py-2">Max Renewals</th>
                    <th className="text-left py-2">Status</th>
                    <th className="text-left py-2">Created</th>
                    <th className="text-left py-2">Assigned To</th>
                    <th className="text-left py-2">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {licenseKeys.map((key) => (
                    <tr key={key.license_key_id} className="border-b">
                      <td className="py-2">
                        <code className="text-sm bg-gray-100 px-2 py-1 rounded">
                          {key.license_key}
                        </code>
                      </td>
                      <td className="py-2">
                        <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm">
                          {key.license_type}
                        </span>
                      </td>
                      <td className="py-2">{key.max_renewals.toLocaleString()}</td>
                      <td className="py-2">
                        <span className={`px-2 py-1 rounded text-sm ${
                          key.is_assigned
                            ? 'bg-red-100 text-red-800'
                            : 'bg-green-100 text-green-800'
                        }`}>
                          {key.is_assigned ? 'Assigned' : 'Available'}
                        </span>
                      </td>
                      <td className="py-2 text-sm">
                        {new Date(key.created_on).toLocaleDateString()}
                      </td>
                      <td className="py-2 text-sm">
                        {key.name || '-'}
                      </td>
                      <td className="py-2">
                        <div className="flex gap-1">
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => copyToClipboard(key.license_key)}
                          >
                            📋 Copy
                          </Button>
                          {!key.is_assigned && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => openAssignDialog(key)}
                            >
                              🔗 Assign
                            </Button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Generate License Modal */}
      <Modal
        isOpen={showGenerateDialog}
        onClose={() => setShowGenerateDialog(false)}
        title="Generate License Keys"
        subtitle="Create new license keys for distribution to clients"
        size="md"
      >
        <Form.Root onSubmit={(e) => { e.preventDefault(); handleGenerateLicenses(); }}>
          <Form.Field>
            <Form.Label htmlFor="license_type_id" required>License Type</Form.Label>
            <Form.Select
              id="license_type_id"
              value={generateForm.license_type_id}
              onChange={(e) => setGenerateForm(prev => ({
                ...prev,
                license_type_id: e.target.value
              }))}
              placeholder="Select license type"
            >
              <option value="">Select license type</option>
              {licenseTypes.map(type => (
                <option key={type.id} value={type.id.toString()}>
                  {type.type_name} ({type.max_renewals.toLocaleString()} renewals)
                </option>
              ))}
            </Form.Select>
          </Form.Field>

          <Form.Field>
            <Form.Label htmlFor="quantity" required>Quantity</Form.Label>
            <Form.Input
              id="quantity"
              type="number"
              value={generateForm.quantity}
              onChange={(e) => setGenerateForm(prev => ({
                ...prev,
                quantity: parseInt(e.target.value) || 1
              }))}
              min="1"
              max="100"
            />
          </Form.Field>

          <Form.Field>
            <Form.Label htmlFor="description">Description</Form.Label>
            <Form.Textarea
              id="description"
              value={generateForm.description}
              onChange={(e) => setGenerateForm(prev => ({
                ...prev,
                description: e.target.value
              }))}
              placeholder="Optional description for the license keys"
            />
          </Form.Field>

          <Form.Actions>
            <Button
              variant="outline"
              onClick={() => setShowGenerateDialog(false)}
              type="button"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={!generateForm.license_type_id || generating}
            >
              {generating ? 'Generating...' : 'Generate'}
            </Button>
          </Form.Actions>
        </Form.Root>
      </Modal>

      {/* Assign License Modal */}
      <Modal
        isOpen={showAssignDialog}
        onClose={() => setShowAssignDialog(false)}
        title="Assign License to Client"
        subtitle={`Assign license key: ${selectedLicenseKey?.license_key}`}
        size="md"
      >
        <Form.Root onSubmit={(e) => { e.preventDefault(); handleAssignLicense(); }}>
          <Form.Field>
            <Form.Label htmlFor="client_id" required>Client</Form.Label>
            <Form.Select
              id="client_id"
              value={assignForm.client_id}
              onChange={(e) => setAssignForm(prev => ({
                ...prev,
                client_id: e.target.value
              }))}
              placeholder="Select client"
            >
              <option value="">Select client</option>
              {clients.map(client => (
                <option key={client.client_id} value={client.client_id.toString()}>
                  {client.name} ({client.email_domain})
                </option>
              ))}
            </Form.Select>
          </Form.Field>

          <Form.Field>
            <Form.Label htmlFor="license_term_id" required>License Term</Form.Label>
            <Form.Select
              id="license_term_id"
              value={assignForm.license_term_id}
              onChange={(e) => setAssignForm(prev => ({
                ...prev,
                license_term_id: e.target.value
              }))}
              placeholder="Select license term"
            >
              <option value="">Select license term</option>
              {licenseTerms.map(term => (
                <option key={term.id} value={term.id.toString()}>
                  {term.term_name} ({term.term_months} months)
                  {term.discount_percentage > 0 && ` - ${term.discount_percentage}% discount`}
                </option>
              ))}
            </Form.Select>
          </Form.Field>

          {selectedLicenseKey && (
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-medium mb-2">License Details</h4>
              <div className="text-sm space-y-1">
                <div><strong>Type:</strong> {selectedLicenseKey.license_type}</div>
                <div><strong>Max Renewals:</strong> {selectedLicenseKey.max_renewals.toLocaleString()}</div>
                <div><strong>Description:</strong> {selectedLicenseKey.type_description}</div>
              </div>
            </div>
          )}

          <Form.Actions>
            <Button
              variant="outline"
              onClick={() => setShowAssignDialog(false)}
              type="button"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={!assignForm.client_id || !assignForm.license_term_id || assigning}
            >
              {assigning ? 'Assigning...' : 'Assign License'}
            </Button>
          </Form.Actions>
        </Form.Root>
      </Modal>
    </div>
  )
}

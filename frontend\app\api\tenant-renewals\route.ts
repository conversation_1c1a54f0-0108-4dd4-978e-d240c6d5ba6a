/**
 * Tenant Renewals API Endpoint
 * 
 * Handles renewal creation with multiple items
 * POST /api/tenant-renewals - Creates a renewal with multiple items
 */

import { NextRequest } from 'next/server';
import { withAuth } from '@/lib/api/auth-middleware';
import { getClientByEmailDomain } from '@/lib/tenant/clients';
import { fetchUserAttributes } from 'aws-amplify/auth';
import { executeTenantQuery, executeTenantQuerySingle } from '@/lib/tenant/database';
import {
  createSuccessResponse,
  createErrorResponse,
  ApiErrorCode,
  HttpStatus
} from '@/lib/api/response';
import { z } from 'zod';

// Validation schema for renewal items
const renewalItemSchema = z.object({
  product_id: z.string().min(1, 'Product ID is required'),
  version_id: z.string().min(1, 'Version ID is required'),
  license_count: z.number().int().positive('License count must be positive').default(1),
  unit_cost: z.number().min(0, 'Unit cost must be non-negative').default(0),
  total_cost: z.number().min(0, 'Total cost must be non-negative').default(0),
  cost_code: z.string().max(100).optional(),
  notes: z.string().optional(),
});

// Validation schema for creating renewals
const createRenewalSchema = z.object({
  renewal_name: z.string().min(1, 'Renewal name is required').max(255),
  vendor_id: z.string().min(1, 'Vendor ID is required'),
  renewal_type_id: z.number().int().positive('Invalid renewal type ID').optional(),
  department_id: z.number().int().positive('Invalid department ID').optional(),
  purchase_type_id: z.number().int().positive('Invalid purchase type ID').optional(),
  licensed_date: z.string().optional(),
  start_date: z.string().min(1, 'Renewal date is required'),
  associated_emails: z.array(z.string().email()).optional(),
  reseller: z.string().max(255).optional(),
  currency_id: z.string().max(10).optional(),
  total_cost: z.number().min(0, 'Total cost must be non-negative').default(0),
  cost_code: z.string().max(100).optional(),
  description: z.string().optional(),
  notes: z.string().optional(),
  renewal_items: z.array(renewalItemSchema).min(1, 'At least one renewal item is required')
});

// POST /api/tenant-renewals - Create a renewal with multiple items
export const POST = withAuth(async (request: NextRequest, session) => {
  console.log('[TENANT-RENEWALS-API] POST request received');

  // Get user attributes for tenant context
  const userAttributes = await fetchUserAttributes();
  const userEmail = userAttributes.email;

  if (!userEmail) {
    return createErrorResponse(
      'User email not found',
      ApiErrorCode.UNAUTHORIZED,
      HttpStatus.UNAUTHORIZED
    );
  }

  // Create session object for tenant context resolution
  const sessionObj = {
    isAuth: true,
    userId: userAttributes.sub || 'unknown',
    email: userEmail,
    roles: ['user']
  };

  // Get tenant context
  const clientResult = await getClientByEmailDomain(userEmail);
  if (!clientResult.success || !clientResult.client) {
    return createErrorResponse(
      clientResult.error || 'Failed to resolve tenant context',
      (clientResult.errorCode as ApiErrorCode) || ApiErrorCode.UNAUTHORIZED,
      HttpStatus.UNAUTHORIZED
    );
  }

  const tenant = clientResult.client;

  try {
      const body = await request.json();
      console.log('[TENANT-RENEWALS-API] Request body:', body);

      // Validate request data
      const validationResult = createRenewalSchema.safeParse(body);
      if (!validationResult.success) {
        console.error('[TENANT-RENEWALS-API] Validation error:', validationResult.error);
        return createErrorResponse(
          'Invalid renewal data',
          ApiErrorCode.VALIDATION_ERROR,
          HttpStatus.BAD_REQUEST,
          validationResult.error.errors
        );
      }

      const renewalData = validationResult.data;

      // Start transaction-like operations
      // First, create the main renewal record
      const renewalQuery = `
        INSERT INTO "${tenant.tenantSchema}"."tenant_renewals" (
          "name", "renewal_type_id", "department_id", "purchase_type_id",
          "start_date", "expiry_date", "assigned_users", "reseller_id",
          "currency_id", "description", "notes",
          "status", "created_on", "changed_on", "created_by"
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, 'A',
          CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, $12
        ) RETURNING "id", "name", "created_on"
      `;

      const renewalResult = await executeTenantQuerySingle(
        renewalQuery,
        [
          renewalData.renewal_name,
          renewalData.renewal_type_id,
          renewalData.department_id || null,
          renewalData.purchase_type_id,
          renewalData.licensed_date || null,
          renewalData.start_date,
          renewalData.associated_emails || null,
          renewalData.reseller || null,
          renewalData.currency_id || null,
          renewalData.description || null,
          renewalData.notes || null,
          userAttributes.sub || 'unknown' // created_by
        ],
        tenant
      );

      if (!renewalResult.success || !renewalResult.data) {
        console.error('[TENANT-RENEWALS-API] Renewal creation error:', renewalResult.error);
        return createErrorResponse(
          'Failed to create renewal',
          ApiErrorCode.DATABASE_ERROR,
          HttpStatus.INTERNAL_SERVER_ERROR
        );
      }

      const renewalId = renewalResult.data.id;
      console.log('[TENANT-RENEWALS-API] Renewal created with ID:', renewalId);

      // Now create the renewal items
      const createdItems = [];

      for (const item of renewalData.renewal_items) {
        // Verify vendor, product, and version exist and are related
        const verificationQuery = `
          SELECT 
            v.id as vendor_id,
            p.id as product_id,
            pv.id as version_id,
            v.name as vendor_name,
            p.name as product_name,
            pv.version as version_name
          FROM "${tenant.tenantSchema}".tenant_vendors v
          JOIN "${tenant.tenantSchema}".tenant_products p ON p.vendor_id = v.id
          JOIN "${tenant.tenantSchema}".tenant_product_versions pv ON pv.product_id = p.id
          WHERE v.id = $1 AND p.id = $2 AND pv.id = $3
        `;

        const verificationResult = await executeTenantQuerySingle(
          verificationQuery,
          [renewalData.vendor_id, item.product_id, item.version_id],
          tenant
        );

        if (!verificationResult.success || !verificationResult.data) {
          return createErrorResponse(
            'Invalid vendor/product/version combination',
            ApiErrorCode.VALIDATION_ERROR,
            HttpStatus.BAD_REQUEST
          );
        }

        // Calculate total cost if not provided
        const totalCost = item.total_cost || (item.unit_cost * item.license_count);

        // Create the renewal item
        const itemQuery = `
          INSERT INTO "${tenant.tenantSchema}".tenant_renewal_items (
            renewal_id, vendor_id, product_id, version_id, license_count,
            unit_cost, total_cost, cost_code, notes,
            created_by, changed_by
          ) VALUES (
            $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11
          ) RETURNING id, renewal_id, vendor_id, product_id, version_id,
                     license_count, unit_cost, total_cost, cost_code, notes, created_on
        `;

        const itemResult = await executeTenantQuerySingle(
          itemQuery,
          [
            renewalId,
            parseInt(verificationResult.data.vendor_id),
            parseInt(verificationResult.data.product_id),
            parseInt(verificationResult.data.version_id),
            item.license_count,
            item.unit_cost,
            totalCost,
            item.cost_code || null,
            item.notes || null,
            'system', // created_by
            'system'  // changed_by
          ],
          tenant
        );

        if (!itemResult.success || !itemResult.data) {
          console.error('[TENANT-RENEWALS-API] Item creation error:', itemResult.error);
          return createErrorResponse(
            'Failed to create renewal item',
            ApiErrorCode.DATABASE_ERROR,
            HttpStatus.INTERNAL_SERVER_ERROR
          );
        }

        // Add the verification data for response
        createdItems.push({
          ...itemResult.data,
          vendor_name: verificationResult.data.vendor_name,
          product_name: verificationResult.data.product_name,
          version_name: verificationResult.data.version_name
        });
      }

      console.log('[TENANT-RENEWALS-API] Renewal and items created successfully');

      return createSuccessResponse(
        {
          renewal: renewalResult.data,
          items: createdItems,
          total_items: createdItems.length,
          total_licenses: createdItems.reduce((sum, item) => sum + item.license_count, 0)
        },
        'Renewal created successfully with multiple items',
        HttpStatus.CREATED
      );

  } catch (error) {
    console.error('[TENANT-RENEWALS-API] Error:', error);
    return createErrorResponse(
      'Failed to create renewal',
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
}, {
  requireAuth: true
});

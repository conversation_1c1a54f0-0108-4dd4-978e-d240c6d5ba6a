/**
 * Tenant Synchronization Service
 * 
 * Provides seamless CRUD operations for tenant users with automatic
 * synchronization integration. This service acts as a facade that:
 * 
 * 1. Handles normal CRUD operations on tenant-specific tables
 * 2. Automatically triggers synchronization when needed
 * 3. Provides unified access to both tenant and global data
 * 4. Manages sync status and conflict resolution
 */

import { Pool, PoolClient } from 'pg'
import { Logger } from '../Logger'
import { SyncEngine } from './SyncEngine'

export interface VendorCreateRequest {
  name: string
  taxId?: string
  domain?: string
  address?: string
  city?: string
  state?: string
  country?: string
  contactEmail?: string
  contactPhone?: string
  website?: string
}

export interface ProductCreateRequest {
  vendorId: string
  name: string
  sku?: string
  gtin?: string
  category?: string
  description?: string
  website?: string
}

export interface ProductVersionCreateRequest {
  productId: string
  version: string
  releaseDate?: Date
  endOfLifeDate?: Date
  supportLevel?: string
  description?: string
  downloadUrl?: string
}

export interface SyncStatus {
  isSync: boolean
  lastSyncDate?: Date
  syncStatus: 'synced' | 'pending' | 'conflict' | 'failed'
  confidence?: number
  matchType?: string
  globalId?: string
}

export class TenantSyncService {
  private db: Pool
  private logger: Logger
  private syncEngine: SyncEngine

  constructor(db: Pool, logger: Logger) {
    this.db = db
    this.logger = logger
    this.syncEngine = new SyncEngine(db, logger)
  }

  /**
   * Create a new vendor with automatic sync
   */
  async createVendor(tenantId: string, vendorData: VendorCreateRequest): Promise<{
    vendor: any
    syncStatus: SyncStatus
  }> {
    const client = await this.db.connect()
    
    try {
      await client.query('BEGIN')
      
      const tenantSchema = `tenant_${tenantId.padStart(16, '0')}`
      
      // Create vendor in tenant table
      const vendorResult = await client.query(`
        INSERT INTO ${tenantSchema}.tenant_vendors (
          name, tax_id, domain, address, city, state, country,
          contact_email, contact_phone, website, created_on, changed_on
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, NOW(), NOW())
        RETURNING *
      `, [
        vendorData.name,
        vendorData.taxId,
        vendorData.domain,
        vendorData.address,
        vendorData.city,
        vendorData.state,
        vendorData.country,
        vendorData.contactEmail,
        vendorData.contactPhone,
        vendorData.website
      ])
      
      const vendor = vendorResult.rows[0]
      
      await client.query('COMMIT')
      
      // Trigger async synchronization
      this.triggerVendorSync(tenantId, vendor.id).catch(error => {
        this.logger.error(`Failed to sync vendor ${vendor.id}`, { error, tenantId })
      })
      
      return {
        vendor,
        syncStatus: {
          isSync: false,
          syncStatus: 'pending'
        }
      }
      
    } catch (error) {
      await client.query('ROLLBACK')
      throw error
    } finally {
      client.release()
    }
  }

  /**
   * Get vendor with sync status
   */
  async getVendor(tenantId: string, vendorId: string): Promise<{
    vendor: any
    syncStatus: SyncStatus
  } | null> {
    const client = await this.db.connect()
    
    try {
      const tenantSchema = `tenant_${tenantId.padStart(16, '0')}`
      
      // Get vendor and sync status
      const result = await client.query(`
        SELECT 
          tv.*,
          tvs.global_vendor_id,
          tvs.confidence,
          tvs.match_type,
          tvs.sync_status,
          tvs.changed_on as sync_updated_at
        FROM ${tenantSchema}.tenant_vendors tv
        LEFT JOIN ${tenantSchema}.tenant_vendor_sync tvs ON tv.id = tvs.tenant_vendor_id
        WHERE tv.id = $1
      `, [vendorId])
      
      if (result.rows.length === 0) {
        return null
      }
      
      const row = result.rows[0]
      
      return {
        vendor: {
          id: row.id,
          name: row.name,
          taxId: row.tax_id,
          domain: row.domain,
          address: row.address,
          city: row.city,
          state: row.state,
          country: row.country,
          contactEmail: row.contact_email,
          contactPhone: row.contact_phone,
          website: row.website,
          createdAt: row.created_on,
          updatedAt: row.changed_on
        },
        syncStatus: {
          isSync: !!row.global_vendor_id,
          lastSyncDate: row.sync_updated_at,
          syncStatus: row.sync_status || 'pending',
          confidence: row.confidence,
          matchType: row.match_type,
          globalId: row.global_vendor_id
        }
      }
      
    } finally {
      client.release()
    }
  }

  /**
   * List vendors with sync status
   */
  async listVendors(tenantId: string, options: {
    limit?: number
    offset?: number
    search?: string
    syncStatus?: 'synced' | 'pending' | 'conflict' | 'failed'
  } = {}): Promise<{
    vendors: Array<{ vendor: any; syncStatus: SyncStatus }>
    total: number
  }> {
    const client = await this.db.connect()
    
    try {
      const tenantSchema = `tenant_${tenantId.padStart(16, '0')}`
      const { limit = 50, offset = 0, search, syncStatus } = options
      
      let whereClause = ''
      const params: any[] = []
      let paramIndex = 1
      
      if (search) {
        whereClause += ` WHERE tv.name ILIKE $${paramIndex}`
        params.push(`%${search}%`)
        paramIndex++
      }
      
      if (syncStatus) {
        const connector = whereClause ? ' AND' : ' WHERE'
        whereClause += `${connector} tvs.sync_status = $${paramIndex}`
        params.push(syncStatus)
        paramIndex++
      }
      
      // Get vendors with sync status
      const result = await client.query(`
        SELECT 
          tv.*,
          tvs.global_vendor_id,
          tvs.confidence,
          tvs.match_type,
          tvs.sync_status,
          tvs.changed_on as sync_updated_at
        FROM ${tenantSchema}.tenant_vendors tv
        LEFT JOIN ${tenantSchema}.tenant_vendor_sync tvs ON tv.id = tvs.tenant_vendor_id
        ${whereClause}
        ORDER BY tv.created_on DESC
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `, [...params, limit, offset])
      
      // Get total count
      const countResult = await client.query(`
        SELECT COUNT(*) as total
        FROM ${tenantSchema}.tenant_vendors tv
        LEFT JOIN ${tenantSchema}.tenant_vendor_sync tvs ON tv.id = tvs.tenant_vendor_id
        ${whereClause}
      `, params)
      
      const vendors = result.rows.map(row => ({
        vendor: {
          id: row.id,
          name: row.name,
          taxId: row.tax_id,
          domain: row.domain,
          address: row.address,
          city: row.city,
          state: row.state,
          country: row.country,
          contactEmail: row.contact_email,
          contactPhone: row.contact_phone,
          website: row.website,
          createdAt: row.created_on,
          updatedAt: row.changed_on
        },
        syncStatus: {
          isSync: !!row.global_vendor_id,
          lastSyncDate: row.sync_updated_at,
          syncStatus: row.sync_status || 'pending',
          confidence: row.confidence,
          matchType: row.match_type,
          globalId: row.global_vendor_id
        }
      }))
      
      return {
        vendors,
        total: parseInt(countResult.rows[0].total, 10)
      }
      
    } finally {
      client.release()
    }
  }

  /**
   * Trigger manual synchronization for a vendor
   */
  async syncVendor(tenantId: string, vendorId: string): Promise<{
    success: boolean
    syncStatus: SyncStatus
    message: string
  }> {
    try {
      // Run synchronization for this specific vendor
      const result = await this.syncEngine.syncVendors(tenantId, { 
        dryRun: false,
        batchSize: 1 
      })
      
      // Get updated sync status
      const vendorData = await this.getVendor(tenantId, vendorId)
      
      return {
        success: result.success,
        syncStatus: vendorData?.syncStatus || { isSync: false, syncStatus: 'failed' },
        message: result.success ? 'Vendor synchronized successfully' : 'Synchronization failed'
      }
      
    } catch (error) {
      this.logger.error(`Manual vendor sync failed`, { tenantId, vendorId, error })
      
      return {
        success: false,
        syncStatus: { isSync: false, syncStatus: 'failed' },
        message: error instanceof Error ? error.message : 'Synchronization failed'
      }
    }
  }

  /**
   * Get synchronization status for tenant
   */
  async getSyncStatus(tenantId: string): Promise<{
    lastSync?: Date
    activeBatches: any[]
    recentBatches: any[]
    stats: {
      totalVendors: number
      syncedVendors: number
      pendingVendors: number
      conflictVendors: number
    }
  }> {
    const [syncStatus, stats] = await Promise.all([
      this.syncEngine.getSyncStatus(tenantId),
      this.getTenantSyncStats(tenantId)
    ])
    
    return {
      ...syncStatus,
      stats
    }
  }

  /**
   * Get tenant synchronization statistics
   */
  private async getTenantSyncStats(tenantId: string): Promise<{
    totalVendors: number
    syncedVendors: number
    pendingVendors: number
    conflictVendors: number
  }> {
    const client = await this.db.connect()
    
    try {
      const tenantSchema = `tenant_${tenantId.padStart(16, '0')}`
      
      const result = await client.query(`
        SELECT 
          COUNT(*) as total_vendors,
          COUNT(CASE WHEN tvs.sync_status = 'synced' THEN 1 END) as synced_vendors,
          COUNT(CASE WHEN tvs.sync_status IS NULL OR tvs.sync_status = 'pending' THEN 1 END) as pending_vendors,
          COUNT(CASE WHEN tvs.sync_status = 'conflict' THEN 1 END) as conflict_vendors
        FROM ${tenantSchema}.tenant_vendors tv
        LEFT JOIN ${tenantSchema}.tenant_vendor_sync tvs ON tv.id = tvs.tenant_vendor_id
      `)
      
      const row = result.rows[0]
      
      return {
        totalVendors: parseInt(row.total_vendors, 10),
        syncedVendors: parseInt(row.synced_vendors, 10),
        pendingVendors: parseInt(row.pending_vendors, 10),
        conflictVendors: parseInt(row.conflict_vendors, 10)
      }
      
    } finally {
      client.release()
    }
  }

  /**
   * Trigger async vendor synchronization
   */
  private async triggerVendorSync(tenantId: string, vendorId: string): Promise<void> {
    // In a production environment, this would typically queue a background job
    // For now, we'll run it directly but catch any errors
    try {
      await this.syncEngine.syncVendors(tenantId, { 
        batchSize: 10,
        autoResolveThreshold: 85
      })
    } catch (error) {
      this.logger.error(`Background vendor sync failed`, { tenantId, vendorId, error })
    }
  }
}

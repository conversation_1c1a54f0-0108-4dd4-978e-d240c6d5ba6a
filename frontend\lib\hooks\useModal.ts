/**
 * Modal State Management Hook
 * 
 * Consolidates modal open/close state management patterns found across components.
 * Provides consistent modal state handling with loading states and callbacks.
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import { handleError } from '@/lib/utils/error-handler';

export interface ModalConfig {
  // Initial state
  initialOpen?: boolean;
  
  // Callbacks
  onOpen?: () => void;
  onClose?: () => void;
  onSubmit?: (data?: any) => Promise<void> | void;
  
  // Auto-close configuration
  autoClose?: {
    enabled: boolean;
    delay?: number; // milliseconds
  };
  
  // Prevent close during loading
  preventCloseOnLoading?: boolean;
}

export interface ModalState {
  isOpen: boolean;
  isLoading: boolean;
  error: string | null;
  data: any;
}

export interface ModalActions {
  open: (data?: any) => void;
  close: () => void;
  toggle: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setData: (data: any) => void;
  submit: (data?: any) => Promise<void>;
  reset: () => void;
}

export interface UseModalReturn {
  state: ModalState;
  actions: ModalActions;
  
  // Convenience getters
  isOpen: boolean;
  isLoading: boolean;
  error: string | null;
  data: any;
  
  // Modal props helpers
  getModalProps: () => {
    isOpen: boolean;
    onClose: () => void;
  };
}

/**
 * Modal State Management Hook
 */
export function useModal(config: ModalConfig = {}): UseModalReturn {
  const {
    initialOpen = false,
    onOpen,
    onClose,
    onSubmit,
    autoClose,
    preventCloseOnLoading = true
  } = config;
  
  // Modal state
  const [isOpen, setIsOpen] = useState(initialOpen);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<any>(null);
  
  // Auto-close timeout ref
  const autoCloseTimeoutRef = useRef<NodeJS.Timeout>();
  
  // Open modal
  const open = useCallback((modalData?: any) => {
    setIsOpen(true);
    setError(null);
    if (modalData !== undefined) {
      setData(modalData);
    }
    
    // Call onOpen callback
    if (onOpen) {
      onOpen();
    }
    
    // Set up auto-close if configured
    if (autoClose?.enabled) {
      const delay = autoClose.delay || 3000;
      autoCloseTimeoutRef.current = setTimeout(() => {
        close();
      }, delay);
    }
  }, [onOpen, autoClose]);
  
  // Close modal
  const close = useCallback(() => {
    // Prevent close if loading and configured to do so
    if (preventCloseOnLoading && isLoading) {
      return;
    }
    
    setIsOpen(false);
    setError(null);
    
    // Clear auto-close timeout
    if (autoCloseTimeoutRef.current) {
      clearTimeout(autoCloseTimeoutRef.current);
      autoCloseTimeoutRef.current = undefined;
    }
    
    // Call onClose callback
    if (onClose) {
      onClose();
    }
  }, [onClose, isLoading, preventCloseOnLoading]);
  
  // Toggle modal
  const toggle = useCallback(() => {
    if (isOpen) {
      close();
    } else {
      open();
    }
  }, [isOpen, open, close]);
  
  // Set loading state
  const setLoadingState = useCallback((loading: boolean) => {
    setIsLoading(loading);
  }, []);
  
  // Set error state
  const setErrorState = useCallback((errorMessage: string | null) => {
    setError(errorMessage);
  }, []);
  
  // Set data
  const setDataState = useCallback((modalData: any) => {
    setData(modalData);
  }, []);
  
  // Submit handler
  const submit = useCallback(async (submitData?: any) => {
    if (!onSubmit) return;

    const dataToSubmit = submitData !== undefined ? submitData : data;

    try {
      setIsLoading(true);
      setError(null);

      await onSubmit(dataToSubmit);

      // Auto-close on successful submit if not configured otherwise
      if (!autoClose?.enabled) {
        close();
      }
    } catch (err) {
      const errorInfo = handleError(err as Error, {
        component: 'useModal',
        operation: 'submit',
        metadata: { dataToSubmit }
      });
      setError(errorInfo.message);
    } finally {
      setIsLoading(false);
    }
  }, [onSubmit, data, close, autoClose]);
  
  // Reset modal state
  const reset = useCallback(() => {
    setIsOpen(initialOpen);
    setIsLoading(false);
    setError(null);
    setData(null);
    
    // Clear auto-close timeout
    if (autoCloseTimeoutRef.current) {
      clearTimeout(autoCloseTimeoutRef.current);
      autoCloseTimeoutRef.current = undefined;
    }
  }, [initialOpen]);
  
  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (autoCloseTimeoutRef.current) {
        clearTimeout(autoCloseTimeoutRef.current);
      }
    };
  }, []);
  
  // Modal props helper
  const getModalProps = useCallback(() => ({
    isOpen,
    onClose: close
  }), [isOpen, close]);
  
  return {
    state: {
      isOpen,
      isLoading,
      error,
      data
    },
    actions: {
      open,
      close,
      toggle,
      setLoading: setLoadingState,
      setError: setErrorState,
      setData: setDataState,
      submit,
      reset
    },
    
    // Convenience getters
    isOpen,
    isLoading,
    error,
    data,
    
    // Helpers
    getModalProps
  };
}

/**
 * Convenience hooks for common modal patterns
 */

// Simple modal with just open/close
export function useSimpleModal(initialOpen = false) {
  return useModal({ initialOpen });
}

// Modal with form submission
export function useFormModal(onSubmit: (data: any) => Promise<void>) {
  return useModal({ onSubmit });
}

// Modal with auto-close
export function useAutoCloseModal(delay = 3000) {
  return useModal({
    autoClose: {
      enabled: true,
      delay
    }
  });
}

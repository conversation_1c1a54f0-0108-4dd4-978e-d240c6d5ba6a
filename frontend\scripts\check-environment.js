#!/usr/bin/env node

/**
 * Environment Variable Checker
 * 
 * This script checks all environment variables and reports any issues.
 * It can be run manually or as part of the build process.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config({ path: path.join(__dirname, '../.env.local') });

// Required environment variables for different environments
const REQUIRED_VARS = {
  development: [
    'DATABASE_URL',
    'DB_HOST',
    'DB_NAME',
    'DB_USER',
    'NEXT_PUBLIC_AWS_REGION',
    'NEXT_PUBLIC_AWS_USER_POOLS_ID',
    'NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID',
    'NEXT_PUBLIC_AWS_COGNITO_DOMAIN',
    'NEXT_PUBLIC_REDIRECT_SIGN_IN',
    'NEXT_PUBLIC_REDIRECT_SIGN_OUT',
  ],
  
  production: [
    'DATABASE_URL',
    'DB_HOST',
    'DB_NAME',
    'DB_USER',
    'DB_PASSWORD',
    'JWT_SECRET',
    'ENCRYPTION_KEY',
    'NEXT_PUBLIC_AWS_REGION',
    'NEXT_PUBLIC_AWS_USER_POOLS_ID',
    'NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID',
    'NEXT_PUBLIC_AWS_COGNITO_DOMAIN',
    'NEXT_PUBLIC_REDIRECT_SIGN_IN',
    'NEXT_PUBLIC_REDIRECT_SIGN_OUT',
    'CRON_SECRET',
  ]
};

// Optional variables with defaults
const OPTIONAL_VARS = [
  'SESSION_COOKIE_MAX_AGE',
  'TOKEN_REFRESH_THRESHOLD',
  'AUTO_REFRESH_TOKENS',
  'JWT_EXPIRATION',
  'REFRESH_TOKEN_EXPIRATION',
  'ENCRYPTION_ALGORITHM',
  'NEXT_PUBLIC_LOG_LEVEL',
  'NEXT_PUBLIC_ENABLE_LOGGING',
  'API_TIMEOUT',
  'DATABASE_TIMEOUT',
  'ENABLE_DEBUG_MODE',
  'ENABLE_PERFORMANCE_MONITORING',
  'ENABLE_ERROR_TRACKING',
  'DATABASE_SSL',
  'USE_IAM_DB_AUTH',
  'NEXT_PUBLIC_APP_URL',
  'NEXT_PUBLIC_API_URL',
  'EMAIL_FROM',
  'EMAIL_REPLY_TO',
  'RATE_LIMIT_WINDOW',
  'RATE_LIMIT_MAX_REQUESTS',
  'NEXT_PUBLIC_ENABLE_ANALYTICS',
  'NEXT_PUBLIC_ANALYTICS_ID',
];

function checkEnvironment() {
  console.log('🔍 Checking Environment Variables...\n');
  
  const env = process.env.NODE_ENV || 'development';
  const requiredVars = REQUIRED_VARS[env] || REQUIRED_VARS.development;
  
  let hasErrors = false;
  let hasWarnings = false;
  
  // Check required variables
  console.log('📋 Required Variables:');
  for (const varName of requiredVars) {
    const value = process.env[varName];
    if (!value || value.trim() === '') {
      console.log(`   ❌ ${varName}: MISSING`);
      hasErrors = true;
    } else {
      // Mask sensitive values
      const displayValue = varName.includes('SECRET') || varName.includes('PASSWORD') || varName.includes('KEY') 
        ? '***HIDDEN***' 
        : value.length > 50 
          ? value.substring(0, 47) + '...' 
          : value;
      console.log(`   ✅ ${varName}: ${displayValue}`);
    }
  }
  
  console.log('\n📋 Optional Variables:');
  for (const varName of OPTIONAL_VARS) {
    const value = process.env[varName];
    if (!value || value.trim() === '') {
      console.log(`   ⚠️  ${varName}: Using default`);
      hasWarnings = true;
    } else {
      const displayValue = varName.includes('SECRET') || varName.includes('PASSWORD') || varName.includes('KEY') 
        ? '***HIDDEN***' 
        : value.length > 50 
          ? value.substring(0, 47) + '...' 
          : value;
      console.log(`   ✅ ${varName}: ${displayValue}`);
    }
  }
  
  // Validate specific formats
  console.log('\n🔍 Format Validation:');
  
  // Database URL
  const dbUrl = process.env.DATABASE_URL;
  if (dbUrl) {
    if (dbUrl.startsWith('postgresql://')) {
      console.log('   ✅ DATABASE_URL: Valid PostgreSQL format');
    } else {
      console.log('   ❌ DATABASE_URL: Invalid format (should start with postgresql://)');
      hasErrors = true;
    }
  }
  
  // AWS Region
  const awsRegion = process.env.NEXT_PUBLIC_AWS_REGION;
  if (awsRegion) {
    if (/^[a-z]{2}-[a-z]+-\d+$/.test(awsRegion)) {
      console.log('   ✅ NEXT_PUBLIC_AWS_REGION: Valid format');
    } else {
      console.log('   ⚠️  NEXT_PUBLIC_AWS_REGION: Format may be invalid');
      hasWarnings = true;
    }
  }
  
  // Redirect URLs
  const redirectSignIn = process.env.NEXT_PUBLIC_REDIRECT_SIGN_IN;
  if (redirectSignIn) {
    if (redirectSignIn.startsWith('http')) {
      console.log('   ✅ NEXT_PUBLIC_REDIRECT_SIGN_IN: Valid URL format');
    } else {
      console.log('   ❌ NEXT_PUBLIC_REDIRECT_SIGN_IN: Invalid URL format');
      hasErrors = true;
    }
  }
  
  const redirectSignOut = process.env.NEXT_PUBLIC_REDIRECT_SIGN_OUT;
  if (redirectSignOut) {
    if (redirectSignOut.startsWith('http')) {
      console.log('   ✅ NEXT_PUBLIC_REDIRECT_SIGN_OUT: Valid URL format');
    } else {
      console.log('   ❌ NEXT_PUBLIC_REDIRECT_SIGN_OUT: Invalid URL format');
      hasErrors = true;
    }
  }
  
  // Numeric values
  const numericVars = [
    'SESSION_COOKIE_MAX_AGE',
    'TOKEN_REFRESH_THRESHOLD',
    'JWT_EXPIRATION',
    'API_TIMEOUT',
    'DATABASE_TIMEOUT',
    'DB_PORT'
  ];
  
  for (const varName of numericVars) {
    const value = process.env[varName];
    if (value) {
      if (!isNaN(parseInt(value))) {
        console.log(`   ✅ ${varName}: Valid number (${value})`);
      } else {
        console.log(`   ❌ ${varName}: Invalid number format (${value})`);
        hasErrors = true;
      }
    }
  }
  
  // Summary
  console.log('\n📊 Summary:');
  if (hasErrors) {
    console.log('   ❌ Environment has errors that need to be fixed');
    process.exit(1);
  } else if (hasWarnings) {
    console.log('   ⚠️  Environment has warnings but should work');
    console.log('   ✅ Environment check passed with warnings');
  } else {
    console.log('   ✅ Environment check passed successfully');
  }
  
  // Environment file check
  console.log('\n📁 Environment Files:');
  const envFiles = ['.env.local', '.env', '.env.example'];
  for (const file of envFiles) {
    const filePath = path.join(__dirname, '..', file);
    if (fs.existsSync(filePath)) {
      console.log(`   ✅ ${file}: Found`);
    } else {
      console.log(`   ❌ ${file}: Not found`);
    }
  }
}

// Run the check
checkEnvironment();

export { checkEnvironment };

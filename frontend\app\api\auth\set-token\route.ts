import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  const { idToken } = await request.json();

  if (!idToken) {
    // Clear the cookie if no token provided (logout case)
    const response = NextResponse.json({ success: true });
    response.cookies.set('idToken', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      path: '/',
      expires: new Date(0),
    });
    return response;
  }

  const response = NextResponse.json({ success: true });
  response.cookies.set('idToken', idToken, {
    httpOnly: true, // More secure; set to false if you need to read it client-side
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    path: '/',
    maxAge: 60 * 60 * 24 * 7, // 7 days
  });
  return response;
} 
/**
 * Integration Tests for Sync Engine
 * 
 * Tests the complete synchronization process including:
 * - End-to-end sync workflows
 * - Database interactions
 * - Conflict handling
 * - Performance characteristics
 */

import { Pool } from 'pg'
import { SyncEngine } from '../../services/sync/SyncEngine'
import { SyncTestDataGenerator } from '../data/SyncTestDataGenerator'
import { Logger } from '../../services/Logger'

describe('SyncEngine Integration Tests', () => {
  let db: Pool
  let syncEngine: SyncEngine
  let testDataGenerator: SyncTestDataGenerator
  let mockLogger: jest.Mocked<Logger>

  beforeAll(async () => {
    // Setup test database connection
    db = new Pool({
      host: process.env.TEST_DB_HOST || 'localhost',
      port: parseInt(process.env.TEST_DB_PORT || '5432'),
      database: process.env.TEST_DB_NAME || 'renewtrack_test',
      user: process.env.TEST_DB_USER || 'postgres',
      password: process.env.TEST_DB_PASSWORD || 'password'
    })

    mockLogger = {
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn()
    } as any

    syncEngine = new SyncEngine(db, mockLogger)
    testDataGenerator = new SyncTestDataGenerator(db, mockLogger)
  })

  afterAll(async () => {
    await db.end()
  })

  beforeEach(async () => {
    // Clean up test data before each test
    await testDataGenerator.cleanupTestData()
  })

  describe('Perfect Matches Scenario', () => {
    it('should sync identical vendors with 95% confidence', async () => {
      // Generate perfect match test data
      await testDataGenerator.generatePerfectMatchesScenario()

      // Run vendor sync
      const result = await syncEngine.syncVendors('0000000000000001', {
        batchSize: 10,
        dryRun: false
      })

      expect(result.success).toBe(true)
      expect(result.totalProcessed).toBeGreaterThan(0)
      expect(result.matched).toBe(result.totalProcessed)
      expect(result.conflicts).toBe(0)

      // Verify sync records were created
      const client = await db.connect()
      try {
        const syncResult = await client.query(`
          SELECT COUNT(*) as count, AVG(confidence) as avg_confidence
          FROM tenant_0000000000000001.tenant_vendor_sync
          WHERE sync_status = 'synced'
        `)

        expect(parseInt(syncResult.rows[0].count, 10)).toBeGreaterThan(0)
        expect(parseFloat(syncResult.rows[0].avg_confidence)).toBeGreaterThanOrEqual(95)
      } finally {
        client.release()
      }
    })

    it('should create global vendors for new records', async () => {
      await testDataGenerator.generatePerfectMatchesScenario()

      const result = await syncEngine.syncVendors('0000000000000001')

      expect(result.success).toBe(true)

      // Verify global vendors were created
      const client = await db.connect()
      try {
        const globalResult = await client.query(`
          SELECT COUNT(*) as count FROM metadata.global_vendors
        `)

        expect(parseInt(globalResult.rows[0].count, 10)).toBeGreaterThan(0)
      } finally {
        client.release()
      }
    })
  })

  describe('Fuzzy Matches Scenario', () => {
    it('should match similar vendors with appropriate confidence', async () => {
      await testDataGenerator.generateFuzzyMatchesScenario()

      const result = await syncEngine.syncVendors('0000000000000001')

      expect(result.success).toBe(true)
      expect(result.matched).toBeGreaterThan(0)

      // Verify confidence scores are in fuzzy range
      const client = await db.connect()
      try {
        const syncResult = await client.query(`
          SELECT confidence FROM tenant_0000000000000001.tenant_vendor_sync
          WHERE sync_status = 'synced'
        `)

        syncResult.rows.forEach(row => {
          const confidence = parseFloat(row.confidence)
          expect(confidence).toBeGreaterThanOrEqual(70)
          expect(confidence).toBeLessThanOrEqual(90)
        })
      } finally {
        client.release()
      }
    })
  })

  describe('Conflicts Scenario', () => {
    it('should create conflicts for ambiguous matches', async () => {
      await testDataGenerator.generateConflictsScenario()

      const result = await syncEngine.syncVendors('0000000000000001')

      expect(result.success).toBe(true)
      expect(result.conflicts).toBeGreaterThan(0)

      // Verify conflicts were created
      const client = await db.connect()
      try {
        const conflictResult = await client.query(`
          SELECT COUNT(*) as count FROM metadata.sync_conflicts
          WHERE status = 'pending' AND entity_type = 'vendor'
        `)

        expect(parseInt(conflictResult.rows[0].count, 10)).toBeGreaterThan(0)
      } finally {
        client.release()
      }
    })
  })

  describe('Full Sync Workflow', () => {
    it('should sync vendors, products, and versions in order', async () => {
      await testDataGenerator.generatePerfectMatchesScenario()

      const result = await syncEngine.syncAll('0000000000000001')

      expect(result.vendors.success).toBe(true)
      expect(result.products.success).toBe(true)
      expect(result.versions.success).toBe(true)
      expect(result.totalTimeMs).toBeGreaterThan(0)

      // Verify all entity types were synced
      const client = await db.connect()
      try {
        const vendorCount = await client.query(`
          SELECT COUNT(*) as count FROM tenant_0000000000000001.tenant_vendor_sync
        `)
        const productCount = await client.query(`
          SELECT COUNT(*) as count FROM tenant_0000000000000001.tenant_product_sync
        `)

        expect(parseInt(vendorCount.rows[0].count, 10)).toBeGreaterThan(0)
        expect(parseInt(productCount.rows[0].count, 10)).toBeGreaterThan(0)
      } finally {
        client.release()
      }
    })
  })

  describe('Error Handling', () => {
    it('should handle database errors gracefully', async () => {
      // Simulate database error by using invalid tenant ID
      const result = await syncEngine.syncVendors('invalid_tenant_id')

      expect(result.success).toBe(false)
      expect(result.errors.length).toBeGreaterThan(0)
    })

    it('should rollback on batch failure', async () => {
      await testDataGenerator.generatePerfectMatchesScenario()

      // Mock a failure in the middle of processing
      const originalProcess = syncEngine['vendorProcessor'].process
      syncEngine['vendorProcessor'].process = jest.fn().mockRejectedValue(new Error('Test error'))

      try {
        await syncEngine.syncVendors('0000000000000001')
      } catch (error) {
        expect(error).toBeDefined()
      }

      // Verify no partial sync records were left
      const client = await db.connect()
      try {
        const syncResult = await client.query(`
          SELECT COUNT(*) as count FROM tenant_0000000000000001.tenant_vendor_sync
        `)

        expect(parseInt(syncResult.rows[0].count, 10)).toBe(0)
      } finally {
        client.release()
      }

      // Restore original method
      syncEngine['vendorProcessor'].process = originalProcess
    })
  })

  describe('Performance Tests', () => {
    it('should handle large datasets efficiently', async () => {
      await testDataGenerator.generatePerformanceScenario()

      const startTime = Date.now()
      const result = await syncEngine.syncVendors('0000000000000001', {
        batchSize: 100
      })
      const endTime = Date.now()

      expect(result.success).toBe(true)
      expect(result.totalProcessed).toBeGreaterThan(100)

      // Performance assertion - should process at least 10 records per second
      const processingRate = result.totalProcessed / ((endTime - startTime) / 1000)
      expect(processingRate).toBeGreaterThan(10)
    }, 30000) // 30 second timeout for performance test

    it('should process batches in reasonable time', async () => {
      await testDataGenerator.generatePerformanceScenario()

      const result = await syncEngine.syncVendors('0000000000000001', {
        batchSize: 50
      })

      expect(result.success).toBe(true)
      expect(result.processingTimeMs).toBeLessThan(60000) // Less than 1 minute
    }, 60000)
  })

  describe('Dry Run Mode', () => {
    it('should not create any records in dry run mode', async () => {
      await testDataGenerator.generatePerfectMatchesScenario()

      const result = await syncEngine.syncVendors('0000000000000001', {
        dryRun: true
      })

      expect(result.success).toBe(true)
      expect(result.totalProcessed).toBeGreaterThan(0)

      // Verify no sync records were created
      const client = await db.connect()
      try {
        const syncResult = await client.query(`
          SELECT COUNT(*) as count FROM tenant_0000000000000001.tenant_vendor_sync
        `)
        const globalResult = await client.query(`
          SELECT COUNT(*) as count FROM metadata.global_vendors
        `)

        expect(parseInt(syncResult.rows[0].count, 10)).toBe(0)
        expect(parseInt(globalResult.rows[0].count, 10)).toBe(0)
      } finally {
        client.release()
      }
    })
  })
})

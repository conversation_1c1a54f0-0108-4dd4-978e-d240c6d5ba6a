-- =====================================================
-- Complete Database Setup Script
-- =====================================================
-- This script sets up the entire RenewTrack database with standardized
-- integer primary keys and proper foreign key relationships
--
-- Usage:
-- 1. Create a new database
-- 2. Run this script as a superuser
-- 3. Run tenant-specific setup for each tenant
-- =====================================================

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";

-- =====================================================
-- STEP 1: Create all schemas
-- =====================================================
CREATE SCHEMA IF NOT EXISTS metadata;

CREATE SCHEMA IF NOT EXISTS tenant_management;

-- =====================================================
-- STEP 2: Create metadata schema (reference data)
-- =====================================================
\i ../migrations/metadata_schema.sql

-- =====================================================
-- STEP 3: Create tenant management schema
-- =====================================================
\i ../migrations/tenant_management_schema.sql

-- =====================================================
-- STEP 4: Create sync tracking tables
-- =====================================================

-- Sync entity types
DO $$ BEGIN
    CREATE TYPE metadata.sync_entity_type AS ENUM ('vendor', 'product', 'product_version');

EXCEPTION WHEN duplicate_object THEN null;

END $$;

-- Sync conflict types
DO $$ BEGIN
    CREATE TYPE metadata.sync_conflict_type AS ENUM ('no_match', 'multiple_matches', 'low_confidence', 'data_conflict');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Sync resolution status
DO $$ BEGIN
    CREATE TYPE metadata.sync_resolution_status AS ENUM ('pending', 'auto_resolved', 'manually_resolved', 'ignored');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Sync batches table
CREATE TABLE IF NOT EXISTS metadata.sync_batches (
    BatchID SERIAL PRIMARY KEY,
    TenantID INTEGER NOT NULL,
    BatchType metadata.sync_entity_type NOT NULL,
    Status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (
        Status IN (
            'pending',
            'processing',
            'completed',
            'failed'
        )
    ),
    StartedAt TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    CompletedAt TIMESTAMP WITH TIME ZONE,
    TotalRecords INTEGER DEFAULT 0,
    ProcessedRecords INTEGER DEFAULT 0,
    MatchedRecords INTEGER DEFAULT 0,
    ConflictRecords INTEGER DEFAULT 0,
    ErrorMessage TEXT,
    CreatedAt TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Sync conflicts table
CREATE TABLE IF NOT EXISTS metadata.sync_conflicts (
    ConflictID SERIAL PRIMARY KEY,
    BatchID INTEGER NOT NULL REFERENCES metadata.sync_batches (BatchID) ON DELETE CASCADE,
    TenantEntityID INTEGER NOT NULL, -- The tenant record ID
    EntityType metadata.sync_entity_type NOT NULL,
    ConflictType metadata.sync_conflict_type NOT NULL,
    SuggestedMatches JSONB DEFAULT '[]', -- Array of potential global matches with scores
    TenantData JSONB NOT NULL, -- Snapshot of tenant record at time of conflict
    ResolutionStatus metadata.sync_resolution_status DEFAULT 'pending',
    ResolvedBy VARCHAR(255), -- User who resolved the conflict
    ResolvedAt TIMESTAMP WITH TIME ZONE,
    ResolutionData JSONB, -- How the conflict was resolved
    CreatedAt TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Sync jobs table
CREATE TABLE IF NOT EXISTS metadata.sync_jobs (
    JobID SERIAL PRIMARY KEY,
    TenantID INTEGER NOT NULL,
    JobType VARCHAR(50) NOT NULL CHECK (
        JobType IN (
            'vendor_sync',
            'product_sync',
            'version_sync',
            'full_sync'
        )
    ),
    Status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (
        Status IN (
            'pending',
            'processing',
            'completed',
            'failed',
            'retrying'
        )
    ),
    Priority INTEGER NOT NULL DEFAULT 5 CHECK (
        Priority >= 1
        AND Priority <= 10
    ),
    Attempts INTEGER NOT NULL DEFAULT 0,
    MaxAttempts INTEGER NOT NULL DEFAULT 3,
    ScheduledAt TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    StartedAt TIMESTAMP WITH TIME ZONE,
    CompletedAt TIMESTAMP WITH TIME ZONE,
    ErrorMessage TEXT,
    JobData JSONB,
    CreatedAt TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Sync metrics table
CREATE TABLE IF NOT EXISTS metadata.sync_metrics (
    MetricID SERIAL PRIMARY KEY,
    MetricName VARCHAR(100) NOT NULL,
    MetricValue DECIMAL(15, 4) NOT NULL,
    Tags JSONB,
    RecordedAt TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    CreatedAt TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- =====================================================
-- STEP 5: Create indexes for sync tables
-- =====================================================
CREATE INDEX IF NOT EXISTS idx_sync_batches_tenant ON metadata.sync_batches (TenantID);

CREATE INDEX IF NOT EXISTS idx_sync_batches_status ON metadata.sync_batches (Status);

CREATE INDEX IF NOT EXISTS idx_sync_batches_type ON metadata.sync_batches (BatchType);

CREATE INDEX IF NOT EXISTS idx_sync_conflicts_batch ON metadata.sync_conflicts (BatchID);

CREATE INDEX IF NOT EXISTS idx_sync_conflicts_entity ON metadata.sync_conflicts (TenantEntityID, EntityType);

CREATE INDEX IF NOT EXISTS idx_sync_conflicts_status ON metadata.sync_conflicts (ResolutionStatus);

CREATE INDEX IF NOT EXISTS idx_sync_jobs_tenant ON metadata.sync_jobs (TenantID);

CREATE INDEX IF NOT EXISTS idx_sync_jobs_status ON metadata.sync_jobs (Status);

CREATE INDEX IF NOT EXISTS idx_sync_jobs_type ON metadata.sync_jobs (JobType);

CREATE INDEX IF NOT EXISTS idx_sync_jobs_scheduled ON metadata.sync_jobs (ScheduledAt);

CREATE INDEX IF NOT EXISTS idx_sync_metrics_name ON metadata.sync_metrics (MetricName);

CREATE INDEX IF NOT EXISTS idx_sync_metrics_recorded ON metadata.sync_metrics (RecordedAt);

-- =====================================================
-- STEP 6: Create audit log table
-- =====================================================
CREATE TABLE IF NOT EXISTS audit_log (
    LogID BIGSERIAL PRIMARY KEY,
    EventID VARCHAR(50) UNIQUE NOT NULL,
    EventType VARCHAR(50) NOT NULL,
    Severity VARCHAR(20) NOT NULL CHECK (
        Severity IN (
            'low',
            'medium',
            'high',
            'critical'
        )
    ),
    Timestamp TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    TenantID INTEGER,
    UserID VARCHAR(50),
    SessionID VARCHAR(100),
    CorrelationID VARCHAR(100),
    TraceID VARCHAR(100),
    IPAddress INET,
    UserAgent TEXT,
    RequestID VARCHAR(100),
    Method VARCHAR(10),
    URL TEXT,
    Referer TEXT,
    Resource VARCHAR(100) NOT NULL,
    ResourceID VARCHAR(100),
    Action VARCHAR(50) NOT NULL,
    Description TEXT NOT NULL,
    Details JSONB,
    Success BOOLEAN DEFAULT true,
    ErrorCode VARCHAR(50),
    ErrorMessage TEXT,
    Duration INTEGER, -- milliseconds
    CreatedAt TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Audit log indexes
CREATE INDEX IF NOT EXISTS idx_audit_log_timestamp ON audit_log (Timestamp DESC);

CREATE INDEX IF NOT EXISTS idx_audit_log_tenant ON audit_log (TenantID);

CREATE INDEX IF NOT EXISTS idx_audit_log_user ON audit_log (UserID);

CREATE INDEX IF NOT EXISTS idx_audit_log_event_type ON audit_log (EventType);

CREATE INDEX IF NOT EXISTS idx_audit_log_severity ON audit_log (Severity);

CREATE INDEX IF NOT EXISTS idx_audit_log_resource ON audit_log (Resource);

-- =====================================================
-- STEP 7: Create sample tenant
-- =====================================================
-- Insert default tenant
INSERT INTO
    tenant_management.tenants (
        TenantName,
        SchemaName,
        Subdomain,
        Status
    )
VALUES (
        'Default Tenant',
        'tenant_0000000000000001',
        'default',
        'active'
    )
ON CONFLICT (SchemaName) DO NOTHING;

-- Insert default client mapping
INSERT INTO
    tenant_management.clients (
        TenantID,
        Name,
        Domain,
        Status
    )
VALUES (
        1,
        'Default Client',
        'renewtrack.com',
        'active'
    )
ON CONFLICT (Domain) DO NOTHING;

-- Insert default domain mapping
INSERT INTO
    tenant_management.domains (
        TenantID,
        DomainName,
        IsPrimary
    )
VALUES (1, 'renewtrack.com', true)
ON CONFLICT (DomainName) DO NOTHING;

-- =====================================================
-- STEP 8: Create tenant schema
-- =====================================================
-- Create the default tenant schema
CREATE SCHEMA IF NOT EXISTS "tenant_0000000000000001";

-- Set search path and create tenant tables
SET search_path TO "tenant_0000000000000001", metadata, public;

-- Run tenant schema creation
\i database/migrations/tenant_schema_standardized.sql

-- =====================================================
-- STEP 9: Setup tenant logging
-- =====================================================
-- Run tenant logging setup
\i database/tenant-logging-setup.sql

-- =====================================================
-- STEP 10: Insert sample data
-- =====================================================
-- Insert sample renewals data
INSERT INTO
    "tenant_0000000000000001".tenant_renewals (
        renewal_name,
        product_name,
        version,
        vendor_name,
        renewal_type_id,
        start_date,
        currency_id,
        cost,
        status,
        license_count,
        description
    )
VALUES (
        'Microsoft Office 365',
        'Office 365 Business Premium',
        'Current',
        'Microsoft',
        1,
        '2025-12-31',
        'CAD',
        15000.00,
        'Active',
        50,
        'Annual Office 365 subscription'
    ),
    (
        'Adobe Creative Cloud',
        'Creative Cloud All Apps',
        '2024',
        'Adobe',
        1,
        '2025-06-30',
        'CAD',
        8400.00,
        'Active',
        12,
        'Creative suite for design team'
    ),
    (
        'Salesforce CRM',
        'Sales Cloud Professional',
        'Current',
        'Salesforce',
        1,
        '2025-09-15',
        'CAD',
        12000.00,
        'Active',
        25,
        'Customer relationship management'
    ),
    (
        'AWS Infrastructure',
        'EC2 + RDS + S3',
        'Current',
        'Amazon Web Services',
        1,
        '2025-03-20',
        'CAD',
        24000.00,
        'Active',
        1,
        'Cloud infrastructure services'
    ),
    (
        'Slack Business+',
        'Business+ Plan',
        'Current',
        'Slack',
        1,
        '2025-11-10',
        'CAD',
        4800.00,
        'Active',
        100,
        'Team communication platform'
    )
ON CONFLICT DO NOTHING;

-- =====================================================
-- SETUP COMPLETE
-- =====================================================
-- Database setup completed successfully!
--
-- Next steps:
-- 1. Create additional tenant schemas as needed
-- 2. Configure application connection strings
-- 3. Set up monitoring and backup procedures
-- 4. Configure user access and permissions
-- =====================================================

-- Reset search path
SET search_path TO public;
/**
 * Search and Filtering Hook
 * 
 * Consolidates search functionality patterns found across components.
 * Provides debounced search, filtering, and result management.
 */

import { useState, useCallback, useMemo } from 'react';
import { useDebounce } from '@/lib/utils';

export interface SearchConfig<T = any> {
  // Initial search query
  initialQuery?: string;
  
  // Debounce delay in milliseconds
  debounceDelay?: number;
  
  // Minimum characters before search triggers
  minSearchLength?: number;
  
  // Search fields to match against
  searchFields?: Array<keyof T>;
  
  // Case sensitive search
  caseSensitive?: boolean;
  
  // Custom search function
  customSearch?: (items: T[], query: string) => T[];
  
  // Callbacks
  onSearch?: (query: string, results: T[]) => void;
  onClear?: () => void;
}

export interface SearchState<T = any> {
  query: string;
  debouncedQuery: string;
  results: T[];
  isSearching: boolean;
  hasSearched: boolean;
  resultCount: number;
}

export interface SearchActions {
  setQuery: (query: string) => void;
  clearSearch: () => void;
  search: (query: string) => void;
  reset: () => void;
}

export interface UseSearchReturn<T = any> {
  state: SearchState<T>;
  actions: SearchActions;
  
  // Convenience getters
  query: string;
  debouncedQuery: string;
  results: T[];
  isSearching: boolean;
  hasResults: boolean;
  isEmpty: boolean;
  
  // Search input props helper
  getSearchInputProps: () => {
    value: string;
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
    onClear: () => void;
    placeholder?: string;
  };
}

/**
 * Search Hook
 */
export function useSearch<T = any>(
  items: T[],
  config: SearchConfig<T> = {}
): UseSearchReturn<T> {
  const {
    initialQuery = '',
    debounceDelay = 300,
    minSearchLength = 1,
    searchFields = [],
    caseSensitive = false,
    customSearch,
    onSearch,
    onClear
  } = config;
  
  // Search state
  const [query, setQueryState] = useState(initialQuery);
  const [hasSearched, setHasSearched] = useState(false);
  
  // Debounced query
  const debouncedQuery = useDebounce(query, debounceDelay);
  
  // Determine if currently searching (query exists but debounced hasn't caught up)
  const isSearching = query !== debouncedQuery && query.length >= minSearchLength;
  
  // Default search function
  const defaultSearch = useCallback((searchItems: T[], searchQuery: string): T[] => {
    if (!searchQuery || searchQuery.length < minSearchLength) {
      return searchItems;
    }
    
    const normalizedQuery = caseSensitive ? searchQuery : searchQuery.toLowerCase();
    
    return searchItems.filter(item => {
      // If no search fields specified, search all string properties
      if (searchFields.length === 0) {
        return Object.values(item as any).some(value => {
          if (typeof value === 'string') {
            const normalizedValue = caseSensitive ? value : value.toLowerCase();
            return normalizedValue.includes(normalizedQuery);
          }
          return false;
        });
      }
      
      // Search specified fields
      return searchFields.some(field => {
        const value = item[field];
        if (typeof value === 'string') {
          const normalizedValue = caseSensitive ? value : value.toLowerCase();
          return normalizedValue.includes(normalizedQuery);
        }
        return false;
      });
    });
  }, [searchFields, caseSensitive, minSearchLength]);
  
  // Search results
  const results = useMemo(() => {
    const searchFunction = customSearch || defaultSearch;
    const searchResults = searchFunction(items, debouncedQuery);
    
    // Call onSearch callback if provided
    if (onSearch && debouncedQuery && debouncedQuery.length >= minSearchLength) {
      onSearch(debouncedQuery, searchResults);
    }
    
    return searchResults;
  }, [items, debouncedQuery, customSearch, defaultSearch, onSearch, minSearchLength]);
  
  // Set query
  const setQuery = useCallback((newQuery: string) => {
    setQueryState(newQuery);
    if (newQuery) {
      setHasSearched(true);
    }
  }, []);
  
  // Clear search
  const clearSearch = useCallback(() => {
    setQueryState('');
    setHasSearched(false);
    
    if (onClear) {
      onClear();
    }
  }, [onClear]);
  
  // Manual search trigger
  const search = useCallback((searchQuery: string) => {
    setQuery(searchQuery);
  }, [setQuery]);
  
  // Reset search state
  const reset = useCallback(() => {
    setQueryState(initialQuery);
    setHasSearched(false);
  }, [initialQuery]);
  
  // Search input props helper
  const getSearchInputProps = useCallback(() => ({
    value: query,
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => setQuery(e.target.value),
    onClear: clearSearch,
    placeholder: `Search ${searchFields.length > 0 ? searchFields.join(', ') : 'items'}...`
  }), [query, setQuery, clearSearch, searchFields]);
  
  return {
    state: {
      query,
      debouncedQuery,
      results,
      isSearching,
      hasSearched,
      resultCount: results.length
    },
    actions: {
      setQuery,
      clearSearch,
      search,
      reset
    },
    
    // Convenience getters
    query,
    debouncedQuery,
    results,
    isSearching,
    hasResults: results.length > 0,
    isEmpty: results.length === 0 && hasSearched,
    
    // Helpers
    getSearchInputProps
  };
}

/**
 * Convenience hooks for common search patterns
 */

// Simple text search
export function useSimpleSearch<T>(items: T[], searchFields: Array<keyof T>) {
  return useSearch(items, { searchFields });
}

// Fast search with shorter debounce
export function useFastSearch<T>(items: T[], searchFields: Array<keyof T>) {
  return useSearch(items, { 
    searchFields, 
    debounceDelay: 150 
  });
}

// Case-sensitive search
export function useCaseSensitiveSearch<T>(items: T[], searchFields: Array<keyof T>) {
  return useSearch(items, { 
    searchFields, 
    caseSensitive: true 
  });
}

/**
 * API Route Factory
 * 
 * Provides utilities for creating standardized API routes with common patterns
 * like authentication, validation, and error handling.
 */

import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { createSuccessResponse, createErrorResponse, ApiErrorCode, HttpStatus } from './response'

export interface ApiRouteConfig<T = any> {
  requireAuth?: boolean
  requireTenant?: boolean
  bodySchema?: z.ZodSchema<T>
  querySchema?: z.ZodSchema<any>
  handler: (context: ApiRouteContext<T>) => Promise<any>
}

export interface ApiRouteContext<T = any> {
  request: NextRequest
  body?: T
  query?: any
  params?: any
  user?: any
  tenant?: any
}

/**
 * Create a standardized API route handler
 */
export function createApiRoute<T = any>(config: ApiRouteConfig<T>) {
  return async (
    request: NextRequest,
    routeContext?: { params?: Promise<any> | any }
  ): Promise<NextResponse> => {
    try {
      const context: ApiRouteContext<T> = { request }

      // Safely resolve params if provided
      if (routeContext?.params) {
        try {
          // Handle both Promise and non-Promise params for Next.js 15 compatibility
          context.params = routeContext.params && typeof routeContext.params.then === 'function'
            ? await routeContext.params
            : routeContext.params;
        } catch (error) {
          console.error('Error resolving route params:', error);
          return createErrorResponse(
            'Invalid route parameters',
            ApiErrorCode.VALIDATION_ERROR,
            HttpStatus.BAD_REQUEST
          )
        }
      }

      // Parse and validate body if schema provided
      if (config.bodySchema && (request.method === 'POST' || request.method === 'PUT' || request.method === 'PATCH')) {
        try {
          const body = await request.json()
          context.body = config.bodySchema.parse(body)
        } catch (error) {
          return createErrorResponse(
            'Invalid request body',
            ApiErrorCode.VALIDATION_ERROR,
            HttpStatus.BAD_REQUEST
          )
        }
      }

      // Parse and validate query parameters if schema provided
      if (config.querySchema) {
        try {
          const url = new URL(request.url)
          const query = Object.fromEntries(url.searchParams.entries())
          context.query = config.querySchema.parse(query)
        } catch (error) {
          return createErrorResponse(
            'Invalid query parameters',
            ApiErrorCode.VALIDATION_ERROR,
            HttpStatus.BAD_REQUEST
          )
        }
      }

      // TODO: Add authentication and tenant validation when needed
      // For now, just call the handler
      const result = await config.handler(context)

      return NextResponse.json(createSuccessResponse(result))
    } catch (error) {
      console.error('API route error:', error)
      return createErrorResponse(
        'Internal server error',
        ApiErrorCode.INTERNAL_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }
}

// Convenience functions for different HTTP methods
export function createGET<T = any>(config: Omit<ApiRouteConfig<T>, 'bodySchema'>) {
  return createApiRoute(config)
}

export function createPOST<T = any>(config: ApiRouteConfig<T>) {
  return createApiRoute(config)
}

export function createPUT<T = any>(config: ApiRouteConfig<T>) {
  return createApiRoute(config)
}

export function createDELETE<T = any>(config: Omit<ApiRouteConfig<T>, 'bodySchema'>) {
  return createApiRoute(config)
}

export function createPATCH<T = any>(config: ApiRouteConfig<T>) {
  return createApiRoute(config)
}

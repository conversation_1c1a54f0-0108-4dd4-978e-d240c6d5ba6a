/**
 * Type Utilities and Advanced Type Guards
 * 
 * This module provides advanced type checking utilities, runtime type validation,
 * and helper functions for working with TypeScript types safely.
 */

import { z } from 'zod';
import {
  User,
  Client,
  Renewal,
  TenantContext,
  ApiResponse,
  AuthSession,
  isUser,
  isClient,
  isRenewal,
  isTenantContext,
  isApiResponse,
  isAuthSession
} from './index';

// Runtime type validation schemas
export const userSchema = z.object({
  id: z.string().uuid(),
  email: z.string().email(),
  name: z.string().optional(),
  given_name: z.string().optional(),
  family_name: z.string().optional(),
  roles: z.array(z.string()),
  status: z.enum(['active', 'inactive', 'suspended', 'deleted']),
  created_on: z.date(),
  changed_on: z.date().optional(),
  last_login: z.date().optional(),
});

export const clientSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1),
  domain: z.string().min(1),
  domains: z.array(z.string()).optional(),
  status: z.enum(['active', 'inactive', 'suspended']),
  settings: z.record(z.any()),
  created_on: z.date(),
  changed_on: z.date().optional(),
});

export const renewalSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1),
  vendor: z.string().min(1),
  vendor_id: z.string().optional(),
  status: z.enum(['active', 'inactive', 'pending', 'expired']),
  due_date: z.date().optional(),
  annual_cost: z.number().min(0).optional(),
  description: z.string().optional(),
  created_on: z.date(),
  changed_on: z.date().optional(),
});

// Advanced type guards with detailed validation
export function validateUser(obj: unknown): obj is User {
  try {
    userSchema.parse(obj);
    return isUser(obj);
  } catch {
    return false;
  }
}

export function validateClient(obj: unknown): obj is Client {
  try {
    clientSchema.parse(obj);
    return isClient(obj);
  } catch {
    return false;
  }
}

export function validateRenewal(obj: unknown): obj is Renewal {
  try {
    renewalSchema.parse(obj);
    return isRenewal(obj);
  } catch {
    return false;
  }
}

// Array type guards
export function isUserArray(obj: unknown): obj is User[] {
  return Array.isArray(obj) && obj.every(isUser);
}

export function isClientArray(obj: unknown): obj is Client[] {
  return Array.isArray(obj) && obj.every(isClient);
}

export function isRenewalArray(obj: unknown): obj is Renewal[] {
  return Array.isArray(obj) && obj.every(isRenewal);
}

// Nullable type guards
export function isNullableUser(obj: unknown): obj is User | null {
  return obj === null || isUser(obj);
}

export function isNullableClient(obj: unknown): obj is Client | null {
  return obj === null || isClient(obj);
}

export function isNullableTenantContext(obj: unknown): obj is TenantContext | null {
  return obj === null || isTenantContext(obj);
}

// API response type guards
export function isSuccessResponse<T>(
  obj: unknown,
  dataValidator?: (data: unknown) => data is T
): obj is ApiResponse<T> & { success: true; data: T } {
  if (!isApiResponse(obj) || !obj.success || !obj.data) {
    return false;
  }
  
  if (dataValidator) {
    return dataValidator(obj.data);
  }
  
  return true;
}

export function isErrorResponse(obj: unknown): obj is ApiResponse & { success: false; error: string } {
  return isApiResponse(obj) && !obj.success && typeof obj.error === 'string';
}

// Type assertion helpers with runtime validation
export function assertUser(obj: unknown, context = 'Unknown'): User {
  if (!isUser(obj)) {
    throw new TypeError(`Expected User object in ${context}, got: ${typeof obj}`);
  }
  return obj;
}

export function assertClient(obj: unknown, context = 'Unknown'): Client {
  if (!isClient(obj)) {
    throw new TypeError(`Expected Client object in ${context}, got: ${typeof obj}`);
  }
  return obj;
}

export function assertTenantContext(obj: unknown, context = 'Unknown'): TenantContext {
  if (!isTenantContext(obj)) {
    throw new TypeError(`Expected TenantContext object in ${context}, got: ${typeof obj}`);
  }
  return obj;
}

export function assertAuthSession(obj: unknown, context = 'Unknown'): AuthSession {
  if (!isAuthSession(obj)) {
    throw new TypeError(`Expected AuthSession object in ${context}, got: ${typeof obj}`);
  }
  return obj;
}

// Safe property access helpers
export function safeGetProperty<T, K extends keyof T>(
  obj: T | null | undefined,
  key: K
): T[K] | undefined {
  return obj?.[key];
}

export function safeGetNestedProperty<T>(
  obj: any,
  path: string,
  defaultValue?: T
): T | undefined {
  try {
    const keys = path.split('.');
    let current = obj;
    
    for (const key of keys) {
      if (current == null || typeof current !== 'object') {
        return defaultValue;
      }
      current = current[key];
    }
    
    return current ?? defaultValue;
  } catch {
    return defaultValue;
  }
}

// Type narrowing helpers
export function hasProperty<T extends object, K extends string>(
  obj: T,
  key: K
): obj is T & Record<K, unknown> {
  return key in obj;
}

export function hasStringProperty<T extends object, K extends string>(
  obj: T,
  key: K
): obj is T & Record<K, string> {
  return key in obj && typeof (obj as any)[key] === 'string';
}

export function hasNumberProperty<T extends object, K extends string>(
  obj: T,
  key: K
): obj is T & Record<K, number> {
  return key in obj && typeof (obj as any)[key] === 'number';
}

export function hasBooleanProperty<T extends object, K extends string>(
  obj: T,
  key: K
): obj is T & Record<K, boolean> {
  return key in obj && typeof (obj as any)[key] === 'boolean';
}

// Enum validation helpers
export function isValidStatus(value: unknown): value is 'active' | 'inactive' | 'suspended' | 'deleted' {
  return typeof value === 'string' && ['active', 'inactive', 'suspended', 'deleted'].includes(value);
}

export function isValidRenewalStatus(value: unknown): value is 'active' | 'inactive' | 'pending' | 'expired' {
  return typeof value === 'string' && ['active', 'inactive', 'pending', 'expired'].includes(value);
}

export function isValidTheme(value: unknown): value is 'light' | 'dark' | 'system' {
  return typeof value === 'string' && ['light', 'dark', 'system'].includes(value);
}

// Date validation helpers
export function isValidDate(value: unknown): value is Date {
  return value instanceof Date && !isNaN(value.getTime());
}

export function isValidDateString(value: unknown): value is string {
  if (typeof value !== 'string') return false;
  const date = new Date(value);
  return isValidDate(date);
}

// Email validation helper
export function isValidEmail(value: unknown): value is string {
  if (typeof value !== 'string') return false;
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(value);
}

// UUID validation helper
export function isValidUUID(value: unknown): value is string {
  if (typeof value !== 'string') return false;
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(value);
}

// Array validation helpers
export function isNonEmptyArray<T>(value: unknown): value is [T, ...T[]] {
  return Array.isArray(value) && value.length > 0;
}

export function isStringArray(value: unknown): value is string[] {
  return Array.isArray(value) && value.every(item => typeof item === 'string');
}

export function isNumberArray(value: unknown): value is number[] {
  return Array.isArray(value) && value.every(item => typeof item === 'number');
}

// Object validation helpers
export function isPlainObject(value: unknown): value is Record<string, unknown> {
  return (
    value !== null &&
    typeof value === 'object' &&
    Object.prototype.toString.call(value) === '[object Object]'
  );
}

export function hasRequiredKeys<T extends Record<string, unknown>>(
  obj: unknown,
  keys: (keyof T)[]
): obj is T {
  if (!isPlainObject(obj)) return false;
  return keys.every(key => key in obj);
}

// Error handling type guards
export function isError(value: unknown): value is Error {
  return value instanceof Error;
}

export function isErrorWithCode(value: unknown): value is Error & { code: string } {
  return isError(value) && 'code' in value && typeof value.code === 'string';
}

export function isErrorWithStatus(value: unknown): value is Error & { statusCode: number } {
  return isError(value) && 'statusCode' in value && typeof value.statusCode === 'number';
}

// Utility type for exhaustive checking
export function assertNever(value: never): never {
  throw new Error(`Unexpected value: ${value}`);
}

// Simplified JSON parsing - use JSON.parse with try/catch directly
// This wrapper adds unnecessary complexity

/**
 * License Keys Management API
 * 
 * Super Admin endpoints for generating and managing license keys
 */

import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { databaseService } from '@/lib/services/database-service'
import { requireSuperAdmin } from '@/lib/api/auth-middleware'
import { createSuccessResponse, createErrorResponse, ApiErrorCode, HttpStatus } from '@/lib/api/response'

// Validation schemas
const generateLicenseKeySchema = z.object({
  license_type_id: z.string().transform(val => parseInt(val)),
  description: z.string().optional(),
  quantity: z.number().min(1).max(100).default(1)
})

const updateLicenseKeySchema = z.object({
  description: z.string().optional(),
  is_assigned: z.boolean().optional()
})

/**
 * GET /api/admin/license-keys
 * Get all license keys (super admin only)
 */
export async function GET(request: NextRequest) {
  try {
    // Require super admin authentication
    const authResult = await requireSuperAdmin(request)
    if (!authResult.success) {
      return authResult.response
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '50')
    const license_type = searchParams.get('license_type')
    const is_assigned = searchParams.get('is_assigned')
    const search = searchParams.get('search')

    const offset = (page - 1) * limit

    // Use the view for simplified querying
    let whereClause = '1=1'
    const params: any[] = []
    let paramIndex = 1

    if (license_type) {
      whereClause += ` AND license_type = $${paramIndex}`
      params.push(license_type)
      paramIndex++
    }

    if (is_assigned !== null && is_assigned !== undefined) {
      whereClause += ` AND is_assigned = $${paramIndex}`
      params.push(is_assigned === 'true')
      paramIndex++
    }

    if (search) {
      whereClause += ` AND (license_key ILIKE $${paramIndex} OR key_description ILIKE $${paramIndex})`
      params.push(`%${search}%`)
      paramIndex++
    }

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total
      FROM metadata.v_license_management
      WHERE ${whereClause}
    `
    const countResult = await databaseService.query(countQuery, params)
    const total = parseInt(countResult.rows[0].total)

    // Get license keys using the view
    const query = `
      SELECT *
      FROM metadata.v_license_management
      WHERE ${whereClause}
      ORDER BY created_on DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `
    params.push(limit, offset)

    const result = await databaseService.query(query, params)

    return createSuccessResponse(result.rows)

  } catch (error) {
    console.error('Error fetching license keys:', error)
    return createErrorResponse('Failed to fetch license keys', ApiErrorCode.INTERNAL_ERROR, HttpStatus.INTERNAL_SERVER_ERROR)
  }
}

/**
 * POST /api/admin/license-keys
 * Generate new license keys (super admin only)
 */
export async function POST(request: NextRequest) {
  try {
    // Require super admin authentication
    const authResult = await requireSuperAdmin(request)
    if (!authResult.success) {
      return authResult.response
    }

    const body = await request.json()
    const validatedData = generateLicenseKeySchema.parse(body)

    const generatedKeys = []

    // Generate the requested number of license keys
    for (let i = 0; i < validatedData.quantity; i++) {
      const query = `
        INSERT INTO metadata.admin_license_keys (
          license_key,
          license_type_id,
          description,
          created_by
        ) VALUES (
          metadata.generate_license_key(),
          $1,
          $2,
          $3
        )
        RETURNING license_key_id, license_key, license_type_id, description, created_on
      `

      const result = await databaseService.query(query, [
        validatedData.license_type_id,
        validatedData.description || `License Key - Generated ${new Date().toISOString()}`,
        authResult.session.email
      ])

      generatedKeys.push(result.rows[0])
    }

    return createSuccessResponse({
      message: `Successfully generated ${validatedData.quantity} license key(s)`,
      license_keys: generatedKeys
    })

  } catch (error) {
    console.error('Error generating license keys:', error)
    
    if (error instanceof z.ZodError) {
      return createErrorResponse('Invalid request data', ApiErrorCode.VALIDATION_ERROR, HttpStatus.BAD_REQUEST, error.errors)
    }

    return createErrorResponse('Failed to generate license keys', ApiErrorCode.INTERNAL_ERROR, HttpStatus.INTERNAL_SERVER_ERROR)
  }
}

/**
 * PUT /api/admin/license-keys/[id]
 * Update license key (super admin only)
 */
export async function PUT(request: NextRequest) {
  try {
    // Require super admin authentication
    const authResult = await requireSuperAdmin(request)
    if (!authResult.success) {
      return authResult.response
    }

    const { searchParams } = new URL(request.url)
    const licenseKeyId = searchParams.get('id')

    if (!licenseKeyId) {
      return createErrorResponse('License key ID is required', ApiErrorCode.VALIDATION_ERROR, HttpStatus.BAD_REQUEST)
    }

    const body = await request.json()
    const validatedData = updateLicenseKeySchema.parse(body)

    // Build update query
    const updateFields = []
    const params = []
    let paramIndex = 1

    if (validatedData.description !== undefined) {
      updateFields.push(`description = $${paramIndex}`)
      params.push(validatedData.description)
      paramIndex++
    }

    if (validatedData.is_assigned !== undefined) {
      updateFields.push(`is_assigned = $${paramIndex}`)
      params.push(validatedData.is_assigned)
      paramIndex++
    }

    if (updateFields.length === 0) {
      return createErrorResponse('No fields to update', ApiErrorCode.VALIDATION_ERROR, HttpStatus.BAD_REQUEST)
    }

    params.push(licenseKeyId)

    const query = `
      UPDATE metadata.admin_license_keys
      SET ${updateFields.join(', ')}, changed_on = CURRENT_TIMESTAMP
      WHERE license_key_id = $${paramIndex}
      RETURNING license_key_id, license_key, license_type_id, description, is_assigned, created_on
    `

    const result = await databaseService.query(query, params)

    if (result.rows.length === 0) {
      return createErrorResponse('License key not found', ApiErrorCode.NOT_FOUND, HttpStatus.NOT_FOUND)
    }

    return createSuccessResponse({
      message: 'License key updated successfully',
      license_key: result.rows[0]
    })

  } catch (error) {
    console.error('Error updating license key:', error)
    
    if (error instanceof z.ZodError) {
      return createErrorResponse('Invalid request data', ApiErrorCode.VALIDATION_ERROR, HttpStatus.BAD_REQUEST, error.errors)
    }

    return createErrorResponse('Failed to update license key', ApiErrorCode.INTERNAL_ERROR, HttpStatus.INTERNAL_SERVER_ERROR)
  }
}

/**
 * DELETE /api/admin/license-keys/[id]
 * Delete unused license key (super admin only)
 */
export async function DELETE(request: NextRequest) {
  try {
    // Require super admin authentication
    const authResult = await requireSuperAdmin(request)
    if (!authResult.success) {
      return authResult.response
    }

    const { searchParams } = new URL(request.url)
    const licenseKeyId = searchParams.get('id')

    if (!licenseKeyId) {
      return createErrorResponse('License key ID is required', ApiErrorCode.VALIDATION_ERROR, HttpStatus.BAD_REQUEST)
    }

    // Only allow deletion of unassigned license keys
    const query = `
      DELETE FROM metadata.admin_license_keys
      WHERE license_key_id = $1 AND is_assigned = FALSE
      RETURNING license_key_id, license_key
    `

    const result = await databaseService.query(query, [licenseKeyId])

    if (result.rows.length === 0) {
      return createErrorResponse('License key not found or already assigned', ApiErrorCode.NOT_FOUND, HttpStatus.NOT_FOUND)
    }

    return createSuccessResponse({
      message: 'License key deleted successfully',
      deleted_license_key: result.rows[0]
    })

  } catch (error) {
    console.error('Error deleting license key:', error)
    return createErrorResponse('Failed to delete license key', ApiErrorCode.INTERNAL_ERROR, HttpStatus.INTERNAL_SERVER_ERROR)
  }
}

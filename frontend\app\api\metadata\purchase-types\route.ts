/**
 * Purchase Types API Endpoint
 *
 * Provides access to purchase type metadata
 * GET /api/metadata/purchase-types - Returns active purchase types
 */

import { NextRequest } from 'next/server';
import { fetchAuthSession } from 'aws-amplify/auth';
import {
  createSuccessResponse,
  createErrorResponse,
  ApiErrorCode,
  HttpStatus,
  withErrorHandling
} from '@/lib/api/response';
import { executeQuery } from '@/lib/database';
import { PurchaseType } from '@/app/api/metadata/route';

// GET /api/metadata/purchase-types - Get active purchase types
export const GET = withErrorHandling(async (request: NextRequest) => {
  // Verify authentication using Amplify
  const session = await fetchAuthSession();
  if (!session?.tokens?.idToken) {
    return createErrorResponse(
      'Authentication required',
      ApiErrorCode.UNAUTHORIZED,
      HttpStatus.UNAUTHORIZED
    );
  }

  try {
    // Try to query purchase types from metadata schema, fallback to default data
    let result;
    try {
      result = await executeQuery<PurchaseType>(
        `SELECT
          id,
          name,
          status
        FROM metadata.global_purchase_types
        WHERE status = 'A'
        ORDER BY name ASC`,
        []
      );
    } catch (dbError) {
      console.error('Metadata schema not available:', dbError);
      return createErrorResponse(
        'Metadata schema not found. Please run database migrations to set up metadata tables.',
        ApiErrorCode.DATABASE_ERROR,
        500
      );
    }

    if (!result.success) {
      console.error('Failed to fetch purchase types:', result.error);
      // Return default data on database error
      const defaultPurchaseTypes = [
        { purchase_type_id: 1, name: 'New Purchase', active: true },
        { purchase_type_id: 2, name: 'Renewal', active: true },
        { purchase_type_id: 3, name: 'Upgrade', active: true },
        { purchase_type_id: 4, name: 'Additional Licenses', active: true }
      ];

      return createSuccessResponse(
        defaultPurchaseTypes,
        'Purchase types retrieved successfully (default data)'
      );
    }

    // Log successful fetch
    console.log(`Purchase types fetched successfully: ${result.data?.length || 0} records`);

    return createSuccessResponse(
      result.data || [],
      'Purchase types retrieved successfully'
    );
  } catch (error) {
    console.error('Error fetching purchase types:', error);
    return createErrorResponse(
      'Failed to fetch purchase types',
      ApiErrorCode.DATABASE_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
});

/**
 * Specialized Error Boundary Components
 * 
 * Pre-configured error boundaries for different application contexts
 */

'use client'

import React from 'react'
import ErrorBoundary, { ErrorBoundaryProps } from './ErrorBoundary'
import { UnifiedErrorDisplay } from './UnifiedErrorDisplay'

// Universal error boundary that adapts based on props
export function UniversalErrorBoundary({
  children,
  level = "component",
  title,
  ...props
}: Omit<ErrorBoundaryProps, 'level'> & {
  level?: 'page' | 'section' | 'component';
  title?: string;
}) {
  const config = {
    page: { maxRetries: 1, retryDelay: 2000, enableReporting: true },
    section: { maxRetries: 2, retryDelay: 1000, enableReporting: true },
    component: { maxRetries: 3, retryDelay: 500, enableReporting: false }
  }[level];

  return (
    <ErrorBoundary
      level={level}
      {...config}
      fallback={({ error, resetError, canRetry }) => (
        <UnifiedErrorDisplay
          error={error}
          variant={level === 'page' ? 'page' : level === 'section' ? 'card' : 'inline'}
          title={title || `${level.charAt(0).toUpperCase() + level.slice(1)} Error`}
          description={level === 'page'
            ? "This page encountered an error and couldn't load properly."
            : "This section couldn't load due to an error."
          }
          showRetry={canRetry}
          onRetry={resetError}
          showDetails={process.env.NODE_ENV === 'development'}
        />
      )}
      {...props}
    >
      {children}
    </ErrorBoundary>
  )
}

// Simplified aliases - only keep the most commonly used ones
export const PageErrorBoundary = ({ children, ...props }: Omit<ErrorBoundaryProps, 'level'>) => (
  <UniversalErrorBoundary level="page" {...props}>{children}</UniversalErrorBoundary>
);

export const SectionErrorBoundary = ({ children, title, ...props }: Omit<ErrorBoundaryProps, 'level'> & { title?: string }) => (
  <UniversalErrorBoundary level="section" title={title} {...props}>{children}</UniversalErrorBoundary>
);

// For all other cases, use UniversalErrorBoundary directly with appropriate level and title
// This reduces the number of specialized components while maintaining flexibility

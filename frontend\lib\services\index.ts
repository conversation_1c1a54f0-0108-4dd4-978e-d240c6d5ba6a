/**
 * Services Index - Centralized Service Exports
 *
 * This file provides clean exports for all service modules,
 * making service access consistent across the application.
 *
 * ⚠️  IMPORTANT: Client vs Server Separation
 * Only client-safe services are exported here.
 * Server-only services must be imported directly in API routes.
 */

// ===== CLIENT-SAFE SERVICES =====
// These can be used in both client and server components

// Core authentication service
export * from './amplify-service'

// Application state management - removed, using simple auth pattern

// Data service for API calls
export * from './data-service'

// Caching services
export * from './data-cache-service'

// Feature-specific services (client-safe)
export * from './advanced-filter-service'

// ===== SERVER-ONLY SERVICES =====
// ⚠️  WARNING: These should ONLY be imported directly in API routes
// DO NOT export these here to prevent client-side usage:
//
// - database-service.ts (database operations)
// - auth-service.ts (server-side auth with database)
// - renewalService.ts (server-side business logic)
// - license-service.ts (server-side license validation)
// - notification-service.ts (server notifications)
// - performance-monitor.ts (server monitoring)

// ===== COMMONLY USED EXPORTS =====
// Re-export frequently used functions for convenience

export {
  ensureAmplifyConfigured
} from './amplify-service'

export {
  dataService
} from './data-service'

// Note: Other service exports removed to prevent client-side usage
// Import directly from specific service files in API routes as needed

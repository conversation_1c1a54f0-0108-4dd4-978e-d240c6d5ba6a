/**
 * License Activation Component
 * 
 * Admin interface for activating license keys for clients
 */

'use client'

import React, { useState } from 'react'
import { useData } from '@/lib/hooks'
import { Button } from '@/components/ui/Button'
import { Form } from '@/components/ui/Form'
import { Modal } from '@/components/ui/Modal'
import { LoadingSpinner } from '@/components/common/LoadingStates'

interface Client {
  client_id: number
  name: string
  domain: string[]
  status: string
  created_on: string
}

interface LicenseActivation {
  id: number
  client_id: number
  license_key: string
  activation_status: string
  activated_at: string
  activated_by: string
  expires_at: string | null
  name: string
  license_type: string
  max_users: number
  max_tenants: number
}

interface ActivationFormData {
  licenseKey: string
  clientId: number
  customExpiryDays: number | null
}

export default function LicenseActivation() {
  const [showActivateModal, setShowActivateModal] = useState(false)
  const [selectedClient, setSelectedClient] = useState<Client | null>(null)

  // Fetch clients
  const {
    data: clientsData,
    loading: clientsLoading,
    error: clientsError
  } = useData<Client[]>({
    endpoint: '/api/admin/clients',
    cache: {
      key: 'admin-clients',
      ttl: 60000 // 1 minute
    }
  })

  // Fetch license activations
  const {
    data: activationsData,
    loading: activationsLoading,
    error: activationsError,
    refetch: refetchActivations
  } = useData<LicenseActivation[]>({
    endpoint: '/api/admin/licenses/activations',
    cache: {
      key: 'license-activations',
      ttl: 30000 // 30 seconds
    }
  })

  const handleActivateLicense = async (formData: ActivationFormData) => {
    try {
      const response = await fetch('/api/admin/licenses/activate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      })

      if (response.ok) {
        setShowActivateModal(false)
        setSelectedClient(null)
        refetchActivations() // Refresh the activations list
      } else {
        const error = await response.json()
        console.error('License activation failed:', error)
        alert(`Activation failed: ${error.message || 'Unknown error'}`)
      }
    } catch (error) {
      console.error('License activation error:', error)
      alert('Activation failed due to network error')
    }
  }

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Never'
    return new Date(dateString).toLocaleDateString()
  }

  const getStatusBadge = (activation: LicenseActivation) => {
    if (activation.activation_status === 'suspended') {
      return <span className="px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded">Suspended</span>
    }
    
    if (activation.expires_at && new Date(activation.expires_at) < new Date()) {
      return <span className="px-2 py-1 text-xs bg-red-100 text-red-800 rounded">Expired</span>
    }
    
    return <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded">Active</span>
  }

  const clients = clientsData || []
  const activations = activationsData || []

  // Get clients without active licenses
  const clientsWithoutLicense = clients.filter(client => 
    !activations.some(activation => 
      activation.client_id === client.client_id && 
      activation.activation_status === 'active'
    )
  )

  if (clientsLoading || activationsLoading) {
    return <LoadingSpinner />
  }

  if (clientsError || activationsError) {
    return (
      <div className="p-4 bg-red-50 border border-red-200 rounded-md">
        <p className="text-red-800">
          Error loading data: {String(clientsError || activationsError)}
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">License Activation</h1>
          <p className="text-gray-600">Activate license keys for clients</p>
        </div>
        <Button 
          onClick={() => setShowActivateModal(true)}
          disabled={clientsWithoutLicense.length === 0}
        >
          Activate License
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="text-2xl font-bold text-gray-900">{clients.length}</div>
          <div className="text-gray-600">Total Clients</div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="text-2xl font-bold text-green-600">{activations.length}</div>
          <div className="text-gray-600">Active Licenses</div>
        </div>
        <div className="bg-white p-6 rounded-lg shadow">
          <div className="text-2xl font-bold text-orange-600">{clientsWithoutLicense.length}</div>
          <div className="text-gray-600">Unlicensed Clients</div>
        </div>
      </div>

      {/* Active License Activations */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">Active License Activations</h2>
        </div>
        
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Client
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                License Key
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Type & Limits
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Activated
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Expires
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {activations.map((activation) => (
              <tr key={activation.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-gray-900">
                    {activation.name}
                  </div>
                  <div className="text-sm text-gray-500">
                    ID: {activation.client_id}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-mono text-gray-900">
                    {activation.license_key}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">
                    <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded capitalize">
                      {activation.license_type}
                    </span>
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {activation.max_users} users, {activation.max_tenants} tenants
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {getStatusBadge(activation)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <div>{formatDate(activation.activated_at)}</div>
                  <div className="text-xs text-gray-500">
                    by {activation.activated_by}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {formatDate(activation.expires_at)}
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        {activations.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500">No license activations found</p>
          </div>
        )}
      </div>

      {/* Clients Without Licenses */}
      {clientsWithoutLicense.length > 0 && (
        <div className="bg-white shadow rounded-lg overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Clients Without Licenses</h2>
          </div>
          
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Client Name
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Domain
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Created
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {clientsWithoutLicense.map((client) => (
                <tr key={client.client_id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">
                      {client.name}
                    </div>
                    <div className="text-sm text-gray-500">
                      ID: {client.client_id}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {client.domain.join(', ')}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatDate(client.created_on)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setSelectedClient(client)
                        setShowActivateModal(true)
                      }}
                    >
                      Activate License
                    </Button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Activate License Modal */}
      <ActivateLicenseModal
        isOpen={showActivateModal}
        onClose={() => {
          setShowActivateModal(false)
          setSelectedClient(null)
        }}
        onSubmit={handleActivateLicense}
        clients={clientsWithoutLicense}
        selectedClient={selectedClient}
      />
    </div>
  )
}

// Activate License Modal Component
function ActivateLicenseModal({
  isOpen,
  onClose,
  onSubmit,
  clients,
  selectedClient
}: {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: ActivationFormData) => void
  clients: Client[]
  selectedClient: Client | null
}) {
  const [formData, setFormData] = useState<ActivationFormData>({
    licenseKey: '',
    clientId: selectedClient?.client_id || 0,
    customExpiryDays: null
  })

  React.useEffect(() => {
    if (selectedClient) {
      setFormData(prev => ({ ...prev, clientId: selectedClient.client_id }))
    }
  }, [selectedClient])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData.licenseKey || !formData.clientId) {
      alert('Please fill in all required fields')
      return
    }
    onSubmit(formData)
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Activate License Key">
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700">License Key *</label>
          <input
            type="text"
            value={formData.licenseKey}
            onChange={(e) => setFormData(prev => ({ ...prev, licenseKey: e.target.value }))}
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
            placeholder="RT-XXXX-XXXX-XXXX-XXXX"
            required
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">Client *</label>
          <select
            value={formData.clientId}
            onChange={(e) => setFormData(prev => ({ ...prev, clientId: parseInt(e.target.value) }))}
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
            required
          >
            <option value={0}>Select a client...</option>
            {clients.map((client) => (
              <option key={client.client_id} value={client.client_id}>
                {client.name} ({client.domain.join(', ')})
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">Custom Expiry (Days)</label>
          <input
            type="number"
            value={formData.customExpiryDays || ''}
            onChange={(e) => setFormData(prev => ({ 
              ...prev, 
              customExpiryDays: e.target.value ? parseInt(e.target.value) : null 
            }))}
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
            placeholder="Leave empty to use license key expiry"
          />
          <p className="mt-1 text-xs text-gray-500">
            Override the license key's expiration date with a custom period
          </p>
        </div>

        <div className="flex justify-end space-x-3 pt-4">
          <Button type="button" variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button type="submit">
            Activate License
          </Button>
        </div>
      </form>
    </Modal>
  )
}

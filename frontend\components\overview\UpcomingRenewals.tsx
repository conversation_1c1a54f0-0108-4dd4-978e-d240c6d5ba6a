/**
 * Upcoming Renewals Component
 * 
 * Displays renewals that are due soon with urgency indicators.
 * Focused responsibility: Rendering upcoming renewals list only.
 */

'use client'

import React, { memo, useMemo, useState } from 'react'
import { Renewal, BaseComponentProps } from '@/lib/types'
// Note: usePerformanceMonitor moved to server-side only

interface UpcomingRenewalsProps extends BaseComponentProps {
  renewals: Renewal[]
  isLoading?: boolean
  onRenewalClick?: (renewal: Renewal) => void
  defaultDaysThreshold?: number
}

const UpcomingRenewals = memo(function UpcomingRenewals({
  renewals = [],
  isLoading = false,
  onRenewalClick,
  defaultDaysThreshold = 30,
  className = '',
  'data-testid': testId
}: UpcomingRenewalsProps) {
  // Performance monitoring in development - removed for now
  // usePerformanceMonitor('UpcomingRenewals')

  // Filter and sort upcoming renewals
  const upcomingRenewals = useMemo(() => {
    return getUpcomingRenewals(renewals, defaultDaysThreshold)
  }, [renewals, defaultDaysThreshold])

  const handleRenewalClick = (renewal: Renewal) => {
    onRenewalClick?.(renewal)
  }

  if (isLoading) {
    return (
      <div className={`upcoming-renewals-container ${className}`} data-testid={testId}>
        <div className="section-header">
          <h2 className="section-title">Upcoming Renewals</h2>
          <p className="section-subtitle">Loading...</p>
        </div>
        <div className="renewals-list">
          {Array.from({ length: 3 }).map((_, index) => (
            <div key={index} className="renewal-item loading">
              <div className="animate-pulse bg-gray-200 h-4 w-32 rounded mb-2"></div>
              <div className="animate-pulse bg-gray-200 h-3 w-24 rounded"></div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  if (upcomingRenewals.length === 0) {
    return (
      <div className={`upcoming-renewals-container ${className}`} data-testid={testId}>
        <div className="section-header">
          <h2 className="section-title">Upcoming Renewals</h2>
        </div>
        <div className="empty-state">
          <div className="empty-icon">✅</div>
          <h3>No upcoming renewals</h3>
          <p>All renewals are up to date for the next {defaultDaysThreshold} days.</p>
        </div>
      </div>
    )
  }

  return (
    <div className={`upcoming-renewals-container ${className}`} data-testid={testId}>
      <div className="section-header">
        <h2 className="section-title">Upcoming Renewals</h2>
        <p className="section-subtitle">
          {upcomingRenewals.length} renewal{upcomingRenewals.length !== 1 ? 's' : ''} in the next {defaultDaysThreshold} days
        </p>
      </div>

      <div className="renewals-list">
        {upcomingRenewals.map((renewal) => {
          const daysUntilDue = getDaysUntilDue(new Date(renewal.due_date!))
          const urgencyLevel = getUrgencyLevel(daysUntilDue)

          return (
            <div
              key={renewal.id}
              className={`renewal-item urgency-${urgencyLevel}`}
              onClick={() => handleRenewalClick(renewal)}
              role="button"
              tabIndex={0}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault()
                  handleRenewalClick(renewal)
                }
              }}
            >
              <div className="renewal-info">
                <h4 className="renewal-title">
                  {renewal.vendor} - {renewal.product_name}
                </h4>
                <p className="renewal-description">
                  {renewal.description || 'No description available'}
                </p>
              </div>
              <div className="renewal-meta">
                <span className="renewal-cost">
                  {renewal.cost ? `$${renewal.cost.toLocaleString()}` : 'N/A'}
                </span>
                <span className={`renewal-urgency urgency-${urgencyLevel}`}>
                  {formatDaysUntilDue(daysUntilDue)}
                </span>
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
})

// Helper function to calculate days until due
function getDaysUntilDue(dueDate: Date): number {
  const now = new Date()
  const diffTime = dueDate.getTime() - now.getTime()
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
}

// Helper function to determine urgency level
function getUrgencyLevel(daysUntilDue: number): 'critical' | 'warning' | 'normal' | 'overdue' {
  if (daysUntilDue < 0) return 'overdue'
  if (daysUntilDue <= 7) return 'critical'
  if (daysUntilDue <= 14) return 'warning'
  return 'normal'
}

// Helper function to format days until due
function formatDaysUntilDue(daysUntilDue: number): string {
  if (daysUntilDue < 0) {
    const overdueDays = Math.abs(daysUntilDue)
    return `${overdueDays} day${overdueDays !== 1 ? 's' : ''} overdue`
  }
  if (daysUntilDue === 0) return 'Due today'
  if (daysUntilDue === 1) return 'Due tomorrow'
  return `Due in ${daysUntilDue} days`
}

// Helper function to filter renewals by days threshold
function getUpcomingRenewals(renewals: Renewal[], daysThreshold: number): Renewal[] {
  const now = new Date()
  return renewals.filter(renewal => {
    if (!renewal.due_date) return false
    const dueDate = new Date(renewal.due_date)
    const daysUntilDue = getDaysUntilDue(dueDate)
    return daysUntilDue >= -7 && daysUntilDue <= daysThreshold // Include overdue up to 7 days
  }).sort((a, b) => {
    // Sort by due date, earliest first
    if (!a.due_date || !b.due_date) return 0
    return new Date(a.due_date).getTime() - new Date(b.due_date).getTime()
  })
}

export default UpcomingRenewals

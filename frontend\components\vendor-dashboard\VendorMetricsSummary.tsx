/**
 * Vendor Metrics Summary Component
 * 
 * Displays key vendor metrics and KPIs in a card layout
 */

'use client'

import React, { useMemo } from 'react'
import { BaseComponentProps } from '@/lib/types'

interface VendorMetrics {
  totalVendors: number
  totalSpend: number
  avgSpendPerVendor: number
  topVendorSpend: number
  topVendorName: string
  vendorConcentration: number // Percentage of spend from top vendor
  avgReliabilityScore: number
  vendorsWithUpcomingRenewals: number
  currency?: string
}

interface VendorMetricsSummaryProps extends BaseComponentProps {
  metrics: VendorMetrics
  isLoading?: boolean
  currency?: string
}

export default function VendorMetricsSummary({
  metrics,
  isLoading = false,
  currency = 'USD',
  className = '',
  'data-testid': testId
}: VendorMetricsSummaryProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`
  }

  const getConcentrationRisk = (concentration: number) => {
    if (concentration >= 50) return { level: 'High', color: 'text-red-600', bg: 'bg-red-50', icon: '⚠️' }
    if (concentration >= 30) return { level: 'Medium', color: 'text-yellow-600', bg: 'bg-yellow-50', icon: '⚡' }
    return { level: 'Low', color: 'text-green-600', bg: 'bg-green-50', icon: '✅' }
  }

  const getReliabilityStatus = (score: number) => {
    if (score >= 90) return { status: 'Excellent', color: 'text-green-600', bg: 'bg-green-50', icon: '🌟' }
    if (score >= 75) return { status: 'Good', color: 'text-blue-600', bg: 'bg-blue-50', icon: '👍' }
    if (score >= 60) return { status: 'Fair', color: 'text-yellow-600', bg: 'bg-yellow-50', icon: '⚠️' }
    return { status: 'Poor', color: 'text-red-600', bg: 'bg-red-50', icon: '❌' }
  }

  const concentrationRisk = getConcentrationRisk(metrics.vendorConcentration)
  const reliabilityStatus = getReliabilityStatus(metrics.avgReliabilityScore)

  if (isLoading) {
    return (
      <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 ${className}`} data-testid={testId}>
        {[...Array(4)].map((_, index) => (
          <div key={index} className="bg-white border rounded-lg p-6 animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
            <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-full"></div>
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 ${className}`} data-testid={testId}>
      {/* Total Vendors */}
      <div className="bg-white border rounded-lg p-6">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-sm font-medium text-gray-500">Total Vendors</h3>
          <span className="text-2xl">🏢</span>
        </div>
        <div className="text-2xl font-bold text-gray-900 mb-1">
          {metrics.totalVendors.toLocaleString()}
        </div>
        <div className="text-sm text-gray-600">
          Active vendor relationships
        </div>
      </div>

      {/* Total Spend */}
      <div className="bg-white border rounded-lg p-6">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-sm font-medium text-gray-500">Total Spend</h3>
          <span className="text-2xl">💰</span>
        </div>
        <div className="text-2xl font-bold text-gray-900 mb-1">
          {formatCurrency(metrics.totalSpend)}
        </div>
        <div className="text-sm text-gray-600">
          Across all vendors
        </div>
      </div>

      {/* Average Spend per Vendor */}
      <div className="bg-white border rounded-lg p-6">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-sm font-medium text-gray-500">Avg per Vendor</h3>
          <span className="text-2xl">📊</span>
        </div>
        <div className="text-2xl font-bold text-gray-900 mb-1">
          {formatCurrency(metrics.avgSpendPerVendor)}
        </div>
        <div className="text-sm text-gray-600">
          Average vendor spend
        </div>
      </div>

      {/* Top Vendor */}
      <div className="bg-white border rounded-lg p-6">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-sm font-medium text-gray-500">Top Vendor</h3>
          <span className="text-2xl">🏆</span>
        </div>
        <div className="text-lg font-bold text-gray-900 mb-1 truncate" title={metrics.topVendorName}>
          {metrics.topVendorName}
        </div>
        <div className="text-sm text-gray-600">
          {formatCurrency(metrics.topVendorSpend)}
        </div>
      </div>

      {/* Vendor Concentration Risk */}
      <div className="bg-white border rounded-lg p-6">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-sm font-medium text-gray-500">Concentration Risk</h3>
          <span className="text-2xl">{concentrationRisk.icon}</span>
        </div>
        <div className="text-2xl font-bold text-gray-900 mb-1">
          {formatPercentage(metrics.vendorConcentration)}
        </div>
        <div className={`text-sm font-medium ${concentrationRisk.color}`}>
          {concentrationRisk.level} Risk
        </div>
      </div>

      {/* Average Reliability */}
      <div className="bg-white border rounded-lg p-6">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-sm font-medium text-gray-500">Avg Reliability</h3>
          <span className="text-2xl">{reliabilityStatus.icon}</span>
        </div>
        <div className="text-2xl font-bold text-gray-900 mb-1">
          {formatPercentage(metrics.avgReliabilityScore)}
        </div>
        <div className={`text-sm font-medium ${reliabilityStatus.color}`}>
          {reliabilityStatus.status}
        </div>
      </div>

      {/* Upcoming Renewals */}
      <div className="bg-white border rounded-lg p-6">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-sm font-medium text-gray-500">Upcoming Renewals</h3>
          <span className="text-2xl">📅</span>
        </div>
        <div className="text-2xl font-bold text-gray-900 mb-1">
          {metrics.vendorsWithUpcomingRenewals}
        </div>
        <div className="text-sm text-gray-600">
          Vendors with renewals due
        </div>
      </div>

      {/* Vendor Diversity */}
      <div className="bg-white border rounded-lg p-6">
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-sm font-medium text-gray-500">Vendor Diversity</h3>
          <span className="text-2xl">🌐</span>
        </div>
        <div className="text-2xl font-bold text-gray-900 mb-1">
          {formatPercentage(100 - metrics.vendorConcentration)}
        </div>
        <div className="text-sm text-gray-600">
          Spend distribution
        </div>
      </div>
    </div>
  )
}

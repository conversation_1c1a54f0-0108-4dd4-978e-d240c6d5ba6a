/**
 * Individual Filter Preset API Endpoint
 *
 * Manages individual filter presets using unified services
 * GET /api/filter-presets/[id] - Get specific preset
 * PUT /api/filter-presets/[id] - Update preset
 * DELETE /api/filter-presets/[id] - Delete preset
 */

import { createApiRoute } from '@/lib/api/route-factory';
import { executeTenantQuery, executeTenantQuerySingle } from '@/lib/tenant/database';
import { z } from 'zod';

interface FilterPreset {
  id: number;
  name: string;
  filter_config: string | object;
  user_email: string;
  created_at: string;
  updated_at: string;
}

// Validation schemas
const updatePresetSchema = z.object({
  name: z.string().min(1).max(255).optional(),
  description: z.string().optional(),
  filter_config: z.any().optional(),
  is_public: z.boolean().optional()
});
// GET /api/filter-presets/[id] - Get specific preset
export const GET = createApiRoute('GET', {
  requireAuth: true,
  requireTenant: true,
  handler: async (context) => {
    const presetId = parseInt(context.request.nextUrl.pathname.split('/').pop() || '');

    if (isNaN(presetId)) {
      throw new Error('Invalid preset ID');
    }

    const result = await executeTenantQuerySingle<FilterPreset>(
      `SELECT preset_id as id, name, description, filter_config,
             created_by as user_email, created_on as created_at, updated_on as updated_at
       FROM tenant_filter_presets
       WHERE preset_id = $1 AND (is_public = true OR created_by = $2)`,
      [presetId, context.session!.email],
      context.tenant!.tenant
    );

    if (!result.success || !result.data) {
      throw new Error('Filter preset not found');
    }

    return {
      ...result.data,
      filter_config: typeof result.data.filter_config === 'string'
        ? JSON.parse(result.data.filter_config)
        : result.data.filter_config
    };
  }
});

// PUT /api/filter-presets/[id] - Update preset
export const PUT = createApiRoute('PUT', {
  requireAuth: true,
  requireTenant: true,
  bodySchema: updatePresetSchema,
  handler: async (context) => {
    const presetId = parseInt(context.request.nextUrl.pathname.split('/').pop() || '');

    if (isNaN(presetId)) {
      throw new Error('Invalid preset ID');
    }

    // Check ownership
    const ownershipCheck = await executeTenantQuerySingle<{created_by: string}>(
      `SELECT created_by FROM tenant_filter_presets WHERE preset_id = $1`,
      [presetId],
      context.tenant!.tenant
    );

    if (!ownershipCheck.success || !ownershipCheck.data) {
      throw new Error('Filter preset not found');
    }

    if (ownershipCheck.data.created_by !== context.session!.email) {
      throw new Error('Access denied - you can only update your own presets');
    }

    // Update the preset
    const updateData: any = { ...context.body! };
    if (updateData.filter_config && typeof updateData.filter_config === 'object') {
      updateData.filter_config = JSON.stringify(updateData.filter_config);
    }
    updateData.updated_on = new Date().toISOString();

    // Build update query manually since we're removing the wrapper
    const columns = Object.keys(updateData);
    const values = Object.values(updateData);
    const setClause = columns.map((col, index) => `${col} = $${index + 1}`).join(', ');

    const query = `
      UPDATE "${context.tenant!.tenant.tenantSchema}".tenant_filter_presets
      SET ${setClause}
      WHERE preset_id = $${values.length + 1}
      RETURNING preset_id, name, description, updated_on
    `;

    const result = await executeTenantQuerySingle<FilterPreset>(
      query,
      [...values, presetId],
      context.tenant!.tenant
    );

    if (!result.success || !result.data) {
      throw new Error('Failed to update filter preset');
    }

    return result.data;
  }
});

// DELETE /api/filter-presets/[id] - Delete preset
export const DELETE = createApiRoute('DELETE', {
  requireAuth: true,
  requireTenant: true,
  handler: async (context) => {
    const presetId = parseInt(context.request.nextUrl.pathname.split('/').pop() || '');

    if (isNaN(presetId)) {
      throw new Error('Invalid preset ID');
    }

    const query = `
      DELETE FROM "${context.tenant!.tenant.tenantSchema}".tenant_filter_presets
      WHERE preset_id = $1 AND created_by = $2
      RETURNING preset_id, name
    `;

    const result = await executeTenantQuerySingle<FilterPreset>(
      query,
      [presetId, context.session!.email],
      context.tenant!.tenant
    );

    if (!result.success || !result.data) {
      throw new Error('Filter preset not found or access denied');
    }

    return { message: 'Filter preset deleted successfully', preset: result.data };
  }
});

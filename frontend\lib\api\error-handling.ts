/**
 * API Error Handling Middleware
 * 
 * Provides consistent error handling for API routes
 */

import { NextRequest } from 'next/server'
import { createErrorResponse, ApiErrorCode, HttpStatus } from './response'

export interface ErrorContext {
  request: NextRequest
  endpoint: string
  method: string
  timestamp: Date
  userAgent?: string
  ip?: string
}

/**
 * Higher-order function that wraps API handlers with error handling
 */
export function withErrorHandling(
  handler: (request: NextRequest) => Promise<Response>
) {
  return async (request: NextRequest): Promise<Response> => {
    const context: ErrorContext = {
      request,
      endpoint: request.url,
      method: request.method,
      timestamp: new Date(),
      userAgent: request.headers.get('user-agent') || undefined,
      ip: request.headers.get('x-forwarded-for') || 
          request.headers.get('x-real-ip') || 
          undefined
    }

    try {
      return await handler(request)
    } catch (error) {
      return handleApiError(error, context)
    }
  }
}

/**
 * Handle API errors with consistent formatting and logging
 */
function handleApiError(error: unknown, context: ErrorContext): Response {
  console.error('[API ERROR]', {
    error: error instanceof Error ? error.message : String(error),
    stack: error instanceof Error ? error.stack : undefined,
    context: {
      endpoint: context.endpoint,
      method: context.method,
      timestamp: context.timestamp.toISOString(),
      userAgent: context.userAgent,
      ip: context.ip
    }
  })

  // Handle specific error types
  if (error instanceof ValidationError) {
    return createErrorResponse(
      error.message,
      ApiErrorCode.VALIDATION_ERROR,
      HttpStatus.BAD_REQUEST,
      error.details
    )
  }

  if (error instanceof AuthenticationError) {
    return createErrorResponse(
      error.message,
      ApiErrorCode.UNAUTHORIZED,
      HttpStatus.UNAUTHORIZED
    )
  }

  if (error instanceof AuthorizationError) {
    return createErrorResponse(
      error.message,
      ApiErrorCode.FORBIDDEN,
      HttpStatus.FORBIDDEN
    )
  }

  if (error instanceof NotFoundError) {
    return createErrorResponse(
      error.message,
      ApiErrorCode.NOT_FOUND,
      HttpStatus.NOT_FOUND
    )
  }

  if (error instanceof ConflictError) {
    return createErrorResponse(
      error.message,
      ApiErrorCode.CONFLICT,
      HttpStatus.CONFLICT
    )
  }

  if (error instanceof RateLimitError) {
    return createErrorResponse(
      error.message,
      ApiErrorCode.RATE_LIMIT_EXCEEDED,
      HttpStatus.TOO_MANY_REQUESTS
    )
  }

  // Handle database errors
  if (error instanceof Error && error.message.includes('database')) {
    return createErrorResponse(
      'Database operation failed',
      ApiErrorCode.DATABASE_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    )
  }

  // Handle network errors
  if (error instanceof Error && (
    error.message.includes('network') ||
    error.message.includes('timeout') ||
    error.message.includes('ECONNREFUSED')
  )) {
    return createErrorResponse(
      'Network error occurred',
      ApiErrorCode.NETWORK_ERROR,
      HttpStatus.SERVICE_UNAVAILABLE
    )
  }

  // Default internal server error
  return createErrorResponse(
    'An unexpected error occurred',
    ApiErrorCode.INTERNAL_ERROR,
    HttpStatus.INTERNAL_SERVER_ERROR
  )
}

// Custom error classes
export class ValidationError extends Error {
  constructor(message: string, public details?: any) {
    super(message)
    this.name = 'ValidationError'
  }
}

export class AuthenticationError extends Error {
  constructor(message: string = 'Authentication required') {
    super(message)
    this.name = 'AuthenticationError'
  }
}

export class AuthorizationError extends Error {
  constructor(message: string = 'Insufficient permissions') {
    super(message)
    this.name = 'AuthorizationError'
  }
}

export class NotFoundError extends Error {
  constructor(message: string = 'Resource not found') {
    super(message)
    this.name = 'NotFoundError'
  }
}

export class ConflictError extends Error {
  constructor(message: string = 'Resource conflict') {
    super(message)
    this.name = 'ConflictError'
  }
}

export class RateLimitError extends Error {
  constructor(message: string = 'Rate limit exceeded') {
    super(message)
    this.name = 'RateLimitError'
  }
}

/**
 * Utility to throw validation errors
 */
export function throwValidationError(message: string, details?: any): never {
  throw new ValidationError(message, details)
}

/**
 * Utility to throw authentication errors
 */
export function throwAuthError(message?: string): never {
  throw new AuthenticationError(message)
}

/**
 * Utility to throw authorization errors
 */
export function throwAuthzError(message?: string): never {
  throw new AuthorizationError(message)
}

/**
 * Utility to throw not found errors
 */
export function throwNotFoundError(message?: string): never {
  throw new NotFoundError(message)
}

/**
 * License Validator Component
 * 
 * Provides a user-friendly license validation experience with proper
 * loading states and error handling. This replaces middleware-based
 * validation with a better UX approach.
 */

'use client'

import React from 'react'
import { useLicenseValidation } from '@/lib/hooks/useLicenseValidation'

interface LicenseValidatorProps {
  children: React.ReactNode
  required?: boolean
  exemptForAdmins?: boolean
  requiredFeatures?: string[]
  fallback?: React.ReactNode
}

export function LicenseValidator({
  children,
  required = true,
  exemptForAdmins = true,
  requiredFeatures = [],
  fallback
}: LicenseValidatorProps) {
  const { isValid, isLoading, error } = useLicenseValidation({
    required,
    redirectOnFailure: false, // Handle in component instead
    exemptForAdmins,
    requiredFeatures
  })

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-lg font-semibold text-gray-900 mb-2">Validating License</h2>
          <p className="text-gray-600">Please wait while we verify your access...</p>
        </div>
      </div>
    )
  }

  if (!isValid && required) {
    if (fallback) {
      return <>{fallback}</>
    }

    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
          <div className="text-red-500 text-6xl mb-4">🔒</div>
          <h1 className="text-2xl font-bold text-gray-900 mb-4">License Required</h1>
          <p className="text-gray-600 mb-6">
            {error || 'A valid license is required to access this feature. Please contact your administrator to activate your license.'}
          </p>
          
          <div className="space-y-3">
            <button
              onClick={() => window.location.href = '/license-error'}
              className="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Learn More
            </button>
            
            <button
              onClick={() => window.location.reload()}
              className="w-full bg-gray-200 text-gray-800 px-4 py-2 rounded-lg hover:bg-gray-300 transition-colors"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    )
  }

  return <>{children}</>
}

/**
 * License Status Badge Component
 * Shows current license status in the UI
 */
export function LicenseStatusBadge() {
  const { isValid, isLoading, error, licenseInfo } = useLicenseValidation({
    required: false,
    redirectOnFailure: false
  })

  if (isLoading) {
    return (
      <div className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-600">
        <div className="animate-spin rounded-full h-3 w-3 border border-gray-400 border-t-transparent mr-1"></div>
        Checking...
      </div>
    )
  }

  if (!isValid) {
    return (
      <div className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-red-100 text-red-800">
        <span className="w-2 h-2 bg-red-500 rounded-full mr-1"></span>
        License Issue
      </div>
    )
  }

  return (
    <div className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800">
      <span className="w-2 h-2 bg-green-500 rounded-full mr-1"></span>
      {licenseInfo?.clientName || 'Licensed'}
    </div>
  )
}

/**
 * Feature Gate Component
 * Conditionally renders content based on license features
 */
interface FeatureGateProps {
  children: React.ReactNode
  requiredFeatures: string[]
  fallback?: React.ReactNode
}

export function FeatureGate({ children, requiredFeatures, fallback }: FeatureGateProps) {
  const { isValid, isLoading, licenseInfo } = useLicenseValidation({
    required: false,
    redirectOnFailure: false,
    requiredFeatures
  })

  if (isLoading) {
    return (
      <div className="animate-pulse bg-gray-200 rounded h-8 w-full"></div>
    )
  }

  if (!isValid || !licenseInfo) {
    return fallback ? <>{fallback}</> : null
  }

  // Check if all required features are available
  const hasAllFeatures = requiredFeatures.every(feature =>
    licenseInfo.features?.includes(feature)
  )

  if (!hasAllFeatures) {
    return fallback ? <>{fallback}</> : null
  }

  return <>{children}</>
}

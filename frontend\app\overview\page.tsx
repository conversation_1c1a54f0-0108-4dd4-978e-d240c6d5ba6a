/**
 * Overview Page - Complete Implementation
 *
 * Main dashboard page showing overview statistics and recent activity
 */

'use client'

import { useState, useCallback } from 'react'
import { useOverviewData } from '@/lib/hooks'
import OverviewStats from '@/components/overview/OverviewStats'
import RecentRenewals from '@/components/overview/RecentRenewals'
import UpcomingRenewals from '@/components/overview/UpcomingRenewals'
import { PageHeader } from '@/components/ui'
import { usePageInfoByName } from '@/lib/hooks/usePageInfo'
import PageAccessGuard from '@/components/auth/PageAccessGuard'
import AddRenewalModal, { RenewalFormData, AlertFormData } from '@/components/modals/AddRenewalModal'

export default function OverviewPage() {
  const { data, isLoading, error, refetch } = useOverviewData()
  const [isAddRenewalModalOpen, setIsAddRenewalModalOpen] = useState(false)

  // Get page info for the header
  const { pageInfo } = usePageInfoByName('overview')

  // Provide default data structure to prevent undefined errors
  const safeData = data || {
    stats: {
      totalRenewals: 0,
      renewalsDue: 0,
      vendors: 0,
      annualSpend: '$0'
    },
    recentRenewals: [],
    upcomingRenewals: []
  }

  // Handle search functionality
  const handleSearch = useCallback((query: string) => {
    // Note: Search functionality will be implemented when search API is available
  }, [])

  // Handle add renewal modal
  const handleAddRenewal = useCallback(() => {
    setIsAddRenewalModalOpen(true)
  }, [])

  const handleCloseAddRenewalModal = useCallback(() => {
    setIsAddRenewalModalOpen(false)
  }, [])

  const handleSubmitRenewal = useCallback(async (renewalData: RenewalFormData, alertData: AlertFormData[]) => {
    try {
      console.log('Submitting renewal:', renewalData, alertData)
      // Note: Actual submission will be implemented when renewal creation API is ready
      setIsAddRenewalModalOpen(false)
      // Refetch data after successful submission
      refetch()
    } catch (error) {
      console.error('Error submitting renewal:', error)
      // Error handling will show user-friendly message via toast system
    }
  }, [refetch])

  return (
    <PageAccessGuard pageName="overview">
      <div className="overview-container">
        {/* Page Header */}
        <PageHeader
          title={pageInfo?.header || 'Overview'}
          subtitle={pageInfo?.description || 'Dashboard overview of your renewals'}
          searchPlaceholder="Search renewals..."
          onSearchChange={handleSearch}
          actions={[
            {
              label: 'Add Renewal',
              onClick: handleAddRenewal,
              variant: 'primary',
              leftIcon: '+'
            },
            {
              label: 'Refresh',
              onClick: refetch,
              variant: 'outline',
              isLoading: isLoading,
              leftIcon: '🔄'
            }
          ]}
        />

        {/* Error State */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Unable to load overview data</h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>Please try refreshing the page or contact support if the problem persists.</p>
                </div>
                <div className="mt-4">
                  <button
                    onClick={() => refetch()}
                    className="bg-red-100 px-2 py-1 rounded text-sm text-red-800 hover:bg-red-200"
                  >
                    Try again
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Main Content */}
        <div className="overview-content">
          {/* Stats Grid */}
          <OverviewStats
            stats={safeData.stats}
            isLoading={isLoading}
            className="mb-8"
          />

          {/* Content Sections - Stacked vertically with Recent Renewals below Upcoming */}
          <div className="space-y-8">
            {/* Upcoming Renewals - First */}
            <div className="overview-section">
              <UpcomingRenewals
                renewals={safeData.upcomingRenewals}
                isLoading={isLoading}
                onRenewalClick={(renewal) => {
                  window.location.href = `/renewals/${renewal.id}`
                }}
              />
            </div>

            {/* Recent Renewals - Second (below Upcoming) */}
            <div className="overview-section">
              <RecentRenewals
                renewals={safeData.recentRenewals}
                isLoading={isLoading}
                onRenewalClick={(renewal) => {
                  window.location.href = `/renewals/${renewal.id}`
                }}
              />
            </div>
          </div>
        </div>

        {/* Add Renewal Modal */}
        <AddRenewalModal
          isOpen={isAddRenewalModalOpen}
          onClose={handleCloseAddRenewalModal}
          onSubmit={handleSubmitRenewal}
        />
      </div>
    </PageAccessGuard>
  )
}

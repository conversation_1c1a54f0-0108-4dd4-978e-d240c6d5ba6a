/**
 * Overview Page - Simplified for Testing
 */

'use client'

import { useState } from 'react'

export default function OverviewPage() {
  const [isLoading] = useState(false)

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading overview...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Overview</h1>
      <p className="text-gray-600">This is a simplified overview page for testing.</p>
      <div className="mt-4">
        <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
          <h2 className="text-lg font-semibold text-blue-800">Test Status</h2>
          <p className="text-blue-700">If you can see this page, the basic React rendering is working correctly.</p>
        </div>
      </div>
    </div>
  )
}

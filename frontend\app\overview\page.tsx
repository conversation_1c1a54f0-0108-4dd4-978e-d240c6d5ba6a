/**
 * Overview Page - Complete Implementation
 *
 * Main dashboard page showing overview statistics and recent activity
 */

'use client'

import { useState, useCallback } from 'react'
import { useOverviewData } from '@/lib/hooks/useOverviewData'
import { usePageInfoByName } from '@/lib/hooks/usePageInfo'
import OverviewStats from '@/components/overview/OverviewStats'
import RecentRenewals from '@/components/overview/RecentRenewals'
import UpcomingRenewals from '@/components/overview/UpcomingRenewals'
import { PageHeader } from '@/components/ui'
import AddRenewalModal, { RenewalFormData, AlertFormData } from '@/components/modals/AddRenewalModal'

export default function OverviewPage() {
  const { data, isLoading, error, refetch } = useOverviewData()
  const [isAddRenewalModalOpen, setIsAddRenewalModalOpen] = useState(false)

  // Provide default data structure to prevent undefined errors
  const safeData = data || {
    stats: {
      totalRenewals: 0,
      renewalsDue: 0,
      vendors: 0,
      annualSpend: '$0'
    },
    recentRenewals: [],
    upcomingRenewals: []
  }

  // Handle search functionality
  const handleSearch = useCallback((query: string) => {
    // Note: Search functionality will be implemented when search API is available
    console.log('Search query:', query)
  }, [])

  // Handle add renewal modal
  const handleAddRenewal = useCallback(() => {
    setIsAddRenewalModalOpen(true)
  }, [])

  const handleCloseAddRenewalModal = useCallback(() => {
    setIsAddRenewalModalOpen(false)
  }, [])

  const handleRenewalSubmit = useCallback(async (renewalData: RenewalFormData, alertData: AlertFormData[]) => {
    try {
      // Note: This will be implemented when the API is available
      console.log('Renewal data:', renewalData)
      console.log('Alert data:', alertData)

      // Close modal and refresh data
      setIsAddRenewalModalOpen(false)
      refetch()
    } catch (error) {
      console.error('Error creating renewal:', error)
    }
  }, [refetch])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading overview...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Unable to load overview</h2>
          <p className="text-gray-600 mb-4">There was an error loading the overview data.</p>
          <button
            onClick={() => refetch()}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <PageHeader
        title="Overview"
        subtitle="Dashboard overview of your renewals and activity"
        searchPlaceholder="Search renewals..."
        onSearchChange={handleSearch}
        actions={[
          {
            label: 'Add Renewal',
            onClick: handleAddRenewal,
            variant: 'primary',
            leftIcon: '+'
          },
          {
            label: 'Refresh',
            onClick: refetch,
            variant: 'outline',
            isLoading: isLoading,
            leftIcon: '🔄'
          }
        ]}
      />

      {/* Overview Stats */}
      <OverviewStats stats={safeData.stats} isLoading={isLoading} />

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Renewals */}
        <RecentRenewals
          renewals={safeData.recentRenewals}
          isLoading={isLoading}
        />

        {/* Upcoming Renewals */}
        <UpcomingRenewals
          renewals={safeData.upcomingRenewals}
          isLoading={isLoading}
        />
      </div>

      {/* Add Renewal Modal */}
      <AddRenewalModal
        isOpen={isAddRenewalModalOpen}
        onClose={handleCloseAddRenewalModal}
        onSubmit={handleRenewalSubmit}
      />
    </div>
  )
}

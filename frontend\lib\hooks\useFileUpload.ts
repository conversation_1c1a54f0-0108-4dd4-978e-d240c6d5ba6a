/**
 * File Upload Hook
 * 
 * Consolidates file upload patterns including drag and drop functionality.
 * Provides consistent file handling with validation and progress tracking.
 */

import { useState, useCallback, useRef, useEffect } from 'react';

export interface FileUploadConfig {
  // Accepted file types (MIME types or extensions)
  acceptedTypes?: string[];
  
  // Maximum file size in bytes
  maxFileSize?: number;
  
  // Maximum number of files
  maxFiles?: number;
  
  // Allow multiple files
  multiple?: boolean;
  
  // Auto-upload on file selection
  autoUpload?: boolean;
  
  // Upload function
  uploadFunction?: (files: File[]) => Promise<any>;
  
  // Validation function
  validateFile?: (file: File) => string | null;
  
  // Callbacks
  onFileSelect?: (files: File[]) => void;
  onFileRemove?: (file: File) => void;
  onUploadStart?: (files: File[]) => void;
  onUploadProgress?: (progress: number) => void;
  onUploadComplete?: (result: any) => void;
  onUploadError?: (error: Error) => void;
  onValidationError?: (errors: string[]) => void;
}

export interface FileUploadState {
  files: File[];
  isUploading: boolean;
  uploadProgress: number;
  errors: string[];
  isDragOver: boolean;
  uploadResult: any;
}

export interface FileUploadActions {
  selectFiles: (files: FileList | File[]) => void;
  removeFile: (file: File) => void;
  clearFiles: () => void;
  upload: () => Promise<void>;
  reset: () => void;
  setDragOver: (isDragOver: boolean) => void;
}

export interface UseFileUploadReturn {
  state: FileUploadState;
  actions: FileUploadActions;
  
  // Convenience getters
  files: File[];
  isUploading: boolean;
  hasFiles: boolean;
  canUpload: boolean;
  
  // Drag and drop handlers
  getDragHandlers: () => {
    onDragOver: (e: React.DragEvent) => void;
    onDragLeave: (e: React.DragEvent) => void;
    onDrop: (e: React.DragEvent) => void;
  };
  
  // File input props
  getFileInputProps: () => {
    type: 'file';
    multiple: boolean;
    accept: string;
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  };
}

/**
 * File Upload Hook
 */
export function useFileUpload(config: FileUploadConfig = {}): UseFileUploadReturn {
  const {
    acceptedTypes = [],
    maxFileSize = 10 * 1024 * 1024, // 10MB default
    maxFiles = 1,
    multiple = false,
    autoUpload = false,
    uploadFunction,
    validateFile,
    onFileSelect,
    onFileRemove,
    onUploadStart,
    onUploadProgress,
    onUploadComplete,
    onUploadError,
    onValidationError
  } = config;
  
  // File upload state
  const [files, setFiles] = useState<File[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [errors, setErrors] = useState<string[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [uploadResult, setUploadResult] = useState<any>(null);
  
  // File input ref
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // Validate file
  const validateFileInternal = useCallback((file: File): string | null => {
    // Custom validation first
    if (validateFile) {
      const customError = validateFile(file);
      if (customError) return customError;
    }
    
    // File size validation
    if (file.size > maxFileSize) {
      return `File size exceeds ${Math.round(maxFileSize / 1024 / 1024)}MB limit`;
    }
    
    // File type validation
    if (acceptedTypes.length > 0) {
      const isAccepted = acceptedTypes.some(type => {
        if (type.startsWith('.')) {
          return file.name.toLowerCase().endsWith(type.toLowerCase());
        }
        return file.type === type;
      });
      
      if (!isAccepted) {
        return `File type not accepted. Allowed types: ${acceptedTypes.join(', ')}`;
      }
    }
    
    return null;
  }, [validateFile, maxFileSize, acceptedTypes]);
  
  // Select files
  const selectFiles = useCallback((fileList: FileList | File[]) => {
    const newFiles = Array.from(fileList);
    const validationErrors: string[] = [];
    const validFiles: File[] = [];
    
    // Validate each file
    newFiles.forEach(file => {
      const error = validateFileInternal(file);
      if (error) {
        validationErrors.push(`${file.name}: ${error}`);
      } else {
        validFiles.push(file);
      }
    });
    
    // Check max files limit
    const totalFiles = multiple ? files.length + validFiles.length : validFiles.length;
    if (totalFiles > maxFiles) {
      validationErrors.push(`Maximum ${maxFiles} file(s) allowed`);
      return;
    }
    
    // Update state
    if (validationErrors.length > 0) {
      setErrors(validationErrors);
      if (onValidationError) {
        onValidationError(validationErrors);
      }
      return;
    }
    
    // Clear errors and update files
    setErrors([]);
    const updatedFiles = multiple ? [...files, ...validFiles] : validFiles;
    setFiles(updatedFiles);
    
    // Call onFileSelect callback
    if (onFileSelect) {
      onFileSelect(updatedFiles);
    }
    
    // Auto-upload if configured
    if (autoUpload && uploadFunction) {
      upload();
    }
  }, [files, multiple, maxFiles, validateFileInternal, onFileSelect, onValidationError, autoUpload, uploadFunction]);
  
  // Remove file
  const removeFile = useCallback((fileToRemove: File) => {
    const updatedFiles = files.filter(file => file !== fileToRemove);
    setFiles(updatedFiles);
    
    if (onFileRemove) {
      onFileRemove(fileToRemove);
    }
  }, [files, onFileRemove]);
  
  // Clear all files
  const clearFiles = useCallback(() => {
    setFiles([]);
    setErrors([]);
    setUploadResult(null);
    setUploadProgress(0);
  }, []);
  
  // Upload files
  const upload = useCallback(async () => {
    if (!uploadFunction || files.length === 0 || isUploading) return;
    
    try {
      setIsUploading(true);
      setUploadProgress(0);
      setErrors([]);
      
      if (onUploadStart) {
        onUploadStart(files);
      }
      
      // Simulate progress if no progress callback provided
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => Math.min(prev + 10, 90));
      }, 100);
      
      const result = await uploadFunction(files);
      
      clearInterval(progressInterval);
      setUploadProgress(100);
      setUploadResult(result);
      
      if (onUploadComplete) {
        onUploadComplete(result);
      }
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      setErrors([errorMessage]);
      
      if (onUploadError) {
        onUploadError(error as Error);
      }
    } finally {
      setIsUploading(false);
    }
  }, [uploadFunction, files, isUploading, onUploadStart, onUploadComplete, onUploadError]);
  
  // Reset state
  const reset = useCallback(() => {
    setFiles([]);
    setIsUploading(false);
    setUploadProgress(0);
    setErrors([]);
    setIsDragOver(false);
    setUploadResult(null);
  }, []);
  
  // Set drag over state
  const setDragOverState = useCallback((dragOver: boolean) => {
    setIsDragOver(dragOver);
  }, []);
  
  // Drag and drop handlers
  const getDragHandlers = useCallback(() => ({
    onDragOver: (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragOver(true);
    },
    onDragLeave: (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragOver(false);
    },
    onDrop: (e: React.DragEvent) => {
      e.preventDefault();
      setIsDragOver(false);
      
      const droppedFiles = e.dataTransfer.files;
      if (droppedFiles.length > 0) {
        selectFiles(droppedFiles);
      }
    }
  }), [selectFiles]);
  
  // File input props
  const getFileInputProps = useCallback(() => ({
    type: 'file' as const,
    multiple,
    accept: acceptedTypes.join(','),
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => {
      const selectedFiles = e.target.files;
      if (selectedFiles && selectedFiles.length > 0) {
        selectFiles(selectedFiles);
      }
    }
  }), [multiple, acceptedTypes, selectFiles]);
  
  return {
    state: {
      files,
      isUploading,
      uploadProgress,
      errors,
      isDragOver,
      uploadResult
    },
    actions: {
      selectFiles,
      removeFile,
      clearFiles,
      upload,
      reset,
      setDragOver: setDragOverState
    },
    
    // Convenience getters
    files,
    isUploading,
    hasFiles: files.length > 0,
    canUpload: files.length > 0 && !isUploading && !!uploadFunction,
    
    // Helpers
    getDragHandlers,
    getFileInputProps
  };
}

/**
 * Convenience hooks for common upload patterns
 */

// Simple single file upload
export function useSingleFileUpload(uploadFunction: (file: File) => Promise<any>) {
  return useFileUpload({
    maxFiles: 1,
    multiple: false,
    uploadFunction: (files) => uploadFunction(files[0])
  });
}

// CSV file upload
export function useCSVUpload(uploadFunction: (file: File) => Promise<any>) {
  return useFileUpload({
    acceptedTypes: ['.csv', 'text/csv'],
    maxFiles: 1,
    multiple: false,
    uploadFunction: (files) => uploadFunction(files[0])
  });
}

// Image upload with preview
export function useImageUpload(uploadFunction: (files: File[]) => Promise<any>) {
  return useFileUpload({
    acceptedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    maxFileSize: 5 * 1024 * 1024, // 5MB
    multiple: true,
    uploadFunction
  });
}

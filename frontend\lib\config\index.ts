/**
 * Configuration Index - Centralized Configuration Exports
 * 
 * This file provides clean exports for all configuration modules,
 * making configuration access consistent across the application.
 */

// Main application configuration
// Note: This would create a circular reference, so we import directly

// AWS-specific configuration
export * from './aws'

// Database configuration
export * from './database'

// Security configuration
export * from './security'

// Environment configuration
export * from './environment'

// Re-export commonly used configuration functions
// Note: Direct import to avoid circular reference
export { getConfig, validateConfig, publicConfig } from './config'
export { getCredentials, getParameter } from './aws'
export {
  getEnvVar,
  getEnvNumber,
  getEnvBoolean,
  getDatabaseConfig,
  getSessionConfig,
  getAWSConfig,
  getLoggingConfig,
  isDevelopment,
  isProduction,
  isTest
} from './environment'

/**
 * Cascading Select Component
 * 
 * A reusable component for vendor -> product -> version selection
 * with the ability to add new items inline
 */

'use client';

import React, { useState, useEffect, useCallback, memo, useMemo } from 'react';
import { Form } from '@/components/ui/Form';
import { Button } from '@/components/ui/Button';
import { useToast } from '@/components/ui/Toast';

export interface CascadingSelectOption {
  id: string;
  name: string;
  display_name?: string;
  version?: string; // For version objects
}

export interface CascadingSelectValue {
  vendorId: string | null;
  vendorName: string;
  productId: string | null;
  product_name: string;
  versionId: string | null;
  versionName: string;
}

export interface CascadingSelectProps {
  value: CascadingSelectValue;
  onChange: (value: CascadingSelectValue) => void;
  onAddNew?: (type: 'vendor' | 'product' | 'version', data: any) => Promise<CascadingSelectOption>;
  disabled?: boolean;
  required?: boolean;
  className?: string;
  hideVendorSelection?: boolean; // Hide vendor dropdown when vendor is selected at parent level
  preselectedVendorId?: string; // Vendor ID selected at parent level
}

export const CascadingSelect = memo(function CascadingSelect({
  value,
  onChange,
  onAddNew,
  disabled = false,
  required = false,
  className = '',
  hideVendorSelection = false,
  preselectedVendorId
}: CascadingSelectProps) {
  const [vendors, setVendors] = useState<CascadingSelectOption[]>([]);
  const [products, setProducts] = useState<CascadingSelectOption[]>([]);
  const [versions, setVersions] = useState<CascadingSelectOption[]>([]);
  
  const [loadingVendors, setLoadingVendors] = useState(false);
  const [loadingProducts, setLoadingProducts] = useState(false);
  const [loadingVersions, setLoadingVersions] = useState(false);

  // Memoized effective vendor ID to prevent unnecessary re-renders
  const effectiveVendorId = useMemo(() => {
    return hideVendorSelection ? preselectedVendorId : value.vendorId;
  }, [hideVendorSelection, preselectedVendorId, value.vendorId]);
  
  const [showAddVendor, setShowAddVendor] = useState(false);
  const [showAddProduct, setShowAddProduct] = useState(false);
  const [showAddVersion, setShowAddVersion] = useState(false);
  
  const [newVendorName, setNewVendorName] = useState('');
  const [newProductName, setNewProductName] = useState('');
  const [newVersionName, setNewVersionName] = useState('');
  
  const toast = useToast();

  // Load vendors on component mount (unless vendor is preselected)
  useEffect(() => {
    if (!hideVendorSelection) {
      loadVendors();
    }
  }, [hideVendorSelection]);

  // Load products when vendor changes or when preselected vendor is provided
  useEffect(() => {
    const vendorId = hideVendorSelection ? preselectedVendorId : value.vendorId;
    if (vendorId) {
      loadProducts(vendorId);
    } else {
      setProducts([]);
      setVersions([]);
    }
  }, [value.vendorId, hideVendorSelection, preselectedVendorId]);

  // Load versions when product changes
  useEffect(() => {
    if (value.productId) {
      loadVersions(value.productId);
    } else {
      setVersions([]);
    }
  }, [value.productId]);

  const loadVendors = useCallback(async () => {
    setLoadingVendors(true);
    try {
      const response = await fetch('/api/tenant-vendors');
      if (!response.ok) {
        throw new Error('Failed to load vendors');
      }
      const data = await response.json();
      setVendors(data.data || []);
    } catch (error) {
      console.error('Error loading vendors:', error);
      toast.error('Failed to load vendors');
    } finally {
      setLoadingVendors(false);
    }
  }, [toast]);

  const loadProducts = useCallback(async (vendorId: string) => {
    setLoadingProducts(true);
    try {
      const response = await fetch(`/api/tenant-products?vendor_id=${vendorId}`);
      if (!response.ok) {
        throw new Error('Failed to load products');
      }
      const data = await response.json();
      setProducts(data.data || []);
    } catch (error) {
      console.error('Error loading products:', error);
      toast.error('Failed to load products');
    } finally {
      setLoadingProducts(false);
    }
  }, [toast]);

  const loadVersions = useCallback(async (productId: string) => {
    setLoadingVersions(true);
    try {
      const response = await fetch(`/api/tenant-product-versions?product_id=${productId}`);
      if (!response.ok) {
        throw new Error('Failed to load versions');
      }
      const data = await response.json();
      setVersions(data.data || []);
    } catch (error) {
      console.error('Error loading versions:', error);
      toast.error('Failed to load versions');
    } finally {
      setLoadingVersions(false);
    }
  }, [toast]);

  const handleVendorChange = useCallback((vendorId: string) => {
    const vendor = Array.isArray(vendors) ? vendors.find(v => v.id === vendorId) : null;
    onChange({
      vendorId: vendorId || null,
      vendorName: vendor?.name || '',
      productId: null,
      product_name: '',
      versionId: null,
      versionName: ''
    });
  }, [vendors, onChange]);

  const handleProductChange = useCallback((productId: string) => {
    const product = Array.isArray(products) ? products.find(p => p.id === productId) : null;
    onChange({
      ...value,
      productId: productId || null,
      product_name: product?.name || '',
      versionId: null,
      versionName: ''
    });
  }, [products, value, onChange]);

  const handleVersionChange = useCallback((versionId: string) => {
    const version = Array.isArray(versions) ? versions.find(v => v.id === versionId) : null;
    onChange({
      ...value,
      versionId: versionId || null,
      versionName: version?.version || ''
    });
  }, [versions, value, onChange]);

  const handleAddVendor = useCallback(async () => {
    if (!newVendorName.trim() || !onAddNew) return;
    
    try {
      const newVendor = await onAddNew('vendor', { name: newVendorName.trim() });
      setVendors(prev => [...(Array.isArray(prev) ? prev : []), newVendor]);
      handleVendorChange(newVendor.id);
      setNewVendorName('');
      setShowAddVendor(false);
      toast.success('Vendor added successfully');
    } catch (error) {
      console.error('Error adding vendor:', error);
      toast.error('Failed to add vendor');
    }
  }, [newVendorName, onAddNew, handleVendorChange, toast]);

  const handleAddProduct = useCallback(async () => {
    if (!newProductName.trim() || !value.vendorId || !onAddNew) return;
    
    try {
      const newProduct = await onAddNew('product', { 
        name: newProductName.trim(),
        vendor_id: value.vendorId
      });
      setProducts(prev => [...(Array.isArray(prev) ? prev : []), newProduct]);
      handleProductChange(newProduct.id);
      setNewProductName('');
      setShowAddProduct(false);
      toast.success('Product added successfully');
    } catch (error) {
      console.error('Error adding product:', error);
      toast.error('Failed to add product');
    }
  }, [newProductName, value.vendorId, onAddNew, handleProductChange, toast]);

  const handleAddVersion = useCallback(async () => {
    if (!newVersionName.trim() || !value.productId || !onAddNew) return;
    
    try {
      const newVersion = await onAddNew('version', { 
        version: newVersionName.trim(),
        product_id: value.productId
      });
      setVersions(prev => [...(Array.isArray(prev) ? prev : []), { ...newVersion, name: newVersion.version || newVersion.name }]);
      handleVersionChange(newVersion.id);
      setNewVersionName('');
      setShowAddVersion(false);
      toast.success('Version added successfully');
    } catch (error) {
      console.error('Error adding version:', error);
      toast.error('Failed to add version');
    }
  }, [newVersionName, value.productId, onAddNew, handleVersionChange, toast]);

  return (
    <div className={`cascading-select ${className}`}>
      {/* Vendor Selection - Hidden when vendor is preselected */}
      {!hideVendorSelection && (
        <Form.Field>
        <Form.Label htmlFor="vendor" required={required}>
          Vendor
        </Form.Label>
        <div className="flex gap-2">
          <Form.Select
            id="vendor"
            value={value.vendorId || ''}
            onChange={(e) => handleVendorChange(e.target.value)}
            disabled={disabled || loadingVendors}
            placeholder={loadingVendors ? "Loading vendors..." : "Select vendor..."}
            className="flex-1"
          >
            {Array.isArray(vendors) && vendors.filter(vendor => vendor && vendor.id).map((vendor) => (
              <option key={vendor.id} value={vendor.id}>
                {vendor.display_name || vendor.name}
              </option>
            ))}
          </Form.Select>
          {onAddNew && (
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => setShowAddVendor(!showAddVendor)}
              disabled={disabled}
              title="Add new vendor"
            >
              +
            </Button>
          )}
        </div>
        
        {showAddVendor && (
          <div className="mt-2 p-3 border rounded-md bg-gray-50">
            <div className="flex gap-2">
              <Form.Input
                placeholder="Enter vendor name"
                value={newVendorName}
                onChange={(e) => setNewVendorName(e.target.value)}
                className="flex-1"
              />
              <Button
                type="button"
                variant="primary"
                size="sm"
                onClick={handleAddVendor}
                disabled={!newVendorName.trim()}
              >
                Add
              </Button>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => {
                  setShowAddVendor(false);
                  setNewVendorName('');
                }}
              >
                Cancel
              </Button>
            </div>
          </div>
        )}
      </Form.Field>
      )}

      {/* Product Selection */}
      <Form.Field>
        <Form.Label htmlFor="product" required={required}>
          Product
        </Form.Label>
        <div className="flex gap-2">
          <Form.Select
            id="product"
            value={value.productId || ''}
            onChange={(e) => handleProductChange(e.target.value)}
            disabled={disabled || loadingProducts || (!value.vendorId && !preselectedVendorId)}
            placeholder={
              (!value.vendorId && !preselectedVendorId)
                ? "Select vendor first"
                : loadingProducts
                  ? "Loading products..."
                  : "Select product..."
            }
            className="flex-1"
          >
            {Array.isArray(products) && products.filter(product => product && product.id).map((product) => (
              <option key={product.id} value={product.id}>
                {product.display_name || product.name}
              </option>
            ))}
          </Form.Select>
          {onAddNew && effectiveVendorId && (
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => setShowAddProduct(!showAddProduct)}
              disabled={disabled}
              title="Add new product"
            >
              +
            </Button>
          )}
        </div>
        
        {showAddProduct && (
          <div className="mt-2 p-3 border rounded-md bg-gray-50">
            <div className="flex gap-2">
              <Form.Input
                placeholder="Enter product name"
                value={newProductName}
                onChange={(e) => setNewProductName(e.target.value)}
                className="flex-1"
              />
              <Button
                type="button"
                variant="primary"
                size="sm"
                onClick={handleAddProduct}
                disabled={!newProductName.trim()}
              >
                Add
              </Button>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => {
                  setShowAddProduct(false);
                  setNewProductName('');
                }}
              >
                Cancel
              </Button>
            </div>
          </div>
        )}
      </Form.Field>

      {/* Version Selection */}
      <Form.Field>
        <Form.Label htmlFor="version" required={required}>
          Version
        </Form.Label>
        <div className="flex gap-2">
          <Form.Select
            id="version"
            value={value.versionId || ''}
            onChange={(e) => handleVersionChange(e.target.value)}
            disabled={disabled || loadingVersions || !value.productId}
            placeholder={
              !value.productId
                ? "Select product first"
                : loadingVersions
                  ? "Loading versions..."
                  : "Select version..."
            }
            className="flex-1"
          >
            {Array.isArray(versions) && versions.filter(version => version && version.id).map((version) => (
              <option key={version.id} value={version.id}>
                {version.version || version.name}
              </option>
            ))}
          </Form.Select>
          {onAddNew && value.productId && (
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => setShowAddVersion(!showAddVersion)}
              disabled={disabled}
              title="Add new version"
            >
              +
            </Button>
          )}
        </div>
        
        {showAddVersion && (
          <div className="mt-2 p-3 border rounded-md bg-gray-50">
            <div className="flex gap-2">
              <Form.Input
                placeholder="Enter version name"
                value={newVersionName}
                onChange={(e) => setNewVersionName(e.target.value)}
                className="flex-1"
              />
              <Button
                type="button"
                variant="primary"
                size="sm"
                onClick={handleAddVersion}
                disabled={!newVersionName.trim()}
              >
                Add
              </Button>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => {
                  setShowAddVersion(false);
                  setNewVersionName('');
                }}
              >
                Cancel
              </Button>
            </div>
          </div>
        )}
      </Form.Field>
    </div>
  );
})

CascadingSelect.displayName = 'CascadingSelect'

/**
 * Page Access Check API
 * 
 * Checks if the current user has access to a specific page
 */

import { NextRequest } from 'next/server'
import { createSuccessResponse, createErrorResponse, ApiErrorCode, HttpStatus } from '@/lib/api/response'
import { authenticateRequest } from '@/lib/api/auth-middleware'
import { executeQuery } from '@/lib/database'

/**
 * GET /api/admin-pages/check-access?page=<pageName>
 * Checks if the current user has access to the specified page
 */
export async function GET(request: NextRequest) {
  try {
    // Use unified authentication middleware
    const authResult = await authenticateRequest(request, {
      requireAuth: true,
      requiredGroups: [] // Allow any authenticated user, filter by groups in query
    })

    if (!authResult.success) {
      return authResult.response
    }

    const { session } = authResult
    const userGroups = session.groups || []
    
    // Get page name from query parameters
    const { searchParams } = new URL(request.url)
    const pageName = searchParams.get('page')

    if (!pageName) {
      return createErrorResponse(
        'Page name is required',
        ApiErrorCode.VALIDATION_ERROR,
        HttpStatus.BAD_REQUEST
      )
    }

    // Normalize groups to lowercase
    const normalizedGroups = userGroups
      .filter(group => typeof group === 'string')
      .map(group => group.toLowerCase().trim())

    if (normalizedGroups.length === 0) {
      return createSuccessResponse({
        hasAccess: false,
        reason: 'User has no groups assigned'
      })
    }

    // Check if user has access to the page
    const query = `
      SELECT COUNT(*) as count
      FROM metadata.pages p
      INNER JOIN metadata.page_groups pg ON p.id = pg.page_id
      WHERE p.name = $1
        AND p.status = 'A'
        AND pg.group_name = ANY($2)
    `

    const result = await executeQuery(query, [pageName, normalizedGroups])

    if (!result.success) {
      return createErrorResponse(
        'Database query failed',
        ApiErrorCode.INTERNAL_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR,
        result.error
      )
    }

    const hasAccess = result.data && result.data[0]?.count > 0

    return createSuccessResponse({
      hasAccess,
      pageName,
      userGroups: normalizedGroups,
      reason: hasAccess ? 'User has required group membership' : 'User lacks required group membership'
    })

  } catch (error) {
    return createErrorResponse(
      'Failed to check page access',
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR,
      error instanceof Error ? error.message : 'Unknown error'
    )
  }
}

/**
 * Database Service - Connection Pool Manager
 * 
 * Eliminates redundant database connections by providing a centralized
 * connection pool with proper lifecycle management and monitoring.
 */

import { Pool, PoolClient, PoolConfig } from 'pg'
import { getServerDatabaseConfig as getDatabaseConfig } from '@/lib/config/config'

class DatabaseService {
  private static instance: DatabaseService
  private pool: Pool | null = null
  private isInitialized = false
  private connectionCount = 0

  private constructor() {
    // Private constructor to enforce singleton pattern
    // Check if we're on the client side and prevent initialization
    if (typeof window !== 'undefined') {
      console.warn('⚠️ [DATABASE] Database service should not be used on the client side')
    }
  }

  /**
   * Get singleton instance
   */
  public static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService()
    }
    return DatabaseService.instance
  }

  /**
   * Initialize database connection pool
   */
  public async initialize(): Promise<void> {
    // Prevent initialization on client side
    if (typeof window !== 'undefined') {
      throw new Error('Database service cannot be initialized on the client side')
    }

    if (this.isInitialized && this.pool) {
      return
    }

    try {
      const databaseConfig = getDatabaseConfig()

      // Validate required database configuration
      if (!databaseConfig.user || !databaseConfig.password || !databaseConfig.host || !databaseConfig.name) {
        throw new Error('Missing required database configuration. Please check your environment variables.')
      }

      const poolConfig: PoolConfig = {
        user: databaseConfig.user,
        host: databaseConfig.host,
        database: databaseConfig.name,
        password: databaseConfig.password,
        port: databaseConfig.port,
        ssl: databaseConfig.ssl ? { rejectUnauthorized: false } : false,
        max: 20,
        min: 2,
        idleTimeoutMillis: 30000,
        connectionTimeoutMillis: 10000,
        // Add retry logic
        keepAlive: true,
        keepAliveInitialDelayMillis: 10000
      }

      console.log('🔍 [DATABASE] Initializing connection pool with config:', {
        host: poolConfig.host,
        database: poolConfig.database,
        user: poolConfig.user,
        port: poolConfig.port,
        ssl: !!poolConfig.ssl
      })

      this.pool = new Pool(poolConfig)

      // Set up event listeners for monitoring
      this.pool.on('connect', (client) => {
        this.connectionCount++
        console.log(`✅ [DATABASE] Client connected. Active connections: ${this.connectionCount}`)
      })

      this.pool.on('remove', (client) => {
        this.connectionCount--
        console.log(`🔌 [DATABASE] Client removed. Active connections: ${this.connectionCount}`)
      })

      this.pool.on('error', (err) => {
        console.error('❌ [DATABASE] Pool error:', err)
        // Don't reset initialization flag on pool errors to allow recovery
      })

      // Test the connection with retry logic
      let retries = 3
      let lastError: Error | null = null

      while (retries > 0) {
        try {
          const client = await this.pool.connect()
          await client.query('SELECT 1 as test')
          client.release()
          console.log('✅ [DATABASE] Connection test successful')
          break
        } catch (error) {
          lastError = error as Error
          retries--
          console.warn(`⚠️ [DATABASE] Connection test failed, retries left: ${retries}`, error)

          if (retries > 0) {
            await new Promise(resolve => setTimeout(resolve, 1000))
          }
        }
      }

      if (retries === 0 && lastError) {
        throw lastError
      }

      this.isInitialized = true
      console.log('🎉 [DATABASE] Database service initialized successfully')

    } catch (error) {
      console.error('❌ [DATABASE] Failed to initialize database pool:', error)
      this.isInitialized = false
      this.pool = null
      throw new Error(`Database initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Get the database pool instance
   */
  public getPool(): Pool {
    // Prevent usage on client side
    if (typeof window !== 'undefined') {
      throw new Error('Database pool cannot be accessed on the client side')
    }

    if (!this.pool) {
      throw new Error('Database pool not initialized. Call initialize() first.')
    }
    return this.pool
  }

  /**
   * Get a database client from the pool
   */
  public async getClient(): Promise<PoolClient> {
    // Prevent usage on client side
    if (typeof window !== 'undefined') {
      throw new Error('Database client cannot be accessed on the client side')
    }

    if (!this.pool || !this.isInitialized) {
      await this.initialize()
    }

    if (!this.pool) {
      throw new Error('Database pool not initialized')
    }

    try {
      return await this.pool.connect()
    } catch (error) {
      console.error('❌ [DATABASE] Failed to get client from pool:', error)
      // Try to reinitialize if connection fails
      if (error instanceof Error && error.message.includes('pool is ending')) {
        console.log('🔄 [DATABASE] Pool is ending, reinitializing...')
        this.isInitialized = false
        this.pool = null
        await this.initialize()
        return await this.pool!.connect()
      }
      throw error
    }
  }

  /**
   * Execute a query with automatic client management
   */
  public async query(text: string, params?: any[]): Promise<any> {
    const client = await this.getClient()
    
    try {
      const result = await client.query(text, params)
      return result
    } finally {
      client.release()
    }
  }

  /**
   * Execute a transaction with automatic rollback on error
   */
  public async transaction<T>(
    callback: (client: PoolClient) => Promise<T>
  ): Promise<T> {
    const client = await this.getClient()
    
    try {
      await client.query('BEGIN')
      const result = await callback(client)
      await client.query('COMMIT')
      return result
    } catch (error) {
      await client.query('ROLLBACK')
      throw error
    } finally {
      client.release()
    }
  }

  /**
   * Get pool statistics
   */
  public getStats() {
    if (!this.pool) {
      return {
        totalCount: 0,
        idleCount: 0,
        waitingCount: 0,
        connectionCount: this.connectionCount
      }
    }

    return {
      totalCount: this.pool.totalCount,
      idleCount: this.pool.idleCount,
      waitingCount: this.pool.waitingCount,
      connectionCount: this.connectionCount
    }
  }

  /**
   * Check if database is healthy
   */
  public async healthCheck(): Promise<{ healthy: boolean; error?: string }> {
    try {
      if (!this.pool) {
        return { healthy: false, error: 'Pool not initialized' }
      }

      const client = await this.pool.connect()
      try {
        await client.query('SELECT 1')
        return { healthy: true }
      } finally {
        client.release()
      }
    } catch (error) {
      return { 
        healthy: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      }
    }
  }

  /**
   * Gracefully close the database pool
   */
  public async close(): Promise<void> {
    if (this.pool) {
      await this.pool.end()
      this.pool = null
      this.isInitialized = false
      this.connectionCount = 0
    }
  }

  /**
   * Check if database is initialized
   */
  public isReady(): boolean {
    return this.isInitialized && this.pool !== null
  }

  /**
   * Get pool status for monitoring
   */
  public getPoolStatus() {
    return {
      isInitialized: this.isInitialized,
      hasPool: this.pool !== null,
      totalConnections: this.connectionCount,
      poolConfig: this.pool ? {
        max: this.pool.options.max,
        min: this.pool.options.min,
        idleTimeoutMillis: this.pool.options.idleTimeoutMillis,
        connectionTimeoutMillis: this.pool.options.connectionTimeoutMillis
      } : null
    }
  }
}

// Export singleton instance
export const databaseService = DatabaseService.getInstance()

// Convenience functions
export async function getDbClient(): Promise<PoolClient> {
  if (typeof window !== 'undefined') {
    throw new Error('Database client cannot be accessed on the client side')
  }
  return databaseService.getClient()
}

export async function dbQuery(text: string, params?: any[]): Promise<any> {
  if (typeof window !== 'undefined') {
    throw new Error('Database query cannot be executed on the client side')
  }
  return databaseService.query(text, params)
}

export async function dbTransaction<T>(
  callback: (client: PoolClient) => Promise<T>
): Promise<T> {
  if (typeof window !== 'undefined') {
    throw new Error('Database transaction cannot be executed on the client side')
  }
  return databaseService.transaction(callback)
}

// Export the class for testing
export { DatabaseService }

// Export convenience function for getting the pool
export function getPool(): Pool {
  if (typeof window !== 'undefined') {
    throw new Error('Database pool cannot be accessed on the client side')
  }
  return databaseService.getPool()
}



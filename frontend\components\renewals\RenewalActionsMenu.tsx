/**
 * Renewal Actions Menu Component
 * 
 * Dropdown menu with actions for individual renewals
 */

'use client'

import React, { useState, useRef, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useRenewals, Renewal } from '@/lib/hooks'
import EditRenewalModal from '@/components/modals/EditRenewalModal'
import ProcessRenewalModal from '@/components/modals/ProcessRenewalModal'

interface RenewalActionsMenuProps {
  renewal: Renewal
  onEdit?: (renewal: Renewal) => void
  onProcess?: (renewal: Renewal) => void
  onDelete?: (renewal: Renewal) => void
}

export default function RenewalActionsMenu({
  renewal,
  onEdit,
  onProcess,
  onDelete
}: RenewalActionsMenuProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [isProcessModalOpen, setIsProcessModalOpen] = useState(false)
  const menuRef = useRef<HTMLDivElement>(null)
  const router = useRouter()
  const { deleteRenewal, processRenewal } = useRenewals()

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [isOpen])

  const handleToggle = (e: React.MouseEvent) => {
    e.stopPropagation()
    setIsOpen(!isOpen)
  }

  const handleViewDetails = () => {
    setIsOpen(false)
    router.push(`/renewals/${renewal.id}`)
  }

  const handleEdit = () => {
    setIsOpen(false)
    if (onEdit) {
      onEdit(renewal)
    } else {
      // Open edit modal
      setIsEditModalOpen(true)
    }
  }

  const handleProcess = () => {
    setIsOpen(false)
    if (onProcess) {
      onProcess(renewal)
    } else {
      // Open process modal
      setIsProcessModalOpen(true)
    }
  }

  const handleDelete = async () => {
    setIsOpen(false)
    
    // Confirm deletion
    const confirmed = window.confirm(
      `Are you sure you want to delete the renewal "${renewal.name}"? This action cannot be undone.`
    )
    
    if (!confirmed) return

    if (onDelete) {
      onDelete(renewal)
    } else {
      // Default delete behavior
      const success = await deleteRenewal(renewal.id)
      if (success) {
        console.log('Renewal deleted successfully')
      } else {
        console.error('Failed to delete renewal')
      }
    }
  }

  return (
    <div className="relative" ref={menuRef}>
      <button
        onClick={handleToggle}
        className="flex items-center justify-center w-8 h-8 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-full transition-colors"
        aria-label="More actions"
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
          <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
        </svg>
      </button>

      {isOpen && (
        <div className="absolute right-0 top-full mt-1 w-56 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
          <div className="py-2">
            <button
              onClick={handleViewDetails}
              className="flex items-center w-full px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
            >
              <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center mr-3">
                <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
              </div>
              <span className="font-medium">View Details</span>
            </button>

            <button
              onClick={handleEdit}
              className="flex items-center w-full px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
            >
              <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center mr-3">
                <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
              </div>
              <span className="font-medium">Edit</span>
            </button>

            <button
              onClick={handleProcess}
              className="flex items-center w-full px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
            >
              <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center mr-3">
                <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <span className="font-medium">Process Renewal</span>
            </button>

            <div className="border-t border-gray-100 my-1"></div>

            <button
              onClick={handleDelete}
              className="flex items-center w-full px-4 py-3 text-sm text-red-600 hover:bg-red-50 transition-colors"
            >
              <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center mr-3">
                <svg className="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </div>
              <span className="font-medium">Delete</span>
            </button>
          </div>
        </div>
      )}

      {/* Edit Renewal Modal */}
      <EditRenewalModal
        isOpen={isEditModalOpen}
        renewal={renewal}
        onClose={() => setIsEditModalOpen(false)}
      />

      {/* Process Renewal Modal */}
      <ProcessRenewalModal
        isOpen={isProcessModalOpen}
        renewal={renewal}
        onClose={() => setIsProcessModalOpen(false)}
      />
    </div>
  )
}

/**
 * Advanced Filters API Endpoint
 * 
 * Provides comprehensive filtering, searching, and saved preset functionality
 * GET /api/advanced-filters?table=X&filters=Y - Apply advanced filters to data
 * POST /api/advanced-filters - Save a new filter preset
 * PUT /api/advanced-filters/[id] - Update existing filter preset
 * DELETE /api/advanced-filters/[id] - Delete filter preset
 */

import { NextRequest } from 'next/server';
import { authenticateRequest } from '@/lib/api/auth-middleware';
import { resolveTenantContext } from '@/lib/tenant/context';
import { databaseService } from '@/lib/services/database-service';
import { AdvancedFilterService, AdvancedFilter, SearchConfig, SavedFilterPreset } from '@/lib/services/advanced-filter-service';
import {
  createSuccessResponse,
  createErrorResponse,
  ApiErrorCode,
  HttpStatus
} from '@/lib/api/response';
import { z } from 'zod';

// Validation schemas
const filterConditionSchema = z.object({
  field: z.string(),
  operator: z.enum([
    'equals', 'not_equals', 'contains', 'not_contains', 'starts_with', 'ends_with',
    'greater_than', 'less_than', 'greater_than_or_equal', 'less_than_or_equal',
    'between', 'not_between', 'in', 'not_in', 'is_null', 'is_not_null',
    'date_equals', 'date_before', 'date_after', 'date_between', 
    'date_in_last_days', 'date_in_next_days'
  ]),
  value: z.any().optional(),
  values: z.array(z.any()).optional(),
  dataType: z.enum(['string', 'number', 'date', 'boolean']).optional()
});

const filterGroupSchema: z.ZodType<any> = z.lazy(() => z.object({
  conditions: z.array(filterConditionSchema),
  operator: z.enum(['AND', 'OR']),
  groups: z.array(filterGroupSchema).optional()
}));

const advancedFilterSchema = z.object({
  name: z.string(),
  description: z.string().optional(),
  table: z.string(),
  groups: z.array(filterGroupSchema),
  sortBy: z.string().optional(),
  sortDirection: z.enum(['ASC', 'DESC']).optional(),
  limit: z.number().optional(),
  offset: z.number().optional()
});

const searchConfigSchema = z.object({
  query: z.string(),
  fields: z.array(z.string()),
  fuzzy: z.boolean().optional(),
  caseSensitive: z.boolean().optional(),
  wholeWords: z.boolean().optional()
});

const savePresetSchema = z.object({
  name: z.string().min(1).max(255),
  description: z.string().optional(),
  table_name: z.string(),
  filter_config: advancedFilterSchema,
  is_public: z.boolean().default(false)
});

// Table configurations for security and field mapping
const TABLE_CONFIGS = {
  tenant_renewals: {
    allowedFields: [
      'id', 'name', 'vendor_name', 'product_name', 'product_version',
      'start_date', 'cost', 'currency', 'status', 'renewal_type',
      'department_name', 'created_on', 'changed_on'
    ],
    searchFields: ['name', 'vendor_name', 'product_name', 'description'],
    baseQuery: `
      SELECT
        r.id,
        r.name,
        COALESCE(v.name, 'Unknown') as vendor_name,
        COALESCE(p.name, 'Unknown') as product_name,
        COALESCE(pv.version, '') as product_version,
        r.start_date,
        r.cost,
        r.currency,
        r.status,
        COALESCE(rt.name, 'Unknown') as renewal_type,
        COALESCE(d.department_name, '') as department_name,
        r.description,
        r.created_on,
        r.changed_on
      FROM tenant_renewals r
      LEFT JOIN tenant_vendors v ON r.vendor_id = v.id
      LEFT JOIN tenant_products p ON r.product_id = p.id
      LEFT JOIN tenant_product_versions pv ON r.product_version_id = pv.id
      LEFT JOIN metadata.global_renewal_types rt ON r.renewal_type_id = rt.id
      LEFT JOIN tenant_departments d ON r.department_id = d.id
      WHERE r.is_deleted = false
    `
  },
  tenant_vendors: {
    allowedFields: [
      'id', 'name', 'display_name', 'contact_email', 'phone', 'website',
      'city', 'state', 'country', 'created_on', 'changed_on'
    ],
    searchFields: ['name', 'display_name', 'contact_email', 'website'],
    baseQuery: `
      SELECT
        id, name, display_name, contact_email, phone, website,
        city, state, country, created_on, changed_on
      FROM tenant_vendors
      WHERE is_deleted = false
    `
  },
  tenant_products: {
    allowedFields: [
      'id', 'name', 'description', 'category', 'sku', 'vendor_name',
      'created_on', 'changed_on'
    ],
    searchFields: ['name', 'description', 'category', 'sku'],
    baseQuery: `
      SELECT
        p.id, p.name, p.description, p.category, p.sku,
        COALESCE(v.name, 'Unknown') as vendor_name,
        p.created_on, p.changed_on
      FROM tenant_products p
      LEFT JOIN tenant_vendors v ON p.vendor_id = v.id
    `
  }
} as const;

// GET /api/advanced-filters - Apply filters and return filtered data
export async function GET(request: NextRequest) {
  try {
    // Authentication
    const authResult = await authenticateRequest(request);
    if (!authResult.success) {
      return authResult.response;
    }

    // Resolve tenant context
    const tenantResult = await resolveTenantContext(authResult.session);
    if (!tenantResult.success) {
      return tenantResult.response || createErrorResponse(
        tenantResult.error || 'Tenant context not found',
        ApiErrorCode.TENANT_NOT_FOUND,
        HttpStatus.NOT_FOUND
      );
    }
    const tenantContext = tenantResult.tenant!;

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const tableName = searchParams.get('table');
    const filtersParam = searchParams.get('filters');
    const searchParam = searchParams.get('search');
    const presetId = searchParams.get('preset_id');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '50'), 1000);
    const offset = (page - 1) * limit;

    if (!tableName || !(tableName in TABLE_CONFIGS)) {
      return createErrorResponse(
        'Invalid or missing table name',
        ApiErrorCode.VALIDATION_ERROR,
        HttpStatus.BAD_REQUEST
      );
    }

    const tableConfig = TABLE_CONFIGS[tableName as keyof typeof TABLE_CONFIGS];
    let baseQuery = tableConfig.baseQuery;

    // Build WHERE conditions
    const whereConditions: string[] = [];
    const queryParams: any[] = [];
    let paramIndex = 1;

    // Apply saved preset if specified
    if (presetId) {
      const presets = await AdvancedFilterService.getFilterPresets(
        tableName,
        authResult.session.email,
        tenantContext
      );
      const preset = presets.find(p => p.preset_id === parseInt(presetId));
      
      if (preset) {
        const filterResult = AdvancedFilterService.buildWhereClause(
          preset.filter_config.groups,
          paramIndex
        );
        whereConditions.push(filterResult.whereClause);
        queryParams.push(...filterResult.params);
        paramIndex += filterResult.paramCount;
      }
    }

    // Apply advanced filters
    if (filtersParam) {
      try {
        const filters: AdvancedFilter = JSON.parse(filtersParam);
        const validatedFilters = advancedFilterSchema.parse(filters);
        
        const filterResult = AdvancedFilterService.buildWhereClause(
          validatedFilters.groups,
          paramIndex
        );
        whereConditions.push(filterResult.whereClause);
        queryParams.push(...filterResult.params);
        paramIndex += filterResult.paramCount;
      } catch (error) {
        return createErrorResponse(
          'Invalid filter configuration',
          ApiErrorCode.VALIDATION_ERROR,
          HttpStatus.BAD_REQUEST
        );
      }
    }

    // Apply search
    if (searchParam) {
      const searchConfig: SearchConfig = {
        query: searchParam,
        fields: [...tableConfig.searchFields],
        caseSensitive: false
      };
      
      const searchResult = AdvancedFilterService.buildSearchClause(
        searchConfig,
        paramIndex
      );
      whereConditions.push(searchResult.searchClause);
      queryParams.push(...searchResult.params);
      paramIndex += searchResult.paramCount;
    }

    // Build final query
    const whereClause = whereConditions.length > 0 
      ? `AND (${whereConditions.join(' AND ')})` 
      : '';
    
    const finalQuery = `
      ${baseQuery}
      ${whereClause}
      ORDER BY created_on DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;
    
    queryParams.push(limit, offset);

    // Execute query
    const result = await databaseService.query(finalQuery, queryParams);

    // Get total count for pagination
    const countQuery = `
      SELECT COUNT(*) as total
      FROM (${baseQuery} ${whereClause}) as filtered_results
    `;
    
    const countResult = await databaseService.query(
      countQuery,
      queryParams.slice(0, -2) // Remove limit and offset
    );

    const total = countResult.success && countResult.data?.[0] 
      ? parseInt(countResult.data[0].total) 
      : 0;

    return createSuccessResponse({
      data: result.data || [],
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasMore: offset + limit < total
      },
      filters: filtersParam ? JSON.parse(filtersParam) : null,
      search: searchParam || null
    });

  } catch (error) {
    console.error('Error in advanced filters GET:', error);
    return createErrorResponse(
      'Internal server error',
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
}

// POST /api/advanced-filters - Save new filter preset
export async function POST(request: NextRequest) {
  try {
    // Authentication
    const authResult = await authenticateRequest(request);
    if (!authResult.success) {
      return authResult.response;
    }

    // Resolve tenant context
    const tenantResult = await resolveTenantContext(authResult.session);
    if (!tenantResult.success) {
      return tenantResult.response || createErrorResponse(
        tenantResult.error || 'Tenant context not found',
        ApiErrorCode.TENANT_NOT_FOUND,
        HttpStatus.NOT_FOUND
      );
    }
    const tenantContext = tenantResult.tenant!;

    // Parse and validate request body
    const body = await request.json();
    const validatedData = savePresetSchema.parse(body);

    // Save preset
    const result = await AdvancedFilterService.saveFilterPreset(
      {
        ...validatedData,
        created_by: authResult.session.email
      },
      tenantContext
    );

    if (result.success) {
      return createSuccessResponse({
        preset_id: result.presetId,
        message: 'Filter preset saved successfully'
      });
    } else {
      return createErrorResponse(
        result.error || 'Failed to save filter preset',
        ApiErrorCode.DATABASE_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }

  } catch (error) {
    if (error instanceof z.ZodError) {
      return createErrorResponse(
        'Invalid request data',
        ApiErrorCode.VALIDATION_ERROR,
        HttpStatus.BAD_REQUEST,
        error.errors
      );
    }

    console.error('Error in advanced filters POST:', error);
    return createErrorResponse(
      'Internal server error',
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
}

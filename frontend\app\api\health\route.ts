/**
 * Comprehensive Health Check API Endpoint
 *
 * Provides detailed health status for the application including
 * database connectivity, configuration validation, and service status
 */

import { NextRequest } from 'next/server'
import { createSuccessResponse, createErrorResponse, ApiErrorCode, HttpStatus } from '@/lib/api/response'
import { getDatabaseStatus } from '@/lib/services/database-health'
import { validateAppConfig } from '@/lib/config/validation'
import { handleError, getUserFriendlyMessage } from '@/lib/utils/error-handler'

const startTime = Date.now()

// GET /api/health - Get comprehensive application health
export async function GET(request: NextRequest) {
  try {
    console.log('🔍 [HEALTH] Starting comprehensive health check...')

    const healthCheckStart = Date.now()

    // Check database health
    const dbStatus = await getDatabaseStatus()
    const dbHealth = dbStatus.health

    // Validate configuration
    const configValidation = validateAppConfig()

    // Check memory usage
    let memoryHealth = {
      status: 'healthy' as 'healthy' | 'degraded' | 'unhealthy',
      message: 'Memory usage normal',
      details: {} as any
    }

    if (process.memoryUsage) {
      const memory = process.memoryUsage()
      const usedMB = memory.heapUsed / 1024 / 1024
      const totalMB = memory.heapTotal / 1024 / 1024
      const usagePercent = (usedMB / totalMB) * 100

      memoryHealth.details = {
        usedMB: Math.round(usedMB),
        totalMB: Math.round(totalMB),
        usagePercent: Math.round(usagePercent)
      }

      if (usagePercent > 90) {
        memoryHealth.status = 'unhealthy'
        memoryHealth.message = `High memory usage: ${usagePercent.toFixed(1)}%`
      } else if (usagePercent > 75) {
        memoryHealth.status = 'degraded'
        memoryHealth.message = `Elevated memory usage: ${usagePercent.toFixed(1)}%`
      }
    }

    // Determine overall status
    const isDatabaseHealthy = dbHealth.isHealthy
    const isConfigValid = configValidation.isValid
    const isMemoryHealthy = memoryHealth.status === 'healthy'

    let overallStatus: 'healthy' | 'degraded' | 'unhealthy' = 'healthy'

    if (!isDatabaseHealthy || !isConfigValid || memoryHealth.status === 'unhealthy') {
      overallStatus = 'unhealthy'
    } else if (dbHealth.warnings.length > 0 || configValidation.warnings.length > 0 || memoryHealth.status === 'degraded') {
      overallStatus = 'degraded'
    }

    const healthCheckDuration = Date.now() - healthCheckStart

    const healthData = {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      responseTime: healthCheckDuration,
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      uptime: Date.now() - startTime,
      services: {
        database: {
          status: isDatabaseHealthy ? 'healthy' : 'unhealthy',
          connected: dbHealth.isConnected,
          responseTime: dbHealth.responseTime,
          connectionCount: dbHealth.connectionCount,
          errors: dbHealth.errors,
          warnings: dbHealth.warnings
        },
        configuration: {
          status: isConfigValid ? 'valid' : 'invalid',
          errors: configValidation.errors,
          warnings: configValidation.warnings
        },
        memory: memoryHealth
      },
      summary: {
        healthy: overallStatus === 'healthy',
        issues: [
          ...dbHealth.errors,
          ...configValidation.errors,
          ...(memoryHealth.status !== 'healthy' ? [memoryHealth.message] : [])
        ],
        warnings: [
          ...dbHealth.warnings,
          ...configValidation.warnings
        ]
      }
    }

    console.log(`${overallStatus === 'healthy' ? '✅' : overallStatus === 'degraded' ? '⚠️' : '❌'} [HEALTH] Health check completed in ${healthCheckDuration}ms - Status: ${overallStatus}`)

    // Return appropriate HTTP status based on health
    const httpStatus = overallStatus === 'healthy' ? HttpStatus.OK :
                      overallStatus === 'degraded' ? HttpStatus.OK :
                      HttpStatus.INTERNAL_SERVER_ERROR // Use existing enum value

    return createSuccessResponse(healthData, undefined, httpStatus)

  } catch (error) {
    console.error('❌ [HEALTH] Health check failed:', error)

    const errorInfo = handleError(error as Error, {
      component: 'health',
      operation: 'healthCheck'
    })

    return createErrorResponse(
      'Health check failed',
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR,
      getUserFriendlyMessage(errorInfo)
    )
  }
}

/**
 * HEAD /api/health - Simple health check that returns only HTTP status
 */
export async function HEAD(request: NextRequest) {
  try {
    const dbStatus = await getDatabaseStatus()

    if (dbStatus.health.isHealthy) {
      return new Response(null, { status: HttpStatus.OK })
    } else {
      return new Response(null, { status: 503 }) // Service Unavailable
    }
  } catch (error) {
    console.error('❌ [HEALTH] HEAD health check failed:', error)
    return new Response(null, { status: 503 }) // Service Unavailable
  }
}

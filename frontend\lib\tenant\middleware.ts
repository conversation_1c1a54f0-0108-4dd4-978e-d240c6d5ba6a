/**
 * Tenant Context Middleware
 * 
 * Provides comprehensive tenant context propagation throughout the application.
 * Ensures tenant isolation and proper context handling at all layers.
 */

import { NextRequest, NextResponse } from 'next/server';
import { TenantContext } from '@/lib/types';
import { getTenantByDomain, getTenantByUserId } from '@/lib/tenant/clients';
import { ApiResponse, createErrorResponse, ApiErrorCode, HttpStatus } from '@/lib/api/response';
import { tenantSecurityEngine, SecurityContext } from './security';

// Import the canonical AuthSession interface
import type { AuthSession } from '@/lib/types';

export interface TenantSession extends AuthSession {
  tenant: TenantContext;
  tenantId: string;
  tenantSchema: string;
}

export interface TenantValidationResult {
  success: boolean;
  session?: TenantSession;
  response?: NextResponse<ApiResponse>;
  error?: string;
}

/**
 * Extract tenant context from request
 * Supports multiple tenant resolution strategies:
 * 1. Domain-based (from request host)
 * 2. User-based (from authenticated user)
 * 3. Header-based (X-Tenant-ID header)
 */
export async function extractTenantContext(
  request: NextRequest,
  session: AuthSession
): Promise<TenantContext | null> {
  try {
    // Strategy 1: Domain-based tenant resolution
    const host = request.headers.get('host');
    if (host) {
      const domain = host.split(':')[0]; // Remove port if present
      const tenant = await getTenantByDomain(domain);
      if (tenant) {
        console.log(`✅ Tenant resolved by domain: ${domain} -> ${tenant.clientName}`);
        return tenant;
      }
    }

    // Strategy 2: User-based tenant resolution
    if (session.userId) {
      const tenant = await getTenantByUserId(session.userId);
      if (tenant) {
        console.log(`✅ Tenant resolved by user: ${session.userId} -> ${tenant.clientName}`);
        return tenant;
      }
    }

    // Strategy 3: Header-based tenant resolution (for API clients)
    const tenantHeader = request.headers.get('X-Tenant-ID');
    if (tenantHeader) {
      // This would require a getTenantById function
      console.log(`⚠️ Header-based tenant resolution not implemented: ${tenantHeader}`);
    }

    console.warn('❌ No tenant context could be resolved');
    return null;
  } catch (error) {
    console.error('Error extracting tenant context:', error);
    return null;
  }
}

/**
 * Validate tenant access for authenticated user
 */
export async function validateTenantAccess(
  tenant: TenantContext,
  session: AuthSession
): Promise<boolean> {
  try {
    // Check if user belongs to this tenant
    // This could be enhanced with more sophisticated access control
    
    // For now, we'll check if the user's email domain matches tenant domains
    if (session.email && tenant.domains) {
      const userDomain = session.email.split('@')[1];
      const hasAccess = tenant.domains.includes(userDomain);
      
      if (!hasAccess) {
        console.warn(`❌ User ${session.email} does not have access to tenant ${tenant.clientName}`);
        return false;
      }
    }

    // Additional checks can be added here:
    // - Role-based access control
    // - Explicit user-tenant mappings
    // - Time-based access restrictions
    
    return true;
  } catch (error) {
    console.error('Error validating tenant access:', error);
    return false;
  }
}

/**
 * Require tenant context for API routes
 */
export async function requireTenant(
  request: NextRequest,
  session: AuthSession
): Promise<TenantValidationResult> {
  try {
    // Extract tenant context
    const tenant = await extractTenantContext(request, session);
    
    if (!tenant) {
      return {
        success: false,
        error: 'No tenant context available',
        response: createErrorResponse(
          'Tenant context required but not available',
          ApiErrorCode.TENANT_NOT_FOUND,
          HttpStatus.BAD_REQUEST
        )
      };
    }

    // Validate tenant access
    const hasAccess = await validateTenantAccess(tenant, session);
    if (!hasAccess) {
      return {
        success: false,
        error: 'Access denied to tenant',
        response: createErrorResponse(
          'Access denied to tenant',
          ApiErrorCode.FORBIDDEN,
          HttpStatus.FORBIDDEN
        )
      };
    }

    // Check if tenant is active
    if (!tenant.isActive) {
      return {
        success: false,
        error: 'Tenant is inactive',
        response: createErrorResponse(
          'Tenant is currently inactive',
          ApiErrorCode.TENANT_INACTIVE,
          HttpStatus.FORBIDDEN
        )
      };
    }

    // Enforce security policies
    const securityContext: SecurityContext = {
      tenant,
      user: session,
      request,
      operation: request.method,
      resource: request.nextUrl.pathname.split('/').pop() || 'unknown'
    };

    const securityResult = await tenantSecurityEngine.enforceSecurityPolicies(securityContext);

    if (!securityResult.allowed) {
      console.error('Security policy violations:', securityResult.violations);
      return {
        success: false,
        error: 'Security policy violation',
        response: createErrorResponse(
          'Access denied due to security policy violation',
          ApiErrorCode.FORBIDDEN,
          HttpStatus.FORBIDDEN
        )
      };
    }

    // Log security warnings
    if (securityResult.warnings.length > 0) {
      console.warn('Security warnings:', securityResult.warnings);
    }

    // Create enhanced session with tenant context
    const tenantSession: TenantSession = {
      ...session,
      tenant,
      tenantId: tenant.clientId,
      tenantSchema: tenant.tenantSchema
    };

    return {
      success: true,
      session: tenantSession
    };
  } catch (error) {
    console.error('Error in requireTenant:', error);
    return {
      success: false,
      error: 'Internal error validating tenant',
      response: createErrorResponse(
        'Internal error validating tenant context',
        ApiErrorCode.INTERNAL_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    };
  }
}

/**
 * Higher-order function to wrap API handlers with tenant context
 */
export function withTenant<T extends any[], R>(
  handler: (session: TenantSession, ...args: T) => Promise<R>
) {
  return async (session: AuthSession, ...args: T): Promise<R | NextResponse<ApiResponse>> => {
    const request = args[0] as NextRequest;
    const tenantResult = await requireTenant(request, session);

    if (!tenantResult.success) {
      return tenantResult.response!;
    }

    return handler(tenantResult.session!, ...args);
  };
}

/**
 * Add tenant context headers to response
 */
export function addTenantHeaders(
  response: NextResponse,
  tenant: TenantContext
): NextResponse {
  response.headers.set('X-Tenant-ID', tenant.clientId);
  response.headers.set('X-Tenant-Name', tenant.clientName);
  response.headers.set('X-Tenant-Schema', tenant.tenantSchema);
  return response;
}

/**
 * Create audit context for tenant operations
 */
export interface AuditContext {
  tenantId: string;
  tenantName: string;
  userId: string;
  userEmail: string;
  action: string;
  resource: string;
  timestamp: Date;
  requestId?: string;
  ipAddress?: string;
  userAgent?: string;
}

export function createAuditContext(
  session: TenantSession,
  request: NextRequest,
  action: string,
  resource: string,
  requestId?: string
): AuditContext {
  return {
    tenantId: session.tenant.clientId,
    tenantName: session.tenant.clientName,
    userId: session.userId,
    userEmail: session.email,
    action,
    resource,
    timestamp: new Date(),
    requestId,
    ipAddress: request.headers.get('x-forwarded-for') ||
               request.headers.get('x-real-ip') ||
               'unknown',
    userAgent: request.headers.get('user-agent') || 'unknown'
  };
}

/**
 * Generate correlation ID for request tracing
 */
export function generateCorrelationId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Enhanced wrapper that combines auth and tenant validation
 */
export function withAuthAndTenant<T extends any[], R>(
  handler: (session: TenantSession, correlationId: string, ...args: T) => Promise<R>
) {
  return async (...args: T): Promise<R | NextResponse<ApiResponse>> => {
    const request = args[0] as NextRequest;
    const correlationId = generateCorrelationId();

    // Add correlation ID to request headers for downstream processing
    request.headers.set('X-Correlation-ID', correlationId);

    try {
      // First check authentication (this would need to be imported from auth-middleware)
      // For now, we'll assume session is passed as a parameter
      // In a real implementation, this would integrate with the existing auth middleware

      console.log(`🔍 [${correlationId}] Processing request: ${request.method} ${request.url}`);

      return await handler({} as TenantSession, correlationId, ...args);
    } catch (error) {
      console.error(`❌ [${correlationId}] Request failed:`, error);
      throw error;
    }
  };
}

/**
 * Utilities Index - Centralized Utility Exports
 *
 * This file organizes all utility functions into logical groups
 * for better discoverability and consistent imports.
 *
 * ⚠️  IMPORTANT: Client vs Server Separation
 * Only client-safe utilities are exported here.
 * Server-only utilities must be imported directly in API routes.
 */

// ===== FORMATTING UTILITIES =====
// Date and time formatting
export {
  formatDate
} from './date-utils'

// Currency and number formatting
export {
  formatCurrency
} from './format-utils'

// ===== VALIDATION UTILITIES =====
// Client-safe validation functions
export * from './validation'
// Note: Additional validation utilities will be added as needed

// ===== STYLING UTILITIES =====
// Design tokens and CSS utilities
export * from './design-tokens'

// ===== PERFORMANCE UTILITIES =====
// React performance optimization hooks and functions
export { useDebounce, useThrottle, debounce } from './performance'

// ===== CACHING UTILITIES =====
// Cache key utilities
export {
  createCacheKey,
  createTenantCacheKey,
  createUser<PERSON>acheKey
} from './cache-keys'
// Cache utilities
export { cacheUtils } from './cache'
// Cache management and invalidation
export {
  clearAllCache,
  invalidateByTags,
  invalidateAdminPagesData,
  invalidateLicenseData,
  invalidateOverviewData,
  invalidateRenewalsData,
  invalidateTenantData,
  invalidateUserData,
  invalidateVendorData,
  getCacheStats,
  useCacheStats,
  CACHE_TAGS,
  CacheTags
} from './cache-invalidation'

// ===== ERROR HANDLING AND LOGGING =====
// Error processing and logging utilities
export { logger, LogLevel, LogCategory } from './logger'
export { globalErrorHandler } from './global-error-handler'
export * from './error-handler'

// Note: debounce utility moved to performance.ts to avoid duplication
// Use: import { useDebounce } from '@/lib/utils/performance' for React hooks
// Use: import { debounce } from '@/lib/utils/performance' for plain functions

// ===== INLINE UTILITIES =====
// CSS class name utility
export function cn(...classes: (string | undefined | null | false)[]): string {
  return classes.filter(Boolean).join(' ')
}

// ===== SERVER-ONLY UTILITIES =====
// ⚠️  WARNING: These should ONLY be imported directly in API routes
// DO NOT export these here to prevent client-side usage:
//
// - cache.ts (server-side caching with Redis/memory)
// - encryption.ts (server-side encryption utilities)
// - database-utils.ts (server-side database helpers)
// - file-utils.ts (server-side file operations)



/**
 * Individual Tenant Alert API Endpoint
 * 
 * Provides operations for individual tenant alerts
 * GET /api/tenant-alerts/[id] - Get specific alert
 * PUT /api/tenant-alerts/[id] - Update specific alert
 * DELETE /api/tenant-alerts/[id] - Delete specific alert
 */

import { NextRequest } from 'next/server';
import { withAuth } from '@/lib/api/auth-middleware';
import { resolveTenantContext } from '@/lib/tenant/context';
import { executeTenantQuery, executeTenantQuerySingle } from '@/lib/tenant/database';
import {
  createSuccessResponse,
  createErrorResponse,
  ApiErrorCode,
  HttpStatus
} from '@/lib/api/response';
import { z } from 'zod';
import { TenantAlert } from '@/app/api/tenant-alerts/route';
import { VALIDATION, ALERTS } from '@/lib/constants/app-constants';

// Validation schemas
const updateAlertSchema = z.object({
  alert_name: z.string().min(VALIDATION.MIN_NAME_LENGTH).max(VALIDATION.MAX_NAME_LENGTH).optional(),
  days_before_renewal: z.number().int().min(ALERTS.MIN_DAYS_BEFORE).max(ALERTS.MAX_DAYS_BEFORE).optional(),
  email_recipients: z.array(z.string().email()).min(1).optional(),
  is_active: z.boolean().optional()
});

// GET /api/tenant-alerts/[id]
export const GET = withAuth(async (
  request: NextRequest,
  session,
  { params }: { params: Promise<{ id: string }> }
) => {
  const resolvedParams = await params;
  try {
    // Resolve tenant context using session
    const tenantResult = await resolveTenantContext(session);
    if (!tenantResult.success) {
      return createErrorResponse(
        'Failed to resolve tenant context',
        ApiErrorCode.DATABASE_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }

    const tenantContext = tenantResult.tenant;
    if (!tenantContext) {
      return createErrorResponse(
        'Tenant context not found',
        ApiErrorCode.TENANT_NOT_FOUND,
        HttpStatus.NOT_FOUND
      );
    }

    const alertId = parseInt(resolvedParams.id);
    if (isNaN(alertId)) {
      return createErrorResponse(
        'Invalid alert ID',
        ApiErrorCode.VALIDATION_ERROR,
        HttpStatus.BAD_REQUEST
      );
    }

    const query = `
      SELECT 
        alert_id,
        renewal_id,
        alert_name,
        days_before_renewal,
        email_recipients,
        is_active,
        last_sent,
        created_on,
        changed_on,
        created_by,
        modified_by
      FROM tenant_alerts 
      WHERE alert_id = $1
    `;

    const result = await executeTenantQuerySingle<TenantAlert>(
      query,
      [alertId],
      tenantContext
    );

    if (!result.success) {
      return createErrorResponse(
        result.error || 'Failed to fetch alert',
        ApiErrorCode.DATABASE_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }

    if (!result.data) {
      return createErrorResponse(
        'Alert not found',
        ApiErrorCode.NOT_FOUND,
        HttpStatus.NOT_FOUND
      );
    }

    return createSuccessResponse(result.data);

  } catch (error) {
    console.error('Error in tenant-alerts/[id] GET:', error);
    return createErrorResponse(
      'Internal server error',
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
}, {
  requireAuth: true
});

// PUT /api/tenant-alerts/[id]
export const PUT = withAuth(async (
  request: NextRequest,
  session,
  { params }: { params: Promise<{ id: string }> }
) => {
  const resolvedParams = await params;
  try {
    // Resolve tenant context using session
    const tenantResult = await resolveTenantContext(session);
    if (!tenantResult.success) {
      return createErrorResponse(
        'Failed to resolve tenant context',
        ApiErrorCode.DATABASE_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }

    const tenantContext = tenantResult.tenant;
    if (!tenantContext) {
      return createErrorResponse(
        'Tenant context not found',
        ApiErrorCode.TENANT_NOT_FOUND,
        HttpStatus.NOT_FOUND
      );
    }

    const alertId = parseInt(resolvedParams.id);
    if (isNaN(alertId)) {
      return createErrorResponse(
        'Invalid alert ID',
        ApiErrorCode.VALIDATION_ERROR,
        HttpStatus.BAD_REQUEST
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = updateAlertSchema.parse(body);

    // Check if alert exists
    const existingAlert = await executeTenantQuerySingle(
      'SELECT alert_id FROM tenant_alerts WHERE alert_id = $1',
      [alertId],
      tenantContext
    );

    if (!existingAlert.success || !existingAlert.data) {
      return createErrorResponse(
        'Alert not found',
        ApiErrorCode.NOT_FOUND,
        HttpStatus.NOT_FOUND
      );
    }

    // Build dynamic update query
    const updateFields: string[] = [];
    const updateValues: any[] = [];
    let paramIndex = 1;

    if (validatedData.alert_name !== undefined) {
      updateFields.push(`alert_name = $${paramIndex++}`);
      updateValues.push(validatedData.alert_name);
    }

    if (validatedData.days_before_renewal !== undefined) {
      updateFields.push(`days_before_renewal = $${paramIndex++}`);
      updateValues.push(validatedData.days_before_renewal);
    }

    if (validatedData.email_recipients !== undefined) {
      updateFields.push(`email_recipients = $${paramIndex++}`);
      updateValues.push(validatedData.email_recipients);
    }

    if (validatedData.is_active !== undefined) {
      updateFields.push(`is_active = $${paramIndex++}`);
      updateValues.push(validatedData.is_active);
    }

    if (updateFields.length === 0) {
      return createErrorResponse(
        'No fields to update',
        ApiErrorCode.VALIDATION_ERROR,
        HttpStatus.BAD_REQUEST
      );
    }

    // Add modified_by and changed_on
    updateFields.push(`modified_by = $${paramIndex++}`);
    updateFields.push(`changed_on = CURRENT_TIMESTAMP`);
    updateValues.push(session.email || session.userId);

    // Add alert_id for WHERE clause
    updateValues.push(alertId);

    const updateQuery = `
      UPDATE tenant_alerts 
      SET ${updateFields.join(', ')}
      WHERE alert_id = $${paramIndex}
      RETURNING alert_id, renewal_id, alert_name, days_before_renewal, 
                email_recipients, is_active, created_on, changed_on
    `;

    const result = await executeTenantQuerySingle<TenantAlert>(
      updateQuery,
      updateValues,
      tenantContext
    );

    if (!result.success || !result.data) {
      return createErrorResponse(
        result.error || 'Failed to update alert',
        ApiErrorCode.DATABASE_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }

    return createSuccessResponse(result.data);

  } catch (error) {
    if (error instanceof z.ZodError) {
      return createErrorResponse(
        'Invalid request data',
        ApiErrorCode.VALIDATION_ERROR,
        HttpStatus.BAD_REQUEST,
        error.errors
      );
    }

    console.error('Error in tenant-alerts/[id] PUT:', error);
    return createErrorResponse(
      'Internal server error',
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
}, {
  requireAuth: true
});

// DELETE /api/tenant-alerts/[id]
export const DELETE = withAuth(async (
  request: NextRequest,
  session,
  { params }: { params: Promise<{ id: string }> }
) => {
  const resolvedParams = await params;
  try {
    // Resolve tenant context using session
    const tenantResult = await resolveTenantContext(session);
    if (!tenantResult.success) {
      return createErrorResponse(
        'Failed to resolve tenant context',
        ApiErrorCode.DATABASE_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }

    const tenantContext = tenantResult.tenant;
    if (!tenantContext) {
      return createErrorResponse(
        'Tenant context not found',
        ApiErrorCode.TENANT_NOT_FOUND,
        HttpStatus.NOT_FOUND
      );
    }

    const alertId = parseInt(resolvedParams.id);
    if (isNaN(alertId)) {
      return createErrorResponse(
        'Invalid alert ID',
        ApiErrorCode.VALIDATION_ERROR,
        HttpStatus.BAD_REQUEST
      );
    }

    // Check if alert exists
    const existingAlert = await executeTenantQuerySingle(
      'SELECT alert_id FROM tenant_alerts WHERE alert_id = $1',
      [alertId],
      tenantContext
    );

    if (!existingAlert.success || !existingAlert.data) {
      return createErrorResponse(
        'Alert not found',
        ApiErrorCode.NOT_FOUND,
        HttpStatus.NOT_FOUND
      );
    }

    // Delete the alert
    const deleteQuery = 'DELETE FROM tenant_alerts WHERE alert_id = $1';
    const result = await executeTenantQuery(
      deleteQuery,
      [alertId],
      tenantContext
    );

    if (!result.success) {
      return createErrorResponse(
        result.error || 'Failed to delete alert',
        ApiErrorCode.DATABASE_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }

    return createSuccessResponse({ message: 'Alert deleted successfully' });

  } catch (error) {
    console.error('Error in tenant-alerts/[id] DELETE:', error);
    return createErrorResponse(
      'Internal server error',
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
}, {
  requireAuth: true
});

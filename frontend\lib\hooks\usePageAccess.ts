/**
 * Page Access Hook
 * 
 * Checks if the current user has access to a specific page
 */

'use client'

import { useState, useEffect } from 'react'
import { useAuth } from './useAuth'

interface UsePageAccessResult {
  hasAccess: boolean | null
  isLoading: boolean
  error: string | null
}

/**
 * Hook to check if user has access to a specific page
 */
export function usePageAccess(pageName: string): UsePageAccessResult {
  const { user, isAuthenticated, isLoading: authLoading } = useAuth()
  const [hasAccess, setHasAccess] = useState<boolean | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const checkAccess = async () => {
      if (authLoading) return
      
      if (!isAuthenticated || !user) {
        setHasAccess(false)
        setIsLoading(false)
        return
      }

      if (!pageName) {
        setHasAccess(false)
        setIsLoading(false)
        return
      }

      try {
        setIsLoading(true)
        setError(null)
        
        const response = await fetch(`/api/admin-pages/check-access?page=${encodeURIComponent(pageName)}`)
        
        if (response.ok) {
          const data = await response.json()
          setHasAccess(data.success && data.hasAccess)
        } else {
          setError('Failed to check page access')
          setHasAccess(false)
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error')
        setHasAccess(false)
      } finally {
        setIsLoading(false)
      }
    }

    checkAccess()
  }, [pageName, isAuthenticated, user, authLoading])

  return {
    hasAccess,
    isLoading,
    error
  }
}

/**
 * Standardized API Response Utilities
 * 
 * This module provides consistent response formatting and error handling
 * for all API routes in the application.
 */

import { NextResponse } from 'next/server';
import { ZodError } from 'zod';
import { errorTracking, ErrorContext } from '@/lib/monitoring';
import { TraceContext, requestTracing } from '@/lib/monitoring';
import { TenantContext } from '@/lib/types';
import { AuthSession } from '@/lib/api/auth-middleware';

// Standard API response interface
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  errorCode?: string;
  message?: string;
  timestamp: string;
}

// Error types for consistent error handling
export enum ApiErrorCode {
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  NOT_FOUND = 'NOT_FOUND',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  RATE_LIMITED = 'RATE_LIMITED',
  INVALID_INPUT = 'INVALID_INPUT',
  MULTIPLE_FOUND = 'MULTIPLE_FOUND',
  CONFLICT = 'CONFLICT',

  // Tenant-specific error codes
  TENANT_NOT_FOUND = 'TENANT_NOT_FOUND',
  TENANT_INACTIVE = 'TENANT_INACTIVE',
  TENANT_ACCESS_DENIED = 'TENANT_ACCESS_DENIED',
  TENANT_SCHEMA_ERROR = 'TENANT_SCHEMA_ERROR',
}

// HTTP status codes
export enum HttpStatus {
  OK = 200,
  CREATED = 201,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  CONFLICT = 409,
  UNPROCESSABLE_ENTITY = 422,
  TOO_MANY_REQUESTS = 429,
  INTERNAL_SERVER_ERROR = 500,
}

/**
 * Create a successful API response
 */
export function createSuccessResponse<T>(
  data: T,
  message?: string,
  status: HttpStatus = HttpStatus.OK
): NextResponse<ApiResponse<T>> {
  const response: ApiResponse<T> = {
    success: true,
    data,
    message,
    timestamp: new Date().toISOString(),
  };

  return NextResponse.json(response, { status });
}

/**
 * Create an error API response
 */
export function createErrorResponse(
  error: string,
  errorCode: ApiErrorCode,
  status: HttpStatus,
  details?: any
): NextResponse<ApiResponse> {
  const response: ApiResponse = {
    success: false,
    error,
    errorCode,
    timestamp: new Date().toISOString(),
  };

  // Add details in development mode only
  if (process.env.NODE_ENV === 'development' && details) {
    (response as any).details = details;
  }

  return NextResponse.json(response, { status });
}

/**
 * Handle validation errors from Zod with enhanced tracking
 */
export function handleValidationError(
  error: ZodError,
  context?: Partial<ErrorContext>,
  tenant?: TenantContext,
  user?: AuthSession,
  traceContext?: TraceContext
): NextResponse<ApiResponse> {
  const errorMessage = error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ');

  // Track the validation error
  const validationError = new Error(`Validation failed: ${errorMessage}`);
  validationError.name = 'ValidationError';

  const errorId = errorTracking.trackError(
    validationError,
    {
      ...context,
      source: 'validation',
      operation: context?.operation || 'validation',
      metadata: { validationErrors: error.errors }
    },
    tenant,
    user,
    traceContext
  );

  const response = createErrorResponse(
    `Validation failed: ${errorMessage}`,
    ApiErrorCode.VALIDATION_ERROR,
    HttpStatus.BAD_REQUEST,
    error.errors
  );

  // Add error tracking headers
  if (response && response.headers && typeof response.headers.set === 'function') {
    response.headers.set('X-Error-ID', errorId);
    if (traceContext) {
      response.headers.set('X-Correlation-ID', traceContext.correlationId);
    }
  }

  return response;
}

/**
 * Handle database errors with enhanced tracking
 */
export function handleDatabaseError(
  error: any,
  context?: Partial<ErrorContext>,
  tenant?: TenantContext,
  user?: AuthSession,
  traceContext?: TraceContext
): NextResponse<ApiResponse> {
  // Log error for debugging (only in development)
  if (process.env.NODE_ENV === 'development') {
    console.error('Database error:', error);
  }

  // Track the error
  const errorId = errorTracking.trackError(
    error instanceof Error ? error : new Error(String(error)),
    {
      ...context,
      source: 'database',
      operation: context?.operation || 'database_operation'
    },
    tenant,
    user,
    traceContext
  );

  // Don't expose internal database errors in production
  const message = process.env.NODE_ENV === 'development'
    ? `Database error: ${error.message}`
    : 'A database error occurred';

  const response = createErrorResponse(
    message,
    ApiErrorCode.DATABASE_ERROR,
    HttpStatus.INTERNAL_SERVER_ERROR,
    process.env.NODE_ENV === 'development' ? error : undefined
  );

  // Add error tracking headers
  if (response && response.headers && typeof response.headers.set === 'function') {
    response.headers.set('X-Error-ID', errorId);
    if (traceContext) {
      response.headers.set('X-Correlation-ID', traceContext.correlationId);
      response.headers.set('X-Trace-ID', traceContext.traceId);
    }
  }

  return response;
}

/**
 * Handle unauthorized access
 */
export function createUnauthorizedResponse(message = 'Unauthorized'): NextResponse<ApiResponse> {
  return createErrorResponse(
    message,
    ApiErrorCode.UNAUTHORIZED,
    HttpStatus.UNAUTHORIZED
  );
}

/**
 * Handle forbidden access
 */
export function createForbiddenResponse(message = 'Forbidden'): NextResponse<ApiResponse> {
  return createErrorResponse(
    message,
    ApiErrorCode.FORBIDDEN,
    HttpStatus.FORBIDDEN
  );
}

/**
 * Handle not found errors
 */
export function createNotFoundResponse(resource = 'Resource'): NextResponse<ApiResponse> {
  return createErrorResponse(
    `${resource} not found`,
    ApiErrorCode.NOT_FOUND,
    HttpStatus.NOT_FOUND
  );
}

/**
 * Handle rate limiting
 */
export function createRateLimitResponse(): NextResponse<ApiResponse> {
  const response = createErrorResponse(
    'Too many requests',
    ApiErrorCode.RATE_LIMITED,
    HttpStatus.TOO_MANY_REQUESTS
  );

  // Add rate limit headers
  if (response && response.headers && typeof response.headers.set === 'function') {
    response.headers.set('Retry-After', '60');
    response.headers.set('X-RateLimit-Limit', '100');
    response.headers.set('X-RateLimit-Remaining', '0');
  }

  return response;
}

/**
 * Handle tenant not found errors
 */
export function createTenantNotFoundResponse(message = 'Tenant not found'): NextResponse<ApiResponse> {
  return createErrorResponse(
    message,
    ApiErrorCode.TENANT_NOT_FOUND,
    HttpStatus.NOT_FOUND
  );
}

/**
 * Handle tenant inactive errors
 */
export function createTenantInactiveResponse(message = 'Tenant is inactive'): NextResponse<ApiResponse> {
  return createErrorResponse(
    message,
    ApiErrorCode.TENANT_INACTIVE,
    HttpStatus.FORBIDDEN
  );
}

/**
 * Handle tenant access denied errors
 */
export function createTenantAccessDeniedResponse(message = 'Access denied to tenant'): NextResponse<ApiResponse> {
  return createErrorResponse(
    message,
    ApiErrorCode.TENANT_ACCESS_DENIED,
    HttpStatus.FORBIDDEN
  );
}

/**
 * Handle tenant schema errors
 */
export function createTenantSchemaErrorResponse(message = 'Tenant schema error'): NextResponse<ApiResponse> {
  return createErrorResponse(
    message,
    ApiErrorCode.TENANT_SCHEMA_ERROR,
    HttpStatus.INTERNAL_SERVER_ERROR
  );
}

/**
 * Enhanced generic error handler for API routes with tracing
 */
export function handleApiError(
  error: any,
  context?: Partial<ErrorContext>,
  tenant?: TenantContext,
  user?: AuthSession,
  traceContext?: TraceContext
): NextResponse<ApiResponse> {
  // Log error for debugging (only in development)
  if (process.env.NODE_ENV === 'development') {
    console.error('API Error:', error);
  }

  // Handle specific error types
  if (error instanceof ZodError) {
    return handleValidationError(error, context, tenant, user, traceContext);
  }

  // Handle database errors
  if (error.code && (error.code.startsWith('23') || error.code.startsWith('42'))) {
    return handleDatabaseError(error, context, tenant, user, traceContext);
  }

  // Track the error
  const errorId = errorTracking.trackError(
    error instanceof Error ? error : new Error(String(error)),
    {
      ...context,
      source: 'api',
      operation: context?.operation || 'api_operation'
    },
    tenant,
    user,
    traceContext
  );

  // Generic internal server error
  const message = process.env.NODE_ENV === 'development'
    ? error.message || 'Internal server error'
    : 'Internal server error';

  const response = createErrorResponse(
    message,
    ApiErrorCode.INTERNAL_ERROR,
    HttpStatus.INTERNAL_SERVER_ERROR,
    process.env.NODE_ENV === 'development' ? error : undefined
  );

  // Add error tracking headers
  if (response && response.headers && typeof response.headers.set === 'function') {
    response.headers.set('X-Error-ID', errorId);
    if (traceContext) {
      response.headers.set('X-Correlation-ID', traceContext.correlationId);
      response.headers.set('X-Trace-ID', traceContext.traceId);
    }
  }

  return response;
}

/**
 * Add security headers to response
 */
export function addSecurityHeaders(response: NextResponse): NextResponse {
  if (response && response.headers && typeof response.headers.set === 'function') {
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('X-Frame-Options', 'DENY');
    response.headers.set('X-XSS-Protection', '1; mode=block');
    response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');
  }

  return response;
}

/**
 * Enhanced wrapper for API route handlers with error handling and tracing
 */
export function withErrorHandling<T extends any[], R>(
  handler: (...args: T) => Promise<NextResponse<ApiResponse<R>>>,
  operationName?: string
) {
  return async (...args: T): Promise<NextResponse<ApiResponse<R>>> => {
    const request = args[0] as any; // Assume first arg is NextRequest
    let traceContext: TraceContext | undefined;

    // Create trace context if we have a request object
    if (request && typeof request === 'object' && request.headers) {
      traceContext = requestTracing.createTraceContext(
        request,
        operationName || 'api_operation',
        request.nextUrl?.pathname || 'unknown'
      );

      const trace = requestTracing.startTrace(traceContext);
      const span = requestTracing.startSpan(
        trace.traceId,
        operationName || 'api_handler',
        traceContext.parentSpanId,
        {
          'http.method': request.method,
          'http.url': request.url
        }
      );

      try {
        const response = await handler(...args);

        // Add trace headers to response if it has headers
        if (response && response.headers && typeof response.headers.set === 'function') {
          response.headers.set('X-Correlation-ID', traceContext.correlationId);
          response.headers.set('X-Trace-ID', traceContext.traceId);
        }

        requestTracing.finishSpan(span.spanId, 'success');
        requestTracing.finishTrace(trace.traceId, 'success');

        return addSecurityHeaders(response) as NextResponse<ApiResponse<R>>;
      } catch (error) {
        requestTracing.addSpanLog(
          span.spanId,
          'error',
          error instanceof Error ? error.message : 'Unknown error'
        );

        const errorResponse = handleApiError(error, {
          correlationId: traceContext.correlationId,
          operation: operationName || 'api_operation',
          url: request.url,
          method: request.method
        }, undefined, undefined, traceContext);

        requestTracing.finishSpan(span.spanId, 'error', error instanceof Error ? error : new Error(String(error)));
        requestTracing.finishTrace(trace.traceId, 'error');

        return addSecurityHeaders(errorResponse) as NextResponse<ApiResponse<R>>;
      }
    } else {
      // Fallback for non-request handlers
      try {
        const response = await handler(...args);
        return addSecurityHeaders(response) as NextResponse<ApiResponse<R>>;
      } catch (error) {
        const errorResponse = handleApiError(error);
        return addSecurityHeaders(errorResponse) as NextResponse<ApiResponse<R>>;
      }
    }
  };
}


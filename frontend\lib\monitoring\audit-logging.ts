/**
 * Comprehensive Audit Logging System
 * 
 * Provides detailed audit logging for compliance, security monitoring,
 * and troubleshooting with tenant-specific context and event categorization.
 */

import { TenantContext } from '@/lib/types';
import { AuthSession } from '@/lib/api/auth-middleware';
import { TraceContext } from './request-tracing';
import { executeTenantQuery } from '@/lib/tenant/database';

export enum AuditEventType {
  // Authentication & Authorization
  LOGIN = 'login',
  LOGOUT = 'logout',
  LOGIN_FAILED = 'login_failed',
  PASSWORD_CHANGE = 'password_change',
  PERMISSION_GRANTED = 'permission_granted',
  PERMISSION_DENIED = 'permission_denied',
  
  // Data Operations
  CREATE = 'create',
  READ = 'read',
  UPDATE = 'update',
  DELETE = 'delete',
  BULK_OPERATION = 'bulk_operation',
  EXPORT = 'export',
  IMPORT = 'import',
  
  // System Events
  SYSTEM_START = 'system_start',
  SYSTEM_STOP = 'system_stop',
  CONFIGURATION_CHANGE = 'configuration_change',
  BACKUP_CREATED = 'backup_created',
  BACKUP_RESTORED = 'backup_restored',
  
  // Security Events
  SECURITY_VIOLATION = 'security_violation',
  SUSPICIOUS_ACTIVITY = 'suspicious_activity',
  RATE_LIMIT_EXCEEDED = 'rate_limit_exceeded',
  UNAUTHORIZED_ACCESS = 'unauthorized_access',
  DATA_BREACH_ATTEMPT = 'data_breach_attempt',
  
  // Tenant Operations
  TENANT_CREATED = 'tenant_created',
  TENANT_UPDATED = 'tenant_updated',
  TENANT_DELETED = 'tenant_deleted',
  TENANT_ACCESS_GRANTED = 'tenant_access_granted',
  TENANT_ACCESS_REVOKED = 'tenant_access_revoked',
  
  // Administrative Actions
  USER_CREATED = 'user_created',
  USER_UPDATED = 'user_updated',
  USER_DELETED = 'user_deleted',
  ROLE_ASSIGNED = 'role_assigned',
  ROLE_REVOKED = 'role_revoked',
  SETTINGS_CHANGED = 'settings_changed'
}

export enum AuditSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export interface AuditEvent {
  id: string;
  eventType: AuditEventType;
  severity: AuditSeverity;
  timestamp: Date;
  
  // Context Information
  tenantId?: string;
  tenantName?: string;
  userId?: string;
  userEmail?: string;
  sessionId?: string;
  correlationId?: string;
  traceId?: string;
  
  // Request Information
  ipAddress?: string;
  userAgent?: string;
  requestId?: string;
  method?: string;
  url?: string;
  referer?: string;
  
  // Event Details
  resource: string;
  resourceId?: string;
  action: string;
  description: string;
  
  // Data Changes
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  affectedFields?: string[];
  
  // Additional Context
  metadata?: Record<string, any>;
  tags?: string[];
  
  // Compliance & Legal
  complianceFlags?: string[];
  retentionPeriod?: number; // days
  
  // Status
  success: boolean;
  errorMessage?: string;
  errorCode?: string;
}

export interface AuditQuery {
  tenantId?: string;
  userId?: string;
  eventType?: AuditEventType;
  severity?: AuditSeverity;
  resource?: string;
  startDate?: Date;
  endDate?: Date;
  success?: boolean;
  tags?: string[];
  limit?: number;
  offset?: number;
}

class AuditLoggingService {
  private events: Map<string, AuditEvent> = new Map();
  private maxEvents = 50000; // Maximum events to keep in memory
  private eventRetentionMs = 90 * 24 * 60 * 60 * 1000; // 90 days

  /**
   * Log an audit event
   */
  async logEvent(
    eventType: AuditEventType,
    resource: string,
    action: string,
    description: string,
    context: {
      tenant?: TenantContext;
      user?: AuthSession;
      traceContext?: TraceContext;
      request?: {
        method?: string;
        url?: string;
        ipAddress?: string;
        userAgent?: string;
        referer?: string;
      };
      data?: {
        resourceId?: string;
        oldValues?: Record<string, any>;
        newValues?: Record<string, any>;
        affectedFields?: string[];
      };
      metadata?: Record<string, any>;
      success?: boolean;
      errorMessage?: string;
      errorCode?: string;
    } = {}
  ): Promise<string> {
    const eventId = this.generateEventId();
    const severity = this.determineSeverity(eventType, context.success !== false);
    
    const auditEvent: AuditEvent = {
      id: eventId,
      eventType,
      severity,
      timestamp: new Date(),
      
      // Context
      tenantId: context.tenant?.clientId,
      tenantName: context.tenant?.clientName,
      userId: context.user?.userId,
      userEmail: context.user?.email,
      sessionId: context.user?.sessionId,
      correlationId: context.traceContext?.correlationId,
      traceId: context.traceContext?.traceId,
      
      // Request
      ipAddress: context.request?.ipAddress,
      userAgent: context.request?.userAgent,
      requestId: context.traceContext?.requestId,
      method: context.request?.method,
      url: context.request?.url,
      referer: context.request?.referer,
      
      // Event details
      resource,
      resourceId: context.data?.resourceId,
      action,
      description,
      
      // Data changes
      oldValues: context.data?.oldValues,
      newValues: context.data?.newValues,
      affectedFields: context.data?.affectedFields,
      
      // Additional context
      metadata: context.metadata,
      tags: this.generateTags(eventType, context),
      
      // Compliance
      complianceFlags: this.getComplianceFlags(eventType, context),
      retentionPeriod: this.getRetentionPeriod(eventType, severity),
      
      // Status
      success: context.success !== false,
      errorMessage: context.errorMessage,
      errorCode: context.errorCode
    };

    // Store in memory
    this.events.set(eventId, auditEvent);

    // Store in database if tenant context is available
    if (context.tenant) {
      await this.persistToDatabase(auditEvent, context.tenant);
    }

    // Log to console with appropriate level
    this.logToConsole(auditEvent);

    // Cleanup old events
    this.cleanupOldEvents();

    return eventId;
  }

  /**
   * Determine event severity
   */
  private determineSeverity(eventType: AuditEventType, success: boolean): AuditSeverity {
    if (!success) {
      // Failed operations are generally more severe
      switch (eventType) {
        case AuditEventType.LOGIN_FAILED:
        case AuditEventType.PERMISSION_DENIED:
        case AuditEventType.UNAUTHORIZED_ACCESS:
          return AuditSeverity.HIGH;
        case AuditEventType.SECURITY_VIOLATION:
        case AuditEventType.DATA_BREACH_ATTEMPT:
          return AuditSeverity.CRITICAL;
        default:
          return AuditSeverity.MEDIUM;
      }
    }

    // Successful operations
    switch (eventType) {
      case AuditEventType.SECURITY_VIOLATION:
      case AuditEventType.DATA_BREACH_ATTEMPT:
      case AuditEventType.TENANT_DELETED:
        return AuditSeverity.CRITICAL;
      
      case AuditEventType.LOGIN:
      case AuditEventType.LOGOUT:
      case AuditEventType.PASSWORD_CHANGE:
      case AuditEventType.DELETE:
      case AuditEventType.TENANT_CREATED:
      case AuditEventType.USER_CREATED:
      case AuditEventType.ROLE_ASSIGNED:
        return AuditSeverity.HIGH;
      
      case AuditEventType.UPDATE:
      case AuditEventType.CONFIGURATION_CHANGE:
      case AuditEventType.SETTINGS_CHANGED:
        return AuditSeverity.MEDIUM;
      
      default:
        return AuditSeverity.LOW;
    }
  }

  /**
   * Generate tags for event categorization
   */
  private generateTags(
    eventType: AuditEventType,
    context: any
  ): string[] {
    const tags = [
      `event_type:${eventType}`,
      `environment:${process.env.NODE_ENV || 'unknown'}`
    ];

    if (context.tenant) {
      tags.push(`tenant:${context.tenant.clientId}`);
    }

    if (context.user) {
      tags.push(`user:${context.user.userId}`);
      if (context.user.roles) {
        context.user.roles.forEach((role: string) => tags.push(`role:${role}`));
      }
    }

    if (context.request?.method) {
      tags.push(`method:${context.request.method}`);
    }

    // Add category tags
    if ([AuditEventType.LOGIN, AuditEventType.LOGOUT, AuditEventType.LOGIN_FAILED].includes(eventType)) {
      tags.push('category:authentication');
    } else if ([AuditEventType.CREATE, AuditEventType.READ, AuditEventType.UPDATE, AuditEventType.DELETE].includes(eventType)) {
      tags.push('category:data_operation');
    } else if (eventType.toString().includes('SECURITY') || eventType.toString().includes('UNAUTHORIZED')) {
      tags.push('category:security');
    } else if (eventType.toString().includes('TENANT')) {
      tags.push('category:tenant_management');
    }

    return tags;
  }

  /**
   * Get compliance flags for the event
   */
  private getComplianceFlags(eventType: AuditEventType, context: any): string[] {
    const flags: string[] = [];

    // GDPR compliance flags
    if ([AuditEventType.READ, AuditEventType.EXPORT].includes(eventType)) {
      flags.push('GDPR_DATA_ACCESS');
    }
    if ([AuditEventType.DELETE].includes(eventType)) {
      flags.push('GDPR_DATA_DELETION');
    }
    if ([AuditEventType.UPDATE].includes(eventType) && context.data?.affectedFields?.includes('personal_data')) {
      flags.push('GDPR_DATA_MODIFICATION');
    }

    // SOX compliance flags
    if ([AuditEventType.DELETE, AuditEventType.UPDATE].includes(eventType) && 
        context.resource?.includes('financial')) {
      flags.push('SOX_FINANCIAL_DATA');
    }

    // HIPAA compliance flags (if applicable)
    if (context.resource?.includes('health') || context.resource?.includes('medical')) {
      flags.push('HIPAA_PHI_ACCESS');
    }

    return flags;
  }

  /**
   * Get retention period for event type
   */
  private getRetentionPeriod(eventType: AuditEventType, severity: AuditSeverity): number {
    // Critical events: 7 years
    if (severity === AuditSeverity.CRITICAL) {
      return 7 * 365;
    }

    // Security events: 3 years
    if (eventType.toString().includes('SECURITY') || 
        eventType.toString().includes('UNAUTHORIZED') ||
        eventType === AuditEventType.LOGIN_FAILED) {
      return 3 * 365;
    }

    // Financial/compliance events: 7 years
    if (eventType === AuditEventType.DELETE || 
        eventType === AuditEventType.EXPORT) {
      return 7 * 365;
    }

    // Regular events: 1 year
    return 365;
  }

  /**
   * Persist event to database
   */
  private async persistToDatabase(event: AuditEvent, tenant: TenantContext): Promise<void> {
    try {
      const query = `
        INSERT INTO audit_log (
          event_id, event_type, severity, timestamp,
          tenant_id, user_id, session_id, correlation_id, trace_id,
          ip_address, user_agent, request_id, method, url,
          resource, resource_id, action, description,
          old_values, new_values, affected_fields,
          metadata, tags, compliance_flags, retention_period,
          success, error_message, error_code
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10,
          $11, $12, $13, $14, $15, $16, $17, $18, $19, $20,
          $21, $22, $23, $24, $25, $26, $27, $28
        )
      `;

      const values = [
        event.id, event.eventType, event.severity, event.timestamp,
        event.tenantId, event.userId, event.sessionId, event.correlationId, event.traceId,
        event.ipAddress, event.userAgent, event.requestId, event.method, event.url,
        event.resource, event.resourceId, event.action, event.description,
        event.oldValues ? JSON.stringify(event.oldValues) : null,
        event.newValues ? JSON.stringify(event.newValues) : null,
        event.affectedFields ? JSON.stringify(event.affectedFields) : null,
        event.metadata ? JSON.stringify(event.metadata) : null,
        event.tags ? JSON.stringify(event.tags) : null,
        event.complianceFlags ? JSON.stringify(event.complianceFlags) : null,
        event.retentionPeriod,
        event.success, event.errorMessage, event.errorCode
      ];

      await executeTenantQuery(query, values, tenant, { validateQuery: false });

    } catch (error) {
      console.error('Failed to persist audit event to database:', error);
      // Don't throw - audit logging should not break the main operation
    }
  }

  /**
   * Log to console with appropriate level
   */
  private logToConsole(event: AuditEvent): void {
    const logData = {
      eventId: event.id,
      eventType: event.eventType,
      severity: event.severity,
      resource: event.resource,
      action: event.action,
      tenantId: event.tenantId,
      userId: event.userId,
      success: event.success,
      correlationId: event.correlationId
    };

    switch (event.severity) {
      case AuditSeverity.CRITICAL:
        console.error(`🚨 CRITICAL AUDIT EVENT:`, logData);
        break;
      case AuditSeverity.HIGH:
        console.warn(`⚠️ HIGH SEVERITY AUDIT EVENT:`, logData);
        break;
      case AuditSeverity.MEDIUM:
        console.info(`📋 AUDIT EVENT:`, logData);
        break;
      case AuditSeverity.LOW:
        console.log(`📝 AUDIT EVENT:`, logData);
        break;
    }
  }

  /**
   * Generate unique event ID
   */
  private generateEventId(): string {
    return `audit_${Date.now()}_${Math.random().toString(36).substr(2, 12)}`;
  }

  /**
   * Clean up old events from memory
   */
  private cleanupOldEvents(): void {
    const now = Date.now();
    const cutoff = now - this.eventRetentionMs;
    let cleaned = 0;

    for (const [eventId, event] of this.events.entries()) {
      if (event.timestamp.getTime() < cutoff) {
        this.events.delete(eventId);
        cleaned++;
      }
    }

    // Enforce max events limit
    if (this.events.size > this.maxEvents) {
      const sortedEvents = Array.from(this.events.entries())
        .sort(([, a], [, b]) => b.timestamp.getTime() - a.timestamp.getTime());

      const toKeep = sortedEvents.slice(0, this.maxEvents);
      this.events.clear();
      
      for (const [eventId, event] of toKeep) {
        this.events.set(eventId, event);
      }

      cleaned += sortedEvents.length - this.maxEvents;
    }

    if (cleaned > 0) {
      console.log(`🧹 Cleaned up ${cleaned} old audit events`);
    }
  }

  /**
   * Query audit events
   */
  queryEvents(query: AuditQuery): AuditEvent[] {
    let events = Array.from(this.events.values());

    // Apply filters
    if (query.tenantId) {
      events = events.filter(e => e.tenantId === query.tenantId);
    }
    if (query.userId) {
      events = events.filter(e => e.userId === query.userId);
    }
    if (query.eventType) {
      events = events.filter(e => e.eventType === query.eventType);
    }
    if (query.severity) {
      events = events.filter(e => e.severity === query.severity);
    }
    if (query.resource) {
      events = events.filter(e => e.resource.includes(query.resource!));
    }
    if (query.startDate) {
      events = events.filter(e => e.timestamp >= query.startDate!);
    }
    if (query.endDate) {
      events = events.filter(e => e.timestamp <= query.endDate!);
    }
    if (query.success !== undefined) {
      events = events.filter(e => e.success === query.success);
    }
    if (query.tags) {
      events = events.filter(e => 
        query.tags!.some(tag => e.tags?.includes(tag))
      );
    }

    // Sort by timestamp (most recent first)
    events.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

    // Apply pagination
    const offset = query.offset || 0;
    const limit = query.limit || 100;
    
    return events.slice(offset, offset + limit);
  }

  /**
   * Get audit statistics
   */
  getAuditStats(): {
    totalEvents: number;
    eventsByType: Record<string, number>;
    eventsBySeverity: Record<string, number>;
    recentEvents: number;
    failureRate: number;
  } {
    const events = Array.from(this.events.values());
    const last24Hours = new Date(Date.now() - 24 * 60 * 60 * 1000);
    
    const eventsByType: Record<string, number> = {};
    const eventsBySeverity: Record<string, number> = {};
    let recentEvents = 0;
    let failures = 0;

    for (const event of events) {
      eventsByType[event.eventType] = (eventsByType[event.eventType] || 0) + 1;
      eventsBySeverity[event.severity] = (eventsBySeverity[event.severity] || 0) + 1;
      
      if (event.timestamp >= last24Hours) {
        recentEvents++;
      }
      
      if (!event.success) {
        failures++;
      }
    }

    return {
      totalEvents: events.length,
      eventsByType,
      eventsBySeverity,
      recentEvents,
      failureRate: events.length > 0 ? failures / events.length : 0
    };
  }
}

// Global audit logging service
export const auditLogger = new AuditLoggingService();

/**
 * Audit logging middleware wrapper
 */
export function withAuditLogging<T extends any[], R>(
  eventType: AuditEventType,
  resource: string,
  action: string,
  handler: (...args: T) => Promise<R>
) {
  return async (...args: T): Promise<R> => {
    const request = args[0] as any;
    let tenant: TenantContext | undefined;
    let user: AuthSession | undefined;
    let traceContext: TraceContext | undefined;

    // Extract context from arguments if available
    if (args.length > 1) {
      const session = args[1] as any;
      if (session && session.tenant) {
        tenant = session.tenant;
        user = session;
      }
      if (session && session.correlationId) {
        traceContext = session;
      }
    }

    const startTime = Date.now();
    let success = true;
    let errorMessage: string | undefined;
    let result: R;

    try {
      result = await handler(...args);
      return result;
    } catch (error) {
      success = false;
      errorMessage = error instanceof Error ? error.message : String(error);
      throw error;
    } finally {
      // Log the audit event
      await auditLogger.logEvent(
        eventType,
        resource,
        action,
        `${action} operation on ${resource}`,
        {
          tenant,
          user,
          traceContext,
          request: request && typeof request === 'object' && request.headers ? {
            method: request.method,
            url: request.url,
            ipAddress: request.headers.get?.('x-forwarded-for') ||
                      request.headers.get?.('x-real-ip'),
            userAgent: request.headers.get?.('user-agent'),
            referer: request.headers.get?.('referer')
          } : undefined,
          metadata: {
            duration: Date.now() - startTime,
            timestamp: new Date().toISOString()
          },
          success,
          errorMessage
        }
      );
    }
  };
}

/**
 * Log authentication events
 */
export async function logAuthEvent(
  eventType: AuditEventType.LOGIN | AuditEventType.LOGOUT | AuditEventType.LOGIN_FAILED,
  user: AuthSession | { email: string; userId?: string },
  request?: {
    ipAddress?: string;
    userAgent?: string;
    url?: string;
  },
  tenant?: TenantContext,
  errorMessage?: string
): Promise<string> {
  return auditLogger.logEvent(
    eventType,
    'authentication',
    eventType,
    `User ${eventType} attempt`,
    {
      user: user as AuthSession,
      tenant,
      request,
      success: eventType !== AuditEventType.LOGIN_FAILED,
      errorMessage
    }
  );
}

/**
 * Log data operation events
 */
export async function logDataOperation(
  operation: AuditEventType.CREATE | AuditEventType.READ | AuditEventType.UPDATE | AuditEventType.DELETE,
  resource: string,
  resourceId: string,
  tenant: TenantContext,
  user: AuthSession,
  data?: {
    oldValues?: Record<string, any>;
    newValues?: Record<string, any>;
    affectedFields?: string[];
  },
  traceContext?: TraceContext
): Promise<string> {
  return auditLogger.logEvent(
    operation,
    resource,
    operation,
    `${operation} operation on ${resource} ${resourceId}`,
    {
      tenant,
      user,
      traceContext,
      data: {
        resourceId,
        ...data
      }
    }
  );
}

/**
 * Log security events
 */
export async function logSecurityEvent(
  eventType: AuditEventType,
  description: string,
  tenant?: TenantContext,
  user?: AuthSession,
  request?: {
    ipAddress?: string;
    userAgent?: string;
    url?: string;
    method?: string;
  },
  metadata?: Record<string, any>
): Promise<string> {
  return auditLogger.logEvent(
    eventType,
    'security',
    'security_event',
    description,
    {
      tenant,
      user,
      request,
      metadata,
      success: false // Security events are typically violations
    }
  );
}

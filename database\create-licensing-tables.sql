-- =====================================================
-- LICENSING SYSTEM TABLES CREATION
-- =====================================================

-- Create metadata schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS metadata;

-- =====================================================
-- LICENSE TYPES TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS metadata.admin_license_types (
    id SERIAL PRIMARY KEY,
    type_name VARCHAR(50) NOT NULL UNIQUE,
    max_renewals INTEGER NOT NULL,
    description TEXT,
    price_per_renewal DECIMAL(10, 2),
    features JSONB,
    status CHAR(1) DEFAULT 'A' CHECK (status IN ('A', 'I')),
    created_on TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    changed_on TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- LICENSE TERMS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS metadata.admin_license_terms (
    id SERIAL PRIMARY KEY,
    term_name VARCHAR(50) NOT NULL UNIQUE,
    term_months INTEGER NOT NULL,
    discount_percentage DECIMAL(5, 2) DEFAULT 0.00,
    status CHAR(1) DEFAULT 'A' CHECK (status IN ('A', 'I')),
    created_on TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    changed_on TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- LICENSE KEYS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS metadata.admin_license_keys (
    license_key_id SERIAL PRIMARY KEY,
    license_key VARCHAR(50) NOT NULL UNIQUE,
    license_type_id INTEGER NOT NULL,
    description TEXT,
    is_assigned BOOLEAN DEFAULT FALSE,
    created_on TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255) NOT NULL,
    assigned_at TIMESTAMP WITH TIME ZONE,
    assigned_to_client_id INTEGER,
    FOREIGN KEY (license_type_id) REFERENCES metadata.admin_license_types (id)
);

-- =====================================================
-- CLIENT LICENSES TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS metadata.admin_client_licenses (
    client_license_id SERIAL PRIMARY KEY,
    client_id INTEGER NOT NULL,
    license_key_id INTEGER NOT NULL,
    license_type_id INTEGER NOT NULL,
    license_term_id INTEGER NOT NULL,
    activation_date DATE NOT NULL DEFAULT CURRENT_DATE,
    expiry_date DATE NOT NULL,
    current_renewals INTEGER DEFAULT 0,
    max_renewals INTEGER NOT NULL,
    activated_by VARCHAR(255) NOT NULL,
    status VARCHAR(20) DEFAULT 'ACTIVE',
    notes TEXT,
    created_on TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    changed_on TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES metadata.clients (id),
    FOREIGN KEY (license_key_id) REFERENCES metadata.admin_license_keys (license_key_id),
    FOREIGN KEY (license_type_id) REFERENCES metadata.admin_license_types (id),
    FOREIGN KEY (license_term_id) REFERENCES metadata.admin_license_terms (id),
    CONSTRAINT chk_expiry_after_activation CHECK (expiry_date > activation_date),
    CONSTRAINT uk_client_active_license UNIQUE (client_id, status) DEFERRABLE INITIALLY DEFERRED
);

-- =====================================================
-- INSERT DEFAULT DATA
-- =====================================================

-- Insert default license types
INSERT INTO
    metadata.admin_license_types (
        type_name,
        max_renewals,
        description,
        price_per_renewal,
        features
    )
VALUES (
        'STARTER',
        50,
        'Basic license for small teams',
        99.00,
        '{"renewals": true, "reports": false, "api_access": false}'
    ),
    (
        'STANDARD',
        500,
        'Standard license for growing businesses',
        299.00,
        '{"renewals": true, "reports": true, "api_access": false}'
    ),
    (
        'PROFESSIONAL',
        5000,
        'Professional license for large organizations',
        999.00,
        '{"renewals": true, "reports": true, "api_access": true}'
    ),
    (
        'ENTERPRISE',
        50000,
        'Enterprise license for unlimited usage',
        2999.00,
        '{"renewals": true, "reports": true, "api_access": true, "custom_integrations": true}'
    )
ON CONFLICT (type_name) DO NOTHING;

-- Insert default license terms
INSERT INTO
    metadata.admin_license_terms (
        term_name,
        term_months,
        discount_percentage
    )
VALUES ('1 Year', 12, 0.00),
    ('2 Years', 24, 10.00),
    ('3 Years', 36, 15.00),
    ('5 Years', 60, 25.00)
ON CONFLICT (term_name) DO NOTHING;

-- Insert demo license keys
INSERT INTO
    metadata.admin_license_keys (
        license_key,
        license_type_id,
        description,
        created_by
    )
VALUES (
        'DEMO-STARTER-2025-001',
        1,
        'Demo Starter License - 50 Renewals',
        'system'
    ),
    (
        'DEMO-STANDARD-2025-001',
        2,
        'Demo Standard License - 500 Renewals',
        'system'
    ),
    (
        'DEMO-PROFESSIONAL-2025-001',
        3,
        'Demo Professional License - 5000 Renewals',
        'system'
    ),
    (
        'DEMO-ENTERPRISE-2025-001',
        4,
        'Demo Enterprise License - 50000 Renewals',
        'system'
    )
ON CONFLICT (license_key) DO NOTHING;

-- =====================================================
-- CREATE SAMPLE LICENSE FOR RENEWTRACK CLIENT
-- =====================================================

-- RenewTrack client already exists with id=2, so we'll skip the insert

-- Activate a license for RenewTrack client
INSERT INTO
    metadata.admin_client_licenses (
        client_id,
        license_key_id,
        license_type_id,
        license_term_id,
        activation_date,
        expiry_date,
        max_renewals,
        activated_by,
        status
    )
SELECT c.id, lk.license_key_id, lk.license_type_id, 1, -- 1 Year term
    CURRENT_DATE, CURRENT_DATE + INTERVAL '1 year', lt.max_renewals, 'system', 'ACTIVE'
FROM metadata.clients c
    CROSS JOIN metadata.admin_license_keys lk
    JOIN metadata.admin_license_types lt ON lk.license_type_id = lt.id
WHERE
    c.name = 'RenewTrack'
    AND lk.license_key = 'DEMO-ENTERPRISE-2025-001'
    AND NOT EXISTS (
        SELECT 1
        FROM metadata.admin_client_licenses
        WHERE
            client_id = c.id
            AND status = 'ACTIVE'
    );

-- Update the license key as assigned
UPDATE metadata.admin_license_keys
SET
    is_assigned = TRUE,
    assigned_at = CURRENT_TIMESTAMP,
    assigned_to_client_id = (
        SELECT id
        FROM metadata.clients
        WHERE
            name = 'RenewTrack'
    )
WHERE
    license_key = 'DEMO-ENTERPRISE-2025-001';

COMMIT;
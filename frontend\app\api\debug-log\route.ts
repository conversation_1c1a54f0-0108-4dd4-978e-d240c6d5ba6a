import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log('🐛 [DEBUG-LOG]', body);
    
    return NextResponse.json({ 
      success: true, 
      message: 'Debug log received' 
    });
  } catch (error) {
    console.error('❌ [DEBUG-LOG] Error:', error);
    return NextResponse.json({
      success: false,
      error: 'Logging service is temporarily unavailable. Please try again later.'
    }, { status: 500 });
  }
}

/**
 * License Types API
 * 
 * Endpoints for managing license types configuration
 */

import { NextRequest, NextResponse } from 'next/server'
import { databaseService } from '@/lib/services/database-service'
import { requireSuperAdmin } from '@/lib/api/auth-middleware'
import { createSuccessResponse, createErrorResponse, ApiErrorCode, HttpStatus } from '@/lib/api/response'
import { STATUS } from '@/lib/constants/app-constants'

/**
 * GET /api/admin/license-types
 * Get all license types
 */
export async function GET(request: NextRequest) {
  try {
    // Require super admin authentication
    const authResult = await requireSuperAdmin(request)
    if (!authResult.success) {
      return authResult.response
    }

    const query = `
      SELECT 
        id,
        type_name,
        max_renewals,
        description,
        price_per_renewal,
        features,
        status
      FROM metadata.admin_license_types
      WHERE status = '${STATUS.ACTIVE}'
      ORDER BY max_renewals ASC
    `

    const result = await databaseService.query(query)

    return createSuccessResponse(result.rows)

  } catch (error) {
    console.error('Error fetching license types:', error)
    return createErrorResponse('Failed to fetch license types', ApiErrorCode.INTERNAL_ERROR, HttpStatus.INTERNAL_SERVER_ERROR)
  }
}

/**
 * Tenant-Safe Database Operations
 * 
 * Provides database operations with enforced tenant isolation
 * and comprehensive validation at the database layer.
 */

import { PoolClient, QueryResult } from 'pg';
import { createTenantConnection, validateTenantQuery, TenantValidationResult } from './validation';
import { TenantContext, DbMultipleResult, DbSingleResult } from '@/lib/types';
import { AuthSession } from '@/lib/api/auth-middleware';

export interface TenantQueryOptions {
  timeout?: number;
  validateQuery?: boolean;
  allowCrossSchema?: boolean;
}

// Use the unified database result types
export interface TenantQueryResult<T = any> extends DbMultipleResult<T> {
  validationResult?: TenantValidationResult;
}

export interface TenantQuerySingleResult<T = any> extends DbSingleResult<T> {
  validationResult?: TenantValidationResult;
}

/**
 * Execute a query with tenant isolation enforcement
 */
export async function executeTenantQuery<T = any>(
  query: string,
  params: any[] = [],
  tenant: TenantContext,
  options: TenantQueryOptions = {}
): Promise<TenantQueryResult<T>> {
  const { timeout = 30000, validateQuery = true, allowCrossSchema = false } = options;
  
  let connection: { client: PoolClient; release: () => void } | null = null;
  
  try {
    // Validate query if requested
    let validationResult: TenantValidationResult | undefined;
    if (validateQuery) {
      validationResult = validateTenantQuery(query, tenant.tenantSchema);
      
      if (!validationResult.isValid) {
        return {
          success: false,
          error: validationResult.error?.message || 'Query validation failed',
          validationResult
        };
      }

      // Check for cross-schema access
      if (!allowCrossSchema && validationResult.warnings) {
        const hasCrossSchemaWarning = validationResult.warnings.some(w => 
          w.includes('metadata schema') || w.includes('does not explicitly reference')
        );
        
        if (hasCrossSchemaWarning && !allowCrossSchema) {
          console.warn('Cross-schema access detected but not allowed:', validationResult.warnings);
        }
      }
    }

    // Create tenant-safe connection
    connection = await createTenantConnection(tenant.tenantSchema);
    
    // Set query timeout
    if (timeout) {
      await connection.client.query(`SET statement_timeout = ${timeout}`);
    }

    // Execute the query
    const startTime = Date.now();
    const result: QueryResult = await connection.client.query(query, params);
    const duration = Date.now() - startTime;

    // Log slow queries
    if (duration > 1000) {
      console.warn(`Slow tenant query detected (${duration}ms) for tenant ${tenant.clientName}:`, {
        query: query.substring(0, 100) + '...',
        params: params.length,
        tenant: tenant.clientId
      });
    }

    // Log successful query execution
    console.log(`✅ Tenant query executed successfully for ${tenant.clientName}: ${result.rowCount} rows affected`);

    return {
      success: true,
      data: result.rows,
      rowCount: result.rowCount || 0,
      warnings: validationResult?.warnings,
      validationResult
    };

  } catch (error) {
    console.error(`❌ Tenant query failed for ${tenant.clientName}:`, error);
    
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown database error',
      rowCount: 0
    };
  } finally {
    if (connection) {
      connection.release();
    }
  }
}

/**
 * Execute a single query and return the first row
 */
export async function executeTenantQuerySingle<T = any>(
  query: string,
  params: any[] = [],
  tenant: TenantContext,
  options: TenantQueryOptions = {}
): Promise<TenantQuerySingleResult<T>> {
  const result = await executeTenantQuery<T>(query, params, tenant, options);

  if (result.success && result.data && result.data.length > 0) {
    return {
      success: result.success,
      error: result.error,
      rowCount: result.rowCount,
      executionTime: result.executionTime,
      warnings: result.warnings,
      validationResult: result.validationResult,
      data: result.data[0]
    };
  }

  return {
    success: result.success,
    error: result.error,
    rowCount: result.rowCount,
    executionTime: result.executionTime,
    warnings: result.warnings,
    validationResult: result.validationResult,
    data: undefined
  };
}

/**
 * Execute multiple queries in a transaction with tenant isolation
 */
export async function executeTenantTransaction<T = any>(
  queries: Array<{ query: string; params?: any[] }>,
  tenant: TenantContext,
  options: TenantQueryOptions = {}
): Promise<TenantQueryResult<T[]>> {
  let connection: { client: PoolClient; release: () => void } | null = null;
  
  try {
    // Validate all queries first
    if (options.validateQuery !== false) {
      for (const { query } of queries) {
        const validation = validateTenantQuery(query, tenant.tenantSchema);
        if (!validation.isValid) {
          return {
            success: false,
            error: `Query validation failed: ${validation.error?.message}`,
            validationResult: validation
          };
        }
      }
    }

    // Create tenant-safe connection
    connection = await createTenantConnection(tenant.tenantSchema);
    
    // Set timeout if specified
    if (options.timeout) {
      await connection.client.query(`SET statement_timeout = ${options.timeout}`);
    }

    // Begin transaction
    await connection.client.query('BEGIN');
    
    const results: any[] = [];
    
    try {
      for (const { query, params = [] } of queries) {
        const result = await connection.client.query(query, params);
        results.push(result.rows);
      }
      
      // Commit transaction
      await connection.client.query('COMMIT');
      
      console.log(`✅ Tenant transaction completed successfully for ${tenant.clientName}: ${queries.length} queries executed`);
      
      return {
        success: true,
        data: results,
        rowCount: results.reduce((sum, rows) => sum + rows.length, 0)
      };
      
    } catch (error) {
      // Rollback on error
      await connection.client.query('ROLLBACK');
      throw error;
    }

  } catch (error) {
    console.error(`❌ Tenant transaction failed for ${tenant.clientName}:`, error);
    
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Transaction failed'
    };
  } finally {
    if (connection) {
      connection.release();
    }
  }
}

/**
 * Check if a record exists in tenant schema
 */
export async function tenantRecordExists(
  table: string,
  conditions: Record<string, any>,
  tenant: TenantContext
): Promise<boolean> {
  const whereClause = Object.keys(conditions)
    .map((key, index) => `"${key}" = $${index + 1}`)
    .join(' AND ');
  
  const query = `SELECT 1 FROM "${table}" WHERE ${whereClause} LIMIT 1`;
  const params = Object.values(conditions);
  
  const result = await executeTenantQuerySingle(
    query,
    params,
    tenant,
    { validateQuery: true }
  );
  
  return result.success && result.data !== undefined;
}

/**
 * Get tenant-specific record count
 */
export async function getTenantRecordCount(
  table: string,
  conditions: Record<string, any> = {},
  tenant: TenantContext
): Promise<number> {
  let whereClause = '';
  let params: any[] = [];
  
  if (Object.keys(conditions).length > 0) {
    whereClause = 'WHERE ' + Object.keys(conditions)
      .map((key, index) => `"${key}" = $${index + 1}`)
      .join(' AND ');
    params = Object.values(conditions);
  }
  
  const query = `SELECT COUNT(*) as count FROM "${table}" ${whereClause}`;
  
  const result = await executeTenantQuerySingle<{ count: string }>(
    query,
    params,
    tenant,
    { validateQuery: true }
  );
  
  if (result.success && result.data) {
    return parseInt(result.data.count, 10);
  }
  
  return 0;
}

/**
 * Audit log for tenant operations
 */
export interface TenantAuditLog {
  tenantId: string;
  userId: string;
  operation: string;
  table: string;
  recordId?: string;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  timestamp: Date;
  ipAddress?: string;
  userAgent?: string;
}

/**
 * Log tenant operation for audit purposes
 */
export async function logTenantOperation(
  auditLog: TenantAuditLog,
  tenant: TenantContext
): Promise<void> {
  try {
    const query = `
      INSERT INTO audit_log (
        tenant_id, user_id, operation, table_name, record_id,
        old_values, new_values, timestamp, ip_address, user_agent
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
    `;
    
    const params = [
      auditLog.tenantId,
      auditLog.userId,
      auditLog.operation,
      auditLog.table,
      auditLog.recordId,
      auditLog.oldValues ? JSON.stringify(auditLog.oldValues) : null,
      auditLog.newValues ? JSON.stringify(auditLog.newValues) : null,
      auditLog.timestamp,
      auditLog.ipAddress,
      auditLog.userAgent
    ];
    
    await executeTenantQuery(query, params, tenant, { validateQuery: false });
    
  } catch (error) {
    // Don't fail the main operation if audit logging fails
    console.error('Failed to log tenant operation:', error);
  }
}

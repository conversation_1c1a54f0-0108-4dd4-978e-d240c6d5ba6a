/**
 * Vendor Details Table Component
 * 
 * Displays vendor renewal details in a table format.
 * Reuses existing table patterns and components.
 */

'use client'

import React, { useMemo } from 'react'
import { BaseComponentProps } from '@/lib/types'
import { formatCurrency } from '@/lib/utils/format-utils'

interface VendorRenewal {
  id: string
  vendor: string
  product_name: string
  cost: number
  currency: string
  reseller?: string
  renewal_date: string
  reliability_score: number
  relationship_duration: string
  description?: string
}

interface VendorDetailsTableProps extends BaseComponentProps {
  data: VendorRenewal[]
  isLoading?: boolean
}

export default function VendorDetailsTable({
  data,
  isLoading = false,
  className = '',
  'data-testid': testId
}: VendorDetailsTableProps) {
  // Group data by vendor and calculate aggregates
  const vendorSummary = useMemo(() => {
    const grouped = data.reduce((acc, renewal) => {
      const vendor = renewal.vendor
      if (!acc[vendor]) {
        acc[vendor] = {
          vendor,
          totalSpend: 0,
          renewalCount: 0,
          nextRenewal: '',
          reliabilityScore: 0,
          relationshipDuration: renewal.relationship_duration,
          description: renewal.description || 'No description available.',
          renewals: []
        }
      }
      
      acc[vendor].totalSpend += renewal.cost
      acc[vendor].renewalCount += 1
      acc[vendor].renewals.push(renewal)
      
      // Find next renewal date
      if (!acc[vendor].nextRenewal || new Date(renewal.renewal_date) < new Date(acc[vendor].nextRenewal)) {
        acc[vendor].nextRenewal = renewal.renewal_date
      }
      
      // Average reliability score
      acc[vendor].reliabilityScore = acc[vendor].renewals.reduce((sum: number, r: any) => sum + r.reliability_score, 0) / acc[vendor].renewals.length
      
      return acc
    }, {} as Record<string, any>)
    
    return Object.values(grouped).sort((a: any, b: any) => b.totalSpend - a.totalSpend)
  }, [data])

  if (isLoading) {
    return (
      <div className={`bg-white border rounded-lg p-6 ${className}`} data-testid={testId}>
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded mb-4 w-1/4"></div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-4 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (!vendorSummary.length) {
    return (
      <div className={`bg-white border rounded-lg p-6 text-center ${className}`} data-testid={testId}>
        <div className="text-gray-500">
          <div className="text-4xl mb-2">📊</div>
          <p>No vendor data available</p>
        </div>
      </div>
    )
  }

  return (
    <div className={`bg-white border rounded-lg ${className}`} data-testid={testId}>
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">Vendor Details</h3>
          <div className="flex items-center gap-4 text-sm text-gray-600">
            <span>Sort by:</span>
            <select className="border border-gray-300 rounded px-2 py-1 text-sm">
              <option value="spend">Spend</option>
              <option value="renewalCount">Renewal Count</option>
              <option value="nextRenewal">Next Renewal</option>
              <option value="reliabilityScore">Reliability Score</option>
            </select>
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Vendor
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Total Spend
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Renewal Count
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Next Renewal
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Reliability Score
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Relationship Duration
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Description
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {vendorSummary.map((vendor: any, index) => (
              <tr key={vendor.vendor} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="font-medium text-gray-900">{vendor.vendor}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-gray-900">
                    {formatCurrency(vendor.totalSpend, 'CAD')}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-gray-900">{vendor.renewalCount}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-gray-900">
                    {vendor.nextRenewal ? new Date(vendor.nextRenewal).toLocaleDateString() : '-'}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-1 bg-gray-200 rounded-full h-2 mr-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full" 
                        style={{ width: `${vendor.reliabilityScore}%` }}
                      ></div>
                    </div>
                    <span className="text-sm text-gray-600">{Math.round(vendor.reliabilityScore)}%</span>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-gray-900">{vendor.relationshipDuration}</div>
                </td>
                <td className="px-6 py-4">
                  <div className="text-gray-600 text-sm max-w-xs truncate" title={vendor.description}>
                    {vendor.description}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Footer */}
      <div className="px-6 py-4 border-t border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <span>Showing {vendorSummary.length} vendors</span>
          <span>
            Total: {formatCurrency(
              vendorSummary.reduce((sum: number, vendor: any) => sum + vendor.totalSpend, 0),
              'CAD'
            )}
          </span>
        </div>
      </div>
    </div>
  )
}

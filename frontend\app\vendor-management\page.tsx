'use client'

import React, { useState } from 'react'
import { useData } from '@/lib/hooks'
import { PageAccessGuard } from '@/components/auth'
import { Button, Modal, Form, PageHeader } from '@/components/ui'
import { useToast } from '@/components/ui/Toast'
import { TENANT_ENDPOINTS } from '@/lib/constants/api-endpoints'

interface Vendor {
  id: number
  name: string
  display_name?: string
  tax_id?: string
  domain?: string
  address?: string
  city?: string
  state?: string
  country?: string
  contact_email?: string
  contact_phone?: string
  website?: string
  created_on: Date
  changed_on?: Date
}

interface Product {
  id: number
  vendor_id: number
  name: string
  description?: string
  category?: string
  sku?: string
  barcode?: string
  unit_of_measure?: string
  vendor_name: string
  created_on: Date
  changed_on?: Date
}

interface ProductVersion {
  id: number
  product_id: number
  version: string
  release_date?: Date
  notes?: string
  is_current: boolean
  product_name: string
  vendor_name: string
  created_on: Date
  changed_on?: Date
}

export default function VendorManagementPage() {
  const [activeTab, setActiveTab] = useState<'vendors' | 'products' | 'versions'>('vendors')
  const [showAddVendorModal, setShowAddVendorModal] = useState(false)
  const [showAddProductModal, setShowAddProductModal] = useState(false)
  const [showAddVersionModal, setShowAddVersionModal] = useState(false)
  const [selectedVendor, setSelectedVendor] = useState<Vendor | null>(null)
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null)
  const toast = useToast()

  // Fetch vendors
  const {
    data: vendors = [],
    loading: vendorsLoading,
    error: vendorsError,
    refetch: refetchVendors
  } = useData<Vendor[]>({
    endpoint: TENANT_ENDPOINTS.VENDORS,
    cache: {
      key: 'tenant-vendors'
    }
  })

  // Fetch products (all products across vendors)
  const {
    data: products = [],
    loading: productsLoading,
    error: productsError,
    refetch: refetchProducts
  } = useData<Product[]>({
    endpoint: TENANT_ENDPOINTS.PRODUCTS,
    cache: {
      key: 'tenant-products'
    }
  })

  // Fetch product versions (all versions across products)
  const {
    data: versions = [],
    loading: versionsLoading,
    error: versionsError,
    refetch: refetchVersions
  } = useData<ProductVersion[]>({
    endpoint: TENANT_ENDPOINTS.PRODUCT_VERSIONS,
    cache: {
      key: 'tenant-product-versions'
    }
  })

  const handleAddVendor = async (vendorData: {
    name: string
    display_name?: string
    tax_id?: string
    domain?: string
    address?: string
    city?: string
    state?: string
    country?: string
    contact_email?: string
    contact_phone?: string
    website?: string
  }) => {
    try {
      const response = await fetch('/api/tenant-vendors', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(vendorData)
      })

      if (response.ok) {
        toast.success('Vendor added successfully')
        refetchVendors()
        setShowAddVendorModal(false)
      } else {
        toast.error('Failed to add vendor')
      }
    } catch (error) {
      toast.error('Error adding vendor')
    }
  }

  const handleAddProduct = async (productData: {
    vendor_id: number
    name: string
    description?: string
    category?: string
    sku?: string
    barcode?: string
    unit_of_measure?: string
  }) => {
    try {
      const response = await fetch('/api/tenant-products', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(productData)
      })

      if (response.ok) {
        toast.success('Product added successfully')
        refetchProducts()
        setShowAddProductModal(false)
      } else {
        toast.error('Failed to add product')
      }
    } catch (error) {
      toast.error('Error adding product')
    }
  }

  const handleAddVersion = async (versionData: {
    product_id: number
    version: string
    release_date?: string
    notes?: string
    is_current?: boolean
  }) => {
    try {
      const response = await fetch('/api/tenant-product-versions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(versionData)
      })

      if (response.ok) {
        toast.success('Product version added successfully')
        refetchVersions()
        setShowAddVersionModal(false)
      } else {
        toast.error('Failed to add product version')
      }
    } catch (error) {
      toast.error('Error adding product version')
    }
  }

  if (vendorsError || productsError || versionsError) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <h3 className="text-red-800 font-medium">Error Loading Data</h3>
          <p className="text-red-600 mt-1">
            {vendorsError instanceof Error ? vendorsError.message : vendorsError ||
             (productsError instanceof Error ? productsError.message : productsError) ||
             (versionsError instanceof Error ? versionsError.message : versionsError)}
          </p>
        </div>
      </div>
    )
  }

  return (
    <PageAccessGuard pageName="vendor-management" redirectTo="/overview">
      <div className="overview-container">
        {/* Header */}
        <PageHeader
          title="Vendor & Product Management"
          subtitle="Manage vendors, products, and product versions with hierarchical relationships"
        />

        {/* Tab Navigation */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('vendors')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'vendors'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Vendors ({vendors?.length || 0})
              </button>
              <button
                onClick={() => setActiveTab('products')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'products'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Products ({products?.length || 0})
              </button>
              <button
                onClick={() => setActiveTab('versions')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'versions'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Versions ({versions?.length || 0})
              </button>
            </nav>
          </div>
        </div>

        {/* Vendors Tab */}
        {activeTab === 'vendors' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-lg font-medium text-gray-900">Vendors</h2>
              <Button
                onClick={() => setShowAddVendorModal(true)}
                className="btn-primary"
              >
                ➕ Add Vendor
              </Button>
            </div>

            {vendorsLoading ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            ) : (
              <div className="bg-white shadow rounded-lg overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Vendor
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Contact
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Location
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {vendors?.map((vendor) => (
                      <tr key={vendor.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {vendor.display_name || vendor.name}
                            </div>
                            <div className="text-sm text-gray-500">{vendor.name}</div>
                            {vendor.website && (
                              <div className="text-sm text-blue-600">
                                <a href={vendor.website} target="_blank" rel="noopener noreferrer">
                                  {vendor.website}
                                </a>
                              </div>
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {vendor.contact_email && (
                              <div>{vendor.contact_email}</div>
                            )}
                            {vendor.contact_phone && (
                              <div>{vendor.contact_phone}</div>
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {vendor.city && vendor.state && (
                              <div>{vendor.city}, {vendor.state}</div>
                            )}
                            {vendor.country && (
                              <div>{vendor.country}</div>
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button
                            onClick={() => setSelectedVendor(vendor)}
                            className="text-blue-600 hover:text-blue-900 mr-3"
                          >
                            Edit
                          </button>
                          <button
                            onClick={() => {
                              setSelectedVendor(vendor)
                              setActiveTab('products')
                            }}
                            className="text-green-600 hover:text-green-900"
                          >
                            View Products
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}

        {/* Products Tab */}
        {activeTab === 'products' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-lg font-medium text-gray-900">Products</h2>
              <Button
                onClick={() => setShowAddProductModal(true)}
                className="btn-primary"
              >
                ➕ Add Product
              </Button>
            </div>

            {productsLoading ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            ) : (
              <div className="bg-white shadow rounded-lg overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Product
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Vendor
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Details
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {products?.map((product) => (
                      <tr key={product.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {product.name}
                            </div>
                            {product.description && (
                              <div className="text-sm text-gray-500">{product.description}</div>
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{product.vendor_name}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {product.category && (
                              <div>Category: {product.category}</div>
                            )}
                            {product.sku && (
                              <div>SKU: {product.sku}</div>
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button
                            onClick={() => setSelectedProduct(product)}
                            className="text-blue-600 hover:text-blue-900 mr-3"
                          >
                            Edit
                          </button>
                          <button
                            onClick={() => {
                              setSelectedProduct(product)
                              setActiveTab('versions')
                            }}
                            className="text-green-600 hover:text-green-900"
                          >
                            View Versions
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}

        {/* Versions Tab */}
        {activeTab === 'versions' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-lg font-medium text-gray-900">Product Versions</h2>
              <Button
                onClick={() => setShowAddVersionModal(true)}
                className="btn-primary"
              >
                ➕ Add Version
              </Button>
            </div>

            {versionsLoading ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            ) : (
              <div className="bg-white shadow rounded-lg overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Version
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Product
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Vendor
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {versions?.map((version) => (
                      <tr key={version.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {version.version}
                            </div>
                            {version.release_date && (
                              <div className="text-sm text-gray-500">
                                Released: {new Date(version.release_date).toLocaleDateString()}
                              </div>
                            )}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{version.product_name}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">{version.vendor_name}</div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            version.is_current
                              ? 'bg-green-100 text-green-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}>
                            {version.is_current ? 'Current' : 'Legacy'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button
                            className="text-blue-600 hover:text-blue-900"
                          >
                            Edit
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}

        {/* Add Vendor Modal */}
        <AddVendorModal
          isOpen={showAddVendorModal}
          onClose={() => setShowAddVendorModal(false)}
          onSubmit={handleAddVendor}
        />

        {/* Add Product Modal */}
        <AddProductModal
          isOpen={showAddProductModal}
          onClose={() => setShowAddProductModal(false)}
          onSubmit={handleAddProduct}
          vendors={vendors || []}
        />

        {/* Add Version Modal */}
        <AddVersionModal
          isOpen={showAddVersionModal}
          onClose={() => setShowAddVersionModal(false)}
          onSubmit={handleAddVersion}
          products={products || []}
        />
      </div>
    </PageAccessGuard>
  )
}

// Add Vendor Modal Component
function AddVendorModal({
  isOpen,
  onClose,
  onSubmit
}: {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: any) => void
}) {
  const [formData, setFormData] = useState({
    name: '',
    display_name: '',
    tax_id: '',
    domain: '',
    address: '',
    city: '',
    state: '',
    country: '',
    contact_email: '',
    contact_phone: '',
    website: ''
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData.name.trim()) return
    
    onSubmit({
      name: formData.name.trim(),
      display_name: formData.display_name.trim() || undefined,
      tax_id: formData.tax_id.trim() || undefined,
      domain: formData.domain.trim() || undefined,
      address: formData.address.trim() || undefined,
      city: formData.city.trim() || undefined,
      state: formData.state.trim() || undefined,
      country: formData.country.trim() || undefined,
      contact_email: formData.contact_email.trim() || undefined,
      contact_phone: formData.contact_phone.trim() || undefined,
      website: formData.website.trim() || undefined
    })
    
    setFormData({
      name: '',
      display_name: '',
      tax_id: '',
      domain: '',
      address: '',
      city: '',
      state: '',
      country: '',
      contact_email: '',
      contact_phone: '',
      website: ''
    })
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Add Vendor">
      <form onSubmit={handleSubmit} className="space-y-4">
        <Form.Field>
          <Form.Label htmlFor="name" required>Vendor Name</Form.Label>
          <Form.Input
            id="name"
            value={formData.name}
            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
            placeholder="Enter vendor name"
            required
          />
        </Form.Field>

        <Form.Field>
          <Form.Label htmlFor="display_name">Display Name</Form.Label>
          <Form.Input
            id="display_name"
            value={formData.display_name}
            onChange={(e) => setFormData(prev => ({ ...prev, display_name: e.target.value }))}
            placeholder="Enter display name (optional)"
          />
        </Form.Field>

        <Form.Actions>
          <Button type="button" variant="secondary" onClick={onClose}>
            Cancel
          </Button>
          <Button type="submit" variant="primary">
            Add Vendor
          </Button>
        </Form.Actions>
      </form>
    </Modal>
  )
}

// Add Product Modal Component
function AddProductModal({
  isOpen,
  onClose,
  onSubmit,
  vendors
}: {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: any) => void
  vendors: Vendor[]
}) {
  const [formData, setFormData] = useState({
    vendor_id: '',
    name: '',
    description: '',
    category: '',
    sku: '',
    barcode: '',
    unit_of_measure: ''
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData.name.trim() || !formData.vendor_id) return

    onSubmit({
      vendor_id: parseInt(formData.vendor_id),
      name: formData.name.trim(),
      description: formData.description.trim() || undefined,
      category: formData.category.trim() || undefined,
      sku: formData.sku.trim() || undefined,
      barcode: formData.barcode.trim() || undefined,
      unit_of_measure: formData.unit_of_measure.trim() || undefined
    })

    setFormData({
      vendor_id: '',
      name: '',
      description: '',
      category: '',
      sku: '',
      barcode: '',
      unit_of_measure: ''
    })
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Add Product">
      <form onSubmit={handleSubmit} className="space-y-4">
        <Form.Field>
          <Form.Label htmlFor="vendor_id" required>Vendor</Form.Label>
          <Form.Select
            id="vendor_id"
            value={formData.vendor_id}
            onChange={(e) => setFormData(prev => ({ ...prev, vendor_id: e.target.value }))}
            required
          >
            <option value="">Select a vendor</option>
            {vendors?.map((vendor) => (
              <option key={vendor.id} value={vendor.id}>
                {vendor.display_name || vendor.name}
              </option>
            ))}
          </Form.Select>
        </Form.Field>

        <Form.Field>
          <Form.Label htmlFor="product_name" required>Product Name</Form.Label>
          <Form.Input
            id="product_name"
            value={formData.name}
            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
            placeholder="Enter product name"
            required
          />
        </Form.Field>

        <Form.Field>
          <Form.Label htmlFor="description">Description</Form.Label>
          <Form.Textarea
            id="description"
            value={formData.description}
            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            placeholder="Enter product description (optional)"
            rows={3}
          />
        </Form.Field>

        <div className="grid grid-cols-2 gap-4">
          <Form.Field>
            <Form.Label htmlFor="category">Category</Form.Label>
            <Form.Input
              id="category"
              value={formData.category}
              onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
              placeholder="Product category"
            />
          </Form.Field>

          <Form.Field>
            <Form.Label htmlFor="sku">SKU</Form.Label>
            <Form.Input
              id="sku"
              value={formData.sku}
              onChange={(e) => setFormData(prev => ({ ...prev, sku: e.target.value }))}
              placeholder="Stock keeping unit"
            />
          </Form.Field>
        </div>

        <Form.Actions>
          <Button type="button" variant="secondary" onClick={onClose}>
            Cancel
          </Button>
          <Button type="submit" variant="primary">
            Add Product
          </Button>
        </Form.Actions>
      </form>
    </Modal>
  )
}

// Add Version Modal Component
function AddVersionModal({
  isOpen,
  onClose,
  onSubmit,
  products
}: {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: any) => void
  products: Product[]
}) {
  const [formData, setFormData] = useState({
    product_id: '',
    version: '',
    release_date: '',
    notes: '',
    is_current: false
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData.version.trim() || !formData.product_id) return

    onSubmit({
      product_id: parseInt(formData.product_id),
      version: formData.version.trim(),
      release_date: formData.release_date || undefined,
      notes: formData.notes.trim() || undefined,
      is_current: formData.is_current
    })

    setFormData({
      product_id: '',
      version: '',
      release_date: '',
      notes: '',
      is_current: false
    })
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Add Product Version">
      <form onSubmit={handleSubmit} className="space-y-4">
        <Form.Field>
          <Form.Label htmlFor="product_id" required>Product</Form.Label>
          <Form.Select
            id="product_id"
            value={formData.product_id}
            onChange={(e) => setFormData(prev => ({ ...prev, product_id: e.target.value }))}
            required
          >
            <option value="">Select a product</option>
            {products?.map((product) => (
              <option key={product.id} value={product.id}>
                {product.vendor_name} - {product.name}
              </option>
            ))}
          </Form.Select>
        </Form.Field>

        <Form.Field>
          <Form.Label htmlFor="version" required>Version</Form.Label>
          <Form.Input
            id="version"
            value={formData.version}
            onChange={(e) => setFormData(prev => ({ ...prev, version: e.target.value }))}
            placeholder="e.g., 1.0.0, 2023.1, v3.2"
            required
          />
        </Form.Field>

        <Form.Field>
          <Form.Label htmlFor="release_date">Release Date</Form.Label>
          <Form.Input
            id="release_date"
            type="date"
            value={formData.release_date}
            onChange={(e) => setFormData(prev => ({ ...prev, release_date: e.target.value }))}
          />
        </Form.Field>

        <Form.Field>
          <Form.Label htmlFor="notes">Notes</Form.Label>
          <Form.Textarea
            id="notes"
            value={formData.notes}
            onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
            placeholder="Version notes or changelog (optional)"
            rows={3}
          />
        </Form.Field>

        <Form.Field>
          <div className="flex items-center space-x-2">
            <Form.Checkbox
              id="is_current"
              checked={formData.is_current}
              onChange={(e) => setFormData(prev => ({ ...prev, is_current: e.target.checked }))}
            />
            <Form.Label htmlFor="is_current">Mark as current version</Form.Label>
          </div>
        </Form.Field>

        <Form.Actions>
          <Button type="button" variant="secondary" onClick={onClose}>
            Cancel
          </Button>
          <Button type="submit" variant="primary">
            Add Version
          </Button>
        </Form.Actions>
      </form>
    </Modal>
  )
}

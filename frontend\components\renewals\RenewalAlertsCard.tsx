/**
 * <PERSON><PERSON> Alerts Card Component
 *
 * Displays configured alerts for the renewal
 */

'use client'

import React, { useState, useEffect } from 'react'
import { Renewal } from '@/lib/types'
import { useData } from '@/lib/hooks/useData'
import { TENANT_ENDPOINTS } from '@/lib/constants/api-endpoints'
import AddAlertModal from './AddAlertModal'

interface RenewalAlertsCardProps {
  renewal: Renewal
}

interface TenantAlert {
  alert_id: number
  renewal_id: number
  alert_name: string
  days_before_renewal: number
  email_recipients: string[]
  is_active: boolean
  last_sent?: string
  created_on: string
  changed_on: string
  created_by?: string
  modified_by?: string
}

export default function RenewalAlertsCard({ renewal }: RenewalAlertsCardProps) {
  const [showAddAlert, setShowAddAlert] = useState(false)

  // Fetch alerts for this renewal
  const {
    data: alerts = [],
    loading: alertsLoading,
    error: alertsError,
    refetch: refetchAlerts
  } = useData<TenantAlert[]>({
    endpoint: `${TENANT_ENDPOINTS.ALERTS}?renewal_id=${renewal.id}`,
    cache: {
      key: `alerts-${renewal.id}`
    },
    dependencies: [renewal.id]
  })

  // Handle creating new alert
  const handleCreateAlert = async (alertData: {
    alert_name: string
    days_before_renewal: number
    email_recipients: string[]
  }) => {
    try {
      const response = await fetch(TENANT_ENDPOINTS.ALERTS, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          renewal_id: renewal.id,
          ...alertData
        }),
      })

      if (response.ok) {
        await refetchAlerts()
        setShowAddAlert(false)
      } else {
        console.error('Failed to create alert')
      }
    } catch (error) {
      console.error('Error creating alert:', error)
    }
  }

  // Handle toggling alert active status
  const handleToggleAlert = async (alertId: number, isActive: boolean) => {
    try {
      const response = await fetch(`${TENANT_ENDPOINTS.ALERTS}/${alertId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          is_active: !isActive
        }),
      })

      if (response.ok) {
        await refetchAlerts()
      } else {
        console.error('Failed to update alert')
      }
    } catch (error) {
      console.error('Error updating alert:', error)
    }
  }

  // Handle deleting alert
  const handleDeleteAlert = async (alertId: number) => {
    if (!confirm('Are you sure you want to delete this alert?')) {
      return
    }

    try {
      const response = await fetch(`${TENANT_ENDPOINTS.ALERTS}/${alertId}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        await refetchAlerts()
      } else {
        console.error('Failed to delete alert')
      }
    } catch (error) {
      console.error('Error deleting alert:', error)
    }
  }

  const getAlertIcon = () => {
    return (
      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
      </svg>
    )
  }

  const formatLastSent = (dateString?: string) => {
    if (!dateString) return 'Never sent'
    
    const date = new Date(dateString)
    const now = new Date()
    const diffTime = now.getTime() - date.getTime()
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays === 0) {
      return 'Sent today'
    } else if (diffDays === 1) {
      return 'Sent yesterday'
    } else if (diffDays < 7) {
      return `Sent ${diffDays} days ago`
    } else {
      return `Sent ${date.toLocaleDateString()}`
    }
  }

  if (alertsLoading) {
    return (
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
          </div>
        </div>
      </div>
    )
  }

  if (alertsError) {
    return (
      <div className="bg-white rounded-lg border border-red-200 p-6">
        <div className="text-red-600 text-sm">
          Error loading alerts: {alertsError?.message || 'Unknown error'}
        </div>
      </div>
    )
  }

  return (
    <div className="renewal-alerts-card bg-white rounded-lg border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
            <span className="text-yellow-600 text-lg">🔔</span>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Alerts</h3>
            <p className="text-sm text-gray-500">
              {alerts?.length || 0} alert{(alerts?.length || 0) !== 1 ? 's' : ''} configured
            </p>
          </div>
        </div>

        <button
          onClick={() => setShowAddAlert(true)}
          className="px-4 py-2 text-sm font-medium text-blue-600 border border-blue-600 rounded-lg hover:bg-blue-50 transition-colors"
        >
          Add Alert
        </button>
      </div>

      {(alerts?.length || 0) === 0 ? (
        <div className="text-center py-8">
          <div className="text-gray-400 text-4xl mb-2">🔔</div>
          <p className="text-gray-500 text-sm">No alerts configured</p>
          <p className="text-gray-400 text-xs mt-1">
            Add your first alert to get notified before renewal
          </p>
          <button
            onClick={() => setShowAddAlert(true)}
            className="mt-3 px-4 py-2 text-sm text-blue-600 border border-blue-600 rounded-lg hover:bg-blue-50 transition-colors"
          >
            Add your first alert
          </button>
        </div>
      ) : (
        <div className="space-y-4">
          {alerts?.map((alert) => (
            <div
              key={alert.alert_id}
              className={`border rounded-lg p-4 ${
                alert.is_active ? 'border-green-200 bg-green-50' : 'border-gray-200 bg-gray-50'
              }`}
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3">
                  <div className={`p-2 rounded-lg ${
                    alert.is_active ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-600'
                  }`}>
                    {getAlertIcon()}
                  </div>

                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium text-gray-900">
                        {alert.alert_name}
                      </span>
                      <span className={`px-2 py-1 text-xs rounded-full ${
                        alert.is_active
                          ? 'bg-green-100 text-green-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {alert.is_active ? 'Active' : 'Inactive'}
                      </span>
                    </div>

                    <p className="text-sm text-gray-600 mt-1">
                      {alert.days_before_renewal} days before renewal
                    </p>

                    <div className="mt-2">
                      <p className="text-xs text-gray-500">Recipients:</p>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {alert.email_recipients.map((recipient, index) => (
                          <span
                            key={index}
                            className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded"
                          >
                            {recipient}
                          </span>
                        ))}
                      </div>
                    </div>

                    <p className="text-xs text-gray-500 mt-2">
                      {formatLastSent(alert.last_sent)}
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handleToggleAlert(alert.alert_id, alert.is_active)}
                    className={`text-sm px-2 py-1 rounded transition-colors ${
                      alert.is_active
                        ? 'text-orange-600 hover:text-orange-700'
                        : 'text-green-600 hover:text-green-700'
                    }`}
                    title={alert.is_active ? 'Deactivate alert' : 'Activate alert'}
                  >
                    {alert.is_active ? 'Deactivate' : 'Activate'}
                  </button>
                  <button
                    onClick={() => handleDeleteAlert(alert.alert_id)}
                    className="text-gray-400 hover:text-red-600 transition-colors"
                    title="Delete alert"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Add Alert Modal */}
      <AddAlertModal
        isOpen={showAddAlert}
        onClose={() => setShowAddAlert(false)}
        onSubmit={handleCreateAlert}
      />
    </div>
  )
}

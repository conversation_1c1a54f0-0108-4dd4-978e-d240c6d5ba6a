'use client'

import { useState, useEffect } from 'react'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/badge'
import { Table, TableHeader, TableBody, TableRow, TableHead, TableCell } from '@/components/ui/table'
import { Form } from '@/components/ui/Form'
import { useAuth, useTenant } from '@/lib/hooks'
import { formatDate } from '@/lib/utils'

interface ClientLicense {
  client_license_id: number
  name: string
  license_key: string
  license_type: string
  max_renewals: number
  term_name: string
  term_months: number
  activation_date: string
  expiry_date: string
  is_active: boolean
  current_usage: number
  usage_percentage: number
  days_until_expiry: number
  status: 'ACTIVE' | 'EXPIRED' | 'EXPIRING_SOON'
}

export default function AdminLicensesPage() {
  const { user } = useAuth()
  const { tenant } = useTenant()
  const [licenses, setLicenses] = useState<ClientLicense[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [filters, setFilters] = useState({
    status: '',
    search: ''
  })

  useEffect(() => {
    if (user && tenant) {
      fetchLicenses()
    }
  }, [user, tenant, filters])

  const fetchLicenses = async () => {
    try {
      setLoading(true)
      setError(null)

      const params = new URLSearchParams()
      if (filters.status) params.append('status', filters.status)
      if (filters.search) params.append('search', filters.search)

      const response = await fetch(`/api/admin/client-licenses?${params}`)
      const data = await response.json()

      if (data.success) {
        setLicenses(data.data)
      } else {
        setError(data.error || 'Failed to fetch licenses')
      }
    } catch (err) {
      console.error('Error fetching licenses:', err)
      setError('Failed to fetch licenses')
    } finally {
      setLoading(false)
    }
  }

  const getStatusBadge = (license: ClientLicense) => {
    switch (license.status) {
      case 'ACTIVE':
        return <Badge variant="success">Active</Badge>
      case 'EXPIRED':
        return <Badge variant="destructive">Expired</Badge>
      case 'EXPIRING_SOON':
        return <Badge variant="warning">Expiring Soon</Badge>
      default:
        return <Badge variant="secondary">Unknown</Badge>
    }
  }

  const getUsageBadge = (percentage: number) => {
    if (percentage >= 90) return <Badge variant="destructive">{percentage}%</Badge>
    if (percentage >= 75) return <Badge variant="warning">{percentage}%</Badge>
    if (percentage >= 50) return <Badge variant="secondary">{percentage}%</Badge>
    return <Badge variant="success">{percentage}%</Badge>
  }

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center h-64">
          <div className="text-lg">Loading licenses...</div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">License Management</h1>
          <p className="text-muted-foreground">
            View and monitor your assigned licenses
          </p>
        </div>
      </div>

      {/* Filters */}
      <Card className="p-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Form.Field>
            <Form.Label htmlFor="status">Status Filter</Form.Label>
            <Form.Select
              id="status"
              value={filters.status}
              onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
              placeholder="All statuses"
            >
              <option value="">All statuses</option>
              <option value="ACTIVE">Active</option>
              <option value="EXPIRING_SOON">Expiring Soon</option>
              <option value="EXPIRED">Expired</option>
            </Form.Select>
          </Form.Field>
          
          <Form.Field>
            <Form.Label htmlFor="search">Search</Form.Label>
            <Form.Input
              id="search"
              type="text"
              value={filters.search}
              onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
              placeholder="Search by client name or license key..."
            />
          </Form.Field>
        </div>
      </Card>

      {error && (
        <Card className="p-4 border-red-200 bg-red-50">
          <p className="text-red-600">{error}</p>
        </Card>
      )}

      {/* Licenses Table */}
      <Card>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Client</TableHead>
              <TableHead>License Type</TableHead>
              <TableHead>Term</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Usage</TableHead>
              <TableHead>Activation Date</TableHead>
              <TableHead>Expiry Date</TableHead>
              <TableHead>Days Left</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {licenses.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-8">
                  No licenses found
                </TableCell>
              </TableRow>
            ) : (
              licenses.map((license) => (
                <TableRow key={license.client_license_id}>
                  <TableCell>
                    <div>
                      <div className="font-medium">{license.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {license.license_key}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{license.license_type}</div>
                      <div className="text-sm text-muted-foreground">
                        {license.max_renewals.toLocaleString()} renewals
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>{license.term_name}</TableCell>
                  <TableCell>{getStatusBadge(license)}</TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      {getUsageBadge(license.usage_percentage)}
                      <div className="text-xs text-muted-foreground">
                        {license.current_usage.toLocaleString()} / {license.max_renewals.toLocaleString()}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>{formatDate(license.activation_date)}</TableCell>
                  <TableCell>{formatDate(license.expiry_date)}</TableCell>
                  <TableCell>
                    <span className={
                      license.days_until_expiry < 30 
                        ? 'text-red-600 font-medium' 
                        : license.days_until_expiry < 90 
                        ? 'text-yellow-600 font-medium' 
                        : 'text-green-600'
                    }>
                      {license.days_until_expiry > 0 
                        ? `${license.days_until_expiry} days` 
                        : 'Expired'
                      }
                    </span>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </Card>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="text-2xl font-bold text-green-600">
            {licenses.filter(l => l.status === 'ACTIVE').length}
          </div>
          <div className="text-sm text-muted-foreground">Active Licenses</div>
        </Card>
        <Card className="p-4">
          <div className="text-2xl font-bold text-yellow-600">
            {licenses.filter(l => l.status === 'EXPIRING_SOON').length}
          </div>
          <div className="text-sm text-muted-foreground">Expiring Soon</div>
        </Card>
        <Card className="p-4">
          <div className="text-2xl font-bold text-red-600">
            {licenses.filter(l => l.status === 'EXPIRED').length}
          </div>
          <div className="text-sm text-muted-foreground">Expired</div>
        </Card>
        <Card className="p-4">
          <div className="text-2xl font-bold text-blue-600">
            {licenses.reduce((sum, l) => sum + l.current_usage, 0).toLocaleString()}
          </div>
          <div className="text-sm text-muted-foreground">Total Usage</div>
        </Card>
      </div>
    </div>
  )
}

/**
 * Secure Configuration Service
 * 
 * Provides secure access to application configuration using AWS services
 * with fallback to environment variables for development.
 */

import { parameterStoreService, getSecureConfig, secretsManager } from './secrets-manager';
import { DATABASE, SESSION } from '@/lib/constants/app-constants';

export interface SecureApplicationConfig {
  database: {
    host: string;
    port: number;
    database: string;
    username: string;
    password?: string;
    useIAM: boolean;
    ssl: boolean;
  };
  auth: {
    jwtSecret: string;
    tokenExpiration: number;
    refreshTokenExpiration: number;
    cognitoDomain: string;
    userPoolId: string;
    clientId: string;
  };
  encryption: {
    key: string;
    algorithm: string;
  };
  apiKeys: {
    external: Record<string, string>;
    internal: Record<string, string>;
  };
  monitoring: {
    logLevel: string;
    enableMetrics: boolean;
    enableTracing: boolean;
  };
  security: {
    enableRateLimit: boolean;
    maxRequestsPerMinute: number;
    enableCSRF: boolean;
    enableCORS: boolean;
    allowedOrigins: string[];
  };
}

class SecureConfigService {
  private config: SecureApplicationConfig | null = null;
  private initialized = false;

  /**
   * Initialize secure configuration
   */
  async initialize(): Promise<void> {
    if (this.initialized) return;

    console.log('🔐 Initializing secure configuration...');

    try {
      // In development, skip AWS Parameter Store and use environment variables
      const isDevelopment = process.env.NODE_ENV === 'development';

      let dbConfig = null;
      let appSecrets = null;

      if (!isDevelopment) {
        try {
          // Get database configuration from Parameter Store
          dbConfig = await parameterStoreService.getDatabaseConfig();
          // Get application parameters from Parameter Store
          appSecrets = await parameterStoreService.getApplicationSecrets();
        } catch (error) {
          console.warn('⚠️ Failed to load from Parameter Store, falling back to environment variables:', error);
        }
      } else {
        console.log('🔧 Development mode: Using environment variables instead of Parameter Store');
      }

      // Build secure configuration
      this.config = {
        database: {
          host: dbConfig?.host || await getSecureConfig('DB_HOST', '/renewtrack/database/host') || DATABASE.DEFAULT_HOST,
          port: dbConfig?.port || parseInt(await getSecureConfig('DB_PORT', '/renewtrack/database/port') || DATABASE.DEFAULT_PORT.toString()),
          database: dbConfig?.database || await getSecureConfig('DB_NAME', '/renewtrack/database/name') || DATABASE.DEFAULT_NAME,
          username: dbConfig?.username || await getSecureConfig('DB_USER', '/renewtrack/database/username') || DATABASE.DEFAULT_USER,
          password: dbConfig?.password,
          useIAM: dbConfig?.useIAM || (await getSecureConfig('USE_IAM_DB_AUTH', '/renewtrack/database/use-iam') === 'true'),
          ssl: (await getSecureConfig('DB_SSL', '/renewtrack/database/ssl') === 'true')
        },
        auth: {
          jwtSecret: appSecrets?.jwtSecret || await getSecureConfig('JWT_SECRET', '/renewtrack/jwt-secret', true) || '',
          tokenExpiration: parseInt(await getSecureConfig('JWT_EXPIRATION', '/renewtrack/auth/token-expiration') || '3600'),
          refreshTokenExpiration: parseInt(await getSecureConfig('REFRESH_TOKEN_EXPIRATION', '/renewtrack/auth/refresh-token-expiration') || SESSION.DEFAULT_MAX_AGE.toString()),
          cognitoDomain: await getSecureConfig('NEXT_PUBLIC_AWS_COGNITO_DOMAIN', '/renewtrack/auth/cognito-domain', true) || '',
          userPoolId: await getSecureConfig('NEXT_PUBLIC_AWS_USER_POOLS_ID', '/renewtrack/auth/user-pool-id', true) || '',
          clientId: await getSecureConfig('NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID', '/renewtrack/auth/client-id', true) || ''
        },
        encryption: {
          key: appSecrets?.encryptionKey || await getSecureConfig('ENCRYPTION_KEY', '/renewtrack/encryption-key', true) || '',
          algorithm: await getSecureConfig('ENCRYPTION_ALGORITHM', '/renewtrack/encryption-algorithm') || 'aes-256-gcm'
        },
        apiKeys: {
          external: appSecrets?.apiKeys || {},
          internal: {
            adminApi: await getSecureConfig('ADMIN_API_KEY', '/renewtrack/api-keys/admin') || '',
            webhookSecret: await getSecureConfig('WEBHOOK_SECRET', '/renewtrack/api-keys/webhook') || ''
          }
        },
        monitoring: {
          logLevel: await getSecureConfig('LOG_LEVEL', '/renewtrack/monitoring/log-level') || 'info',
          enableMetrics: (await getSecureConfig('ENABLE_METRICS', '/renewtrack/monitoring/enable-metrics') === 'true'),
          enableTracing: (await getSecureConfig('ENABLE_TRACING', '/renewtrack/monitoring/enable-tracing') === 'true')
        },
        security: {
          enableRateLimit: (await getSecureConfig('ENABLE_RATE_LIMIT', '/renewtrack/security/enable-rate-limit') !== 'false'),
          maxRequestsPerMinute: parseInt(await getSecureConfig('MAX_REQUESTS_PER_MINUTE', '/renewtrack/security/max-requests-per-minute') || '100'),
          enableCSRF: (await getSecureConfig('ENABLE_CSRF', '/renewtrack/security/enable-csrf') !== 'false'),
          enableCORS: (await getSecureConfig('ENABLE_CORS', '/renewtrack/security/enable-cors') !== 'false'),
          allowedOrigins: (await getSecureConfig('ALLOWED_ORIGINS', '/renewtrack/security/allowed-origins') || 'http://localhost:3000').split(',')
        }
      };

      this.initialized = true;
      console.log('✅ Secure configuration initialized successfully');

    } catch (error) {
      console.error('❌ Error initializing secure configuration:', error);
      throw new Error('Failed to initialize secure configuration');
    }
  }

  /**
   * Get secure configuration
   */
  async getConfig(): Promise<SecureApplicationConfig> {
    if (!this.initialized) {
      await this.initialize();
    }

    if (!this.config) {
      throw new Error('Secure configuration not available');
    }

    return this.config;
  }

  /**
   * Get database configuration
   */
  async getDatabaseConfig() {
    const config = await this.getConfig();
    return config.database;
  }

  /**
   * Get authentication configuration
   */
  async getAuthConfig() {
    const config = await this.getConfig();
    return config.auth;
  }

  /**
   * Get encryption configuration
   */
  async getEncryptionConfig() {
    const config = await this.getConfig();
    return config.encryption;
  }

  /**
   * Get API keys
   */
  async getApiKeys() {
    const config = await this.getConfig();
    return config.apiKeys;
  }

  /**
   * Get monitoring configuration
   */
  async getMonitoringConfig() {
    const config = await this.getConfig();
    return config.monitoring;
  }

  /**
   * Get security configuration
   */
  async getSecurityConfig() {
    const config = await this.getConfig();
    return config.security;
  }

  /**
   * Refresh configuration from AWS
   */
  async refresh(): Promise<void> {
    console.log('🔄 Refreshing secure configuration...');
    
    // Clear secrets cache
    secretsManager.clearCache();
    
    // Reset initialization state
    this.initialized = false;
    this.config = null;
    
    // Re-initialize
    await this.initialize();
    
    console.log('✅ Secure configuration refreshed');
  }

  /**
   * Validate configuration
   */
  async validate(): Promise<{
    valid: boolean;
    errors: string[];
    warnings: string[];
  }> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      const config = await this.getConfig();

      // Validate database configuration
      if (!config.database.host) {
        errors.push('Database host is required');
      }
      if (!config.database.username) {
        errors.push('Database username is required');
      }
      if (!config.database.useIAM && !config.database.password) {
        errors.push('Database password is required when not using IAM authentication');
      }

      // Validate authentication configuration
      if (!config.auth.jwtSecret) {
        errors.push('JWT secret is required');
      }
      if (!config.auth.userPoolId) {
        errors.push('Cognito User Pool ID is required');
      }
      if (!config.auth.clientId) {
        errors.push('Cognito Client ID is required');
      }

      // Validate encryption configuration
      if (!config.encryption.key) {
        errors.push('Encryption key is required');
      }

      // Check for weak configurations
      if (config.auth.jwtSecret && config.auth.jwtSecret.length < 32) {
        warnings.push('JWT secret should be at least 32 characters long');
      }
      if (config.encryption.key && config.encryption.key.length < 32) {
        warnings.push('Encryption key should be at least 32 characters long');
      }
      if (!config.security.enableRateLimit) {
        warnings.push('Rate limiting is disabled');
      }
      if (!config.security.enableCSRF) {
        warnings.push('CSRF protection is disabled');
      }

      return {
        valid: errors.length === 0,
        errors,
        warnings
      };

    } catch (error) {
      return {
        valid: false,
        errors: [`Configuration validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
        warnings: []
      };
    }
  }

  /**
   * Get configuration status
   */
  getStatus(): {
    initialized: boolean;
    cacheStats: any;
  } {
    return {
      initialized: this.initialized,
      cacheStats: secretsManager.getCacheStats()
    };
  }
}

// Global secure configuration service
export const secureConfig = new SecureConfigService();

/**
 * Utility function to ensure secure configuration is initialized
 */
export async function ensureSecureConfig(): Promise<SecureApplicationConfig> {
  return await secureConfig.getConfig();
}

/**
 * Middleware to validate secure configuration on startup
 */
export async function validateSecureConfigOnStartup(): Promise<void> {
  console.log('🔍 Validating secure configuration on startup...');
  
  const validation = await secureConfig.validate();
  
  if (!validation.valid) {
    console.error('❌ Secure configuration validation failed:');
    validation.errors.forEach(error => console.error(`  - ${error}`));
    throw new Error('Invalid secure configuration');
  }
  
  if (validation.warnings.length > 0) {
    console.warn('⚠️ Secure configuration warnings:');
    validation.warnings.forEach(warning => console.warn(`  - ${warning}`));
  }
  
  console.log('✅ Secure configuration validation passed');
}

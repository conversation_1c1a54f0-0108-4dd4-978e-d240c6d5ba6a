/**
 * Params Handler Utility
 * 
 * Provides safe handling of Next.js 15 route parameters to prevent
 * "Cannot assign to read only property 'params'" errors
 */

import { NextRequest } from 'next/server';

/**
 * Safely resolve params Promise in Next.js 15
 * This utility prevents "Cannot assign to read only property" errors
 */
export async function safeResolveParams<T>(
  params: Promise<T> | T
): Promise<T> {
  try {
    // If params is already resolved (synchronous), return it
    if (params && typeof params === 'object' && !('then' in params)) {
      return params as T;
    }
    
    // If params is a Promise, await it
    return await params;
  } catch (error) {
    console.error('Error resolving params:', error);
    throw new Error('Failed to resolve route parameters');
  }
}

/**
 * Create a safe copy of params object to prevent read-only property errors
 */
export function createSafeParamsCopy<T extends Record<string, any>>(
  params: T
): T {
  try {
    // Create a deep copy to avoid any reference issues
    return JSON.parse(JSON.stringify(params));
  } catch (error) {
    console.error('Error creating safe params copy:', error);
    // Fallback to shallow copy
    return { ...params };
  }
}

/**
 * Wrapper for API route handlers that safely handles params
 */
export function withSafeParams<T = any>(
  handler: (
    request: NextRequest,
    context: { params: T }
  ) => Promise<Response>
) {
  return async (
    request: NextRequest,
    routeContext: { params: Promise<T> | T }
  ): Promise<Response> => {
    try {
      // Safely resolve params
      const resolvedParams = await safeResolveParams(routeContext.params);
      
      // Create a safe copy to prevent read-only errors
      const safeParams = createSafeParamsCopy(resolvedParams);
      
      // Call the original handler with safe params
      return await handler(request, { params: safeParams });
    } catch (error) {
      console.error('Error in withSafeParams wrapper:', error);
      
      // Return a proper error response
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Failed to process route parameters',
          code: 'PARAMS_ERROR'
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
  };
}

/**
 * Extract ID parameter safely from params object
 */
export function extractIdParam(params: any): string | null {
  try {
    if (!params) return null;
    
    // Handle both direct access and array access
    const id = params.id || params['id'];
    
    if (Array.isArray(id)) {
      return id[0] || null;
    }
    
    return id || null;
  } catch (error) {
    console.error('Error extracting ID param:', error);
    return null;
  }
}

/**
 * Validate and extract numeric ID from params
 */
export function extractNumericId(params: any): number | null {
  try {
    const id = extractIdParam(params);
    if (!id) return null;
    
    const numericId = parseInt(id, 10);
    return isNaN(numericId) ? null : numericId;
  } catch (error) {
    console.error('Error extracting numeric ID:', error);
    return null;
  }
}

/**
 * Error handler specifically for params-related errors
 */
export function handleParamsError(error: any): Response {
  console.error('Params handling error:', error);
  
  // Check if it's the specific "Cannot assign to read only property" error
  if (error.message && error.message.includes('Cannot assign to read only property')) {
    return new Response(
      JSON.stringify({
        success: false,
        error: 'Route parameter handling error - this is a known Next.js 15 compatibility issue',
        code: 'READONLY_PARAMS_ERROR',
        details: 'The application is being updated to handle Next.js 15 parameter changes'
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
  
  // Generic params error
  return new Response(
    JSON.stringify({
      success: false,
      error: 'Failed to process route parameters',
      code: 'PARAMS_ERROR'
    }),
    {
      status: 400,
      headers: { 'Content-Type': 'application/json' }
    }
  );
}

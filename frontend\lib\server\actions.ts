'use server'

import { fetchAuthSession, fetchUserAttributes } from 'aws-amplify/auth'
import { cookies } from 'next/headers'
import * as crypto from 'crypto'

// ISSUE: CSRF token implementation could be strengthened
// RECOMMENDATION: Use double submit cookie pattern with secure, httpOnly flags

const verifyCSRFToken = async (formData: FormData) => {
  const cookieStore = await cookies();
  const storedToken = cookieStore.get('csrf-token')?.value;
  const formToken = formData.get('csrf-token') as string;

  if (!storedToken || !formToken || storedToken !== formToken) {
    // RECOMMENDATION: Add logging for security events
    console.error('CSRF token validation failed', {
      hasStoredToken: !!storedToken,
      hasFormToken: !!formToken,
      // Don't log the actual tokens for security reasons
    });
    throw new Error('Security validation failed. Please refresh the page and try again.');
  }

  return true;
}

// RECOMMENDATION: Add a function to generate CSRF tokens
export async function generateCSRFToken() {
  const token = crypto.randomBytes(32).toString('hex');

  // Set the cookie with proper security flags
  (await cookies()).set('csrf-token', token, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    path: '/',
    maxAge: 3600 // 1 hour
  });

  return token;
}

// Example of a secure server action
export async function updateUserProfile(formData: FormData) {
  // 1. Verify CSRF token
  await verifyCSRFToken(formData)
  
  // 2. Verify user session using Amplify
  try {
    const session = await fetchAuthSession()
    if (!session?.tokens?.idToken) {
      throw new Error('Authentication failed. Please sign in again.')
    }

    // 3. Check permissions for this action
    // For now, allowing authenticated users
    // Implementation note: Role checking can be implemented by examining
    // session.tokens.idToken.payload['cognito:groups'] for user roles
  } catch (error) {
    throw new Error('Authentication failed. Please sign in again.')
  }
  
  // 4. Sanitize and validate input data
  const name = String(formData.get('name'))
  if (!name || name.length < 2) {
    throw new Error('Please enter a valid name (at least 2 characters).')
  }
  
  // 5. Perform the action
  try {
    // Update user profile logic here
    return { success: true }
  } catch (error) {
    console.error('Profile update error:', error)
    throw new Error('Unable to update profile. Please try again later.')
  }
}


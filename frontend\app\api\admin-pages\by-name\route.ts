/**
 * Admin Pages By Name API
 * 
 * Get page information by page name
 */

import { NextRequest } from 'next/server'
import { createSuccessResponse, createErrorResponse, ApiErrorCode, HttpStatus } from '@/lib/api/response'
import { executeQuery } from '@/lib/database'

/**
 * GET /api/admin-pages/by-name?name=overview
 * Returns page information for the specified page name
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const pageName = searchParams.get('name')

    if (!pageName) {
      return createErrorResponse(
        'Name parameter is required',
        ApiErrorCode.VALIDATION_ERROR,
        HttpStatus.BAD_REQUEST
      )
    }

    const query = `
      SELECT
        id,
        name,
        header,
        description,
        route_path,
        display_order,
        icon_svg
      FROM metadata.pages
      WHERE name = $1
        AND status = 'A'
      LIMIT 1
    `

    const result = await executeQuery(query, [pageName])

    if (!result.success) {
      return createErrorResponse(
        'Database query failed',
        ApiErrorCode.INTERNAL_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR,
        result.error
      )
    }

    const pageData = result.data?.[0] || null

    return createSuccessResponse(pageData)

  } catch (error) {
    return createErrorResponse(
      'Failed to fetch page information',
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR,
      error instanceof Error ? error.message : 'Unknown error'
    )
  }
}

/**
 * @fileoverview Comprehensive form validation tests for renewal forms with new dropdown system
 * Tests the integration between Form.Select components, CascadingSelect, and validation logic
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { z } from 'zod';
import '@testing-library/jest-dom';

// Import components to test
import { Form } from '@/components/ui/Form';

// Zod already imported above

// Define validation schemas directly in test to avoid Next.js server dependencies
const nonEmptyStringSchema = z.string().min(1, 'This field is required');

const renewalCreateSchema = z.object({
  name: nonEmptyStringSchema.max(255, 'Name cannot exceed 255 characters'),
  vendor: nonEmptyStringSchema.max(255, 'Vendor cannot exceed 255 characters'),
  status: z.enum(['active', 'inactive', 'pending', 'expired']).default('active'),
  dueDate: z.string().datetime('Invalid date format').optional(),
  annualCost: z.number().min(0, 'Annual cost cannot be negative').optional(),
  description: z.string().max(1000, 'Description cannot exceed 1000 characters').optional(),
});

const renewalUpdateSchema = z.object({
  name: z.string().max(255, 'Name cannot exceed 255 characters').optional(),
  vendor: z.string().max(255, 'Vendor cannot exceed 255 characters').optional(),
  status: z.enum(['active', 'inactive', 'pending', 'expired']).optional(),
  dueDate: z.string().datetime('Invalid date format').optional(),
  annualCost: z.number().min(0, 'Annual cost cannot be negative').optional(),
  description: z.string().max(1000, 'Description cannot exceed 1000 characters').optional(),
});

// Mock dependencies
jest.mock('@/lib/hooks/useData', () => ({
  useData: jest.fn(() => ({
    data: [],
    loading: false,
    error: null,
    refetch: jest.fn()
  }))
}));

jest.mock('@/components/ui/Toast', () => ({
  useToast: () => ({
    success: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
    warning: jest.fn()
  })
}));

// Mock CascadingSelect component to avoid Next.js server dependencies
jest.mock('@/components/ui/CascadingSelect', () => ({
  CascadingSelect: ({ value, onChange, required, disabled }: any) => (
    <div data-testid="cascading-select">
      <select
        data-testid="vendor-select"
        value={value.vendorId || ''}
        onChange={(e) => onChange({ ...value, vendorId: e.target.value })}
        disabled={disabled}
      >
        <option value="">Select vendor</option>
        <option value="1">Microsoft</option>
        <option value="2">Adobe</option>
      </select>
      <select
        data-testid="product-select"
        value={value.productId || ''}
        onChange={(e) => onChange({ ...value, productId: e.target.value })}
        disabled={disabled || !value.vendorId}
      >
        <option value="">Select product</option>
        <option value="1">Office 365</option>
        <option value="2">Creative Cloud</option>
      </select>
      <select
        data-testid="version-select"
        value={value.versionId || ''}
        onChange={(e) => onChange({ ...value, versionId: e.target.value })}
        disabled={disabled || !value.productId}
      >
        <option value="">Select version</option>
        <option value="1">Business Premium</option>
        <option value="2">All Apps</option>
      </select>
    </div>
  )
}));

// Mock RenewalItemsManager component
jest.mock('@/components/ui/RenewalItemsManager', () => ({
  RenewalItemsManager: ({ items, onChange, disabled, vendorId }: any) => (
    <div data-testid="renewal-items-manager">
      <div data-testid="add-item-form">
        <input data-testid="quantity-input" defaultValue="1" />
        <button data-testid="add-item-button">Add Item</button>
      </div>
      <div data-testid="validation-errors">
        <div data-testid="product-error">Product is required</div>
        <div data-testid="version-error">Version is required</div>
        <div data-testid="quantity-error">Quantity must be greater than 0</div>
      </div>
    </div>
  )
}));

// Mock fetch for API calls
const mockFetch = jest.fn();
global.fetch = mockFetch;

describe('Renewal Form Validation Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockFetch.mockResolvedValue({
      ok: true,
      json: async () => ({ data: [], total: 0 })
    });
  });

  describe('Validation Schemas', () => {
    describe('renewalCreateSchema', () => {
      it('should validate required fields', () => {
        const invalidData = {
          name: '',
          vendor: '',
          status: 'active'
        };

        const result = renewalCreateSchema.safeParse(invalidData);
        expect(result.success).toBe(false);
        
        if (!result.success) {
          const errors = result.error.errors;
          expect(errors.some(e => e.path.includes('name'))).toBe(true);
          expect(errors.some(e => e.path.includes('vendor'))).toBe(true);
        }
      });

      it('should accept valid renewal data', () => {
        const validData = {
          name: 'Microsoft Office 365',
          vendor: 'Microsoft',
          status: 'active',
          dueDate: new Date().toISOString(),
          annualCost: 1200.00,
          description: 'Office productivity suite'
        };

        const result = renewalCreateSchema.safeParse(validData);
        expect(result.success).toBe(true);
      });

      it('should validate field length limits', () => {
        const invalidData = {
          name: 'a'.repeat(256), // Exceeds 255 character limit
          vendor: 'Microsoft',
          status: 'active'
        };

        const result = renewalCreateSchema.safeParse(invalidData);
        expect(result.success).toBe(false);
        
        if (!result.success) {
          expect(result.error.errors[0].message).toContain('cannot exceed 255 characters');
        }
      });

      it('should validate annual cost is non-negative', () => {
        const invalidData = {
          name: 'Test Renewal',
          vendor: 'Test Vendor',
          status: 'active',
          annualCost: -100
        };

        const result = renewalCreateSchema.safeParse(invalidData);
        expect(result.success).toBe(false);
        
        if (!result.success) {
          expect(result.error.errors[0].message).toContain('cannot be negative');
        }
      });
    });

    describe('renewalUpdateSchema', () => {
      it('should allow partial updates', () => {
        const partialData = {
          name: 'Updated Name',
          status: 'inactive'
        };

        const result = renewalUpdateSchema.safeParse(partialData);
        expect(result.success).toBe(true);
      });

      it('should validate field constraints on updates', () => {
        const invalidData = {
          name: 'a'.repeat(256),
          annualCost: -50
        };

        const result = renewalUpdateSchema.safeParse(invalidData);
        expect(result.success).toBe(false);
        
        if (!result.success) {
          const errors = result.error.errors;
          expect(errors.some(e => e.message.includes('cannot exceed 255 characters'))).toBe(true);
          expect(errors.some(e => e.message.includes('cannot be negative'))).toBe(true);
        }
      });
    });
  });

  describe('Form.Select Validation', () => {
    it('should display error state when validation fails', () => {
      const TestForm = () => (
        <Form.Field>
          <Form.Label htmlFor="test-select">Test Select</Form.Label>
          <Form.Select
            id="test-select"
            error="This field is required"
            placeholder="Select an option"
          >
            <option value="option1">Option 1</option>
            <option value="option2">Option 2</option>
          </Form.Select>
          <Form.Error>This field is required</Form.Error>
        </Form.Field>
      );

      render(<TestForm />);
      
      const select = screen.getByRole('combobox');
      expect(select).toHaveClass('form-select-error');
      expect(screen.getByText('This field is required')).toBeInTheDocument();
    });

    it('should handle required field validation', async () => {
      const user = userEvent.setup();
      let formData = { testField: '' };
      let validationErrors: { [key: string]: string } = {};

      const TestForm = () => {
        const handleChange = (value: string) => {
          formData.testField = value;
          // Simulate validation
          if (!value) {
            validationErrors.testField = 'This field is required';
          } else {
            delete validationErrors.testField;
          }
        };

        return (
          <Form.Field>
            <Form.Label htmlFor="test-select" required>Test Select</Form.Label>
            <Form.Select
              id="test-select"
              value={formData.testField}
              onChange={(e) => handleChange(e.target.value)}
              error={validationErrors.testField}
              placeholder="Select an option"
            >
              <option value="option1">Option 1</option>
              <option value="option2">Option 2</option>
            </Form.Select>
            {validationErrors.testField && (
              <Form.Error>{validationErrors.testField}</Form.Error>
            )}
          </Form.Field>
        );
      };

      render(<TestForm />);
      
      const select = screen.getByRole('combobox');
      
      // Initially empty, should trigger validation
      expect(formData.testField).toBe('');
      
      // Select an option
      await user.selectOptions(select, 'option1');
      expect(formData.testField).toBe('option1');
    });
  });

  describe('CascadingSelect Validation', () => {
    // Import the mocked component
    const { CascadingSelect } = require('@/components/ui/CascadingSelect');

    it('should validate required cascading selections', async () => {
      const user = userEvent.setup();
      let cascadingValue = { vendorId: '', productId: '', versionId: '' };
      let validationErrors: { [key: string]: string } = {};

      const validateCascadingSelect = () => {
        const errors: { [key: string]: string } = {};
        if (!cascadingValue.vendorId) errors.vendor = 'Vendor is required';
        if (!cascadingValue.productId) errors.product = 'Product is required';
        if (!cascadingValue.versionId) errors.version = 'Version is required';
        return errors;
      };

      const TestCascadingForm = () => {
        const handleChange = (newValue: typeof cascadingValue) => {
          cascadingValue = newValue;
          validationErrors = validateCascadingSelect();
        };

        return (
          <div>
            <CascadingSelect
              value={cascadingValue}
              onChange={handleChange}
              required={true}
              disabled={false}
            />
            {validationErrors.vendor && <div data-testid="vendor-error">{validationErrors.vendor}</div>}
            {validationErrors.product && <div data-testid="product-error">{validationErrors.product}</div>}
            {validationErrors.version && <div data-testid="version-error">{validationErrors.version}</div>}
          </div>
        );
      };

      render(<TestCascadingForm />);

      // Wait for component to render
      await waitFor(() => {
        expect(screen.getByTestId('cascading-select')).toBeInTheDocument();
      });

      // Initially should have validation errors
      const initialErrors = validateCascadingSelect();
      expect(initialErrors.vendor).toBe('Vendor is required');
      expect(initialErrors.product).toBe('Product is required');
      expect(initialErrors.version).toBe('Version is required');
    });

    it('should enforce cascading dependency validation', async () => {
      const user = userEvent.setup();
      let cascadingValue = { vendorId: '', productId: '', versionId: '' };

      const TestCascadingForm = () => {
        const handleChange = (newValue: typeof cascadingValue) => {
          cascadingValue = newValue;
        };

        return (
          <CascadingSelect
            value={cascadingValue}
            onChange={handleChange}
            required={true}
            disabled={false}
          />
        );
      };

      render(<TestCascadingForm />);

      await waitFor(() => {
        expect(screen.getByTestId('cascading-select')).toBeInTheDocument();
      });

      // Try to select product without vendor - should be disabled
      const productSelect = screen.getByTestId('product-select');
      expect(productSelect).toBeDisabled();

      // Try to select version without product - should be disabled
      const versionSelect = screen.getByTestId('version-select');
      expect(versionSelect).toBeDisabled();
    });
  });

  describe('RenewalItemsManager Validation', () => {
    // Import the mocked component
    const { RenewalItemsManager } = require('@/components/ui/RenewalItemsManager');

    it('should validate required fields when adding new item', async () => {
      const user = userEvent.setup();
      const mockOnChange = jest.fn();

      render(
        <RenewalItemsManager
          items={[]}
          onChange={mockOnChange}
          disabled={false}
          vendorId="1"
        />
      );

      // Should show validation errors in the mock
      await waitFor(() => {
        expect(screen.getByTestId('renewal-items-manager')).toBeInTheDocument();
        expect(screen.getByTestId('product-error')).toBeInTheDocument();
        expect(screen.getByTestId('version-error')).toBeInTheDocument();
        expect(screen.getByTestId('quantity-error')).toBeInTheDocument();
      });

      // Try to add item
      const addButton = screen.getByTestId('add-item-button');
      await user.click(addButton);

      // Mock component should still show errors
      expect(screen.getByText('Product is required')).toBeInTheDocument();
      expect(screen.getByText('Version is required')).toBeInTheDocument();
      expect(screen.getByText('Quantity must be greater than 0')).toBeInTheDocument();
    });

    it('should display form elements for adding items', async () => {
      const mockOnChange = jest.fn();

      render(
        <RenewalItemsManager
          items={[]}
          onChange={mockOnChange}
          disabled={false}
          vendorId="1"
        />
      );

      // Should show add item form elements
      await waitFor(() => {
        expect(screen.getByTestId('add-item-form')).toBeInTheDocument();
        expect(screen.getByTestId('quantity-input')).toBeInTheDocument();
        expect(screen.getByTestId('add-item-button')).toBeInTheDocument();
      });
    });
  });

  describe('Form Validation Integration', () => {
    it('should validate form data structure matches schema requirements', () => {
      // Test that form data structure aligns with validation schemas
      const formData = {
        name: 'Microsoft Office 365',
        vendor: 'Microsoft',
        status: 'active',
        dueDate: new Date().toISOString(),
        annualCost: 1200.00,
        description: 'Office productivity suite'
      };

      const result = renewalCreateSchema.safeParse(formData);
      expect(result.success).toBe(true);

      if (result.success) {
        expect(result.data.name).toBe('Microsoft Office 365');
        expect(result.data.vendor).toBe('Microsoft');
        expect(result.data.status).toBe('active');
      }
    });

    it('should handle validation errors in form submission flow', () => {
      const invalidFormData = {
        name: '', // Required field missing
        vendor: '', // Required field missing
        status: 'invalid_status' // Invalid enum value
      };

      const result = renewalCreateSchema.safeParse(invalidFormData);
      expect(result.success).toBe(false);

      if (!result.success) {
        const errors = result.error.errors;
        expect(errors.length).toBeGreaterThan(0);

        // Check for specific validation errors
        const nameError = errors.find(e => e.path.includes('name'));
        const vendorError = errors.find(e => e.path.includes('vendor'));
        const statusError = errors.find(e => e.path.includes('status'));

        expect(nameError).toBeDefined();
        expect(vendorError).toBeDefined();
        expect(statusError).toBeDefined();
      }
    });
  });

  describe('Error Display and User Experience', () => {
    it('should display field-specific error messages', () => {
      const TestForm = () => (
        <div>
          <Form.Field>
            <Form.Label htmlFor="name" required>Name</Form.Label>
            <Form.Input
              id="name"
              error="Name is required"
              value=""
              onChange={() => {}}
            />
            <Form.Error>Name is required</Form.Error>
          </Form.Field>

          <Form.Field>
            <Form.Label htmlFor="vendor" required>Vendor</Form.Label>
            <Form.Select
              id="vendor"
              error="Vendor is required"
              value=""
              onChange={() => {}}
              placeholder="Select vendor"
            >
              <option value="1">Microsoft</option>
            </Form.Select>
            <Form.Error>Vendor is required</Form.Error>
          </Form.Field>
        </div>
      );

      render(<TestForm />);

      // Check that error messages are displayed
      expect(screen.getByText('Name is required')).toBeInTheDocument();
      expect(screen.getByText('Vendor is required')).toBeInTheDocument();

      // Check that form fields have error styling
      const nameInput = screen.getByLabelText('Name');
      const vendorSelect = screen.getByLabelText('Vendor');

      expect(nameInput).toHaveClass('form-input-error');
      expect(vendorSelect).toHaveClass('form-select-error');
    });

    it('should clear errors when fields become valid', async () => {
      const user = userEvent.setup();
      let formData = { name: '', vendor: '' };
      let errors = { name: 'Name is required', vendor: 'Vendor is required' };

      const TestForm = () => {
        const handleNameChange = (value: string) => {
          formData.name = value;
          if (value.trim()) {
            delete errors.name;
          } else {
            errors.name = 'Name is required';
          }
        };

        const handleVendorChange = (value: string) => {
          formData.vendor = value;
          if (value) {
            delete errors.vendor;
          } else {
            errors.vendor = 'Vendor is required';
          }
        };

        return (
          <div>
            <Form.Field>
              <Form.Label htmlFor="name" required>Name</Form.Label>
              <Form.Input
                id="name"
                error={errors.name}
                value={formData.name}
                onChange={(e) => handleNameChange(e.target.value)}
              />
              {errors.name && <Form.Error>{errors.name}</Form.Error>}
            </Form.Field>

            <Form.Field>
              <Form.Label htmlFor="vendor" required>Vendor</Form.Label>
              <Form.Select
                id="vendor"
                error={errors.vendor}
                value={formData.vendor}
                onChange={(e) => handleVendorChange(e.target.value)}
                placeholder="Select vendor"
              >
                <option value="1">Microsoft</option>
              </Form.Select>
              {errors.vendor && <Form.Error>{errors.vendor}</Form.Error>}
            </Form.Field>
          </div>
        );
      };

      render(<TestForm />);

      // Initially should show errors
      expect(screen.getByText('Name is required')).toBeInTheDocument();
      expect(screen.getByText('Vendor is required')).toBeInTheDocument();

      // Fill in name field
      const nameInput = screen.getByLabelText('Name');
      await user.type(nameInput, 'Test Renewal');

      // Name error should be cleared
      expect(formData.name).toBe('Test Renewal');

      // Fill in vendor field
      const vendorSelect = screen.getByLabelText('Vendor');
      await user.selectOptions(vendorSelect, '1');

      // Vendor error should be cleared
      expect(formData.vendor).toBe('1');
    });
  });
});

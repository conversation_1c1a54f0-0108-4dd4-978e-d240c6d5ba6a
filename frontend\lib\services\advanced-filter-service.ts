/**
 * Advanced Filter Service
 * 
 * Provides comprehensive filtering, searching, and saved filter preset functionality
 * across all data tables in the application
 */

import { TenantContext } from '@/lib/tenant/context'
import { executeTenantQuery, executeTenantQuerySingle } from '@/lib/tenant/database'
import { buildQueryString } from '@/lib/constants/api-endpoints'

// Filter types
export type FilterOperator = 
  | 'equals' | 'not_equals' 
  | 'contains' | 'not_contains' 
  | 'starts_with' | 'ends_with'
  | 'greater_than' | 'less_than' 
  | 'greater_than_or_equal' | 'less_than_or_equal'
  | 'between' | 'not_between'
  | 'in' | 'not_in'
  | 'is_null' | 'is_not_null'
  | 'date_equals' | 'date_before' | 'date_after'
  | 'date_between' | 'date_in_last_days' | 'date_in_next_days'

export interface FilterCondition {
  field: string
  operator: FilterOperator
  value: any
  values?: any[] // For 'in', 'not_in', 'between' operators
  dataType?: 'string' | 'number' | 'date' | 'boolean'
}

export interface FilterGroup {
  conditions: FilterCondition[]
  operator: 'AND' | 'OR'
  groups?: FilterGroup[] // Nested groups for complex filtering
}

export interface AdvancedFilter {
  name: string
  description?: string
  table: string
  groups: FilterGroup[]
  sortBy?: string
  sortDirection?: 'ASC' | 'DESC'
  limit?: number
  offset?: number
}

export interface SavedFilterPreset {
  preset_id?: number
  name: string
  description?: string
  table_name: string
  filter_config: AdvancedFilter
  is_public: boolean
  created_by: string
  created_on?: string
  updated_on?: string
}

export interface SearchConfig {
  query: string
  fields: string[]
  fuzzy?: boolean
  caseSensitive?: boolean
  wholeWords?: boolean
}

export interface FilterOptions {
  field: string
  values: Array<{ label: string; value: any; count?: number }>
  dataType: 'string' | 'number' | 'date' | 'boolean'
}

export class AdvancedFilterService {
  /**
   * Build SQL WHERE clause from filter groups
   */
  static buildWhereClause(groups: FilterGroup[], paramOffset: number = 1): {
    whereClause: string
    params: any[]
    paramCount: number
  } {
    const params: any[] = []
    let paramIndex = paramOffset

    const buildGroupClause = (group: FilterGroup): string => {
      const conditionClauses: string[] = []

      // Process conditions
      for (const condition of group.conditions) {
        const clause = this.buildConditionClause(condition, paramIndex)
        conditionClauses.push(clause.clause)
        params.push(...clause.params)
        paramIndex += clause.params.length
      }

      // Process nested groups
      if (group.groups) {
        for (const nestedGroup of group.groups) {
          const nestedClause = buildGroupClause(nestedGroup)
          conditionClauses.push(`(${nestedClause})`)
        }
      }

      return conditionClauses.join(` ${group.operator} `)
    }

    const whereClauses = groups.map(group => `(${buildGroupClause(group)})`)
    const whereClause = whereClauses.length > 0 ? whereClauses.join(' AND ') : '1=1'

    return {
      whereClause,
      params,
      paramCount: paramIndex - paramOffset
    }
  }

  /**
   * Build individual condition clause
   */
  private static buildConditionClause(condition: FilterCondition, paramIndex: number): {
    clause: string
    params: any[]
  } {
    const { field, operator, value, values, dataType } = condition
    const params: any[] = []

    switch (operator) {
      case 'equals':
        return { clause: `${field} = $${paramIndex}`, params: [value] }
      
      case 'not_equals':
        return { clause: `${field} != $${paramIndex}`, params: [value] }
      
      case 'contains':
        return { clause: `${field} ILIKE $${paramIndex}`, params: [`%${value}%`] }
      
      case 'not_contains':
        return { clause: `${field} NOT ILIKE $${paramIndex}`, params: [`%${value}%`] }
      
      case 'starts_with':
        return { clause: `${field} ILIKE $${paramIndex}`, params: [`${value}%`] }
      
      case 'ends_with':
        return { clause: `${field} ILIKE $${paramIndex}`, params: [`%${value}`] }
      
      case 'greater_than':
        return { clause: `${field} > $${paramIndex}`, params: [value] }
      
      case 'less_than':
        return { clause: `${field} < $${paramIndex}`, params: [value] }
      
      case 'greater_than_or_equal':
        return { clause: `${field} >= $${paramIndex}`, params: [value] }
      
      case 'less_than_or_equal':
        return { clause: `${field} <= $${paramIndex}`, params: [value] }
      
      case 'between':
        if (!values || values.length !== 2) {
          throw new Error('Between operator requires exactly 2 values')
        }
        return { 
          clause: `${field} BETWEEN $${paramIndex} AND $${paramIndex + 1}`, 
          params: values 
        }
      
      case 'not_between':
        if (!values || values.length !== 2) {
          throw new Error('Not between operator requires exactly 2 values')
        }
        return { 
          clause: `${field} NOT BETWEEN $${paramIndex} AND $${paramIndex + 1}`, 
          params: values 
        }
      
      case 'in':
        if (!values || values.length === 0) {
          return { clause: '1=0', params: [] } // No matches
        }
        const inPlaceholders = values.map((_, i) => `$${paramIndex + i}`).join(', ')
        return { clause: `${field} IN (${inPlaceholders})`, params: values }
      
      case 'not_in':
        if (!values || values.length === 0) {
          return { clause: '1=1', params: [] } // All matches
        }
        const notInPlaceholders = values.map((_, i) => `$${paramIndex + i}`).join(', ')
        return { clause: `${field} NOT IN (${notInPlaceholders})`, params: values }
      
      case 'is_null':
        return { clause: `${field} IS NULL`, params: [] }
      
      case 'is_not_null':
        return { clause: `${field} IS NOT NULL`, params: [] }
      
      case 'date_equals':
        return { clause: `DATE(${field}) = DATE($${paramIndex})`, params: [value] }
      
      case 'date_before':
        return { clause: `DATE(${field}) < DATE($${paramIndex})`, params: [value] }
      
      case 'date_after':
        return { clause: `DATE(${field}) > DATE($${paramIndex})`, params: [value] }
      
      case 'date_between':
        if (!values || values.length !== 2) {
          throw new Error('Date between operator requires exactly 2 values')
        }
        return { 
          clause: `DATE(${field}) BETWEEN DATE($${paramIndex}) AND DATE($${paramIndex + 1})`, 
          params: values 
        }
      
      case 'date_in_last_days':
        return { 
          clause: `${field} >= CURRENT_DATE - INTERVAL '${value} days'`, 
          params: [] 
        }
      
      case 'date_in_next_days':
        return { 
          clause: `${field} <= CURRENT_DATE + INTERVAL '${value} days'`, 
          params: [] 
        }
      
      default:
        throw new Error(`Unsupported filter operator: ${operator}`)
    }
  }

  /**
   * Build search clause for full-text search
   */
  static buildSearchClause(searchConfig: SearchConfig, paramOffset: number = 1): {
    searchClause: string
    params: any[]
    paramCount: number
  } {
    if (!searchConfig.query.trim()) {
      return { searchClause: '1=1', params: [], paramCount: 0 }
    }

    const { query, fields, fuzzy = false, caseSensitive = false, wholeWords = false } = searchConfig
    const params: any[] = []
    let searchValue = query.trim()

    // Prepare search value based on options
    if (!wholeWords) {
      searchValue = `%${searchValue}%`
    }

    // Build field conditions
    const operator = caseSensitive ? 'LIKE' : 'ILIKE'
    const fieldConditions = fields.map((field, index) => {
      params.push(searchValue)
      return `${field} ${operator} $${paramOffset + index}`
    })

    const searchClause = fieldConditions.length > 0 
      ? `(${fieldConditions.join(' OR ')})` 
      : '1=1'

    return {
      searchClause,
      params,
      paramCount: params.length
    }
  }

  /**
   * Get filter options for a specific field
   */
  static async getFilterOptions(
    tableName: string,
    fieldName: string,
    tenantContext: TenantContext,
    limit: number = 100
  ): Promise<FilterOptions> {
    try {
      const query = `
        SELECT 
          ${fieldName} as value,
          ${fieldName} as label,
          COUNT(*) as count
        FROM "${tenantContext.tenantSchema}".${tableName}
        WHERE ${fieldName} IS NOT NULL
          AND ${fieldName} != ''
        GROUP BY ${fieldName}
        ORDER BY count DESC, ${fieldName} ASC
        LIMIT $1
      `

      const result = await executeTenantQuery(query, [limit], tenantContext)

      if (!result.success || !result.data) {
        return { field: fieldName, values: [], dataType: 'string' }
      }

      return {
        field: fieldName,
        values: result.data.map(row => ({
          label: String(row.label),
          value: row.value,
          count: parseInt(row.count)
        })),
        dataType: 'string' // TODO: Detect data type from schema
      }
    } catch (error) {
      console.error(`Error getting filter options for ${fieldName}:`, error)
      return { field: fieldName, values: [], dataType: 'string' }
    }
  }

  /**
   * Save filter preset
   */
  static async saveFilterPreset(
    preset: Omit<SavedFilterPreset, 'preset_id'>,
    tenantContext: TenantContext
  ): Promise<{ success: boolean; presetId?: number; error?: string }> {
    try {
      const query = `
        INSERT INTO "${tenantContext.tenantSchema}".tenant_filter_presets
        (name, description, table_name, filter_config, is_public, created_by)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING preset_id
      `

      const result = await executeTenantQuerySingle(
        query,
        [
          preset.name,
          preset.description || null,
          preset.table_name,
          JSON.stringify(preset.filter_config),
          preset.is_public,
          preset.created_by
        ],
        tenantContext
      )

      if (result.success && result.data) {
        return { success: true, presetId: result.data.preset_id }
      }

      return { success: false, error: 'Failed to save filter preset' }
    } catch (error) {
      console.error('Error saving filter preset:', error)
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  }

  /**
   * Get saved filter presets
   */
  static async getFilterPresets(
    tableName: string,
    userId: string,
    tenantContext: TenantContext
  ): Promise<SavedFilterPreset[]> {
    try {
      const query = `
        SELECT
          preset_id,
          name,
          description,
          table_name,
          filter_config,
          is_public,
          created_by,
          created_on,
          updated_on
        FROM "${tenantContext.tenantSchema}".tenant_filter_presets
        WHERE table_name = $1
          AND (is_public = true OR created_by = $2)
        ORDER BY name ASC
      `

      const result = await executeTenantQuery(query, [tableName, userId], tenantContext)

      if (!result.success || !result.data) {
        return []
      }

      return result.data.map(row => ({
        ...row,
        filter_config: typeof row.filter_config === 'string'
          ? JSON.parse(row.filter_config)
          : row.filter_config
      }))
    } catch (error) {
      console.error('Error getting filter presets:', error)
      return []
    }
  }

  /**
   * Apply filters to data array (client-side filtering)
   */
  static applyFiltersToData<T extends Record<string, any>>(
    data: T[],
    groups: FilterGroup[]
  ): T[] {
    if (!groups || groups.length === 0) {
      return data
    }

    return data.filter(item => {
      return groups.every(group => this.evaluateGroup(item, group))
    })
  }

  /**
   * Evaluate a filter group against a data item
   */
  private static evaluateGroup<T extends Record<string, any>>(
    item: T,
    group: FilterGroup
  ): boolean {
    const conditionResults = group.conditions.map(condition =>
      this.evaluateCondition(item, condition)
    )

    const nestedGroupResults = group.groups?.map(nestedGroup =>
      this.evaluateGroup(item, nestedGroup)
    ) || []

    const allResults = [...conditionResults, ...nestedGroupResults]

    return group.operator === 'AND'
      ? allResults.every(result => result)
      : allResults.some(result => result)
  }

  /**
   * Evaluate a single condition against a data item
   */
  private static evaluateCondition<T extends Record<string, any>>(
    item: T,
    condition: FilterCondition
  ): boolean {
    const fieldValue = item[condition.field]
    const { operator, value, values } = condition

    switch (operator) {
      case 'equals':
        return fieldValue == value

      case 'not_equals':
        return fieldValue != value

      case 'contains':
        return String(fieldValue || '').toLowerCase().includes(String(value || '').toLowerCase())

      case 'not_contains':
        return !String(fieldValue || '').toLowerCase().includes(String(value || '').toLowerCase())

      case 'starts_with':
        return String(fieldValue || '').toLowerCase().startsWith(String(value || '').toLowerCase())

      case 'ends_with':
        return String(fieldValue || '').toLowerCase().endsWith(String(value || '').toLowerCase())

      case 'greater_than':
        return Number(fieldValue) > Number(value)

      case 'less_than':
        return Number(fieldValue) < Number(value)

      case 'greater_than_or_equal':
        return Number(fieldValue) >= Number(value)

      case 'less_than_or_equal':
        return Number(fieldValue) <= Number(value)

      case 'between':
        if (!values || values.length !== 2) return false
        const numValue = Number(fieldValue)
        return numValue >= Number(values[0]) && numValue <= Number(values[1])

      case 'not_between':
        if (!values || values.length !== 2) return true
        const numVal = Number(fieldValue)
        return numVal < Number(values[0]) || numVal > Number(values[1])

      case 'in':
        if (!values || values.length === 0) return false
        return values.includes(fieldValue)

      case 'not_in':
        if (!values || values.length === 0) return true
        return !values.includes(fieldValue)

      case 'is_null':
        return fieldValue == null || fieldValue === '' || fieldValue === undefined

      case 'is_not_null':
        return fieldValue != null && fieldValue !== '' && fieldValue !== undefined

      case 'date_equals':
        return new Date(fieldValue).toDateString() === new Date(value).toDateString()

      case 'date_before':
        return new Date(fieldValue) < new Date(value)

      case 'date_after':
        return new Date(fieldValue) > new Date(value)

      case 'date_between':
        if (!values || values.length !== 2) return false
        const dateValue = new Date(fieldValue)
        return dateValue >= new Date(values[0]) && dateValue <= new Date(values[1])

      case 'date_in_last_days':
        const daysAgo = new Date()
        daysAgo.setDate(daysAgo.getDate() - Number(value))
        return new Date(fieldValue) >= daysAgo

      case 'date_in_next_days':
        const daysFromNow = new Date()
        daysFromNow.setDate(daysFromNow.getDate() + Number(value))
        return new Date(fieldValue) <= daysFromNow

      default:
        return true
    }
  }
}

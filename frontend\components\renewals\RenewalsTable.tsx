/**
 * Renewals Table Component
 * 
 * Table display for renewals with sorting, actions, and status indicators
 */

'use client'

import React, { useState, useMemo } from 'react'
import { BaseComponentProps } from '@/lib/types'
import { Renewal } from '@/lib/hooks'
import RenewalActionsMenu from './RenewalActionsMenu'
import { Table, TableHeader, TableBody, TableHead, TableRow, TableCell, Button } from '@/components/ui'
import { formatDate, formatCurrency } from '@/lib/utils'

interface RenewalsTableProps extends BaseComponentProps {
  renewals: Renewal[]
  isLoading?: boolean
  onRenewalAction?: (renewalId: string, action: string) => void
}

type SortField = keyof Renewal
type SortDirection = 'asc' | 'desc'

const RenewalsTable = React.memo(function RenewalsTable({
  renewals,
  isLoading = false,
  onRenewalAction,
  className = '',
  'data-testid': testId
}: RenewalsTableProps) {
  const [sortField, setSortField] = useState<SortField>('start_date')
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc')

  // Sort renewals
  const sortedRenewals = useMemo(() => {
    return [...renewals].sort((a, b) => {
      let aValue = a[sortField]
      let bValue = b[sortField]

      // Handle undefined values
      if (aValue === undefined && bValue === undefined) return 0
      if (aValue === undefined) return sortDirection === 'asc' ? 1 : -1
      if (bValue === undefined) return sortDirection === 'asc' ? -1 : 1

      // Handle date sorting
      if (sortField === 'start_date') {
        aValue = new Date(aValue as string).getTime()
        bValue = new Date(bValue as string).getTime()
      }

      // Handle string sorting
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        aValue = aValue.toLowerCase()
        bValue = bValue.toLowerCase()
      }

      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1
      return 0
    })
  }, [renewals, sortField, sortDirection])

  const handleSort = (field: SortField) => {
    if (field === sortField) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('asc')
    }
  }

  const handleAction = (renewalId: string, action: string) => {
    onRenewalAction?.(renewalId, action)
  }

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount) // Amount is already in the correct format from API
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const getStatusBadge = (status: string) => {
    const normalizedStatus = status?.toLowerCase() || ''
    const statusClasses = {
      'active': 'status-badge status-active',
      'expired': 'status-badge status-expired',
      'expiring': 'status-badge status-expiring',
      'pending': 'status-badge status-pending',
      'cancelled': 'status-badge status-cancelled',
      'inactive': 'status-badge status-inactive'
    }

    return (
      <span className={statusClasses[normalizedStatus as keyof typeof statusClasses] || 'status-badge'}>
        {status}
      </span>
    )
  }

  const getSortIcon = (field: SortField) => {
    if (field !== sortField) return '↕️'
    return sortDirection === 'asc' ? '↑' : '↓'
  }

  if (isLoading) {
    return (
      <div className="animate-pulse">
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead className="bg-gray-50">
              <tr>
                {[...Array(10)].map((_, i) => (
                  <th key={i} className="px-4 py-3">
                    <div className="h-4 bg-gray-200 rounded"></div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {[...Array(5)].map((_, i) => (
                <tr key={i}>
                  {[...Array(10)].map((_, j) => (
                    <td key={j} className="px-4 py-4">
                      <div className="h-4 bg-gray-200 rounded"></div>
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    )
  }

  const getDaysUntilRenewal = (start_date: string) => {
    const today = new Date()
    const renewal = new Date(start_date)
    const diffTime = renewal.getTime() - today.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  const formatRenewalDate = (start_date: string) => {
    const date = new Date(start_date)
    const days = getDaysUntilRenewal(start_date)

    const formattedDate = date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })

    if (days < 0) {
      return `${formattedDate}\nExpired ${Math.abs(days)} days ago`
    } else if (days === 0) {
      return `${formattedDate}\nToday`
    } else {
      return `${formattedDate}\nin ${days} days`
    }
  }

  return (
    <div className={`${className}`} data-testid={testId}>
      <div className="overflow-x-auto">
        <table className="renewals-table">
          <thead>
            <tr>
              <th className="cursor-pointer hover:bg-gray-100" onClick={() => handleSort('name')}>
                <div className="flex items-center space-x-1">
                  <span>Renewal</span>
                  <span className="text-gray-400">{getSortIcon('name')}</span>
                </div>
              </th>
              <th className="cursor-pointer hover:bg-gray-100" onClick={() => handleSort('product_name')}>
                <div className="flex items-center space-x-1">
                  <span>Product</span>
                  <span className="text-gray-400">{getSortIcon('product_name')}</span>
                </div>
              </th>
              <th className="cursor-pointer hover:bg-gray-100" onClick={() => handleSort('vendor')}>
                <div className="flex items-center space-x-1">
                  <span>Vendor</span>
                  <span className="text-gray-400">{getSortIcon('vendor')}</span>
                </div>
              </th>
              <th className="cursor-pointer hover:bg-gray-100" onClick={() => handleSort('type')}>
                <div className="flex items-center space-x-1">
                  <span>Type</span>
                  <span className="text-gray-400">{getSortIcon('type')}</span>
                </div>
              </th>
              <th className="cursor-pointer hover:bg-gray-100" onClick={() => handleSort('start_date')}>
                <div className="flex items-center space-x-1">
                  <span>Start Date</span>
                  <span className="text-gray-400">{getSortIcon('start_date')}</span>
                </div>
              </th>
              <th className="cursor-pointer hover:bg-gray-100" onClick={() => handleSort('cost')}>
                <div className="flex items-center space-x-1">
                  <span>Cost</span>
                  <span className="text-gray-400">{getSortIcon('cost')}</span>
                </div>
              </th>
              <th className="cursor-pointer hover:bg-gray-100" onClick={() => handleSort('currency')}>
                <div className="flex items-center space-x-1">
                  <span>Currency</span>
                  <span className="text-gray-400">{getSortIcon('currency')}</span>
                </div>
              </th>
              <th className="cursor-pointer hover:bg-gray-100" onClick={() => handleSort('status')}>
                <div className="flex items-center space-x-1">
                  <span>Status</span>
                  <span className="text-gray-400">{getSortIcon('status')}</span>
                </div>
              </th>
              <th>
                Alerts
              </th>
              <th>
                Actions
              </th>
            </tr>
          </thead>
          <tbody>
            {sortedRenewals.map((renewal) => (
              <tr key={renewal.id}>
                <td>
                  <div className="flex items-center">
                    <span className="text-gray-400 mr-2">📄</span>
                    <span className="font-medium">{renewal.name}</span>
                  </div>
                </td>
                <td>{renewal.product_name}</td>
                <td>{renewal.vendor}</td>
                <td>{renewal.type}</td>
                <td>
                  <div className="whitespace-pre-line">{renewal.start_date ? formatRenewalDate(renewal.start_date) : '-'}</div>
                </td>
                <td>
                  {renewal.cost ? formatCurrency(renewal.cost, renewal.currency || 'USD') : '-'}
                </td>
                <td>{renewal.currency}</td>
                <td>
                  <span className={`status-badge ${
                    renewal.status === 'active' ? 'active' :
                    renewal.status === 'expired' ? 'expired' :
                    'pending'
                  }`}>
                    {renewal.status}
                  </span>
                </td>
                <td className="text-center">
                  {renewal.alerts && renewal.alerts > 0 ? (
                    <span className="inline-flex items-center justify-center w-6 h-6 text-xs font-bold text-white bg-blue-600 rounded-full">
                      {renewal.alerts}
                    </span>
                  ) : (
                    <span className="text-gray-400">0</span>
                  )}
                </td>
                <td>
                  <Button variant="ghost" size="sm">
                    ⋯
                  </Button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {renewals.length === 0 && !isLoading && (
        <div className="text-center py-12">
          <div className="text-4xl mb-4">📋</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No renewals found</h3>
          <p className="text-gray-500">Try adjusting your search or filters to find renewals.</p>
        </div>
      )}
    </div>
  )
})

export default RenewalsTable

/**
 * Enhanced Error Tracking System
 * 
 * Provides comprehensive error tracking with tenant-specific context,
 * error categorization, and integration with distributed tracing.
 */

import { TraceContext, requestTracing } from './request-tracing';
import { TenantContext } from '@/lib/types';
import { AuthSession } from '@/lib/api/auth-middleware';

export interface ErrorContext {
  correlationId: string;
  traceId?: string;
  spanId?: string;
  tenantId?: string;
  tenantName?: string;
  userId?: string;
  userEmail?: string;
  sessionId?: string;
  timestamp: Date;
  environment: string;
  source: string;
  operation: string;
  resource: string;
  requestId?: string;
  ipAddress?: string;
  userAgent?: string;
  url?: string;
  method?: string;
  headers?: Record<string, string>;
  metadata?: Record<string, any>;
}

export interface CategorizedError {
  id: string;
  category: ErrorCategory;
  severity: ErrorSeverity;
  type: string;
  message: string;
  stack?: string;
  code?: string | number;
  context: ErrorContext;
  fingerprint: string;
  firstSeen: Date;
  lastSeen: Date;
  count: number;
  resolved: boolean;
  tags: string[];
  affectedUsers: Set<string>;
  affectedTenants: Set<string>;
}

export enum ErrorCategory {
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  VALIDATION = 'validation',
  DATABASE = 'database',
  EXTERNAL_SERVICE = 'external_service',
  BUSINESS_LOGIC = 'business_logic',
  SYSTEM = 'system',
  NETWORK = 'network',
  TENANT_ISOLATION = 'tenant_isolation',
  SECURITY = 'security'
}

export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export interface ErrorAlert {
  id: string;
  errorId: string;
  severity: ErrorSeverity;
  threshold: number;
  timeWindow: number; // minutes
  triggered: boolean;
  lastTriggered?: Date;
  recipients: string[];
  conditions: {
    errorCount?: number;
    affectedUsers?: number;
    affectedTenants?: number;
    errorRate?: number;
  };
}

class ErrorTrackingService {
  private errors: Map<string, CategorizedError> = new Map();
  private alerts: Map<string, ErrorAlert> = new Map();
  private maxErrors = 10000;
  private errorRetentionMs = 7 * 24 * 60 * 60 * 1000; // 7 days

  /**
   * Track an error with full context
   */
  trackError(
    error: Error,
    context: Partial<ErrorContext>,
    tenant?: TenantContext,
    user?: AuthSession,
    traceContext?: TraceContext
  ): string {
    const errorContext: ErrorContext = {
      correlationId: traceContext?.correlationId || context.correlationId || this.generateId(),
      traceId: traceContext?.traceId || context.traceId,
      spanId: traceContext?.spanId || context.spanId,
      tenantId: tenant?.clientId || context.tenantId,
      tenantName: tenant?.clientName || context.tenantName,
      userId: user?.userId || context.userId,
      userEmail: user?.email || context.userEmail,
      sessionId: user?.sessionId || context.sessionId,
      timestamp: new Date(),
      environment: process.env.NODE_ENV || 'unknown',
      source: context.source || 'api',
      operation: context.operation || 'unknown',
      resource: context.resource || 'unknown',
      requestId: context.requestId,
      ipAddress: context.ipAddress,
      userAgent: context.userAgent,
      url: context.url,
      method: context.method,
      headers: context.headers,
      metadata: context.metadata
    };

    const category = this.categorizeError(error, errorContext);
    const severity = this.determineSeverity(error, category, errorContext);
    const fingerprint = this.generateFingerprint(error, errorContext);

    // Check if this error already exists
    let categorizedError = this.errors.get(fingerprint);
    
    if (categorizedError) {
      // Update existing error
      categorizedError.lastSeen = new Date();
      categorizedError.count++;
      
      if (errorContext.userId) {
        categorizedError.affectedUsers.add(errorContext.userId);
      }
      if (errorContext.tenantId) {
        categorizedError.affectedTenants.add(errorContext.tenantId);
      }
    } else {
      // Create new error
      categorizedError = {
        id: this.generateId(),
        category,
        severity,
        type: error.name,
        message: error.message,
        stack: error.stack,
        code: (error as any).code,
        context: errorContext,
        fingerprint,
        firstSeen: new Date(),
        lastSeen: new Date(),
        count: 1,
        resolved: false,
        tags: this.generateTags(error, errorContext),
        affectedUsers: new Set(errorContext.userId ? [errorContext.userId] : []),
        affectedTenants: new Set(errorContext.tenantId ? [errorContext.tenantId] : [])
      };

      this.errors.set(fingerprint, categorizedError);
    }

    // Add to trace if available
    if (traceContext?.traceId) {
      requestTracing.addSpanLog(
        traceContext.spanId,
        'error',
        error.message,
        {
          errorId: categorizedError.id,
          category,
          severity,
          fingerprint
        }
      );
    }

    // Check alerts
    this.checkAlerts(categorizedError);

    // Log error
    this.logError(categorizedError);

    // Cleanup old errors
    this.cleanupOldErrors();

    return categorizedError.id;
  }

  /**
   * Categorize error based on type and context
   */
  private categorizeError(error: Error, context: ErrorContext): ErrorCategory {
    const message = error.message.toLowerCase();
    const stack = error.stack?.toLowerCase() || '';

    // Authentication errors
    if (message.includes('unauthorized') || message.includes('authentication') || 
        message.includes('token') || error.name === 'AuthenticationError') {
      return ErrorCategory.AUTHENTICATION;
    }

    // Authorization errors
    if (message.includes('forbidden') || message.includes('permission') || 
        message.includes('access denied') || error.name === 'AuthorizationError') {
      return ErrorCategory.AUTHORIZATION;
    }

    // Validation errors
    if (message.includes('validation') || message.includes('invalid') || 
        error.name === 'ValidationError' || error.name === 'ZodError') {
      return ErrorCategory.VALIDATION;
    }

    // Database errors
    if (message.includes('database') || message.includes('sql') || 
        message.includes('connection') || stack.includes('pg') || 
        (error as any).code?.toString().startsWith('23')) {
      return ErrorCategory.DATABASE;
    }

    // Tenant isolation errors
    if (message.includes('tenant') || message.includes('schema') || 
        context.tenantId && message.includes('isolation')) {
      return ErrorCategory.TENANT_ISOLATION;
    }

    // Security errors
    if (message.includes('security') || message.includes('csrf') || 
        message.includes('xss') || message.includes('injection')) {
      return ErrorCategory.SECURITY;
    }

    // Network errors
    if (message.includes('network') || message.includes('timeout') || 
        message.includes('connection refused') || error.name === 'NetworkError') {
      return ErrorCategory.NETWORK;
    }

    // External service errors
    if (message.includes('external') || message.includes('api') || 
        message.includes('service unavailable')) {
      return ErrorCategory.EXTERNAL_SERVICE;
    }

    // Default to system error
    return ErrorCategory.SYSTEM;
  }

  /**
   * Determine error severity
   */
  private determineSeverity(
    error: Error, 
    category: ErrorCategory, 
    context: ErrorContext
  ): ErrorSeverity {
    // Critical errors
    if (category === ErrorCategory.SECURITY || 
        category === ErrorCategory.TENANT_ISOLATION ||
        error.message.includes('critical') ||
        context.environment === 'production' && category === ErrorCategory.DATABASE) {
      return ErrorSeverity.CRITICAL;
    }

    // High severity errors
    if (category === ErrorCategory.AUTHENTICATION ||
        category === ErrorCategory.DATABASE ||
        error.message.includes('fatal') ||
        context.tenantId && category === ErrorCategory.AUTHORIZATION) {
      return ErrorSeverity.HIGH;
    }

    // Medium severity errors
    if (category === ErrorCategory.BUSINESS_LOGIC ||
        category === ErrorCategory.EXTERNAL_SERVICE ||
        category === ErrorCategory.NETWORK) {
      return ErrorSeverity.MEDIUM;
    }

    // Low severity errors (validation, etc.)
    return ErrorSeverity.LOW;
  }

  /**
   * Generate error fingerprint for deduplication
   */
  private generateFingerprint(error: Error, context: ErrorContext): string {
    const components = [
      error.name,
      error.message.replace(/\d+/g, 'N'), // Replace numbers with N
      context.operation,
      context.resource,
      context.tenantId || 'no-tenant'
    ];

    return `fp_${Buffer.from(components.join('|')).toString('base64').substr(0, 16)}`;
  }

  /**
   * Generate tags for error
   */
  private generateTags(error: Error, context: ErrorContext): string[] {
    const tags = [
      `env:${context.environment}`,
      `source:${context.source}`,
      `operation:${context.operation}`
    ];

    if (context.tenantId) {
      tags.push(`tenant:${context.tenantId}`);
    }

    if (context.method) {
      tags.push(`method:${context.method}`);
    }

    if (error.name) {
      tags.push(`error_type:${error.name}`);
    }

    return tags;
  }

  /**
   * Check and trigger alerts
   */
  private checkAlerts(error: CategorizedError): void {
    for (const alert of this.alerts.values()) {
      if (this.shouldTriggerAlert(alert, error)) {
        this.triggerAlert(alert, error);
      }
    }
  }

  /**
   * Check if alert should be triggered
   */
  private shouldTriggerAlert(alert: ErrorAlert, error: CategorizedError): boolean {
    if (alert.triggered) return false;
    if (error.severity !== alert.severity && alert.severity !== ErrorSeverity.LOW) return false;

    const timeWindow = alert.timeWindow * 60 * 1000; // Convert to milliseconds
    const cutoff = new Date(Date.now() - timeWindow);

    // Check conditions
    if (alert.conditions.errorCount && error.count >= alert.conditions.errorCount) {
      return true;
    }

    if (alert.conditions.affectedUsers && 
        error.affectedUsers.size >= alert.conditions.affectedUsers) {
      return true;
    }

    if (alert.conditions.affectedTenants && 
        error.affectedTenants.size >= alert.conditions.affectedTenants) {
      return true;
    }

    return false;
  }

  /**
   * Trigger alert
   */
  private triggerAlert(alert: ErrorAlert, error: CategorizedError): void {
    alert.triggered = true;
    alert.lastTriggered = new Date();

    console.error(`🚨 ALERT TRIGGERED: ${alert.id}`, {
      errorId: error.id,
      severity: error.severity,
      category: error.category,
      count: error.count,
      affectedUsers: error.affectedUsers.size,
      affectedTenants: error.affectedTenants.size
    });

    // TODO: Implement actual alerting (email, Slack, PagerDuty, etc.)
  }

  /**
   * Log error with appropriate level
   */
  private logError(error: CategorizedError): void {
    const logData = {
      errorId: error.id,
      category: error.category,
      severity: error.severity,
      message: error.message,
      count: error.count,
      tenantId: error.context.tenantId,
      userId: error.context.userId,
      correlationId: error.context.correlationId
    };

    switch (error.severity) {
      case ErrorSeverity.CRITICAL:
        console.error(`🔥 CRITICAL ERROR:`, logData);
        break;
      case ErrorSeverity.HIGH:
        console.error(`❌ HIGH SEVERITY ERROR:`, logData);
        break;
      case ErrorSeverity.MEDIUM:
        console.warn(`⚠️ MEDIUM SEVERITY ERROR:`, logData);
        break;
      case ErrorSeverity.LOW:
        console.log(`ℹ️ LOW SEVERITY ERROR:`, logData);
        break;
    }
  }

  /**
   * Generate unique ID
   */
  private generateId(): string {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 8)}`;
  }

  /**
   * Clean up old errors
   */
  private cleanupOldErrors(): void {
    const now = Date.now();
    const cutoff = now - this.errorRetentionMs;
    let cleaned = 0;

    for (const [fingerprint, error] of this.errors.entries()) {
      if (error.firstSeen.getTime() < cutoff) {
        this.errors.delete(fingerprint);
        cleaned++;
      }
    }

    // Enforce max errors limit
    if (this.errors.size > this.maxErrors) {
      const sortedErrors = Array.from(this.errors.entries())
        .sort(([, a], [, b]) => b.lastSeen.getTime() - a.lastSeen.getTime());

      const toKeep = sortedErrors.slice(0, this.maxErrors);
      this.errors.clear();
      
      for (const [fingerprint, error] of toKeep) {
        this.errors.set(fingerprint, error);
      }

      cleaned += sortedErrors.length - this.maxErrors;
    }

    if (cleaned > 0) {
      console.log(`🧹 Cleaned up ${cleaned} old errors`);
    }
  }

  /**
   * Get errors by tenant
   */
  getErrorsByTenant(tenantId: string, limit: number = 50): CategorizedError[] {
    return Array.from(this.errors.values())
      .filter(error => error.context.tenantId === tenantId)
      .sort((a, b) => b.lastSeen.getTime() - a.lastSeen.getTime())
      .slice(0, limit);
  }

  /**
   * Get errors by severity
   */
  getErrorsBySeverity(severity: ErrorSeverity, limit: number = 50): CategorizedError[] {
    return Array.from(this.errors.values())
      .filter(error => error.severity === severity)
      .sort((a, b) => b.lastSeen.getTime() - a.lastSeen.getTime())
      .slice(0, limit);
  }

  /**
   * Get error statistics
   */
  getErrorStats(): {
    totalErrors: number;
    errorsByCategory: Record<ErrorCategory, number>;
    errorsBySeverity: Record<ErrorSeverity, number>;
    topErrors: Array<{ fingerprint: string; count: number; message: string }>;
    affectedTenants: number;
    affectedUsers: number;
  } {
    const errors = Array.from(this.errors.values());
    
    const errorsByCategory = {} as Record<ErrorCategory, number>;
    const errorsBySeverity = {} as Record<ErrorSeverity, number>;
    const allTenants = new Set<string>();
    const allUsers = new Set<string>();

    for (const error of errors) {
      errorsByCategory[error.category] = (errorsByCategory[error.category] || 0) + error.count;
      errorsBySeverity[error.severity] = (errorsBySeverity[error.severity] || 0) + error.count;
      
      error.affectedTenants.forEach(t => allTenants.add(t));
      error.affectedUsers.forEach(u => allUsers.add(u));
    }

    const topErrors = errors
      .sort((a, b) => b.count - a.count)
      .slice(0, 10)
      .map(error => ({
        fingerprint: error.fingerprint,
        count: error.count,
        message: error.message
      }));

    return {
      totalErrors: errors.length,
      errorsByCategory,
      errorsBySeverity,
      topErrors,
      affectedTenants: allTenants.size,
      affectedUsers: allUsers.size
    };
  }

  /**
   * Alias for trackError for compatibility
   */
  captureError(
    error: Error,
    context: Partial<ErrorContext>,
    tenant?: TenantContext,
    user?: AuthSession,
    traceContext?: TraceContext
  ): string {
    return this.trackError(error, context, tenant, user, traceContext);
  }
}

// Global error tracking service
export const errorTracking = new ErrorTrackingService();

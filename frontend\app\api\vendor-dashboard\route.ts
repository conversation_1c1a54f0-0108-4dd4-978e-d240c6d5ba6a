/**
 * Vendor Dashboard API Route
 * 
 * Provides vendor analytics data including spending distribution,
 * vendor details, and renewal information.
 */

import { createApiRoute } from '@/lib/api/route-factory'
import { z } from 'zod'

const vendorDashboardSchema = z.object({
  search: z.string().optional()
})

export const GET = createApiRoute('GET', {
  requireAuth: true,
  requireTenant: true,
  querySchema: vendorDashboardSchema,
  handler: async (context) => {
    const { tenant, query } = context
    const { search } = query || {}

    try {
      // Build base query for renewals with vendor information
      let renewalsQuery = `
        SELECT 
          r.id,
          v.name as vendor,
          r.name as product_name,
          r.annual_cost as cost,
          r.currency,
          res.name as reseller,
          r.due_date as renewal_date,
          COALESCE(r.reliability_score, 85) as reliability_score,
          CASE 
            WHEN r.created_on IS NOT NULL THEN 
              EXTRACT(YEAR FROM AGE(CURRENT_DATE, r.created_on)) || ' years'
            ELSE '1 year'
          END as relationship_duration,
          r.description
        FROM ${tenant?.tenantSchema}.renewals r
        LEFT JOIN ${tenant?.tenantSchema}.vendors v ON r.vendor_id = v.id
        LEFT JOIN ${tenant?.tenantSchema}.resellers res ON r.reseller_id = res.id
        WHERE r.status = 'A'
      `

      const queryParams: any[] = []

      // Add search filter if provided
      if (search) {
        renewalsQuery += ` AND (v.name ILIKE $1 OR r.name ILIKE $1)`
        queryParams.push(`%${search}%`)
      }

      renewalsQuery += ` ORDER BY r.annual_cost DESC`

      // Execute renewals query
      const renewalsResult = await context.executeQuery!(renewalsQuery, queryParams)

      if (!renewalsResult.success) {
        throw new Error('Failed to fetch renewals data')
      }

      const renewals = renewalsResult.data || []

      // Calculate statistics
      const totalVendors = new Set(renewals.map((r: any) => r.vendor)).size
      const totalSpend = renewals.reduce((sum: number, r: any) => sum + (r.cost || 0), 0)
      const avgSpendPerVendor = totalVendors > 0 ? totalSpend / totalVendors : 0
      
      // Find top vendor by spend
      const vendorSpends = renewals.reduce((acc: Record<string, number>, r: any) => {
        acc[r.vendor] = (acc[r.vendor] || 0) + (r.cost || 0)
        return acc
      }, {})
      
      const topVendor = Object.entries(vendorSpends)
        .sort(([,a], [,b]) => (b as number) - (a as number))[0]?.[0] || 'N/A'

      const stats = {
        totalVendors,
        totalSpend: new Intl.NumberFormat('en-CA', {
          style: 'currency',
          currency: 'CAD'
        }).format(totalSpend),
        avgSpendPerVendor: new Intl.NumberFormat('en-CA', {
          style: 'currency',
          currency: 'CAD'
        }).format(avgSpendPerVendor),
        topVendor
      }

      return {
        stats,
        renewals
      }

    } catch (error) {
      console.error('Vendor dashboard API error:', error)
      throw new Error('Failed to fetch vendor dashboard data')
    }
  }
})

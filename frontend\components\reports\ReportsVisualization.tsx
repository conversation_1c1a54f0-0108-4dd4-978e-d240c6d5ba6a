/**
 * Reports Visualization Component
 * 
 * Chart visualization for renewal data with configurable axes and chart types
 */

'use client'

import React, { useMemo } from 'react'
import { Form } from '@/components/ui/Form'
import { Button } from '@/components/ui/Button'
import { BaseComponentProps, Renewal } from '@/lib/types'

interface ReportsVisualizationProps extends BaseComponentProps {
  data: Renewal[]
  chartType: 'bar' | 'pie'
  xAxis: string
  yAxis: string
  onConfigChange: (config: {
    chartType?: 'bar' | 'pie'
    xAxis?: string
    yAxis?: string
  }) => void
  onExportChart: () => void
}

export default function ReportsVisualization({
  data,
  chartType,
  xAxis,
  yAxis,
  onConfigChange,
  onExportChart,
  className = '',
  'data-testid': testId
}: ReportsVisualizationProps) {
  // Chart data processing
  const chartData = useMemo(() => {
    if (!data.length) return []

    // Group data by x-axis field
    const grouped = data.reduce((acc, item) => {
      const key = (item as any)[xAxis] || 'Unknown'
      if (!acc[key]) {
        acc[key] = { name: key, value: 0, count: 0 }
      }
      
      // Aggregate based on y-axis field
      if (yAxis === 'annualCost') {
        acc[key].value += item.cost || 0
      } else if (yAxis === 'count') {
        acc[key].value += 1
      }
      acc[key].count += 1
      
      return acc
    }, {} as Record<string, { name: string; value: number; count: number }>)

    return Object.values(grouped).sort((a, b) => b.value - a.value)
  }, [data, xAxis, yAxis])

  // Chart configuration options
  const xAxisOptions = [
    { label: 'Vendor', value: 'vendor' },
    { label: 'Product Name', value: 'product_name' },
    { label: 'Renewal Type', value: 'type' },
    { label: 'Currency', value: 'currency' }
  ]

  const yAxisOptions = [
    { label: 'Annual Cost', value: 'annualCost' },
    { label: 'Count', value: 'count' }
  ]

  const chartTypeOptions = [
    { label: 'Bar Chart', value: 'bar' },
    { label: 'Pie Chart', value: 'pie' }
  ]

  // Calculate total for display
  const total = chartData.reduce((sum, item) => sum + item.value, 0)
  const totalFormatted = yAxis === 'annualCost' 
    ? `$${total.toLocaleString()}` 
    : total.toLocaleString()

  return (
    <div 
      className={`reports-visualization ${className}`}
      data-testid={testId}
    >
      {/* Chart Configuration */}
      <div className="flex flex-col lg:flex-row gap-4 mb-6">
        <div className="flex flex-col sm:flex-row gap-4 flex-1">
          <Form.Field className="flex-1">
            <Form.Label htmlFor="chart-type-select">Chart Type</Form.Label>
            <Form.Select
              id="chart-type-select"
              value={chartType}
              onChange={(e) => onConfigChange({ chartType: e.target.value as 'bar' | 'pie' })}
              data-testid="chart-type-select"
            >
              {chartTypeOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </Form.Select>
          </Form.Field>
          <Form.Field className="flex-1">
            <Form.Label htmlFor="x-axis-select">X-Axis</Form.Label>
            <Form.Select
              id="x-axis-select"
              value={xAxis}
              onChange={(e) => onConfigChange({ xAxis: e.target.value })}
              data-testid="x-axis-select"
            >
              {xAxisOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </Form.Select>
          </Form.Field>
          <Form.Field className="flex-1">
            <Form.Label htmlFor="y-axis-select">Y-Axis</Form.Label>
            <Form.Select
              id="y-axis-select"
              value={yAxis}
              onChange={(e) => onConfigChange({ yAxis: e.target.value })}
              data-testid="y-axis-select"
            >
              {yAxisOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </Form.Select>
          </Form.Field>
        </div>
        
        <div className="flex items-end">
          <Button
            variant="outline"
            size="sm"
            onClick={onExportChart}
            leftIcon={
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path 
                  d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" 
                  stroke="currentColor" 
                  strokeWidth="2" 
                  strokeLinecap="round" 
                  strokeLinejoin="round"
                />
                <polyline 
                  points="7,10 12,15 17,10" 
                  stroke="currentColor" 
                  strokeWidth="2" 
                  strokeLinecap="round" 
                  strokeLinejoin="round"
                />
                <line 
                  x1="12" 
                  y1="15" 
                  x2="12" 
                  y2="3" 
                  stroke="currentColor" 
                  strokeWidth="2" 
                  strokeLinecap="round" 
                  strokeLinejoin="round"
                />
              </svg>
            }
            data-testid="export-chart-button"
          >
            Export Chart
          </Button>
        </div>
      </div>

      {/* Chart Display */}
      {chartData.length > 0 ? (
        <div className="bg-white border rounded-lg p-6">
          {/* Simple Bar Chart Implementation */}
          {chartType === 'bar' && (
            <div className="space-y-4">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium">
                  {yAxisOptions.find(opt => opt.value === yAxis)?.label} by{' '}
                  {xAxisOptions.find(opt => opt.value === xAxis)?.label}
                </h3>
                <span className="text-sm text-gray-600">
                  Total: {totalFormatted}
                </span>
              </div>
              
              <div className="space-y-2">
                {chartData.slice(0, 10).map((item, index) => {
                  const percentage = total > 0 ? (item.value / total) * 100 : 0
                  const displayValue = yAxis === 'annualCost' 
                    ? `$${item.value.toLocaleString()}` 
                    : item.value.toLocaleString()
                  
                  return (
                    <div key={item.name} className="flex items-center gap-3">
                      <div className="w-24 text-sm text-gray-600 truncate" title={item.name}>
                        {item.name}
                      </div>
                      <div className="flex-1 bg-gray-200 rounded-full h-6 relative">
                        <div
                          className="bg-blue-600 h-6 rounded-full flex items-center justify-end pr-2"
                          style={{ width: `${Math.max(percentage, 2)}%` }}
                        >
                          <span className="text-xs text-white font-medium">
                            {displayValue}
                          </span>
                        </div>
                      </div>
                      <div className="w-12 text-xs text-gray-500 text-right">
                        {percentage.toFixed(1)}%
                      </div>
                    </div>
                  )
                })}
              </div>
              
              <div className="text-sm text-gray-500 mt-4">
                Showing {Math.min(chartData.length, 10)} of {chartData.length} data points • Total: {totalFormatted}
              </div>
            </div>
          )}

          {/* Simple Pie Chart Implementation (Text-based) */}
          {chartType === 'pie' && (
            <div className="space-y-4">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium">
                  {yAxisOptions.find(opt => opt.value === yAxis)?.label} Distribution by{' '}
                  {xAxisOptions.find(opt => opt.value === xAxis)?.label}
                </h3>
                <span className="text-sm text-gray-600">
                  Total: {totalFormatted}
                </span>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {chartData.slice(0, 8).map((item, index) => {
                  const percentage = total > 0 ? (item.value / total) * 100 : 0
                  const displayValue = yAxis === 'annualCost' 
                    ? `$${item.value.toLocaleString()}` 
                    : item.value.toLocaleString()
                  
                  const colors = [
                    'bg-blue-500', 'bg-green-500', 'bg-yellow-500', 'bg-red-500',
                    'bg-purple-500', 'bg-pink-500', 'bg-indigo-500', 'bg-gray-500'
                  ]
                  
                  return (
                    <div key={item.name} className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                      <div className={`w-4 h-4 rounded-full ${colors[index % colors.length]}`}></div>
                      <div className="flex-1">
                        <div className="font-medium text-sm" title={item.name}>
                          {item.name}
                        </div>
                        <div className="text-xs text-gray-600">
                          {displayValue} ({percentage.toFixed(1)}%)
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>
          )}
        </div>
      ) : (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-8 text-center">
          <div className="text-4xl mb-2">📊</div>
          <p className="text-gray-600">No data available for visualization</p>
          <p className="text-sm text-gray-500 mt-1">Try adjusting your filters to see chart data</p>
        </div>
      )}
    </div>
  )
}

/**
 * Table Sorting Hook
 * 
 * Consolidates table sorting patterns found across components.
 * Provides consistent sorting logic with multiple data types support.
 */

import { useState, useCallback, useMemo } from 'react';

export type SortDirection = 'asc' | 'desc';

export interface SortConfig<T = any> {
  // Initial sort field
  initialSortField?: keyof T;
  
  // Initial sort direction
  initialSortDirection?: SortDirection;
  
  // Custom sort functions for specific fields
  customSortFunctions?: Partial<Record<keyof T, (a: T, b: T, direction: SortDirection) => number>>;
  
  // Date fields (for proper date sorting)
  dateFields?: Array<keyof T>;
  
  // Number fields (for proper numeric sorting)
  numberFields?: Array<keyof T>;
  
  // Case sensitive string sorting
  caseSensitive?: boolean;
  
  // Callbacks
  onSort?: (field: keyof T, direction: SortDirection, sortedData: T[]) => void;
}

export interface SortState<T = any> {
  sortField: keyof T | null;
  sortDirection: SortDirection;
  isSorted: boolean;
}

export interface SortActions<T = any> {
  sort: (field: keyof T) => void;
  setSortField: (field: keyof T) => void;
  setSortDirection: (direction: SortDirection) => void;
  toggleDirection: () => void;
  reset: () => void;
}

export interface UseTableSortReturn<T = any> {
  state: SortState<T>;
  actions: SortActions<T>;
  sortedData: T[];
  
  // Convenience getters
  sortField: keyof T | null;
  sortDirection: SortDirection;
  isSorted: boolean;
  
  // Table header helpers
  getSortIcon: (field: keyof T) => string;
  getHeaderProps: (field: keyof T) => {
    onClick: () => void;
    'aria-sort': 'none' | 'ascending' | 'descending';
    className: string;
  };
}

/**
 * Table Sort Hook
 */
export function useTableSort<T = any>(
  data: T[],
  config: SortConfig<T> = {}
): UseTableSortReturn<T> {
  const {
    initialSortField = null,
    initialSortDirection = 'asc',
    customSortFunctions = {},
    dateFields = [],
    numberFields = [],
    caseSensitive = false,
    onSort
  } = config;
  
  // Sort state
  const [sortField, setSortFieldState] = useState<keyof T | null>(initialSortField);
  const [sortDirection, setSortDirectionState] = useState<SortDirection>(initialSortDirection);
  
  // Default sort function
  const defaultSortFunction = useCallback((a: T, b: T, field: keyof T, direction: SortDirection): number => {
    let aValue = a[field];
    let bValue = b[field];
    
    // Handle null/undefined values
    if (aValue == null && bValue == null) return 0;
    if (aValue == null) return direction === 'asc' ? -1 : 1;
    if (bValue == null) return direction === 'asc' ? 1 : -1;
    
    // Handle date fields
    if (dateFields.includes(field)) {
      const aTime = new Date(aValue as any).getTime();
      const bTime = new Date(bValue as any).getTime();
      const result = aTime - bTime;
      return direction === 'asc' ? result : -result;
    }
    
    // Handle number fields
    if (numberFields.includes(field)) {
      const aNum = Number(aValue);
      const bNum = Number(bValue);
      const result = aNum - bNum;
      return direction === 'asc' ? result : -result;
    }
    
    // Handle string fields
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      const aStr = caseSensitive ? aValue : aValue.toLowerCase();
      const bStr = caseSensitive ? bValue : bValue.toLowerCase();
      const result = aStr.localeCompare(bStr);
      return direction === 'asc' ? result : -result;
    }
    
    // Handle boolean fields
    if (typeof aValue === 'boolean' && typeof bValue === 'boolean') {
      const result = Number(aValue) - Number(bValue);
      return direction === 'asc' ? result : -result;
    }
    
    // Fallback to string comparison
    const aStr = String(aValue);
    const bStr = String(bValue);
    const result = aStr.localeCompare(bStr);
    return direction === 'asc' ? result : -result;
  }, [dateFields, numberFields, caseSensitive]);
  
  // Sorted data
  const sortedData = useMemo(() => {
    if (!sortField) return data;
    
    const sorted = [...data].sort((a, b) => {
      // Use custom sort function if provided
      if (sortField && customSortFunctions && sortField in customSortFunctions) {
        const customSort = (customSortFunctions as any)[sortField];
        if (customSort) {
          return customSort(a, b, sortDirection);
        }
      }

      // Use default sort function
      return defaultSortFunction(a, b, sortField, sortDirection);
    });
    
    // Call onSort callback if provided
    if (onSort) {
      onSort(sortField, sortDirection, sorted);
    }
    
    return sorted;
  }, [data, sortField, sortDirection, customSortFunctions, defaultSortFunction, onSort]);
  
  // Sort by field (toggle direction if same field)
  const sort = useCallback((field: keyof T) => {
    if (field === sortField) {
      // Toggle direction if same field
      setSortDirectionState(prev => prev === 'asc' ? 'desc' : 'asc');
    } else {
      // Set new field and reset to ascending
      setSortFieldState(field);
      setSortDirectionState('asc');
    }
  }, [sortField]);
  
  // Set sort field
  const setSortField = useCallback((field: keyof T) => {
    setSortFieldState(field);
  }, []);
  
  // Set sort direction
  const setSortDirection = useCallback((direction: SortDirection) => {
    setSortDirectionState(direction);
  }, []);
  
  // Toggle sort direction
  const toggleDirection = useCallback(() => {
    setSortDirectionState(prev => prev === 'asc' ? 'desc' : 'asc');
  }, []);
  
  // Reset sort state
  const reset = useCallback(() => {
    setSortFieldState(initialSortField);
    setSortDirectionState(initialSortDirection);
  }, [initialSortField, initialSortDirection]);
  
  // Get sort icon for field
  const getSortIcon = useCallback((field: keyof T): string => {
    if (field !== sortField) return '↕️'; // Unsorted
    return sortDirection === 'asc' ? '↑' : '↓';
  }, [sortField, sortDirection]);
  
  // Get header props for sortable columns
  const getHeaderProps = useCallback((field: keyof T): {
    onClick: () => void;
    'aria-sort': 'none' | 'ascending' | 'descending';
    className: string;
  } => ({
    onClick: () => sort(field),
    'aria-sort': (
      field !== sortField ? 'none' :
      sortDirection === 'asc' ? 'ascending' : 'descending'
    ) as 'none' | 'ascending' | 'descending',
    className: `sortable-header ${field === sortField ? 'sorted' : ''}`
  }), [sort, sortField, sortDirection]);
  
  return {
    state: {
      sortField,
      sortDirection,
      isSorted: sortField !== null
    },
    actions: {
      sort,
      setSortField,
      setSortDirection,
      toggleDirection,
      reset
    },
    sortedData,
    
    // Convenience getters
    sortField,
    sortDirection,
    isSorted: sortField !== null,
    
    // Helpers
    getSortIcon,
    getHeaderProps
  };
}

/**
 * Convenience hooks for common sorting patterns
 */

// Simple string sorting
export function useSimpleSort<T>(data: T[], initialField?: keyof T) {
  return useTableSort(data, { initialSortField: initialField });
}

// Date-aware sorting
export function useDateSort<T>(data: T[], dateFields: Array<keyof T>, initialField?: keyof T) {
  return useTableSort(data, { 
    dateFields, 
    initialSortField: initialField 
  });
}

// Number-aware sorting
export function useNumberSort<T>(data: T[], numberFields: Array<keyof T>, initialField?: keyof T) {
  return useTableSort(data, { 
    numberFields, 
    initialSortField: initialField 
  });
}

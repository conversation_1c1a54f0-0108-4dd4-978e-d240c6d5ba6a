/**
 * Test Sidebar API (No Auth Required)
 * Simple endpoint to test if sidebar API calls are working
 */

import { NextRequest } from 'next/server'
import { createSuccessResponse, createErrorResponse, ApiErrorCode, HttpStatus } from '@/lib/api/response'
import { executeQuery } from '@/lib/database'

export interface TestSidebarPage {
  id: number
  name: string
  header: string
  description: string | null
  display_order: number
  icon_svg: string | null
  route_path: string
}

/**
 * GET /api/test-sidebar
 * Test sidebar pages without authentication
 */
export async function GET(request: NextRequest) {
  try {
    console.log('🔍 [TEST-SIDEBAR] Testing sidebar pages query...')

    // Simple query to get all active sidebar pages
    const query = `
      SELECT 
        id,
        name,
        header,
        description,
        display_order,
        icon_svg,
        route_path
      FROM metadata.admin_pages 
      WHERE status = 'A' AND sidebar = true
      ORDER BY display_order ASC, name ASC
      LIMIT 10
    `

    const result = await executeQuery(query)

    if (!result.success) {
      console.error('❌ [TEST-SIDEBAR] Database query failed:', result.error)
      return createErrorResponse(
        'Database query failed',
        ApiErrorCode.INTERNAL_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR,
        result.error
      )
    }

    console.log('✅ [TEST-SIDEBAR] Query result:', {
      rowCount: result.rowCount,
      dataLength: result.data?.length || 0,
      data: result.data
    })

    const sidebarPages: TestSidebarPage[] = (result.data || []).map((row: any) => ({
      id: row.id,
      name: row.name,
      header: row.header,
      description: row.description,
      display_order: row.display_order,
      icon_svg: row.icon_svg,
      route_path: row.route_path
    }))

    console.log(`🎉 [TEST-SIDEBAR] Found ${sidebarPages.length} sidebar pages`)

    return createSuccessResponse({
      message: 'Sidebar pages retrieved successfully',
      pages: sidebarPages,
      count: sidebarPages.length,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('❌ [TEST-SIDEBAR] Error fetching sidebar pages:', error)
    return createErrorResponse(
      'Failed to fetch sidebar pages',
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR,
      error instanceof Error ? error.message : 'Unknown error'
    )
  }
}

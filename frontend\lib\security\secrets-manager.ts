/**
 * AWS Parameter Store Service
 *
 * Provides secure access to parameters stored in AWS Parameter Store only.
 * Implements caching and fallback mechanisms for production environments.
 *
 * Note: We only use Parameter Store, not Secrets Manager, as per user preference.
 */

import {
  SSMClient,
  GetParameterCommand,
  GetParametersCommand,
  Parameter
} from '@aws-sdk/client-ssm';
import { getConfig } from '@/lib/config';

export interface ParameterConfig {
  name: string;
  required: boolean;
  cached?: boolean;
  ttl?: number; // Time to live in seconds
}

export interface CachedParameter {
  value: string;
  timestamp: number;
  ttl: number;
}

export class ParameterStoreService {
  private ssmClient: SSMClient;
  private cache: Map<string, CachedParameter> = new Map();
  private readonly defaultTTL = 300; // 5 minutes

  constructor() {
    // Try multiple ways to get the region
    const region = process.env.NEXT_PUBLIC_AWS_REGION ||
                   process.env.AWS_REGION ||
                   process.env.AWS_DEFAULT_REGION ||
                   'ca-central-1'; // fallback to your region



    if (!region) {
      throw new Error('AWS region is required but not configured. Please set NEXT_PUBLIC_AWS_REGION environment variable.');
    }

    this.ssmClient = new SSMClient({
      region,
      // Use IAM roles in production, credentials will be automatically resolved
    });
  }

  /**
   * Get a parameter from AWS Parameter Store
   */
  async getParameter(
    name: string, 
    decrypt: boolean = true,
    useCache: boolean = true,
    ttl: number = this.defaultTTL
  ): Promise<string | null> {
    const cacheKey = `param:${name}`;
    
    // Check cache first
    if (useCache && this.isCacheValid(cacheKey)) {
      const cached = this.cache.get(cacheKey);

      return cached!.value;
    }

    try {
      const command = new GetParameterCommand({
        Name: name,
        WithDecryption: decrypt
      });

      const response = await this.ssmClient.send(command);
      const value = response.Parameter?.Value;

      if (!value) {
        return null;
      }

      // Cache the value
      if (useCache) {
        this.cache.set(cacheKey, {
          value,
          timestamp: Date.now(),
          ttl: ttl * 1000 // Convert to milliseconds
        });
      }

      return value;

    } catch (error) {
      return null;
    }
  }

  /**
   * Get multiple parameters at once
   */
  async getParameters(
    names: string[],
    decrypt: boolean = true,
    useCache: boolean = true
  ): Promise<Record<string, string>> {
    const result: Record<string, string> = {};
    const uncachedNames: string[] = [];

    // Check cache for each parameter
    if (useCache) {
      for (const name of names) {
        const cacheKey = `param:${name}`;
        if (this.isCacheValid(cacheKey)) {
          const cached = this.cache.get(cacheKey);
          result[name] = cached!.value;
        } else {
          uncachedNames.push(name);
        }
      }
    } else {
      uncachedNames.push(...names);
    }

    // Fetch uncached parameters
    if (uncachedNames.length > 0) {
      try {
        const command = new GetParametersCommand({
          Names: uncachedNames,
          WithDecryption: decrypt
        });

        const response = await this.ssmClient.send(command);
        
        if (response.Parameters) {
          for (const param of response.Parameters) {
            if (param.Name && param.Value) {
              result[param.Name] = param.Value;
              
              // Cache the value
              if (useCache) {
                this.cache.set(`param:${param.Name}`, {
                  value: param.Value,
                  timestamp: Date.now(),
                  ttl: this.defaultTTL * 1000
                });
              }
            }
          }
        }

      } catch (error) {
        // Error retrieving parameters
      }
    }

    return result;
  }

  // Removed getSecret method - we only use Parameter Store

  /**
   * Get database configuration from Parameter Store
   */
  async getDatabaseConfig(): Promise<{
    host: string;
    port: number;
    database: string;
    username: string;
    password?: string;
    useIAM: boolean;
  } | null> {
    try {
      // Get database configuration from Parameter Store only
      const dbParams = await this.getParameters([
        '/renewtrack/database/host',
        '/renewtrack/database/port',
        '/renewtrack/database/name',
        '/renewtrack/database/username',
        '/renewtrack/database/password',
        '/renewtrack/database/use-iam'
      ]);

      const useIAM = dbParams['/renewtrack/database/use-iam'] === 'true';

      return {
        host: dbParams['/renewtrack/database/host'] || 'localhost',
        port: parseInt(dbParams['/renewtrack/database/port'] || '5432'),
        database: dbParams['/renewtrack/database/name'] || 'renewtrack',
        username: dbParams['/renewtrack/database/username'] || 'postgres',
        password: useIAM ? undefined : dbParams['/renewtrack/database/password'],
        useIAM
      };

    } catch (error) {
      return null;
    }
  }

  /**
   * Get application parameters from Parameter Store
   */
  async getApplicationSecrets(): Promise<{
    jwtSecret: string | null;
    encryptionKey: string | null;
    apiKeys: Record<string, string>;
  }> {
    try {
      const params = await this.getParameters([
        '/renewtrack/jwt-secret',
        '/renewtrack/encryption-key',
        '/renewtrack/api-keys'
      ]);

      let apiKeys: Record<string, string> = {};
      const apiKeysJson = params['/renewtrack/api-keys'];
      if (apiKeysJson) {
        try {
          apiKeys = JSON.parse(apiKeysJson);
        } catch (error) {
          // Error parsing API keys JSON
        }
      }

      return {
        jwtSecret: params['/renewtrack/jwt-secret'] || null,
        encryptionKey: params['/renewtrack/encryption-key'] || null,
        apiKeys
      };

    } catch (error) {
      return {
        jwtSecret: null,
        encryptionKey: null,
        apiKeys: {}
      };
    }
  }

  /**
   * Check if cached value is still valid
   */
  private isCacheValid(key: string): boolean {
    const cached = this.cache.get(key);
    if (!cached) return false;
    
    const now = Date.now();
    return (now - cached.timestamp) < cached.ttl;
  }

  /**
   * Clear cache
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * Clear expired cache entries
   */
  clearExpiredCache(): void {
    const now = Date.now();
    let cleared = 0;
    
    for (const [key, cached] of this.cache.entries()) {
      if ((now - cached.timestamp) >= cached.ttl) {
        this.cache.delete(key);
        cleared++;
      }
    }
    
    // Cache entries cleared silently
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): {
    size: number;
    entries: Array<{ key: string; age: number; ttl: number }>;
  } {
    const now = Date.now();
    const entries = Array.from(this.cache.entries()).map(([key, cached]) => ({
      key,
      age: now - cached.timestamp,
      ttl: cached.ttl
    }));

    return {
      size: this.cache.size,
      entries
    };
  }
}

// Global parameter store service instance
export const parameterStoreService = new ParameterStoreService();

// For backward compatibility
export const secretsManager = parameterStoreService;

// Utility function to get environment variable with fallback to Parameter Store
export async function getSecureConfig(
  envVar: string,
  parameterName?: string,
  required: boolean = false
): Promise<string | null> {
  // First try environment variable
  const envValue = process.env[envVar];
  if (envValue) {
    return envValue;
  }

  // In development, don't try Parameter Store to avoid AWS configuration issues
  const isDevelopment = process.env.NODE_ENV === 'development';

  if (!isDevelopment && parameterName) {
    try {
      // Then try Parameter Store
      const paramValue = await parameterStoreService.getParameter(parameterName);
      if (paramValue) {
        return paramValue;
      }
    } catch (error) {
      // Failed to get parameter from Parameter Store
    }
  }

  if (required) {
    throw new Error(`Required configuration '${envVar}' not found in environment${!isDevelopment ? ' or Parameter Store' : ''}`);
  }

  return null;
}

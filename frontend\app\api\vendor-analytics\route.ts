/**
 * Vendor Analytics API Route
 * 
 * Provides comprehensive vendor analytics including:
 * - Vendor performance metrics
 * - Spending trends over time
 * - Risk analysis
 * - Vendor comparison data
 */

import { createApiRoute } from '@/lib/api/route-factory'
import { z } from 'zod'
import { vendorAnalyticsCache } from '@/lib/services/vendor-analytics-cache'
import { performanceMonitor } from '@/lib/services/performance-monitor'

const vendorAnalyticsSchema = z.object({
  vendor_id: z.string().optional(),
  period: z.enum(['month', 'quarter', 'year']).optional().default('month'),
  include_trends: z.boolean().optional().default(true),
  include_risk_analysis: z.boolean().optional().default(true),
  limit: z.number().min(1).max(100).optional().default(50)
})

export const GET = createApiRoute('GET', {
  requireAuth: true,
  requireTenant: true,
  querySchema: vendorAnalyticsSchema,
  handler: async (context) => {
    const startTime = Date.now()
    const { tenant, query } = context
    if (!tenant) {
      throw new Error('Tenant context is required')
    }
    const { vendor_id, period, include_trends, include_risk_analysis, limit } = query || {}

    try {
      // Use cached analytics with performance monitoring
      const result = await vendorAnalyticsCache.getVendorAnalytics(
        { vendor_id, period, include_trends, include_risk_analysis, limit },
        tenant.tenantId,
        async () => {
          // Original fetch logic here
          return await fetchVendorAnalyticsData(context, query)
        }
      )

      // Track performance
      const responseTime = Date.now() - startTime
      performanceMonitor.trackAPICall(
        '/api/vendor-analytics',
        'GET',
        responseTime,
        200,
        tenant.tenantId
      )

      return result
    } catch (error) {
      const responseTime = Date.now() - startTime
      performanceMonitor.trackAPICall(
        '/api/vendor-analytics',
        'GET',
        responseTime,
        500,
        tenant.tenantId
      )
      throw error
    }

    try {
      // Base vendor analytics query
      let analyticsQuery = `
        WITH vendor_metrics AS (
          SELECT 
            v.id as vendor_id,
            v.name as vendor_name,
            v.display_name,
            v.contact_email,
            v.website,
            v.city,
            v.state,
            v.country,
            COUNT(r.id) as total_renewals,
            COUNT(DISTINCT r.product_id) as unique_products,
            COALESCE(SUM(r.annual_cost), 0) as total_spend,
            COALESCE(AVG(r.annual_cost), 0) as avg_renewal_cost,
            COALESCE(MIN(r.annual_cost), 0) as min_renewal_cost,
            COALESCE(MAX(r.annual_cost), 0) as max_renewal_cost,
            COUNT(CASE WHEN r.due_date BETWEEN CURRENT_DATE AND CURRENT_DATE + INTERVAL '90 days' THEN 1 END) as upcoming_renewals,
            COUNT(CASE WHEN r.due_date < CURRENT_DATE THEN 1 END) as overdue_renewals,
            COALESCE(AVG(r.reliability_score), 85) as avg_reliability_score,
            MIN(r.created_on) as first_renewal_date,
            MAX(r.changed_on) as last_activity_date,
            ARRAY_AGG(DISTINCT r.currency) FILTER (WHERE r.currency IS NOT NULL) as currencies,
            ARRAY_AGG(DISTINCT p.category) FILTER (WHERE p.category IS NOT NULL) as product_categories
          FROM "${tenant!.tenantSchema}".vendors v
          LEFT JOIN "${tenant!.tenantSchema}".renewals r ON v.id = r.vendor_id AND r.status = 'A'
          LEFT JOIN "${tenant!.tenantSchema}".products p ON r.product_id = p.id
          WHERE v.is_deleted = false
      `

      let queryParams: any[] = []
      let paramIndex = 1

      // Filter by specific vendor if requested
      if (vendor_id) {
        analyticsQuery += ` AND v.id = $${paramIndex}`
        queryParams.push(vendor_id)
        paramIndex++
      }

      analyticsQuery += `
          GROUP BY v.id, v.name, v.display_name, v.contact_email, v.website, v.city, v.state, v.country
        ),
        vendor_rankings AS (
          SELECT 
            *,
            ROW_NUMBER() OVER (ORDER BY total_spend DESC) as spend_rank,
            ROW_NUMBER() OVER (ORDER BY total_renewals DESC) as renewal_count_rank,
            ROW_NUMBER() OVER (ORDER BY avg_reliability_score DESC) as reliability_rank,
            CASE 
              WHEN total_spend > 0 THEN 
                ROUND((total_spend / SUM(total_spend) OVER ()) * 100, 2)
              ELSE 0 
            END as spend_percentage
          FROM vendor_metrics
        )
        SELECT * FROM vendor_rankings
        ORDER BY total_spend DESC
        LIMIT $${paramIndex}
      `
      queryParams.push(limit)

      const analyticsResult = await context.executeQuery!(analyticsQuery, queryParams)

      if (!analyticsResult.success) {
        throw new Error('Failed to fetch vendor analytics data')
      }

      const vendors = analyticsResult.data || []

      // Calculate additional metrics
      const totalVendors = vendors.length
      const totalSpend = vendors.reduce((sum: number, v: any) => sum + (v.total_spend || 0), 0)
      const avgSpendPerVendor = totalVendors > 0 ? totalSpend / totalVendors : 0

      // Risk analysis if requested
      let riskAnalysis = null
      if (include_risk_analysis) {
        riskAnalysis = vendors.map((vendor: any) => {
          const concentrationRisk = vendor.spend_percentage > 30 ? 'high' : 
                                   vendor.spend_percentage > 15 ? 'medium' : 'low'
          
          const reliabilityRisk = vendor.avg_reliability_score < 70 ? 'high' :
                                 vendor.avg_reliability_score < 85 ? 'medium' : 'low'
          
          const overdueRisk = vendor.overdue_renewals > 0 ? 'high' : 'low'
          
          // Calculate overall risk score (0-100, lower is better)
          let riskScore = 0
          if (concentrationRisk === 'high') riskScore += 40
          else if (concentrationRisk === 'medium') riskScore += 20
          
          if (reliabilityRisk === 'high') riskScore += 30
          else if (reliabilityRisk === 'medium') riskScore += 15
          
          if (overdueRisk === 'high') riskScore += 30

          return {
            vendor_id: vendor.vendor_id,
            vendor_name: vendor.vendor_name,
            overall_risk_score: riskScore,
            risk_level: riskScore > 60 ? 'high' : riskScore > 30 ? 'medium' : 'low',
            risk_factors: {
              concentration: concentrationRisk,
              reliability: reliabilityRisk,
              overdue_renewals: overdueRisk
            },
            recommendations: generateRiskRecommendations(concentrationRisk, reliabilityRisk, overdueRisk)
          }
        })
      }

      // Spending trends if requested
      let spendingTrends = null
      if (include_trends && !vendor_id) {
        const trendsQuery = `
          SELECT 
            DATE_TRUNC($1, r.created_on) as period,
            v.name as vendor_name,
            COUNT(r.id) as renewal_count,
            SUM(r.annual_cost) as period_spend
          FROM "${tenant!.tenantSchema}".renewals r
          JOIN "${tenant!.tenantSchema}".vendors v ON r.vendor_id = v.id
          WHERE r.status = 'A' 
            AND r.created_on >= CURRENT_DATE - INTERVAL '12 ${period}s'
          GROUP BY DATE_TRUNC($1, r.created_on), v.name
          ORDER BY period DESC, period_spend DESC
        `

        const trendsResult = await context.executeQuery!(trendsQuery, [period])
        spendingTrends = trendsResult.success ? trendsResult.data : []
      }

      return {
        summary: {
          total_vendors: totalVendors,
          total_spend: totalSpend,
          avg_spend_per_vendor: avgSpendPerVendor,
          period,
          generated_at: new Date().toISOString()
        },
        vendors,
        risk_analysis: riskAnalysis,
        spending_trends: spendingTrends,
        metadata: {
          query_params: query,
          result_count: vendors.length,
          includes_trends: include_trends,
          includes_risk_analysis: include_risk_analysis
        }
      }

    } catch (error) {
      console.error('[VENDOR-ANALYTICS-API] Error:', error)
      throw error
    }
  }
})

/**
 * Original vendor analytics data fetching logic
 */
async function fetchVendorAnalyticsData(context: any, query: any) {
  const { tenant } = context
  const { vendor_id, period, include_trends, include_risk_analysis, limit } = query || {}

  if (!tenant) {
    throw new Error('Tenant context is required')
  }

  console.log('[VENDOR-ANALYTICS-API] GET request received')
  console.log('[VENDOR-ANALYTICS-API] Query parameters:', { vendor_id, period, include_trends, include_risk_analysis, limit })

  // Build the analytics query based on parameters
  let analyticsQuery = `
    SELECT
      v.vendor_id,
      v.name as vendor_name,
      v.display_name,
      COUNT(r.renewal_id) as total_renewals,
      SUM(CASE WHEN r.status = 'active' THEN 1 ELSE 0 END) as active_renewals,
      SUM(CASE WHEN r.status = 'expired' THEN 1 ELSE 0 END) as expired_renewals,
      SUM(CASE WHEN r.renewal_date < CURRENT_DATE + INTERVAL '30 days' THEN 1 ELSE 0 END) as upcoming_renewals,
      AVG(r.annual_cost) as avg_annual_cost,
      SUM(r.annual_cost) as total_annual_cost,
      MIN(r.created_on) as first_renewal_date,
      MAX(r.renewal_date) as latest_renewal_date,
      EXTRACT(DAYS FROM (MAX(r.renewal_date) - MIN(r.created_on))) as relationship_days
    FROM "${tenant.tenantSchema}".tenant_vendors v
    LEFT JOIN "${tenant.tenantSchema}".tenant_renewals r ON v.vendor_id = r.vendor_id
  `

  let queryParams: any[] = []
  let whereConditions: string[] = []

  // Add vendor filter if specified
  if (vendor_id) {
    whereConditions.push('v.vendor_id = $' + (queryParams.length + 1))
    queryParams.push(vendor_id)
  }

  // Add period filter if specified
  if (period) {
    const periodDays = period === '30d' ? 30 : period === '90d' ? 90 : period === '1y' ? 365 : null
    if (periodDays) {
      whereConditions.push('r.renewal_date >= CURRENT_DATE - INTERVAL \'' + periodDays + ' days\'')
    }
  }

  // Add WHERE clause if we have conditions
  if (whereConditions.length > 0) {
    analyticsQuery += ' WHERE ' + whereConditions.join(' AND ')
  }

  analyticsQuery += `
    GROUP BY v.vendor_id, v.name, v.display_name
    ORDER BY total_annual_cost DESC NULLS LAST
  `

  // Add limit if specified
  if (limit) {
    analyticsQuery += ` LIMIT ${parseInt(limit)}`
  }

  console.log('[VENDOR-ANALYTICS-API] Executing query:', analyticsQuery)
  console.log('[VENDOR-ANALYTICS-API] Query parameters:', queryParams)

  const result = await context.executeQuery!(analyticsQuery, queryParams)

  if (!result.success) {
    console.error('[VENDOR-ANALYTICS-API] Database error:', result.error)
    throw new Error('Failed to fetch vendor analytics')
  }

  let analytics = result.data || []

  // Add trends data if requested
  if (include_trends && analytics.length > 0) {
    console.log('[VENDOR-ANALYTICS-API] Including trends analysis')

    for (const vendor of analytics) {
      const trendsQuery = `
        SELECT
          DATE_TRUNC('month', r.renewal_date) as month,
          COUNT(*) as renewal_count,
          SUM(r.annual_cost) as monthly_cost
        FROM "${tenant.tenantSchema}".tenant_renewals r
        WHERE r.vendor_id = $1
          AND r.renewal_date >= CURRENT_DATE - INTERVAL '12 months'
        GROUP BY DATE_TRUNC('month', r.renewal_date)
        ORDER BY month
      `

      const trendsResult = await context.executeQuery!(trendsQuery, [vendor.vendor_id])
      vendor.trends = trendsResult.success ? trendsResult.data : []
    }
  }

  // Add risk analysis if requested
  if (include_risk_analysis && analytics.length > 0) {
    console.log('[VENDOR-ANALYTICS-API] Including risk analysis')

    for (const vendor of analytics) {
      // Calculate risk factors
      const riskFactors = {
        concentration_risk: vendor.total_annual_cost > 50000 ? 'high' : vendor.total_annual_cost > 20000 ? 'medium' : 'low',
        renewal_frequency: vendor.total_renewals > 10 ? 'high' : vendor.total_renewals > 5 ? 'medium' : 'low',
        upcoming_renewals_risk: vendor.upcoming_renewals > 5 ? 'high' : vendor.upcoming_renewals > 2 ? 'medium' : 'low'
      }

      vendor.risk_analysis = riskFactors
    }
  }

  console.log('[VENDOR-ANALYTICS-API] Returning analytics for', analytics.length, 'vendors')

  return {
    analytics,
    total_vendors: analytics.length,
    period: period || 'all',
    includes: {
      trends: !!include_trends,
      risk_analysis: !!include_risk_analysis
    }
  }
}

// Helper function to generate risk recommendations
function generateRiskRecommendations(
  concentrationRisk: string, 
  reliabilityRisk: string, 
  overdueRisk: string
): string[] {
  const recommendations: string[] = []

  if (concentrationRisk === 'high') {
    recommendations.push('Consider diversifying vendor portfolio to reduce concentration risk')
  }

  if (reliabilityRisk === 'high') {
    recommendations.push('Review vendor performance and consider alternative suppliers')
  } else if (reliabilityRisk === 'medium') {
    recommendations.push('Monitor vendor performance closely and establish improvement plans')
  }

  if (overdueRisk === 'high') {
    recommendations.push('Immediate action required: Process overdue renewals')
  }

  if (recommendations.length === 0) {
    recommendations.push('Vendor performance is within acceptable parameters')
  }

  return recommendations
}

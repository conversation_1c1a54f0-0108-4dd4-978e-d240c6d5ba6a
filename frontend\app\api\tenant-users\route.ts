/**
 * Tenant Users API Endpoint
 * 
 * Provides CRUD operations for tenant-specific users
 * GET /api/tenant-users - Returns active users for the tenant
 */

import { NextRequest } from 'next/server';
import { fetchAuthSession, fetchUserAttributes } from 'aws-amplify/auth';
import { TenantContext } from '@/lib/tenant/context';
import { getClientByEmailDomain } from '@/lib/tenant/clients';
import { executeTenantQuery } from '@/lib/tenant/database';
import {
  createSuccessResponse,
  createErrorResponse,
  ApiErrorCode,
  HttpStatus,
  withErrorHandling
} from '@/lib/api/response';

import { TenantUser } from '@/lib/types';

// GET /api/tenant-users - Get all users for the tenant
export const GET = withErrorHandling(async (request: NextRequest) => {
  console.log('[TENANT-USERS-API] GET request received');

  // Verify authentication using Amplify
  try {
    const session = await fetchAuthSession();
    if (!session?.tokens?.idToken) {
      return createErrorResponse(
        'Authentication required',
        ApiErrorCode.UNAUTHORIZED,
        HttpStatus.UNAUTHORIZED
      );
    }
  } catch (error) {
    return createErrorResponse(
      'Authentication failed',
      ApiErrorCode.UNAUTHORIZED,
      HttpStatus.UNAUTHORIZED
    );
  }

  // Get user attributes for tenant context
  const userAttributes = await fetchUserAttributes();
  const userEmail = userAttributes.email;

  if (!userEmail) {
    return createErrorResponse(
      'User email not found',
      ApiErrorCode.UNAUTHORIZED,
      HttpStatus.UNAUTHORIZED
    );
  }

  // Create session object for tenant context resolution
  const sessionObj = {
    isAuth: true,
    userId: userAttributes.sub || 'unknown',
    email: userEmail,
    roles: ['user']
  };

  // Get tenant context
  const clientResult = await getClientByEmailDomain(userEmail);
  if (!clientResult.success || !clientResult.client) {
    return createErrorResponse(
      clientResult.error || 'Failed to resolve tenant context',
      (clientResult.errorCode as ApiErrorCode) || ApiErrorCode.UNAUTHORIZED,
      HttpStatus.UNAUTHORIZED
    );
  }

  const tenant = clientResult.client;

  try {
    const query = `
      SELECT
        user_id as id,
        cognito_user_id,
        email,
        given_name,
        family_name,
        last_login as last_logged,
        user_group
      FROM "${tenant.tenantSchema}".tenant_users
      ORDER BY email ASC
    `;

    console.log(`[TENANT-USERS-API] Executing query for tenant: ${tenant.tenantSchema}`);

    const result = await executeTenantQuery(query, [], tenant);

    if (!result.success) {
      console.error('[TENANT-USERS-API] Database query failed:', result.error);
      return createErrorResponse(
        'Failed to fetch tenant users',
        ApiErrorCode.DATABASE_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }

      const users = result.data || [];
      console.log(`[TENANT-USERS-API] Found ${users.length} users for tenant ${tenant.tenantSchema}`);

      return createSuccessResponse(
        users,
        'Tenant users retrieved successfully'
      );

    } catch (error) {
      console.error('[TENANT-USERS-API] Unexpected error:', error);
      return createErrorResponse(
        'Internal server error',
        ApiErrorCode.INTERNAL_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
});

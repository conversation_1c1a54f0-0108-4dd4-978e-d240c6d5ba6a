# Codebase Organization Guide

## 📁 **Improved File Structure**

The RenewTrack codebase has been reorganized for better maintainability, discoverability, and developer experience.

## 🗂️ **Directory Structure**

### **Frontend Organization**

```
frontend/
├── app/                          # Next.js 13+ app directory
│   ├── api/                      # API routes (kebab-case)
│   ├── (pages)/                  # Page components
│   └── layout.tsx                # Root layout
│
├── components/                   # React components
│   ├── ui/                       # Design system components
│   ├── layout/                   # Layout components
│   ├── modals/                   # Modal components
│   ├── overview/                 # Dashboard/overview components
│   ├── renewals/                 # Renewal-specific components
│   ├── admin/                    # Admin components
│   ├── providers/                # Context providers
│   └── index.ts                  # Centralized exports
│
├── lib/                          # Utility libraries
│   ├── api/                      # API utilities
│   ├── config/                   # Configuration modules
│   ├── services/                 # Business logic services
│   ├── utils/                    # Utility functions
│   ├── sync/                     # Data synchronization
│   ├── monitoring/               # Monitoring utilities
│   └── index.ts                  # Centralized exports
│
├── hooks/                        # Custom React hooks
│   └── index.ts                  # Centralized exports
│
├── contexts/                     # React contexts
├── styles/                       # CSS and styling
└── types/                        # TypeScript definitions
```

## 📦 **Import Organization**

### **Centralized Exports**

Each major directory now has an `index.ts` file for clean imports:

```typescript
// ✅ Clean imports using centralized exports
import { Button, Modal, Form } from "@/components";
import { formatDate, formatCurrency } from "@/lib/utils";
import { getMetadataOptions } from "@/lib/services";

// ❌ Avoid scattered imports
import { Button } from "@/components/ui/Button";
import { Modal } from "@/components/ui/Modal";
import { formatDate } from "@/lib/utils/date-utils";
```

### **Import Patterns**

1. **Components**: Use centralized component exports
2. **Utilities**: Import from organized utility modules
3. **Services**: Use service index for business logic
4. **Configuration**: Use config index for all settings

## 🔧 **Configuration Organization**

### **Structured Configuration**

```
lib/config/
├── index.ts          # Main configuration exports
├── aws.ts            # AWS-specific configuration
├── database.ts       # Database configuration
└── security.ts       # Security settings
```

### **Usage Examples**

```typescript
// Configuration access
import { getConfig, getDatabaseConfig, RATE_LIMITS } from "@/lib/config";

// AWS services
import { getCredentials, getParameter } from "@/lib/config/aws";

// Security settings
import { CORS_CONFIG, RATE_LIMITS } from "@/lib/config/security";
```

## 🛠️ **Utility Organization**

### **Categorized Utilities**

```
lib/utils/
├── index.ts          # Centralized utility exports
├── date-utils.ts     # Date manipulation functions
├── format-utils.ts   # Formatting functions
├── string-utils.ts   # String manipulation
├── number-utils.ts   # Number utilities
├── array-utils.ts    # Array helpers
├── object-utils.ts   # Object manipulation
├── validation-utils.ts # Validation helpers
├── url-utils.ts      # URL utilities
└── file-utils.ts     # File handling
```

### **Utility Usage**

```typescript
// Date utilities
import {
  formatDate,
  getDaysUntilDate,
  isDateInPast,
} from "@/lib/utils/date-utils";

// Format utilities
import {
  formatCurrency,
  formatFileSize,
  truncateText,
} from "@/lib/utils/format-utils";

// Or import all utilities
import { formatDate, formatCurrency } from "@/lib/utils";
```

## 🏗️ **Service Organization**

### **Business Logic Services**

```
lib/services/
├── index.ts              # Service exports
├── amplify-service.ts    # AWS Amplify configuration
├── auth-service.ts       # Authentication logic
├── database-service.ts   # Database operations
├── metadataService.ts    # Metadata management
├── renewalService.ts     # Renewal operations
└── app-state-service.ts  # Application state
```

### **Service Usage**

```typescript
// Metadata operations
import { getMetadataOptions, createVendor } from "@/lib/services";

// Renewal operations
import { getRenewalsData, createRenewal } from "@/lib/services";

// Authentication
import { getCurrentUser, signOut } from "@/lib/services";
```

## 🎯 **Component Organization**

### **Categorized Components**

- **UI Components**: Design system elements (`Button`, `Modal`, `Form`)
- **Layout Components**: Page structure (`Sidebar`, `Header`)
- **Feature Components**: Business logic (`RecentRenewals`, `RenewalsList`)
- **Modal Components**: Dialog interfaces (`AddRenewalModal`, `ImportCSVModal`)

### **Component Usage**

```typescript
// Clean component imports
import {
  Button,
  Modal,
  Form,
  RecentRenewals,
  AddRenewalModal,
} from "@/components";

// Type imports
import type { BaseComponentProps } from "@/components";
```

## 📋 **Best Practices**

### **Import Guidelines**

1. **Use centralized exports** when available
2. **Group imports** by source (React, libraries, internal)
3. **Use type-only imports** for TypeScript types
4. **Avoid deep imports** when index files exist

### **File Organization**

1. **Keep related files together** in logical directories
2. **Use consistent naming** (kebab-case for files, PascalCase for components)
3. **Create index files** for directories with multiple exports
4. **Separate concerns** (UI, business logic, utilities)

### **Dependency Management**

1. **Avoid circular dependencies** between modules
2. **Keep dependencies shallow** when possible
3. **Use dependency injection** for testability
4. **Document complex dependencies** in comments

## 🔍 **Migration Guide**

### **Updating Existing Imports**

When updating existing code to use the new organization:

1. **Replace scattered imports** with centralized ones
2. **Update configuration imports** to use config index
3. **Use utility functions** instead of inline implementations
4. **Leverage service modules** for business logic

### **Example Migration**

```typescript
// Before
import { Button } from "@/components/ui/Button";
import { formatDate } from "@/lib/some-utils";
import { getMetadata } from "@/lib/services/metadataService";

// After
import { Button } from "@/components";
import { formatDate } from "@/lib/utils";
import { getMetadataOptions } from "@/lib/services";
```

## 🚀 **Benefits**

### **Developer Experience**

- **Faster imports**: Centralized exports reduce typing
- **Better discoverability**: Logical organization makes finding code easier
- **Consistent patterns**: Standardized import/export patterns
- **Reduced cognitive load**: Clear separation of concerns

### **Maintainability**

- **Easier refactoring**: Centralized exports simplify updates
- **Better testing**: Organized code is easier to test
- **Reduced duplication**: Shared utilities prevent code repetition
- **Clear dependencies**: Organized structure shows relationships

### **Performance**

- **Better tree shaking**: Clean exports improve bundle optimization
- **Reduced bundle size**: Eliminated duplicate utilities
- **Faster builds**: Organized imports speed up compilation

---

_This organization follows industry best practices and makes the codebase more maintainable and developer-friendly._

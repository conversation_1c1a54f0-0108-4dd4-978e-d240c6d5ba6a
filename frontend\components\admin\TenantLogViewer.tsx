'use client'

import React, { useState, useEffect } from 'react'
import { useTenantLogs } from '@/lib/hooks'
import { TenantLogFilters, TenantLogEntry } from '@/lib/types'
import { Button } from '@/components/ui/Button'
import { Form } from '@/components/ui/Form'

interface TenantLogViewerProps {
  className?: string
}

export const TenantLogViewer: React.FC<TenantLogViewerProps> = ({ className }) => {
  const [filters, setFilters] = useState<TenantLogFilters>({
    limit: 50,
    offset: 0
  })
  
  const [searchField, setSearchField] = useState('')
  const [searchOldValue, setSearchOldValue] = useState('')
  const [searchNewValue, setSearchNewValue] = useState('')
  
  const {
    logs,
    activitySummary,
    userSummary,
    loading,
    error,
    pagination,
    fetchLogs,
    searchLogs,
    getActivitySummary,
    getUserSummary,
    clearLogs
  } = useTenantLogs()

  // Load initial data
  useEffect(() => {
    fetchLogs(filters)
  }, [])

  const handleFilterChange = (key: keyof TenantLogFilters, value: any) => {
    const newFilters = { ...filters, [key]: value, offset: 0 }
    setFilters(newFilters)
    fetchLogs(newFilters)
  }

  const handleSearch = () => {
    if (!searchField) return
    
    searchLogs({
      field_name: searchField,
      old_value: searchOldValue || undefined,
      new_value: searchNewValue || undefined,
      limit: 100
    })
  }

  const handleLoadMore = () => {
    const newFilters = { ...filters, offset: filters.offset! + filters.limit! }
    setFilters(newFilters)
    fetchLogs(newFilters)
  }

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString()
  }

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'critical': return 'text-red-600 bg-red-50'
      case 'high': return 'text-orange-600 bg-orange-50'
      case 'medium': return 'text-yellow-600 bg-yellow-50'
      case 'low': return 'text-green-600 bg-green-50'
      default: return 'text-gray-600 bg-gray-50'
    }
  }

  const getOperationColor = (operation: string) => {
    switch (operation) {
      case 'INSERT': return 'text-green-600 bg-green-50'
      case 'UPDATE': return 'text-blue-600 bg-blue-50'
      case 'DELETE': return 'text-red-600 bg-red-50'
      case 'SYSTEM': return 'text-purple-600 bg-purple-50'
      default: return 'text-gray-600 bg-gray-50'
    }
  }

  return (
    <div className={`space-y-6 ${className || ''}`}>
      {/* Header */}
      <div className="dashboard-header">
        <div className="dashboard-title-section">
          <h1 className="text-2xl font-bold text-gray-900">Tenant Activity Logs</h1>
          <p className="text-gray-600 mt-1">Monitor and analyze all changes in tenant data</p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => getActivitySummary()}
            disabled={loading}
          >
            Activity Summary
          </Button>
          <Button
            variant="outline"
            onClick={() => getUserSummary()}
            disabled={loading}
          >
            User Summary
          </Button>
          <Button
            variant="outline"
            onClick={clearLogs}
          >
            Clear
          </Button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white p-6 rounded-lg shadow-sm border space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">Filters</h3>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Form.Field>
            <Form.Label>Table</Form.Label>
            <Form.Select
              value={filters.table || ''}
              onChange={(e) => handleFilterChange('table', e.target.value || undefined)}
              placeholder="All Tables"
            >
              <option value="tenant_renewals">Renewals</option>
              <option value="tenant_alerts">Alerts</option>
              <option value="tenant_vendors">Vendors</option>
              <option value="tenant_products">Products</option>
              <option value="tenant_product_versions">Product Versions</option>
              <option value="tenant_users">Users</option>
            </Form.Select>
          </Form.Field>
          
          <Form.Field>
            <Form.Label>Operation</Form.Label>
            <Form.Select
              value={filters.operation || ''}
              onChange={(e) => handleFilterChange('operation', e.target.value || undefined)}
              placeholder="All Operations"
            >
              <option value="INSERT">Insert</option>
              <option value="UPDATE">Update</option>
              <option value="DELETE">Delete</option>
              <option value="SYSTEM">System</option>
            </Form.Select>
          </Form.Field>
          
          <Form.Field>
            <Form.Label>Business Impact</Form.Label>
            <Form.Select
              value={filters.business_impact || ''}
              onChange={(e) => handleFilterChange('business_impact', e.target.value || undefined)}
              placeholder="All Impacts"
            >
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
              <option value="critical">Critical</option>
            </Form.Select>
          </Form.Field>
          
          <Form.Field>
            <Form.Label>User Email</Form.Label>
            <Form.Input
              value={filters.user_id || ''}
              onChange={(e) => handleFilterChange('user_id', e.target.value || undefined)}
              placeholder="Filter by user..."
            />
          </Form.Field>
        </div>

        <Form.Grid columns={2}>
          <Form.Field>
            <Form.Label>Start Date</Form.Label>
            <Form.Input
              type="datetime-local"
              value={filters.start_date || ''}
              onChange={(e) => handleFilterChange('start_date', e.target.value || undefined)}
            />
          </Form.Field>

          <Form.Field>
            <Form.Label>End Date</Form.Label>
            <Form.Input
              type="datetime-local"
              value={filters.end_date || ''}
              onChange={(e) => handleFilterChange('end_date', e.target.value || undefined)}
            />
          </Form.Field>
        </Form.Grid>
      </div>

      {/* Field Search */}
      <div className="bg-white p-6 rounded-lg shadow-sm border space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">Field Change Search</h3>
        
        <Form.Grid columns={4}>
          <Form.Field>
            <Form.Label>Field Name</Form.Label>
            <Form.Input
              value={searchField}
              onChange={(e) => setSearchField(e.target.value)}
              placeholder="e.g., name, cost, status"
            />
          </Form.Field>

          <Form.Field>
            <Form.Label>Old Value</Form.Label>
            <Form.Input
              value={searchOldValue}
              onChange={(e) => setSearchOldValue(e.target.value)}
              placeholder="Previous value..."
            />
          </Form.Field>

          <Form.Field>
            <Form.Label>New Value</Form.Label>
            <Form.Input
              value={searchNewValue}
              onChange={(e) => setSearchNewValue(e.target.value)}
              placeholder="New value..."
            />
          </Form.Field>
          
          <div className="flex items-end">
            <Button
              onClick={handleSearch}
              disabled={!searchField || loading}
              className="w-full"
            >
              Search Changes
            </Button>
          </div>
        </Form.Grid>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-600">Error: {error}</p>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="flex justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      )}

      {/* Activity Summary */}
      {activitySummary.length > 0 && (
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Activity Summary</h3>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Table</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Operation</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Count</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Users</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">High Impact</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {activitySummary.map((item, index) => (
                  <tr key={index}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{item.table_name}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getOperationColor(item.operation)}`}>
                        {item.operation}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.count}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.unique_users}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{item.high_impact_count}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* User Summary */}
      {userSummary.length > 0 && (
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">User Activity Summary</h3>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Actions</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tables</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">High Impact</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Activity</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {userSummary.map((user, index) => (
                  <tr key={index}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {user.user_email || user.user_id || 'Unknown'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{user.total_actions}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {user.tables_affected.join(', ')}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{user.high_impact_actions}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatTimestamp(user.last_activity)}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Logs Table */}
      {logs.length > 0 && (
        <div className="bg-white rounded-lg shadow-sm border">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Activity Logs</h3>
            <p className="text-sm text-gray-600">
              Showing {logs.length} of {pagination.total} entries
            </p>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Timestamp</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Operation</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Table</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Record ID</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Impact</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Changed Fields</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {logs.map((log) => (
                  <tr key={log.log_id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatTimestamp(log.timestamp)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getOperationColor(log.operation)}`}>
                        {log.operation}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {log.table_name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 font-mono">
                      {log.record_id.substring(0, 8)}...
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {log.user_email || log.user_id || 'System'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getImpactColor(log.business_impact)}`}>
                        {log.business_impact}
                      </span>
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-900">
                      <div className="max-w-xs truncate">
                        {log.changed_fields.join(', ')}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {pagination.has_more && (
            <div className="px-4 py-3 border-t border-gray-200 flex justify-center">
              <Button
                variant="outline"
                onClick={handleLoadMore}
                disabled={loading}
              >
                Load More
              </Button>
            </div>
          )}
        </div>
      )}

      {/* No Data State */}
      {!loading && logs.length === 0 && activitySummary.length === 0 && userSummary.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-500">
            <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <h3 className="mt-2 text-sm font-medium text-gray-900">No logs found</h3>
            <p className="mt-1 text-sm text-gray-500">
              Try adjusting your filters or check back later.
            </p>
          </div>
        </div>
      )}
    </div>
  )
}

export default TenantLogViewer

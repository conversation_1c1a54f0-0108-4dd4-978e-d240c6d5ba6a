/**
 * Loading State Components
 * 
 * Reusable loading components with different styles and animations.
 * Focused responsibility: Consistent loading UI across the application.
 */

'use client'

import { BaseComponentProps } from '@/lib/types'

// Unified loading component props
interface UniversalLoadingProps extends BaseComponentProps {
  variant?: 'spinner' | 'skeleton' | 'page' | 'card' | 'inline'
  size?: 'sm' | 'md' | 'lg' | 'xl'
  color?: 'primary' | 'secondary' | 'white'
  text?: string
  title?: string
  subtitle?: string
  lines?: number
  height?: string
  width?: string
  animate?: boolean
}

// Universal Loading Component
export function UniversalLoading({
  variant = 'spinner',
  size = 'md',
  color = 'primary',
  text,
  title,
  subtitle,
  lines = 3,
  height = '1rem',
  width = '100%',
  animate = true,
  className = '',
  'data-testid': testId
}: UniversalLoadingProps) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12'
  }

  const colorClasses = {
    primary: 'text-blue-600',
    secondary: 'text-gray-600',
    white: 'text-white'
  }

  // Spinner variant
  if (variant === 'spinner') {
    return (
      <div
        className={`flex items-center justify-center ${className}`}
        data-testid={testId}
        role="status"
        aria-label={text || 'Loading'}
      >
        <svg
          className={`animate-spin ${sizeClasses[size]} ${colorClasses[color]}`}
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          />
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          />
        </svg>
        {text && (
          <span className="ml-2 text-sm text-secondary">{text}</span>
        )}
      </div>
    )
  }

  // Skeleton variant
  if (variant === 'skeleton') {
    const animationClass = animate ? 'animate-pulse' : ''

    if (lines === 1) {
      return (
        <div
          className={`bg-gray-200 rounded ${animationClass} ${className}`}
          style={{ height, width }}
          data-testid={testId}
          role="status"
          aria-label="Loading content"
        />
      )
    }

    return (
      <div className={`space-y-2 ${className}`} data-testid={testId}>
        {Array.from({ length: lines }).map((_, index) => (
          <div
            key={index}
            className={`bg-gray-200 rounded ${animationClass}`}
            style={{
              height,
              width: index === lines - 1 ? '75%' : width
            }}
          />
        ))}
      </div>
    )
  }

  // Page variant
  if (variant === 'page') {
    return (
      <div className={`flex flex-col items-center justify-center min-h-screen ${className}`} data-testid={testId}>
        <svg
          className={`animate-spin ${sizeClasses.xl} ${colorClasses[color]}`}
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          />
          <path
            className="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          />
        </svg>
        {title && <h2 className="mt-4 text-xl font-semibold text-gray-900">{title}</h2>}
        {subtitle && <p className="mt-2 text-gray-600">{subtitle}</p>}
      </div>
    )
  }

  // Card variant
  if (variant === 'card') {
    return (
      <div className={`p-6 bg-white rounded-lg border ${className}`} data-testid={testId}>
        {title && <div className="h-6 bg-gray-200 rounded mb-4 animate-pulse" />}
        <div className={`space-y-2`}>
          {Array.from({ length: lines }).map((_, index) => (
            <div
              key={index}
              className={`bg-gray-200 rounded ${animate ? 'animate-pulse' : ''}`}
              style={{
                height,
                width: index === lines - 1 ? '75%' : width
              }}
            />
          ))}
        </div>
      </div>
    )
  }

  // Inline variant (default fallback)
  return (
    <div className={`flex items-center space-x-2 ${className}`} data-testid={testId}>
      <svg
        className={`animate-spin ${sizeClasses.sm} ${colorClasses[color]}`}
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          className="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="4"
        />
        <path
          className="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        />
      </svg>
      {text && <span className="text-sm text-gray-600">{text}</span>}
    </div>
  )
}

// Simplified exports using the universal component
export const LoadingSpinner = (props: Omit<UniversalLoadingProps, 'variant'>) => (
  <UniversalLoading variant="spinner" {...props} />
);

export const LoadingSkeleton = (props: Omit<UniversalLoadingProps, 'variant'>) => (
  <UniversalLoading variant="skeleton" {...props} />
);

export const LoadingPage = (props: Omit<UniversalLoadingProps, 'variant'>) => (
  <UniversalLoading variant="page" {...props} />
);

export const LoadingCard = (props: Omit<UniversalLoadingProps, 'variant'>) => (
  <UniversalLoading variant="card" {...props} />
);

export const LoadingInline = (props: Omit<UniversalLoadingProps, 'variant'>) => (
  <UniversalLoading variant="inline" {...props} />
);

// Specialized loading components that provide unique value
export const LoadingButton = ({ children, isLoading = false, disabled = false, className = '', onClick, ...props }: {
  children: React.ReactNode;
  isLoading?: boolean;
  disabled?: boolean;
  className?: string;
  onClick?: () => void;
  [key: string]: any;
}) => (
  <button
    className={`btn ${className} ${isLoading ? 'opacity-75 cursor-not-allowed' : ''}`}
    disabled={disabled || isLoading}
    onClick={isLoading ? undefined : onClick}
    {...props}
  >
    {isLoading ? (
      <div className="flex items-center">
        <LoadingSpinner size="sm" color="white" />
        <span className="ml-2">Loading...</span>
      </div>
    ) : (
      children
    )}
  </button>
);

export const LoadingOverlay = ({ isVisible = false, text = 'Loading...', className = '', 'data-testid': testId }: {
  isVisible?: boolean;
  text?: string;
  className?: string;
  'data-testid'?: string;
}) => {
  if (!isVisible) return null;

  return (
    <div
      className={`fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 ${className}`}
      data-testid={testId}
      role="dialog"
      aria-modal="true"
      aria-label="Loading overlay"
    >
      <div className="bg-white rounded-lg p-6 shadow-lg">
        <LoadingSpinner size="lg" text={text} />
      </div>
    </div>
  );
};

// Specialized loading patterns can use the universal component
export const LoadingList = (props: Omit<UniversalLoadingProps, 'variant'> & { items?: number; showAvatar?: boolean }) => (
  <UniversalLoading variant="skeleton" lines={props.items || 5} {...props} />
);

export const LoadingTable = (props: Omit<UniversalLoadingProps, 'variant'> & { rows?: number; columns?: number }) => (
  <UniversalLoading variant="skeleton" lines={props.rows || 5} {...props} />
);

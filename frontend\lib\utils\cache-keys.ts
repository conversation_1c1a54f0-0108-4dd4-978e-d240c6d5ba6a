/**
 * Cache Key Utilities
 * 
 * Provides utilities for creating consistent cache keys across the application.
 * This file is separate from cache.ts to avoid circular dependencies.
 */

/**
 * Create cache key from parameters with consistent formatting
 */
export function createCacheKey(prefix: string, ...params: (string | number | boolean)[]): string {
  return [prefix, ...params.filter(Boolean)].join('-')
}

/**
 * Create tenant-specific cache key
 */
export function createTenantCacheKey(tenantId: string, prefix: string, ...parts: (string | number)[]): string {
  return createCacheKey(prefix, tenantId, ...parts)
}

/**
 * Create user-specific cache key
 */
export function createUserCacheKey(userId: string, prefix: string, ...parts: (string | number)[]): string {
  return createCache<PERSON>ey(prefix, userId, ...parts)
} 
/**
 * Performance Tests for Synchronization System
 * 
 * Tests performance characteristics including:
 * - Large dataset processing
 * - Memory usage optimization
 * - Concurrent processing
 * - Throughput benchmarks
 */

import { Pool } from 'pg'
import { SyncEngine } from '../../services/sync/SyncEngine'
import { SyncTestDataGenerator } from '../data/SyncTestDataGenerator'
import { BackgroundJobService } from '../../services/sync/BackgroundJobService'
import { Logger } from '../../services/Logger'

describe('Sync Performance Tests', () => {
  let db: Pool
  let syncEngine: SyncEngine
  let jobService: BackgroundJobService
  let testDataGenerator: SyncTestDataGenerator
  let mockLogger: jest.Mocked<Logger>

  beforeAll(async () => {
    db = new Pool({
      host: process.env.TEST_DB_HOST || 'localhost',
      port: parseInt(process.env.TEST_DB_PORT || '5432'),
      database: process.env.TEST_DB_NAME || 'renewtrack_test',
      user: process.env.TEST_DB_USER || 'postgres',
      password: process.env.TEST_DB_PASSWORD || 'password',
      max: 20 // Increase pool size for performance tests
    })

    mockLogger = {
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn()
    } as any

    syncEngine = new SyncEngine(db, mockLogger)
    jobService = new BackgroundJobService(db, mockLogger)
    testDataGenerator = new SyncTestDataGenerator(db, mockLogger)
  })

  afterAll(async () => {
    await db.end()
  })

  beforeEach(async () => {
    await testDataGenerator.cleanupTestData()
  })

  describe('Large Dataset Processing', () => {
    it('should process 10,000 vendors within acceptable time limits', async () => {
      // Generate large dataset
      await testDataGenerator.generatePerformanceScenario()

      const startTime = Date.now()
      const result = await syncEngine.syncVendors('0000000000000001', {
        batchSize: 200
      })
      const endTime = Date.now()

      const processingTimeSeconds = (endTime - startTime) / 1000
      const throughput = result.totalProcessed / processingTimeSeconds

      expect(result.success).toBe(true)
      expect(result.totalProcessed).toBeGreaterThan(1000)
      expect(throughput).toBeGreaterThan(50) // At least 50 records per second
      expect(processingTimeSeconds).toBeLessThan(300) // Less than 5 minutes
    }, 300000) // 5 minute timeout

    it('should maintain consistent performance across multiple batches', async () => {
      await testDataGenerator.generatePerformanceScenario()

      const batchTimes: number[] = []
      const batchSizes: number[] = []

      // Process in multiple smaller batches to measure consistency
      for (let i = 0; i < 5; i++) {
        const startTime = Date.now()
        const result = await syncEngine.syncVendors('0000000000000001', {
          batchSize: 100
        })
        const endTime = Date.now()

        batchTimes.push(endTime - startTime)
        batchSizes.push(result.totalProcessed)
      }

      // Calculate coefficient of variation (should be < 0.5 for consistent performance)
      const avgTime = batchTimes.reduce((sum, time) => sum + time, 0) / batchTimes.length
      const variance = batchTimes.reduce((sum, time) => sum + Math.pow(time - avgTime, 2), 0) / batchTimes.length
      const stdDev = Math.sqrt(variance)
      const coefficientOfVariation = stdDev / avgTime

      expect(coefficientOfVariation).toBeLessThan(0.5)
    }, 180000)
  })

  describe('Memory Usage Optimization', () => {
    it('should not exceed memory limits during large dataset processing', async () => {
      await testDataGenerator.generatePerformanceScenario()

      const initialMemory = process.memoryUsage()

      await syncEngine.syncVendors('0000000000000001', {
        batchSize: 500
      })

      const finalMemory = process.memoryUsage()
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed

      // Memory increase should be reasonable (less than 100MB)
      expect(memoryIncrease).toBeLessThan(100 * 1024 * 1024)
    }, 120000)

    it('should release memory between batches', async () => {
      await testDataGenerator.generatePerformanceScenario()

      const memoryReadings: number[] = []

      // Process multiple batches and measure memory
      for (let i = 0; i < 3; i++) {
        await syncEngine.syncVendors('0000000000000001', {
          batchSize: 200
        })

        // Force garbage collection if available
        if (global.gc) {
          global.gc()
        }

        memoryReadings.push(process.memoryUsage().heapUsed)
      }

      // Memory should not continuously increase
      const firstReading = memoryReadings[0]
      const lastReading = memoryReadings[memoryReadings.length - 1]
      const memoryGrowth = (lastReading - firstReading) / firstReading

      expect(memoryGrowth).toBeLessThan(0.5) // Less than 50% growth
    }, 180000)
  })

  describe('Concurrent Processing', () => {
    it('should handle multiple concurrent sync operations', async () => {
      await testDataGenerator.generatePerformanceScenario()

      const tenantIds = ['0000000000000001', '0000000000000002', '0000000000000003']
      
      const startTime = Date.now()
      const promises = tenantIds.map(tenantId =>
        syncEngine.syncVendors(tenantId, { batchSize: 100 })
      )

      const results = await Promise.all(promises)
      const endTime = Date.now()

      // All syncs should succeed
      results.forEach(result => {
        expect(result.success).toBe(true)
      })

      // Concurrent processing should be faster than sequential
      const concurrentTime = endTime - startTime
      expect(concurrentTime).toBeLessThan(120000) // Less than 2 minutes
    }, 180000)

    it('should maintain database consistency during concurrent operations', async () => {
      await testDataGenerator.generatePerformanceScenario()

      const tenantIds = ['0000000000000001', '0000000000000002']
      
      // Run concurrent syncs
      const promises = tenantIds.map(tenantId =>
        syncEngine.syncVendors(tenantId, { batchSize: 50 })
      )

      await Promise.all(promises)

      // Verify no duplicate global vendors were created
      const client = await db.connect()
      try {
        const duplicateCheck = await client.query(`
          SELECT name, COUNT(*) as count
          FROM metadata.global_vendors
          GROUP BY name
          HAVING COUNT(*) > 1
        `)

        expect(duplicateCheck.rows.length).toBe(0)
      } finally {
        client.release()
      }
    }, 120000)
  })

  describe('Background Job Performance', () => {
    it('should process job queue efficiently', async () => {
      await testDataGenerator.generatePerformanceScenario()

      // Queue multiple jobs
      const jobIds: string[] = []
      for (let i = 0; i < 5; i++) {
        const jobId = await jobService.queueSyncJob('0000000000000001', 'vendor_sync', {
          priority: 5,
          jobData: { batchSize: 100 }
        })
        jobIds.push(jobId)
      }

      // Start job processor
      jobService.start(1000) // Check every second

      // Wait for jobs to complete
      let completedJobs = 0
      const maxWaitTime = 120000 // 2 minutes
      const startTime = Date.now()

      while (completedJobs < jobIds.length && (Date.now() - startTime) < maxWaitTime) {
        await new Promise(resolve => setTimeout(resolve, 2000)) // Wait 2 seconds

        completedJobs = 0
        for (const jobId of jobIds) {
          const job = await jobService.getJobStatus(jobId)
          if (job && job.status === 'completed') {
            completedJobs++
          }
        }
      }

      jobService.stop()

      expect(completedJobs).toBe(jobIds.length)
    }, 180000)
  })

  describe('Throughput Benchmarks', () => {
    it('should achieve minimum throughput targets', async () => {
      await testDataGenerator.generatePerformanceScenario()

      const startTime = Date.now()
      const result = await syncEngine.syncAll('0000000000000001', {
        batchSize: 150
      })
      const endTime = Date.now()

      const totalRecords = result.vendors.totalProcessed + 
                          result.products.totalProcessed + 
                          result.versions.totalProcessed
      const throughput = totalRecords / ((endTime - startTime) / 1000)

      // Minimum throughput targets
      expect(throughput).toBeGreaterThan(30) // 30 records per second overall
      expect(result.vendors.totalProcessed).toBeGreaterThan(0)
      expect(result.products.totalProcessed).toBeGreaterThan(0)
    }, 300000)

    it('should scale linearly with batch size', async () => {
      await testDataGenerator.generatePerformanceScenario()

      const batchSizes = [50, 100, 200]
      const throughputs: number[] = []

      for (const batchSize of batchSizes) {
        // Clean up between tests
        await testDataGenerator.cleanupTestData()
        await testDataGenerator.generatePerformanceScenario()

        const startTime = Date.now()
        const result = await syncEngine.syncVendors('0000000000000001', {
          batchSize
        })
        const endTime = Date.now()

        const throughput = result.totalProcessed / ((endTime - startTime) / 1000)
        throughputs.push(throughput)
      }

      // Throughput should generally increase with batch size
      // (allowing for some variance due to overhead)
      expect(throughputs[2]).toBeGreaterThan(throughputs[0] * 0.8)
    }, 300000)
  })

  describe('Resource Utilization', () => {
    it('should efficiently use database connections', async () => {
      await testDataGenerator.generatePerformanceScenario()

      const initialConnections = db.totalCount
      
      await syncEngine.syncVendors('0000000000000001', {
        batchSize: 100
      })

      const finalConnections = db.totalCount

      // Should not create excessive connections
      expect(finalConnections - initialConnections).toBeLessThan(5)
    }, 60000)

    it('should handle connection pool exhaustion gracefully', async () => {
      await testDataGenerator.generatePerformanceScenario()

      // Create a small connection pool
      const smallDb = new Pool({
        host: process.env.TEST_DB_HOST || 'localhost',
        port: parseInt(process.env.TEST_DB_PORT || '5432'),
        database: process.env.TEST_DB_NAME || 'renewtrack_test',
        user: process.env.TEST_DB_USER || 'postgres',
        password: process.env.TEST_DB_PASSWORD || 'password',
        max: 2 // Very small pool
      })

      const smallSyncEngine = new SyncEngine(smallDb, mockLogger)

      try {
        const result = await smallSyncEngine.syncVendors('0000000000000001', {
          batchSize: 50
        })

        expect(result.success).toBe(true)
      } finally {
        await smallDb.end()
      }
    }, 60000)
  })
})

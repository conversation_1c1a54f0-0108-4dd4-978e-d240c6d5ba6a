import { NextRequest, NextResponse } from 'next/server'
import { executeQuery } from '@/lib/database'
import { createSuccessResponse, createErrorResponse } from '@/lib/api'
import { ApiErrorCode, HttpStatus } from '@/lib/api/response'

export async function GET(request: NextRequest) {
  try {
    const result = await executeQuery(`
      SELECT
        status_id as id,
        name,
        active_indicator,
        display_order
      FROM metadata.global_statuses
      WHERE active_indicator = true
      ORDER BY display_order ASC, name ASC
    `)

    return NextResponse.json(createSuccessResponse(result))
  } catch (error) {
    console.error('Error fetching statuses:', error)
    return createErrorResponse(
      'Failed to fetch statuses',
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    )
  }
}

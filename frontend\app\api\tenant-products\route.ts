/**
 * Tenant Products API Endpoint
 * 
 * Provides CRUD operations for tenant-specific products
 * GET /api/tenant-products?vendor_id=xxx - Returns active products for the vendor
 * POST /api/tenant-products - Creates a new product for the tenant
 */

import { createApiRoute } from '@/lib/api/route-factory';
import { z } from 'zod';
import { NextRequest } from 'next/server';
import {
  createSuccessResponse,
  createErrorResponse,
  ApiErrorCode,
  HttpStatus,
  withErrorHandling
} from '@/lib/api/response';
import { fetchAuthSession, fetchUserAttributes } from 'aws-amplify/auth';
import { getClientByEmailDomain } from '@/lib/tenant/clients-api';
import { executeQuery, executeQuerySingle } from '@/lib/database';

// Validation schema for creating products
const createProductSchema = z.object({
  vendor_id: z.string().uuid('Invalid vendor ID'),
  name: z.string().min(1, 'Product name is required').max(255),
  description: z.string().optional(),
  category: z.string().max(100).optional(),
  sku: z.string().max(100).optional(),
  barcode: z.string().max(50).optional(),
  unit_of_measure: z.string().max(50).optional(),
});

const tenantProductsQuerySchema = z.object({
  vendor_id: z.string().uuid().optional()
});

// GET /api/tenant-products - Get products for a vendor
export const GET = createApiRoute('GET', {
  requireAuth: true,
  requireTenant: true,
  querySchema: tenantProductsQuerySchema,
  handler: async (context) => {
    const { tenant, query } = context;
    if (!tenant) {
      throw new Error('Tenant context is required');
    }
    const { vendor_id: vendorId } = query || {};

    console.log('[TENANT-PRODUCTS-API] GET request received');

  try {
    let query: string;
    let queryParams: any[];

    if (vendorId) {
      // Fetch products for specific vendor
      query = `
        SELECT
          p.id,
          p.name,
          p.description,
          p.category,
          p.sku,
          p.vendor_id,
          v.name as vendor_name,
          p.created_on,
          p.changed_on
        FROM "${tenant.tenantSchema}".tenant_products p
        JOIN "${tenant.tenantSchema}".tenant_vendors v ON p.vendor_id = v.id
        WHERE p.vendor_id = $1
        ORDER BY p.name ASC
      `;
      queryParams = [vendorId];
    } else {
      // Fetch all products
      query = `
        SELECT
          p.id,
          p.name,
          p.description,
          p.category,
          p.sku,
          p.vendor_id,
          v.name as vendor_name,
          p.created_on,
          p.changed_on
        FROM "${tenant.tenantSchema}".tenant_products p
        JOIN "${tenant.tenantSchema}".tenant_vendors v ON p.vendor_id = v.id
        ORDER BY v.name ASC, p.name ASC
      `;
      queryParams = [];
    }

    const result = await executeQuery(query, queryParams);

    if (!result.success) {
      console.error('[TENANT-PRODUCTS-API] Database error:', result.error);
      return createErrorResponse(
        'Failed to fetch products',
        ApiErrorCode.DATABASE_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }

    console.log(`[TENANT-PRODUCTS-API] Found ${result.data?.length || 0} products for vendor ${vendorId}`);

    return createSuccessResponse(
      result.data || [],
      'Products retrieved successfully'
    );

  } catch (error) {
    console.error('[TENANT-PRODUCTS-API] Unexpected error:', error);
    return createErrorResponse(
      'Internal server error',
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
  }
});

// POST /api/tenant-products - Create a new product
export const POST = createApiRoute('POST', {
  requireAuth: true,
  requireTenant: true,
  bodySchema: createProductSchema,
  handler: async (context) => {
    const { body: productData, tenant } = context;
    if (!tenant || !productData) {
      throw new Error('Tenant context and request body are required');
    }
    console.log('[TENANT-PRODUCTS-API] POST request received');

    console.log('[TENANT-PRODUCTS-API] Request body:', productData);

    // Verify vendor exists
    const vendorQuery = `
      SELECT vendor_id as id FROM "${tenant.tenantSchema}".tenant_vendors
      WHERE vendor_id = $1
    `;

    const vendorResult = await executeQuerySingle(
      vendorQuery,
      [productData.vendor_id]
    );

    if (!vendorResult.success || !vendorResult.data) {
      throw new Error('Vendor not found');
    }

    // Check if product with same name already exists for this vendor
    const existingProductQuery = `
      SELECT product_id as id FROM "${tenant.tenantSchema}".tenant_products
      WHERE vendor_id = $1 AND LOWER(product_name) = LOWER($2)
    `;

    const existingResult = await executeQuerySingle(
      existingProductQuery,
      [productData.vendor_id, productData.name]
    );

    if (existingResult.success && existingResult.data) {
      throw new Error('A product with this name already exists for this vendor');
    }

    // Create the product
    const insertQuery = `
      INSERT INTO "${tenant.tenantSchema}".tenant_products (
        vendor_id, product_name, description, category, sku, barcode, unit_of_measure,
        created_by, changed_by, created_on, changed_on
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
      ) RETURNING product_id as id, vendor_id, product_name as name, description, category, sku, created_on as created_on
    `;

    const insertResult = await executeQuerySingle(
      insertQuery,
      [
        productData.vendor_id,
        productData.name,
        productData.description || null,
        productData.category || null,
        productData.sku || null,
        productData.barcode || null,
        productData.unit_of_measure || null,
        context.user || 'unknown',
        context.user || 'unknown'
      ]
    );

    if (!insertResult.success || !insertResult.data) {
      console.error('[TENANT-PRODUCTS-API] Insert error:', insertResult.error);
      throw new Error('Failed to create product');
    }

    console.log('[TENANT-PRODUCTS-API] Product created successfully:', insertResult.data);

    return {
      ...insertResult.data,
      message: 'Product created successfully'
    };
  }
});

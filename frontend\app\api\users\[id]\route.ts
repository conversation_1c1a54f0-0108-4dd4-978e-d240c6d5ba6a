/**
 * Individual User API Route
 * 
 * This route demonstrates CRUD operations for individual users with:
 * - Path parameter validation
 * - Role-based access control
 * - Optimistic concurrency control
 * - Audit logging
 */

import { createApiRoute } from '@/lib/api/route-factory';
import {
  createSuccessResponse,
  createErrorResponse,
  createNotFoundResponse,
  ApiErrorCode,
  HttpStatus
} from '@/lib/api/response';
import { 
  validateRequestBody, 
  validatePathParams,
  userUpdateSchema,
  idParamSchema,
  UserUpdateData,
  IdParam 
} from '@/lib/utils/validation';
import { executeQuerySingle, executeTransaction } from '@/lib/database';

import { User } from '@/lib/types';

// GET /api/users/[id] - Get specific user
export const GET = createApiRoute('GET', {
  requireAuth: true,
  handler: async (context) => {
    const { params } = context;

    // Validate path parameters
    const paramValidation = validatePathParams(await params, idParamSchema);
  if (!paramValidation.success) {
    return paramValidation.response;
  }

  const { id }: IdParam = paramValidation.data;

  // Check if user can access this resource
  // Users can access their own data, admins can access any user data
  const adminGroups = ['admin', 'user_manager']
  const isAdmin = context.session!.groups.some(group => adminGroups.includes(group.toLowerCase()));
  const isOwnData = context.session!.userId === id;

  if (!isAdmin && !isOwnData) {
    return createErrorResponse(
      'Access denied',
      ApiErrorCode.FORBIDDEN,
      HttpStatus.FORBIDDEN
    );
  }

  // Fetch user data
  const query = `
    SELECT 
      id,
      email,
      name,
      given_name,
      family_name,
      roles,
      created_on,
      changed_on,
      last_login,
      version
    FROM users 
    WHERE id = $1
  `;

  const result = await executeQuerySingle<User>(query, [id]);

  if (!result.success) {
    return createErrorResponse(
      'Failed to fetch user',
      ApiErrorCode.DATABASE_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }

  if (!result.data) {
    return createNotFoundResponse('User');
  }

  // Remove sensitive data if not admin and not own data
  const userData = result.data;
  if (!isAdmin && !isOwnData) {
    // Remove sensitive fields for non-admin users
    delete (userData as any).roles;
    delete (userData as any).last_login;
  }

    return createSuccessResponse(userData, 'User retrieved successfully');
  }
});

// PUT /api/users/[id] - Update specific user
export const PUT = createApiRoute('PUT', {
  requireAuth: true,
  bodySchema: userUpdateSchema,
  handler: async (context) => {
    const { params } = context;

  // Validate path parameters
  const paramValidation = validatePathParams(await params, idParamSchema);
  if (!paramValidation.success) {
    return paramValidation.response;
  }

    const { id }: IdParam = paramValidation.data;

    // Check if user can access this resource
    // Users can access their own data, admins can access any user data
    const adminGroups = ['admin', 'user_manager']
    const isAdmin = context.session!.groups.some(group => adminGroups.includes(group.toLowerCase()));
    const isOwnData = context.session!.userId === id;

    if (!isAdmin && !isOwnData) {
      return createErrorResponse(
        'Access denied',
        ApiErrorCode.FORBIDDEN,
        HttpStatus.FORBIDDEN
      );
    }

    // Get validated request body from context
    if (!context.body) {
      throw new Error('Request body is required');
    }
    const updateData: UserUpdateData = context.body;

  // Check if user exists and get current version
  const currentUserQuery = 'SELECT id, version FROM users WHERE id = $1';
  const currentUser = await executeQuerySingle<{ id: string; version: number }>(
    currentUserQuery, 
    [id]
  );

  if (!currentUser.success) {
    return createErrorResponse(
      'Failed to fetch user',
      ApiErrorCode.DATABASE_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }

  if (!currentUser.data) {
    return createNotFoundResponse('User');
  }

  // Build dynamic update query
  const updateFields: string[] = [];
  const values: any[] = [];
  let paramIndex = 1;

  if (updateData.name !== undefined) {
    updateFields.push(`name = $${paramIndex++}`);
    values.push(updateData.name);
  }

  if (updateData.given_name !== undefined) {
    updateFields.push(`given_name = $${paramIndex++}`);
    values.push(updateData.given_name);
  }

  if (updateData.family_name !== undefined) {
    updateFields.push(`family_name = $${paramIndex++}`);
    values.push(updateData.family_name);
  }

  if (updateData.roles !== undefined) {
    updateFields.push(`roles = $${paramIndex++}`);
    values.push(JSON.stringify(updateData.roles));
  }

  if (updateFields.length === 0) {
    return createErrorResponse(
      'No valid fields to update',
      ApiErrorCode.VALIDATION_ERROR,
      HttpStatus.BAD_REQUEST
    );
  }

  // Add changed_on, version increment, and WHERE clause
  updateFields.push(`changed_on = CURRENT_TIMESTAMP`);
  updateFields.push(`version = version + 1`);
  
  // Add WHERE conditions
  values.push(id, currentUser.data.version);

  const updateQuery = `
    UPDATE users 
    SET ${updateFields.join(', ')}
    WHERE id = $${paramIndex++} AND version = $${paramIndex++}
    RETURNING 
      id,
      email,
      name,
      given_name,
      family_name,
      roles,
      created_on,
      changed_on,
      version
  `;

  const updateResult = await executeQuerySingle<User>(updateQuery, values);

  if (!updateResult.success) {
    return createErrorResponse(
      'Failed to update user',
      ApiErrorCode.DATABASE_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }

  if (!updateResult.data) {
    return createErrorResponse(
      'User was modified by another process. Please refresh and try again.',
      ApiErrorCode.VALIDATION_ERROR,
      HttpStatus.CONFLICT
    );
  }

    // Log user update for audit
    console.log(`User updated: ${id} by ${context.session!.email}`, updateData);

    return createSuccessResponse(updateResult.data, 'User updated successfully');
  }
});

// DELETE /api/users/[id] - Delete specific user
export const DELETE = createApiRoute('DELETE', {
  requireAuth: true,
  handler: async (context) => {
    const { params } = context;

    // Validate path parameters
    const paramValidation = validatePathParams(await params, idParamSchema);
    if (!paramValidation.success) {
      return paramValidation.response;
    }

    const { id }: IdParam = paramValidation.data;

    // Prevent self-deletion
    if (context.session!.userId === id) {
    return createErrorResponse(
      'Cannot delete your own account',
      ApiErrorCode.VALIDATION_ERROR,
      HttpStatus.BAD_REQUEST
    );
  }

  // Soft delete user (mark as inactive instead of hard delete)
  const deleteQuery = `
    UPDATE users 
    SET 
      status = 'deleted',
      changed_on = CURRENT_TIMESTAMP,
      version = version + 1
    WHERE id = $1
    RETURNING id, email
  `;

  const deleteResult = await executeQuerySingle<{ id: string; email: string }>(
    deleteQuery, 
    [id]
  );

  if (!deleteResult.success) {
    return createErrorResponse(
      'Failed to delete user',
      ApiErrorCode.DATABASE_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }

  if (!deleteResult.data) {
    return createNotFoundResponse('User');
  }

    // Log user deletion for audit
    console.log(`User deleted: ${deleteResult.data.email} by ${context.session!.email}`);

    return createSuccessResponse(
      { id: deleteResult.data.id },
      'User deleted successfully'
    );
  }
});

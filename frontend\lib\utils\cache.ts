/**
 * Advanced Caching System
 * 
 * This module provides a comprehensive caching solution with multiple strategies,
 * automatic invalidation, and performance optimizations.
 */

// Cache entry interface
interface CacheEntry<T> {
  data: T
  timestamp: number
  ttl: number
  accessCount: number
  lastAccessed: number
  tags: string[]
}

// Cache configuration
interface CacheConfig {
  maxSize: number
  defaultTTL: number
  cleanupInterval: number
  enableMetrics: boolean
}

// Cache metrics
interface CacheMetrics {
  hits: number
  misses: number
  evictions: number
  size: number
  hitRate: number
}

// Cache strategies
type EvictionStrategy = 'lru' | 'lfu' | 'ttl' | 'fifo'

class AdvancedCache<T = any> {
  private cache = new Map<string, CacheEntry<T>>()
  private config: CacheConfig
  private metrics: CacheMetrics = {
    hits: 0,
    misses: 0,
    evictions: 0,
    size: 0,
    hitRate: 0,
  }
  private cleanupTimer?: NodeJS.Timeout
  private evictionStrategy: EvictionStrategy

  constructor(
    config: Partial<CacheConfig> = {},
    evictionStrategy: EvictionStrategy = 'lru'
  ) {
    this.config = {
      maxSize: 100,
      defaultTTL: 5 * 60 * 1000, // 5 minutes
      cleanupInterval: 60 * 1000, // 1 minute
      enableMetrics: true,
      ...config,
    }
    this.evictionStrategy = evictionStrategy

    // Start cleanup timer
    this.startCleanup()
  }

  // Get item from cache
  get(key: string): T | null {
    const entry = this.cache.get(key)

    if (!entry) {
      this.updateMetrics('miss')
      return null
    }

    // Check if expired
    if (this.isExpired(entry)) {
      this.cache.delete(key)
      this.updateMetrics('miss')
      return null
    }

    // Update access information
    entry.accessCount++
    entry.lastAccessed = Date.now()

    this.updateMetrics('hit')
    return entry.data
  }

  // Set item in cache
  set(key: string, data: T, ttl?: number, tags: string[] = []): void {
    // Check if we need to evict items
    if (this.cache.size >= this.config.maxSize) {
      this.evict()
    }

    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl: ttl || this.config.defaultTTL,
      accessCount: 0,
      lastAccessed: Date.now(),
      tags,
    }

    this.cache.set(key, entry)
    this.updateMetrics('set')
  }

  // Check if item exists and is valid
  has(key: string): boolean {
    const entry = this.cache.get(key)
    if (!entry) return false

    if (this.isExpired(entry)) {
      this.cache.delete(key)
      return false
    }

    return true
  }

  // Delete item from cache
  delete(key: string): boolean {
    return this.cache.delete(key)
  }

  // Clear cache by tags
  clearByTags(tags: string[]): number {
    let cleared = 0
    for (const [key, entry] of Array.from(this.cache.entries())) {
      if (entry.tags.some((tag: string) => tags.includes(tag))) {
        this.cache.delete(key)
        cleared++
      }
    }
    return cleared
  }

  // Clear all cache
  clear(): void {
    this.cache.clear()
    this.resetMetrics()
  }

  // Get cache metrics
  getMetrics(): CacheMetrics {
    const totalRequests = this.metrics.hits + this.metrics.misses
    return {
      ...this.metrics,
      size: this.cache.size,
      hitRate: totalRequests > 0 ? this.metrics.hits / totalRequests : 0,
    }
  }

  // Get all keys
  keys(): string[] {
    return Array.from(this.cache.keys())
  }

  // Get cache size
  size(): number {
    return this.cache.size
  }

  // Check if entry is expired
  private isExpired(entry: CacheEntry<T>): boolean {
    return Date.now() - entry.timestamp > entry.ttl
  }

  // Evict items based on strategy
  private evict(): void {
    if (this.cache.size === 0) return

    let keyToEvict: string | null = null

    switch (this.evictionStrategy) {
      case 'lru':
        keyToEvict = this.findLRUKey()
        break
      case 'lfu':
        keyToEvict = this.findLFUKey()
        break
      case 'ttl':
        keyToEvict = this.findExpiredKey()
        break
      case 'fifo':
        keyToEvict = this.findFIFOKey()
        break
    }

    if (keyToEvict) {
      this.cache.delete(keyToEvict)
      this.metrics.evictions++
    }
  }

  // Find least recently used key
  private findLRUKey(): string | null {
    let oldestKey: string | null = null
    let oldestTime = Date.now()

    for (const [key, entry] of Array.from(this.cache.entries())) {
      if (entry.lastAccessed < oldestTime) {
        oldestTime = entry.lastAccessed
        oldestKey = key
      }
    }

    return oldestKey
  }

  // Find least frequently used key
  private findLFUKey(): string | null {
    let leastUsedKey: string | null = null
    let leastCount = Infinity

    for (const [key, entry] of Array.from(this.cache.entries())) {
      if (entry.accessCount < leastCount) {
        leastCount = entry.accessCount
        leastUsedKey = key
      }
    }

    return leastUsedKey
  }

  // Find expired key
  private findExpiredKey(): string | null {
    for (const [key, entry] of Array.from(this.cache.entries())) {
      if (this.isExpired(entry)) {
        return key
      }
    }
    return null
  }

  // Find first in, first out key
  private findFIFOKey(): string | null {
    let oldestKey: string | null = null
    let oldestTime = Date.now()

    for (const [key, entry] of Array.from(this.cache.entries())) {
      if (entry.timestamp < oldestTime) {
        oldestTime = entry.timestamp
        oldestKey = key
      }
    }

    return oldestKey
  }

  // Update metrics
  private updateMetrics(operation: 'hit' | 'miss' | 'set'): void {
    if (!this.config.enableMetrics) return

    switch (operation) {
      case 'hit':
        this.metrics.hits++
        break
      case 'miss':
        this.metrics.misses++
        break
      case 'set':
        // No specific metric for set operations
        break
    }
  }

  // Reset metrics
  private resetMetrics(): void {
    this.metrics = {
      hits: 0,
      misses: 0,
      evictions: 0,
      size: 0,
      hitRate: 0,
    }
  }

  // Start cleanup timer
  private startCleanup(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup()
    }, this.config.cleanupInterval)
  }

  // Cleanup expired entries
  private cleanup(): void {
    const now = Date.now()
    const keysToDelete: string[] = []

    for (const [key, entry] of Array.from(this.cache.entries())) {
      if (now - entry.timestamp > entry.ttl) {
        keysToDelete.push(key)
      }
    }

    keysToDelete.forEach(key => this.cache.delete(key))
  }

  // Destroy cache and cleanup
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
    }
    this.clear()
  }
}

// Global cache instances
export const apiCache = new AdvancedCache({
  maxSize: 200,
  defaultTTL: 5 * 60 * 1000, // 5 minutes
  enableMetrics: true,
}, 'lru')

export const componentCache = new AdvancedCache({
  maxSize: 50,
  defaultTTL: 10 * 60 * 1000, // 10 minutes
  enableMetrics: true,
}, 'lfu')

export const userDataCache = new AdvancedCache({
  maxSize: 100,
  defaultTTL: 15 * 60 * 1000, // 15 minutes
  enableMetrics: true,
}, 'lru')

// Cache utilities
export const cacheUtils = {
  // Create cache key from parameters - unified with cache-invalidation.ts
  createKey: (prefix: string, ...params: (string | number | boolean)[]): string => {
    return [prefix, ...params.filter(Boolean)].join('-')
  },

  // Serialize object for caching
  serialize: (obj: any): string => {
    try {
      return JSON.stringify(obj, (key, value) => {
        if (value instanceof Date) {
          return { __type: 'Date', value: value.toISOString() }
        }
        return value
      })
    } catch {
      return String(obj)
    }
  },

  // Deserialize object from cache
  deserialize: <T>(str: string): T | null => {
    try {
      return JSON.parse(str, (key, value) => {
        if (value && value.__type === 'Date') {
          return new Date(value.value)
        }
        return value
      })
    } catch {
      return null
    }
  },

  // Get cache statistics
  getGlobalStats: () => ({
    api: apiCache.getMetrics(),
    component: componentCache.getMetrics(),
    userData: userDataCache.getMetrics(),
  }),

  // Clear all caches
  clearAll: () => {
    apiCache.clear()
    componentCache.clear()
    userDataCache.clear()
  },
}

// React hook for using cache
export function useCache<T>(
  cache: AdvancedCache<T> = apiCache as AdvancedCache<T>
) {
  return {
    get: (key: string) => cache.get(key),
    set: (key: string, data: T, ttl?: number, tags?: string[]) => 
      cache.set(key, data, ttl, tags),
    has: (key: string) => cache.has(key),
    delete: (key: string) => cache.delete(key),
    clear: () => cache.clear(),
    metrics: cache.getMetrics(),
  }
}

export { AdvancedCache }
export type { CacheConfig, CacheMetrics, EvictionStrategy }

/**
 * Enhanced Admin Pages Sidebar API
 *
 * This endpoint returns only pages that should appear in the sidebar
 * based on the current user's group membership AND their client's addon packages.
 * Supports two-level admin access (admin vs super-admin).
 */

import { NextRequest } from 'next/server'
import { createSuccessResponse, createErrorResponse, ApiErrorCode, HttpStatus } from '@/lib/api/response'
import { authenticateRequest } from '@/lib/api/auth-middleware'
import { executeQuery } from '@/lib/database'

export interface SidebarPage {
  id: number
  name: string
  header: string
  description: string | null
  display_order: number
  icon_svg: string | null
  route_path: string
}

/**
 * GET /api/admin-pages/sidebar
 * Returns pages that should appear in the sidebar for the current user
 */
export async function GET(request: NextRequest) {
  try {
    // Use unified authentication middleware
    const authResult = await authenticateRequest(request, {
      requireAuth: true,
      requiredGroups: [] // Allow any authenticated user, filter by groups in query
    })

    if (!authResult.success) {
      return authResult.response
    }

    const { session } = authResult
    const userGroups = session.groups || []
    const userEmail = session.email || ''

    console.log('🔍 [SIDEBAR-API] User session:', { userEmail, userGroups })

    // Normalize groups to lowercase
    const normalizedGroups = userGroups
      .filter(group => typeof group === 'string')
      .map(group => group.toLowerCase().trim())

    console.log('🔍 [SIDEBAR-API] Normalized groups:', normalizedGroups)

    // If no groups found, return empty array (no access)
    if (normalizedGroups.length === 0) {
      return createSuccessResponse([])
    }

    // Extract client domain from email
    const clientDomain = userEmail.includes('@') ? userEmail.split('@')[1] : ''

    if (!clientDomain) {
      return createSuccessResponse([])
    }

    // Query sidebar pages accessible to user's groups
    // Special <NAME_EMAIL> - they get access to all pages
    let query: string
    let queryParams: any[]

    // All users get pages based on their group membership - no hardcoded exceptions
    query = `
      SELECT DISTINCT
        p.id,
        p.name,
        p.header,
        p.description,
        p.display_order,
        p.icon_svg,
        p.route_path
      FROM metadata.pages p
      INNER JOIN metadata.page_groups pg ON p.id = pg.page_id
      WHERE p.status = 'A'
        AND p.sidebar = true
        AND pg.group_name = ANY($1)
      ORDER BY p.display_order ASC, p.name ASC
    `
    queryParams = [normalizedGroups]

    // Use the unified database query function with proper error handling
    const result = await executeQuery(query, queryParams)

    if (!result.success) {
      return createErrorResponse(
        'Database query failed',
        ApiErrorCode.INTERNAL_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR,
        result.error
      )
    }

    const sidebarPages: SidebarPage[] = (result.data || []).map((row: any) => ({
      id: row.id,
      name: row.name,
      header: row.header,
      description: row.description,
      display_order: row.display_order,
      icon_svg: row.icon_svg,
      route_path: row.route_path
    }))

    console.log('✅ [SIDEBAR-API] Returning pages:', sidebarPages.length, sidebarPages.map(p => p.name))

    return createSuccessResponse(sidebarPages)

  } catch (error) {
    return createErrorResponse(
      'Failed to fetch sidebar pages',
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR,
      error instanceof Error ? error.message : 'Unknown error'
    )
  }
}

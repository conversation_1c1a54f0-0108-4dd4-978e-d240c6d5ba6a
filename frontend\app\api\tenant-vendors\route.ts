/**
 * Tenant Vendors API Endpoint
 * 
 * Provides CRUD operations for tenant-specific vendors
 * GET /api/tenant-vendors - Returns active vendors for the tenant
 * POST /api/tenant-vendors - Creates a new vendor for the tenant
 */

import { NextRequest } from 'next/server';
import { authenticateRequest } from '@/lib/api/auth-middleware';
import { resolveTenantContext, TenantContext } from '@/lib/tenant/context';
import { executeTenantQuery, executeTenantQuerySingle } from '@/lib/tenant/database';
import {
  createSuccessResponse,
  createErrorResponse,
  ApiErrorCode,
  HttpStatus,
  withErrorHandling
} from '@/lib/api/response';
import { z } from 'zod';
import { getClientByEmailDomain } from '@/lib/tenant/clients';

// Validation schema for creating vendors
const createVendorSchema = z.object({
  name: z.string().min(1, 'Vendor name is required').max(255),
  display_name: z.string().max(255).optional(),
  contact_email: z.string().email().optional().or(z.literal('')),
  phone: z.string().max(50).optional(),
  address_line1: z.string().max(255).optional(),
  address_line2: z.string().max(255).optional(),
  city: z.string().max(100).optional(),
  state: z.string().max(100).optional(),
  postal_code: z.string().max(20).optional(),
  country: z.string().max(100).optional(),
  tax_id: z.string().max(100).optional(),
  website: z.string().url().optional().or(z.literal('')),
  notes: z.string().optional(),
});

// GET /api/tenant-vendors - Get all vendors for the tenant with pagination
export const GET = withErrorHandling(async (request: NextRequest) => {
  console.log('[TENANT-VENDORS-API] GET request received');

  // Verify authentication using Amplify
  try {
    const session = await fetchAuthSession();
    if (!session?.tokens?.idToken) {
      return createErrorResponse(
        'Authentication required',
        ApiErrorCode.UNAUTHORIZED,
        HttpStatus.UNAUTHORIZED
      );
    }
  } catch (error) {
    return createErrorResponse(
      'Authentication failed',
      ApiErrorCode.UNAUTHORIZED,
      HttpStatus.UNAUTHORIZED
    );
  }

  // Get user attributes for tenant context
  const userAttributes = await fetchUserAttributes();
  const userEmail = userAttributes.email;

  if (!userEmail) {
    return createErrorResponse(
      'User email not found',
      ApiErrorCode.UNAUTHORIZED,
      HttpStatus.UNAUTHORIZED
    );
  }

  // Get tenant context using direct client lookup
  const clientResult = await getClientByEmailDomain(userEmail);
  if (!clientResult.success) {
    return createErrorResponse(
      clientResult.error || 'Failed to resolve tenant context',
      (clientResult.errorCode as ApiErrorCode) || ApiErrorCode.UNAUTHORIZED,
      HttpStatus.UNAUTHORIZED
    );
  }

  const tenant = clientResult.client;

  if (!tenant) {
    return createErrorResponse(
      'Tenant context not found',
      ApiErrorCode.TENANT_NOT_FOUND,
      HttpStatus.NOT_FOUND
    );
  }

  try {
    // Parse pagination parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = Math.min(parseInt(searchParams.get('limit') || '100', 10), 1000); // Max 1000 items
    const offset = (page - 1) * limit;
    // Main query with pagination
    const query = `
      SELECT
        id,
        name,
        display_name,
        contact_email,
        phone,
        address_line1,
        address_line2,
        city,
        state,
        postal_code,
        country,
        tax_id,
        website,
        notes,
        sync_status,
        sync_confidence,
        last_sync_attempt,
        is_deleted,
        created_by,
        changed_by,
        created_on,
        global_vendor_id
      FROM "${tenant.tenantSchema}".tenant_vendors
      WHERE is_deleted = false
      ORDER BY name ASC
      LIMIT $1 OFFSET $2
    `;

    // Count query for pagination metadata
    const countQuery = `
      SELECT COUNT(*) as total
      FROM "${tenant.tenantSchema}".tenant_vendors
      WHERE is_deleted = false
    `;

    // Execute both queries
    const [result, countResult] = await Promise.all([
      executeTenantQuery(query, [limit, offset], tenant),
      executeTenantQuery(countQuery, [], tenant)
    ]);

    if (!result.success || !countResult.success) {
      console.error('[TENANT-VENDORS-API] Database error:', result.error || countResult.error);

      // Return empty array if table doesn't exist yet
      if (result.error?.includes('does not exist') || countResult.error?.includes('does not exist')) {
        console.log('[TENANT-VENDORS-API] Tenant vendors table does not exist yet, returning empty array');
        return createSuccessResponse({
          data: [],
          pagination: {
            page: 1,
            limit: limit,
            total: 0,
            totalPages: 0
          }
        });
      }

      return createErrorResponse(
        'Failed to fetch vendors',
        ApiErrorCode.DATABASE_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }

    const total = parseInt(countResult.data?.[0]?.total || '0', 10);
    const totalPages = Math.ceil(total / limit);

    console.log(`[TENANT-VENDORS-API] Found ${result.data?.length || 0} vendors (page ${page}/${totalPages})`);

    return createSuccessResponse({
      data: result.data || [],
      pagination: {
        page,
        limit,
        total,
        totalPages
      }
    },
      'Vendors retrieved successfully'
    );
  } catch (error) {
    console.error('[TENANT-VENDORS-API] Unexpected error:', error);
    return createErrorResponse(
      'Internal server error',
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
});

// POST /api/tenant-vendors - Create a new vendor
export const POST = withErrorHandling(async (request: NextRequest) => {
  console.log('[TENANT-VENDORS-API] POST request received');

  // Use unified authentication middleware
  const authResult = await authenticateRequest(request, {
    requireAuth: true,
    requiredGroups: []
  });

  if (!authResult.success) {
    return authResult.response;
  }

  const { session } = authResult;
  const userEmail = session.email;

  if (!userEmail) {
    return createErrorResponse(
      'User email not found',
      ApiErrorCode.UNAUTHORIZED,
      HttpStatus.UNAUTHORIZED
    );
  }

  // Create session object for tenant context resolution
  // Get tenant context using direct client lookup
  const clientResult = await getClientByEmailDomain(userEmail);
  if (!clientResult.success) {
    return createErrorResponse(
      clientResult.error || 'Failed to resolve tenant context',
      (clientResult.errorCode as ApiErrorCode) || ApiErrorCode.UNAUTHORIZED,
      HttpStatus.UNAUTHORIZED
    );
  }

  const tenant = clientResult.client;

  if (!tenant) {
    return createErrorResponse(
      'Tenant context not found',
      ApiErrorCode.TENANT_NOT_FOUND,
      HttpStatus.NOT_FOUND
    );
  }

  try {
    const body = await request.json();
    console.log('[TENANT-VENDORS-API] Request body:', body);

    // Validate request data
    const validationResult = createVendorSchema.safeParse(body);
    if (!validationResult.success) {
      console.error('[TENANT-VENDORS-API] Validation error:', validationResult.error);
      return createErrorResponse(
        'Invalid vendor data',
        ApiErrorCode.VALIDATION_ERROR,
        HttpStatus.BAD_REQUEST,
        validationResult.error.errors
      );
    }

    const vendorData = validationResult.data;

    // Check if vendor with same name already exists
    const existingVendorQuery = `
      SELECT id FROM "${tenant.tenantSchema}".tenant_vendors
      WHERE LOWER(name) = LOWER($1) AND is_deleted = false
    `;

    const existingResult = await executeTenantQuerySingle(
      existingVendorQuery,
      [vendorData.name],
      tenant
    );

    if (existingResult.success && existingResult.data) {
      return createErrorResponse(
        'A vendor with this name already exists',
        ApiErrorCode.VALIDATION_ERROR,
        HttpStatus.CONFLICT
      );
    }

    // Create the vendor
    const insertQuery = `
      INSERT INTO "${tenant.tenantSchema}".tenant_vendors (
        name, display_name, contact_email, phone, address_line1, address_line2,
        city, state, postal_code, country, tax_id, website, notes,
        created_by, is_deleted
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, false
      ) RETURNING id, name, display_name, created_by, changed_by, created_on, is_deleted
    `;

    const insertResult = await executeTenantQuerySingle(
      insertQuery,
      [
        vendorData.name,
        vendorData.display_name || vendorData.name,
        vendorData.contact_email || null,
        vendorData.phone || null,
        vendorData.address_line1 || null,
        vendorData.address_line2 || null,
        vendorData.city || null,
        vendorData.state || null,
        vendorData.postal_code || null,
        vendorData.country || null,
        vendorData.tax_id || null,
        vendorData.website || null,
        vendorData.notes || null,
        userAttributes.sub || 'system' // created_by
      ],
      tenant
    );

    if (!insertResult.success || !insertResult.data) {
      console.error('[TENANT-VENDORS-API] Insert error:', insertResult.error);
      return createErrorResponse(
        'Failed to create vendor',
        ApiErrorCode.DATABASE_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }

    console.log('[TENANT-VENDORS-API] Vendor created successfully:', insertResult.data);

    return createSuccessResponse(
      insertResult.data,
      'Vendor created successfully',
      HttpStatus.CREATED
    );

  } catch (error) {
    console.error('[TENANT-VENDORS-API] Error:', error);
    return createErrorResponse(
      'Failed to create vendor',
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
});

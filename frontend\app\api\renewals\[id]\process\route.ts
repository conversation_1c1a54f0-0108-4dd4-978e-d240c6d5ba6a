/**
 * Process Renewal API Endpoint
 * 
 * Handles renewal processing operations
 * POST /api/renewals/[id]/process - Process/renew a renewal
 */

import { NextRequest } from 'next/server';
import { fetchAuthSession, fetchUserAttributes } from 'aws-amplify/auth';
import { getClientByEmailDomain } from '@/lib/tenant/clients';
import {
  createSuccessResponse,
  createErrorResponse,
  ApiErrorCode,
  HttpStatus,
  withErrorHandling
} from '@/lib/api/response';
import { executeQuery, schemaExists } from '@/lib/database';

// POST /api/renewals/[id]/process - Process renewal
export const POST = withErrorHandling(async (request: NextRequest, { params }: { params: Promise<{ id: string }> }) => {
  // Verify authentication using Amplify
  try {
    const session = await fetchAuthSession();
    if (!session?.tokens?.idToken) {
      return createErrorResponse(
        'Authentication required',
        ApiErrorCode.UNAUTHORIZED,
        HttpStatus.UNAUTHORIZED
      );
    }
  } catch (error) {
    return createErrorResponse(
      'Authentication failed',
      ApiErrorCode.UNAUTHORIZED,
      HttpStatus.UNAUTHORIZED
    );
  }

  // Get user attributes for tenant context
  const userAttributes = await fetchUserAttributes();
  const userEmail = userAttributes.email;

  if (!userEmail) {
    return createErrorResponse(
      'User email not found',
      ApiErrorCode.UNAUTHORIZED,
      HttpStatus.UNAUTHORIZED
    );
  }

  const clientResult = await getClientByEmailDomain(userEmail);

  if (!clientResult.success) {
    return createErrorResponse(
      'Failed to fetch tenant information',
      ApiErrorCode.DATABASE_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }

  const tenant = clientResult.client!;
  const resolvedParams = await params;
  const renewalId = resolvedParams.id;

  if (!renewalId) {
    return createErrorResponse(
      'Renewal ID is required',
      ApiErrorCode.VALIDATION_ERROR,
      HttpStatus.BAD_REQUEST
    );
  }

  // Check if tenant schema exists
  const schemaReady = await schemaExists(tenant.tenantSchema);
  if (!schemaReady) {
    return createErrorResponse(
      'Tenant schema not found',
      ApiErrorCode.DATABASE_ERROR,
      HttpStatus.NOT_FOUND
    );
  }

  try {
    // Get current renewal details
    const getCurrentRenewalQuery = `
      SELECT 
        "RenewalID",
        "start_date",
        "Status"
      FROM "${tenant.tenantSchema}"."Renewals"
      WHERE "RenewalID" = $1 AND "Active" = true
    `;

    const currentRenewalResult = await executeQuery(getCurrentRenewalQuery, [renewalId]);

    if (!currentRenewalResult.success || !currentRenewalResult.data || currentRenewalResult.data.length === 0) {
      return createErrorResponse(
        'Renewal not found',
        ApiErrorCode.NOT_FOUND,
        HttpStatus.NOT_FOUND
      );
    }

    const currentRenewal = currentRenewalResult.data[0];
    const currentRenewalDate = new Date(currentRenewal.start_date);
    
    // Calculate new Start Date (add 1 year)
    const newRenewalDate = new Date(currentRenewalDate);
    newRenewalDate.setFullYear(newRenewalDate.getFullYear() + 1);

    // Update the renewal with new date and status
    const processRenewalQuery = `
      UPDATE "${tenant.tenantSchema}"."Renewals"
      SET 
        "start_date" = $1,
        "Status" = 'Active',
        "UpdatedAt" = $2
      WHERE "RenewalID" = $3 AND "Active" = true
      RETURNING 
        "RenewalID" as id,
        "start_date" as start_date,
        "Status" as status
    `;

    const processResult = await executeQuery(processRenewalQuery, [
      newRenewalDate,
      new Date(),
      renewalId
    ]);

    if (!processResult.success) {
      throw new Error(processResult.error || 'Failed to process renewal');
    }

    if (!processResult.data || processResult.data.length === 0) {
      return createErrorResponse(
        'Failed to process renewal',
        ApiErrorCode.DATABASE_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }

    const processedRenewal = processResult.data[0];

    console.log(`Renewal processed successfully: ${renewalId}, new date: ${newRenewalDate.toISOString()}`);

    return createSuccessResponse(
      {
        id: processedRenewal.id.toString(),
        start_date: new Date(processedRenewal.start_date).toISOString().split('T')[0],
        status: processedRenewal.status,
        processedAt: new Date().toISOString()
      },
      'Renewal processed successfully'
    );

  } catch (error) {
    console.error('Error processing renewal:', error);
    return createErrorResponse(
      'Failed to process renewal',
      ApiErrorCode.DATABASE_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
});

/**
 * Database Health Check API
 * 
 * This endpoint provides database connection health status and metrics
 * for monitoring and debugging purposes.
 */

import { getConnectionHealth } from '@/lib/database';
import { fetchAuthSession } from 'aws-amplify/auth';
import {
  createSuccessResponse,
  createErrorResponse,
  ApiErrorCode,
  HttpStatus,
  withErrorHandling
} from '@/lib/api/response';

export const GET = withErrorHandling(async () => {
  // Verify authentication using Amplify
  try {
    const session = await fetchAuthSession();
    if (!session?.tokens?.idToken) {
      return createErrorResponse(
        'Authentication required',
        ApiErrorCode.UNAUTHORIZED,
        HttpStatus.UNAUTHORIZED
      );
    }
  } catch (error) {
    return createErrorResponse(
      'Authentication failed',
      ApiErrorCode.UNAUTHORIZED,
      HttpStatus.UNAUTHORIZED
    );
  }

  // Get database health status
  const health = await getConnectionHealth();
  
  if (!health.healthy) {
    return createErrorResponse(
      'Database is unhealthy',
      ApiErrorCode.DATABASE_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }

  return createSuccessResponse({
    status: 'healthy',
    database: {
      healthy: health.healthy,
      totalConnections: health.totalConnections,
      idleConnections: health.idleConnections,
      waitingCount: health.waitingCount,
      connectionUtilization: health.totalConnections > 0 
        ? Math.round(((health.totalConnections - health.idleConnections) / health.totalConnections) * 100)
        : 0
    },
    timestamp: new Date().toISOString()
  }, 'Database health check completed successfully');
});

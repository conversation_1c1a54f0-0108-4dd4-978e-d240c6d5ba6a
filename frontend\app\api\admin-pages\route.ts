/**
 * Enhanced Admin Pages API - Dynamic Sidebar and Access Control
 *
 * This endpoint returns pages that the current user has access to
 * based on their Cognito group membership AND their client's addon packages.
 * Supports two-level admin access (admin vs super-admin).
 */

import { createApiRoute } from '@/lib/api/route-factory'
import { databaseService } from '@/lib/services/database-service'
import { STATUS } from '@/lib/constants/app-constants'

export interface AdminPage {
  id: number
  name: string
  header: string
  description: string | null
  sidebar: boolean
  status: string
  display_order: number
  icon_svg: string | null
  route_path: string
  groups: string[]
  packages?: string[]
}

/**
 * GET /api/admin-pages
 * Returns pages accessible to the current user based on their groups
 */
export const GET = createApiRoute('GET', {
  requireAuth: true,
  handler: async (context) => {
    const { session } = context;

    // Normalize groups to lowercase
    const normalizedGroups = (session?.groups || [])
      .filter(group => typeof group === 'string')
      .map(group => group.toLowerCase().trim())

    console.log('[ADMIN-PAGES] User groups:', normalizedGroups)
    console.log('[ADMIN-PAGES] User email:', session?.email)

    // If no groups found, return empty array (no access)
    if (normalizedGroups.length === 0) {
      return []
    }

    // Extract client domain from email
    const clientDomain = session?.email?.includes('@') ? session.email.split('@')[1] : ''
    if (!clientDomain) {
      console.log('[ADMIN-PAGES] No client domain found in email')
      return []
    }

    // Get database connection
    const db = databaseService

    // Query pages accessible to user's groups AND client's addon packages
    const query = `
      SELECT DISTINCT
        p.id,
        p.name,
        p.header,
        p.description,
        p.sidebar,
        p.status,
        p.display_order,
        p.icon_svg,
        p.route_path,
        ARRAY_AGG(DISTINCT pg.group_name) as groups,
        ARRAY_AGG(DISTINCT ap.display_name) as packages
      FROM metadata.pages p
      INNER JOIN metadata.page_groups pg ON p.id = pg.page_id
      INNER JOIN metadata.page_addon_packages pap ON p.id = pap.page_id
      INNER JOIN metadata.addon_packages ap ON pap.package_id = ap.id
      INNER JOIN metadata.client_addon_packages cap ON ap.id = cap.package_id
      INNER JOIN metadata.clients c ON cap.client_id = c.id
      WHERE p.status = 'A'
        AND pg.group_name = ANY($1)
        AND $2 = ANY(c.domain)
        AND c.status = 'active'
        AND ap.status = '${STATUS.ACTIVE}'
        AND cap.status = '${STATUS.ACTIVE}'
      GROUP BY p.id, p.name, p.header, p.description, p.sidebar, p.status, p.display_order, p.icon_svg, p.route_path
      ORDER BY p.display_order ASC, p.name ASC
    `

    const result = await context.executeQuery!(query, [normalizedGroups, clientDomain])

    if (!result.success) {
      throw new Error('Failed to fetch admin pages from database')
    }

    const pages: AdminPage[] = result.data.map((row: any) => ({
      id: row.id,
      name: row.name,
      header: row.header,
      description: row.description,
      sidebar: row.sidebar,
      status: row.status,
      display_order: row.display_order,
      icon_svg: row.icon_svg,
      route_path: row.route_path,
      groups: row.groups || [],
      packages: row.packages || []
    }))

    console.log(`[ADMIN-PAGES] Found ${pages.length} accessible pages for user`)

    return pages
  }
});

// Note: Sidebar functionality is handled by /api/admin-pages/sidebar/route.ts

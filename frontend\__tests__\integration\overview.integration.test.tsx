/**
 * Overview Integration Tests
 * 
 * End-to-end integration tests for the overview functionality
 * including component interactions, data flow, and user workflows
 */

import React from 'react'
import { render, screen, waitFor, fireEvent } from '../utils/test-utils'
import { jest } from '@jest/globals'
import OverviewPage from '@/app/overview/page'
import { testUtils } from '../utils/test-utils'

// Mock the hooks and contexts
const mockUseTenant = jest.fn()
const mockUseDashboardData = jest.fn()

jest.mock('@/contexts/AppContext', () => ({
  useTenant: () => mockUseTenant(),
}))

jest.mock('@/hooks/useDashboardData', () => ({
  useDashboardData: () => mockUseDashboardData(),
}))

// Mock Next.js navigation
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
  }),
  usePathname: () => '/overview',
  useSearchParams: () => new URLSearchParams(),
}))

describe('Overview Integration Tests', () => {
  const mockTenant = testUtils.generateTestData.tenant()
  const mockOverviewData = testUtils.generateTestData.dashboardStats()
  const mockRenewals = [
    testUtils.generateTestData.renewal({
      id: '1',
      name: 'Microsoft Office 365',
      start_date: new Date('2025-02-15'),
    }),
    testUtils.generateTestData.renewal({
      id: '2',
      name: 'Adobe Creative Suite',
      start_date: new Date('2025-03-01'),
    }),
  ]

  beforeEach(() => {
    jest.clearAllMocks()

    // Mock successful tenant
    mockUseTenant.mockReturnValue({
      tenant: mockTenant,
      loading: false,
      error: null,
    })

    // Mock successful overview data
    mockUseDashboardData.mockReturnValue({
      data: {
        stats: mockOverviewData,
        recentRenewals: mockRenewals,
        upcomingRenewals: mockRenewals,
      },
      isLoading: false,
      error: null,
      refetch: jest.fn(),
    })

    // Mock fetch for API calls
    global.fetch = jest.fn().mockResolvedValue(
      testUtils.mockApiSuccess(mockOverviewData)
    )
  })

  describe('Full Overview Workflow', () => {
    it('should render complete overview with all components', async () => {
      render(<OverviewPage />)

      // Wait for components to load
      await waitFor(() => {
        expect(screen.getByText('Total Renewals')).toBeInTheDocument()
      })

      // Check header
      expect(screen.getByText(mockTenant.clientName)).toBeInTheDocument()

      // Check stats
      expect(screen.getByText('Total Renewals')).toBeInTheDocument()
      expect(screen.getByText('Renewals Due')).toBeInTheDocument()
      expect(screen.getByText('Vendors')).toBeInTheDocument()
      expect(screen.getByText('Annual Spend')).toBeInTheDocument()

      // Check renewals sections
      expect(screen.getByText('Microsoft Office 365')).toBeInTheDocument()
      expect(screen.getByText('Adobe Creative Suite')).toBeInTheDocument()
    })

    it('should handle loading states correctly', async () => {
      // Start with loading state
      mockUseDashboardData.mockReturnValue({
        data: {
          stats: { totalRenewals: 0, renewalsDue: 0, vendors: 0, annualSpend: '$0' },
          recentRenewals: [],
          upcomingRenewals: [],
        },
        isLoading: true,
        error: null,
        refetch: jest.fn(),
      })

      const { rerender } = render(<OverviewPage />)

      // Should show loading indicators
      expect(screen.getAllByText('⏳')).toHaveLength(4)

      // Update to loaded state
      mockUseDashboardData.mockReturnValue({
        data: {
          stats: mockOverviewData,
          recentRenewals: mockRenewals,
          upcomingRenewals: mockRenewals,
        },
        isLoading: false,
        error: null,
        refetch: jest.fn(),
      })

      rerender(<OverviewPage />)

      // Should show actual data
      await waitFor(() => {
        expect(screen.queryByText('⏳')).not.toBeInTheDocument()
        expect(screen.getByText('Microsoft Office 365')).toBeInTheDocument()
      })
    })

    it('should handle error states gracefully', async () => {
      const errorMessage = 'Failed to load overview data'
      
      mockUseDashboardData.mockReturnValue({
        data: {
          stats: { totalRenewals: 0, renewalsDue: 0, vendors: 0, annualSpend: '$0' },
          recentRenewals: [],
          upcomingRenewals: [],
        },
        isLoading: false,
        error: errorMessage,
        refetch: jest.fn(),
      })

      render(<OverviewPage />)

      await waitFor(() => {
        expect(screen.getByText(errorMessage)).toBeInTheDocument()
      })
    })
  })

  describe('User Interactions', () => {
    it('should handle search functionality', async () => {
      const mockRefetch = jest.fn()
      mockUseDashboardData.mockReturnValue({
        data: {
          stats: mockOverviewData,
          recentRenewals: mockRenewals,
          upcomingRenewals: mockRenewals,
        },
        isLoading: false,
        error: null,
        refetch: mockRefetch,
      })

      render(<OverviewPage />)

      const searchInput = screen.getByPlaceholderText('Search renewals...')
      
      fireEvent.change(searchInput, { target: { value: 'Microsoft' } })

      // Should trigger search (debounced)
      await waitFor(() => {
        expect(searchInput).toHaveValue('Microsoft')
      })
    })

    it('should handle add renewal button click', async () => {
      render(<OverviewPage />)

      const addButton = screen.getByText('Add Renewal')
      fireEvent.click(addButton)

      // Should trigger add renewal functionality
      // (Implementation depends on actual navigation/modal logic)
    })

    it('should handle renewal item clicks', async () => {
      render(<OverviewPage />)

      await waitFor(() => {
        expect(screen.getByText('Microsoft Office 365')).toBeInTheDocument()
      })

      const renewalItem = screen.getByText('Microsoft Office 365')
      fireEvent.click(renewalItem)

      // Should trigger renewal details view
      // (Implementation depends on actual navigation logic)
    })
  })

  describe('Accessibility Integration', () => {
    it('should maintain accessibility throughout interactions', async () => {
      render(<OverviewPage />)

      await waitFor(() => {
        expect(screen.getByRole('region', { name: 'Overview Statistics' })).toBeInTheDocument()
      })

      // Check for proper heading structure
      const headings = screen.getAllByRole('heading')
      expect(headings.length).toBeGreaterThan(0)

      // Check for proper button accessibility
      const buttons = screen.getAllByRole('button')
      buttons.forEach(button => {
        expect(button).toBeVisible()
      })
    })

    it('should support keyboard navigation', async () => {
      render(<OverviewPage />)

      await waitFor(() => {
        expect(screen.getByText('Add Renewal')).toBeInTheDocument()
      })

      const addButton = screen.getByText('Add Renewal')
      
      // Should be focusable
      addButton.focus()
      expect(document.activeElement).toBe(addButton)

      // Should respond to keyboard events
      fireEvent.keyDown(addButton, { key: 'Enter' })
      // Should trigger the same action as click
    })
  })

  describe('Error Boundary Integration', () => {
    it('should catch and handle component errors', () => {
      // Mock a component that throws an error
      const ThrowingComponent = () => {
        throw new Error('Test error')
      }

      const consoleSpy = jest.spyOn(console, 'error').mockImplementation()

      expect(() => {
        render(<ThrowingComponent />)
      }).not.toThrow()

      consoleSpy.mockRestore()
    })

    it('should recover from errors when props change', async () => {
      // This would test error boundary recovery
      // Implementation depends on actual error boundary setup
      render(<OverviewPage />)

      // Should render without throwing
      await waitFor(() => {
        expect(screen.getByText('Total Renewals')).toBeInTheDocument()
      })
    })
  })
})

/**
 * Product Matching Algorithm
 * 
 * Implements sophisticated product matching logic within vendor context:
 * 1. GTIN (Global Trade Item Number) match (95% confidence)
 * 2. Vendor + SKU match (90% confidence)
 * 3. Vendor + Fuzzy Name match (70-85% confidence)
 * 
 * Products are only matched within the same vendor to ensure accuracy.
 */

import { Logger } from '../../Logger'
import { TenantProduct, GlobalProduct, ProductMatch } from '../processors/ProductSyncProcessor'

export class ProductMatcher {
  private logger: Logger

  constructor(logger: Logger) {
    this.logger = logger
  }

  /**
   * Find potential matches for a tenant product against global products
   */
  async findMatches(tenantProduct: TenantProduct, globalProducts: GlobalProduct[]): Promise<ProductMatch[]> {
    const matches: ProductMatch[] = []

    for (const globalProduct of globalProducts) {
      // Try GTIN match first (highest confidence)
      const gtinMatch = this.matchByGTIN(tenantProduct, globalProduct)
      if (gtinMatch) {
        matches.push(gtinMatch)
        continue // GTIN match is definitive, skip other checks
      }

      // Try SKU match
      const skuMatch = this.matchBySKU(tenantProduct, globalProduct)
      if (skuMatch) {
        matches.push(skuMatch)
        continue // SKU match is very strong, skip fuzzy matching
      }

      // Try fuzzy name match
      const fuzzyMatch = this.matchByFuzzyName(tenantProduct, globalProduct)
      if (fuzzyMatch) {
        matches.push(fuzzyMatch)
      }
    }

    // Sort by confidence descending
    matches.sort((a, b) => b.confidence - a.confidence)

    this.logger.debug(`Found ${matches.length} potential matches for product ${tenantProduct.name}`, {
      tenantProductId: tenantProduct.id,
      matches: matches.map(m => ({ globalProductId: m.globalProductId, confidence: m.confidence, matchType: m.matchType }))
    })

    return matches
  }

  /**
   * Match by GTIN (95% confidence)
   */
  private matchByGTIN(tenantProduct: TenantProduct, globalProduct: GlobalProduct): ProductMatch | null {
    if (!tenantProduct.gtin || !globalProduct.gtin) {
      return null
    }

    // Normalize GTINs (remove spaces, hyphens)
    const tenantGTIN = this.normalizeGTIN(tenantProduct.gtin)
    const globalGTIN = this.normalizeGTIN(globalProduct.gtin)

    if (tenantGTIN === globalGTIN) {
      return {
        tenantProductId: tenantProduct.id,
        globalProductId: globalProduct.id,
        confidence: 95,
        matchType: 'gtin',
        matchDetails: {
          tenantGTIN: tenantProduct.gtin,
          globalGTIN: globalProduct.gtin,
          normalizedMatch: tenantGTIN
        }
      }
    }

    return null
  }

  /**
   * Match by SKU (90% confidence)
   */
  private matchBySKU(tenantProduct: TenantProduct, globalProduct: GlobalProduct): ProductMatch | null {
    if (!tenantProduct.sku || !globalProduct.sku) {
      return null
    }

    // Normalize SKUs (remove spaces, convert to uppercase)
    const tenantSKU = this.normalizeSKU(tenantProduct.sku)
    const globalSKU = this.normalizeSKU(globalProduct.sku)

    if (tenantSKU === globalSKU) {
      return {
        tenantProductId: tenantProduct.id,
        globalProductId: globalProduct.id,
        confidence: 90,
        matchType: 'vendor_sku',
        matchDetails: {
          tenantSKU: tenantProduct.sku,
          globalSKU: globalProduct.sku,
          normalizedMatch: tenantSKU
        }
      }
    }

    return null
  }

  /**
   * Match by fuzzy name (70-85% confidence)
   */
  private matchByFuzzyName(tenantProduct: TenantProduct, globalProduct: GlobalProduct): ProductMatch | null {
    // Calculate name similarity
    const nameSimilarity = this.calculateStringSimilarity(
      this.normalizeProductName(tenantProduct.name),
      this.normalizeProductName(globalProduct.name)
    )

    // Require at least 70% name similarity
    if (nameSimilarity < 0.7) {
      return null
    }

    // Calculate confidence based on name similarity and additional factors
    let confidence = Math.round(nameSimilarity * 100)

    // Boost confidence if categories match
    if (tenantProduct.category && globalProduct.category) {
      const categorySimilarity = this.calculateStringSimilarity(
        tenantProduct.category.toLowerCase(),
        globalProduct.category.toLowerCase()
      )
      
      if (categorySimilarity > 0.8) {
        confidence = Math.min(confidence + 5, 85) // Boost by 5%, cap at 85%
      }
    }

    // Cap fuzzy matches at 85%
    confidence = Math.min(confidence, 85)

    if (confidence >= 70) {
      return {
        tenantProductId: tenantProduct.id,
        globalProductId: globalProduct.id,
        confidence,
        matchType: 'fuzzy_name',
        matchDetails: {
          nameSimilarity,
          tenantName: tenantProduct.name,
          globalName: globalProduct.name,
          tenantCategory: tenantProduct.category,
          globalCategory: globalProduct.category
        }
      }
    }

    return null
  }

  /**
   * Normalize GTIN for comparison
   */
  private normalizeGTIN(gtin: string): string {
    return gtin.replace(/[\s\-]/g, '')
  }

  /**
   * Normalize SKU for comparison
   */
  private normalizeSKU(sku: string): string {
    return sku.replace(/\s/g, '').toUpperCase()
  }

  /**
   * Normalize product name for comparison
   */
  private normalizeProductName(name: string): string {
    return name.toLowerCase()
      .replace(/[^\w\s]/g, '')
      .replace(/\s+/g, ' ')
      .trim()
  }

  /**
   * Calculate string similarity using Levenshtein distance
   */
  private calculateStringSimilarity(str1: string, str2: string): number {
    if (str1 === str2) return 1.0
    if (str1.length === 0 || str2.length === 0) return 0.0

    const distance = this.levenshteinDistance(str1, str2)
    const maxLength = Math.max(str1.length, str2.length)
    
    return 1 - (distance / maxLength)
  }

  /**
   * Calculate Levenshtein distance between two strings
   */
  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null))

    for (let i = 0; i <= str1.length; i++) {
      matrix[0][i] = i
    }

    for (let j = 0; j <= str2.length; j++) {
      matrix[j][0] = j
    }

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,     // deletion
          matrix[j - 1][i] + 1,     // insertion
          matrix[j - 1][i - 1] + indicator // substitution
        )
      }
    }

    return matrix[str2.length][str1.length]
  }
}

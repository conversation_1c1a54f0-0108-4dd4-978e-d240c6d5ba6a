/**
 * Dynamic Sidebar Component
 * 
 * Database-driven navigation with role-based access control
 * Replaces hardcoded sidebar with dynamic page loading
 */

'use client'

import React, { memo } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useAuth } from '@/lib/hooks/useAuth'
import { useSidebarPages } from '@/lib/hooks/useSidebarPages'
import { LoadingSkeleton } from '@/components/common/LoadingStates'
import { BaseComponentProps } from '@/lib/types'

interface DynamicSidebarProps extends BaseComponentProps {
  collapsed?: boolean
  onToggle?: () => void
}

function DynamicSidebar({ 
  collapsed = false, 
  onToggle,
  className = '',
  'data-testid': testId 
}: DynamicSidebarProps) {
  const pathname = usePathname()
  const { user, logout } = useAuth()
  const { pages, isLoading, error } = useSidebarPages()

  // Handle sign out
  const handleSignOut = async () => {
    try {
      await logout()
      console.log('[DynamicSidebar] User signed out successfully')
    } catch (error) {
      console.error('[DynamicSidebar] Sign out error:', error)
    }
  }

  // Render SVG icon from database
  const renderIcon = (iconSvg: string | null) => {
    if (!iconSvg) {
      // Default icon
      return (
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
        />
      )
    }

    try {
      // Parse SVG content and extract path
      const pathMatch = iconSvg.match(/<path[^>]*d="([^"]*)"[^>]*>/i)
      if (pathMatch) {
        return <path d={pathMatch[1]} fill="currentColor" />
      }
      
      // Fallback to default icon
      return (
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
        />
      )
    } catch (error) {
      console.warn('[DynamicSidebar] Error parsing icon SVG:', error)
      return (
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
        />
      )
    }
  }

  return (
    <div 
      className={`dynamic-sidebar ${collapsed ? 'collapsed' : ''} ${className}`}
      data-testid={testId}
    >
      {/* Header */}
      <div className="sidebar-header">
        <div className="sidebar-brand">
          <h1 className="sidebar-title">RenewTrack</h1>
        </div>
        
        {onToggle && (
          <button
            onClick={onToggle}
            className="sidebar-toggle"
            aria-label={collapsed ? 'Expand sidebar' : 'Collapse sidebar'}
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d={collapsed ? "M9 5l7 7-7 7" : "M15 19l-7-7 7-7"}
              />
            </svg>
          </button>
        )}
      </div>

      {/* User Info */}
      <div className="sidebar-user">
        <div className="user-info">
          <div className="user-name">
            {user?.given_name && user?.family_name 
              ? `${user.given_name} ${user.family_name}`
              : user?.name || user?.email || 'User'
            }
          </div>
          <div className="user-email">{user?.email}</div>
        </div>
        
        <button
          onClick={handleSignOut}
          className="sidebar-signout-btn"
          title="Sign out"
          aria-label="Sign out of your account"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
            />
          </svg>
        </button>
      </div>

      {/* Navigation */}
      <nav className="sidebar-nav">
        {isLoading ? (
          <div className="px-4">
            <LoadingSkeleton lines={5} height="40px" className="mb-2" />
          </div>
        ) : error ? (
          <div className="px-4 py-2 text-sm text-red-500">
            <p>Failed to load navigation</p>
          </div>
        ) : pages.length === 0 ? (
          <div className="px-4 py-2 text-sm text-gray-500">
            <p>No navigation pages available</p>
          </div>
        ) : (
          pages.map((page) => {
            const isActive = pathname === page.route_path

            return (
              <Link
                key={page.id}
                href={page.route_path}
                className={`sidebar-nav-item ${isActive ? 'active' : ''}`}
                title={page.description || page.header}
              >
                <svg
                  className="sidebar-nav-icon"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  {renderIcon(page.icon_svg)}
                </svg>
                {!collapsed && (
                  <span className="sidebar-nav-text">{page.header}</span>
                )}
              </Link>
            )
          })
        )}
      </nav>
    </div>
  )
}

export default memo(DynamicSidebar)

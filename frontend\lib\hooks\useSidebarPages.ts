/**
 * Sidebar Pages Hook
 * 
 * Fetches and caches sidebar pages accessible to the current user
 */

'use client'

import { useData } from './useData'
import { useAuth } from './useAuth'
import { TIME } from '@/lib/constants/app-constants'

export interface SidebarPage {
  id: number
  name: string
  header: string
  description: string | null
  display_order: number
  icon_svg: string | null
  route_path: string
}

interface UseSidebarPagesResult {
  pages: SidebarPage[]
  isLoading: boolean
  error: string | null
  refetch: () => void
}

/**
 * Hook to fetch sidebar pages for the current user
 */
export function useSidebarPages(): UseSidebarPagesResult {
  const { user } = useAuth()
  
  const {
    data: pages = [],
    loading: isLoading,
    error,
    refetch
  } = useData<SidebarPage[]>({
    endpoint: '/api/admin-pages/sidebar',
    cache: {
      key: `sidebar-pages-${user?.email || 'anonymous'}`,
      ttl: 5 * TIME.MINUTE, // 5 minutes cache
      tags: ['sidebar-pages', 'user-pages']
    },
    enabled: !!user // Only fetch if user is authenticated
  })

  return {
    pages,
    isLoading,
    error: error || null,
    refetch
  }
}

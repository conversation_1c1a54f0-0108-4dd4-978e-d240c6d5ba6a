# Code Organization Guide

This document outlines the organized code structure and import patterns for the RenewTrack frontend application.

## 🏗️ **Directory Structure**

```
frontend/
├── app/                    # Next.js App Router pages
├── components/             # React components
├── lib/                   # Core library code
│   ├── api/               # API utilities (client & server)
│   ├── auth/              # Authentication (server-only)
│   ├── config/            # Configuration
│   ├── constants/         # Application constants
│   ├── database/          # Database utilities (server-only)
│   ├── hooks/             # React hooks (client-only)
│   ├── monitoring/        # Monitoring & logging (server-only)
│   ├── security/          # Security utilities (server-only)
│   ├── services/          # Business logic services
│   ├── tenant/            # Multi-tenancy (server-only)
│   ├── types/             # TypeScript type definitions
│   └── utils/             # Utility functions
├── public/                # Static assets
└── docs/                  # Documentation
```

## 🚦 **Client vs Server Separation**

### **Client-Safe Modules**
These can be imported in both client and server components:

- `@/lib/types` - Type definitions
- `@/lib/constants` - Application constants
- `@/lib/hooks` - React hooks (client-only)
- `@/lib/utils` - Client-safe utilities
- `@/lib/services` - Client-safe services
- `@/lib/config` - Client-safe configuration

### **Server-Only Modules**
These should ONLY be imported in API routes and server components:

- `@/lib/database` - Database operations
- `@/lib/auth` - Server-side authentication
- `@/lib/tenant` - Tenant management
- `@/lib/security` - Security utilities
- `@/lib/monitoring` - Logging and monitoring

## 📦 **Import Patterns**

### **✅ Recommended Imports**

```typescript
// From main lib index (client-safe)
import { TenantContext, ApiResponse } from '@/lib'
import { useData, useAppState } from '@/lib'
import { formatCurrency, formatDate } from '@/lib'

// Direct imports for specific functionality
import { executeQuery } from '@/lib/database'           // API routes only
import { validateTenantAccess } from '@/lib/tenant'     // API routes only
import { trackError } from '@/lib/monitoring'           // API routes only

// Component imports
import { Button } from '@/components/ui/Button'
import RenewalCard from '@/components/renewals/RenewalCard'
```

### **❌ Avoid These Imports**

```typescript
// Don't import server-only modules in client components
import { executeQuery } from '@/lib/database'           // ❌ Client component
import { validateTenantAccess } from '@/lib/tenant'     // ❌ Client component

// Don't use wildcard imports from mixed modules
import * from '@/lib'                                   // ❌ Too broad
import * from '@/lib/services'                          // ❌ May include server code
```

## 🎯 **Specific Module Guidelines**

### **lib/index.ts**
- Only exports client-safe utilities
- Provides convenient access to commonly used functions
- Server-only utilities are intentionally excluded

### **lib/hooks/index.ts**
- All React hooks (client-side only)
- Organized by functionality
- Includes state management and data fetching hooks

### **lib/services/index.ts**
- Only client-safe services
- Server-side services must be imported directly
- Includes app state and HTTP client services

### **lib/utils/index.ts**
- Client-safe utility functions
- Formatting, validation, and helper functions
- Server-only utilities excluded

## 🔧 **Migration Guide**

### **From Old Patterns**
```typescript
// Old (problematic)
import { saveRenewal } from '@/lib/services'            // Server-only function
import { usePerformanceMonitor } from '@/lib/utils'     // Server-only hook

// New (correct)
const { mutate: createRenewal } = usePost('/api/renewals')  // Use API endpoint
// Performance monitoring removed from client-side
```

### **Component Updates**
- Replace direct service calls with API endpoints
- Use `useData` hook for data fetching
- Remove server-only performance monitoring from client components

## 📋 **Best Practices**

1. **Import Specificity**: Import only what you need
2. **Client/Server Awareness**: Know which code runs where
3. **Type Safety**: Use TypeScript types consistently
4. **Performance**: Avoid unnecessary imports
5. **Maintainability**: Keep imports organized and documented

## 🚀 **Quick Reference**

### **Common Client-Side Imports**
```typescript
import { useState, useEffect } from 'react'
import { useAppState, useData } from '@/lib/hooks'
import { formatCurrency, formatDate } from '@/lib/utils'
import { TenantContext } from '@/lib/types'
```

### **Common Server-Side Imports (API Routes)**
```typescript
import { NextRequest, NextResponse } from 'next/server'
import { executeQuery } from '@/lib/database'
import { authenticateRequest } from '@/lib/api/auth-middleware'
import { createSuccessResponse } from '@/lib/api/response'
```

## 🔍 **Troubleshooting**

### **Import Errors**
- Check if importing server-only code in client component
- Verify the module is exported from the index file
- Use direct imports for server-only functionality

### **Type Errors**
- Ensure types are imported from `@/lib/types`
- Check for conflicting type definitions
- Use type-only imports when needed: `import type { ... }`

---

This organization ensures clean separation of concerns, better performance, and easier maintenance.

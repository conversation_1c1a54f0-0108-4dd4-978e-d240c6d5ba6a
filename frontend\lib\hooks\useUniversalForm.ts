/**
 * Universal Form Hook
 * 
 * Provides unified form handling with validation, submission, and state management
 */

'use client'

import { useState, useCallback, useRef, useEffect } from 'react'
import { z } from 'zod'

export interface UseUniversalFormOptions<T> {
  validationSchema?: z.ZodSchema<T>
  initialValues?: Partial<T>
  onSubmit: (values: T) => Promise<void> | void
  onValidate?: (values: Partial<T>) => Record<string, string> | void
  validateOnChange?: boolean
  validateOnBlur?: boolean
  resetOnSubmit?: boolean
}

export interface FormField {
  value: any
  error?: string
  touched: boolean
  dirty: boolean
}

export interface FormState<T> {
  values: Partial<T>
  errors: Record<string, string>
  touched: Record<string, boolean>
  dirty: Record<string, boolean>
  isSubmitting: boolean
  isValid: boolean
  isDirty: boolean
  submitCount: number
}

export interface FormActions<T> {
  setValue: (field: keyof T, value: any) => void
  setValues: (values: Partial<T>) => void
  setError: (field: keyof T, error: string) => void
  setErrors: (errors: Record<string, string>) => void
  setTouched: (field: keyof T, touched?: boolean) => void
  setFieldTouched: (field: keyof T, touched?: boolean) => void
  reset: (values?: Partial<T>) => void
  submit: () => Promise<void>
  validate: () => boolean
  validateField: (field: keyof T) => boolean
}

export interface UseUniversalFormResult<T> {
  state: FormState<T>
  actions: FormActions<T>
  getFieldProps: (field: keyof T) => {
    value: any
    onChange: (value: any) => void
    onBlur: () => void
    error?: string
    touched: boolean
    dirty: boolean
  }
  getFormProps: () => {
    onSubmit: (e: React.FormEvent) => void
  }
}

export function useUniversalForm<T extends Record<string, any>>(
  options: UseUniversalFormOptions<T>
): UseUniversalFormResult<T> {
  const {
    validationSchema,
    initialValues = {} as Partial<T>,
    onSubmit,
    onValidate,
    validateOnChange = false,
    validateOnBlur = true,
    resetOnSubmit = false
  } = options

  // Form state
  const [values, setValues] = useState<Partial<T>>(initialValues)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [touched, setTouched] = useState<Record<string, boolean>>({})
  const [dirty, setDirty] = useState<Record<string, boolean>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitCount, setSubmitCount] = useState(0)

  const initialValuesRef = useRef(initialValues)

  // Update initial values if they change
  useEffect(() => {
    if (JSON.stringify(initialValues) !== JSON.stringify(initialValuesRef.current)) {
      initialValuesRef.current = initialValues
      setValues(initialValues)
      setDirty({})
      setTouched({})
      setErrors({})
    }
  }, [initialValues])

  // Validation function
  const validateForm = useCallback((valuesToValidate: Partial<T> = values): Record<string, string> => {
    let formErrors: Record<string, string> = {}

    // Zod validation
    if (validationSchema) {
      try {
        validationSchema.parse(valuesToValidate)
      } catch (error) {
        if (error instanceof z.ZodError) {
          error.errors.forEach(err => {
            const path = err.path.join('.')
            formErrors[path] = err.message
          })
        }
      }
    }

    // Custom validation
    if (onValidate) {
      const customErrors = onValidate(valuesToValidate)
      if (customErrors) {
        formErrors = { ...formErrors, ...customErrors }
      }
    }

    return formErrors
  }, [validationSchema, onValidate, values])

  // Validate single field
  const validateField = useCallback((field: keyof T): boolean => {
    const fieldErrors = validateForm({ [field]: values[field] } as Partial<T>)
    const fieldError = fieldErrors[field as string]
    
    setErrors(prev => ({
      ...prev,
      [field]: fieldError || ''
    }))

    return !fieldError
  }, [validateForm, values])

  // Validate entire form
  const validate = useCallback((): boolean => {
    const formErrors = validateForm()
    setErrors(formErrors)
    return Object.keys(formErrors).length === 0
  }, [validateForm])

  // Set field value
  const setValue = useCallback((field: keyof T, value: any) => {
    setValues(prev => ({ ...prev, [field]: value }))
    setDirty(prev => ({ ...prev, [field]: true }))

    if (validateOnChange) {
      setTimeout(() => validateField(field), 0)
    }
  }, [validateOnChange, validateField])

  // Set multiple values
  const setValuesAction = useCallback((newValues: Partial<T>) => {
    setValues(prev => ({ ...prev, ...newValues }))
    
    Object.keys(newValues).forEach(key => {
      setDirty(prev => ({ ...prev, [key]: true }))
    })

    if (validateOnChange) {
      setTimeout(() => validate(), 0)
    }
  }, [validateOnChange, validate])

  // Set field error
  const setError = useCallback((field: keyof T, error: string) => {
    setErrors(prev => ({ ...prev, [field]: error }))
  }, [])

  // Set multiple errors
  const setErrorsAction = useCallback((newErrors: Record<string, string>) => {
    setErrors(prev => ({ ...prev, ...newErrors }))
  }, [])

  // Set field touched
  const setFieldTouched = useCallback((field: keyof T, isTouched: boolean = true) => {
    setTouched(prev => ({ ...prev, [field]: isTouched }))
    
    if (isTouched && validateOnBlur) {
      setTimeout(() => validateField(field), 0)
    }
  }, [validateOnBlur, validateField])

  // Reset form
  const reset = useCallback((newValues?: Partial<T>) => {
    const resetValues = newValues || initialValuesRef.current
    setValues(resetValues)
    setErrors({})
    setTouched({})
    setDirty({})
    setIsSubmitting(false)
    setSubmitCount(0)
  }, [])

  // Submit form
  const submit = useCallback(async () => {
    setSubmitCount(prev => prev + 1)
    
    // Mark all fields as touched
    const allFields = Object.keys(values) as (keyof T)[]
    const touchedState = allFields.reduce((acc, field) => ({ ...acc, [field]: true }), {})
    setTouched(touchedState)

    // Validate
    const isValid = validate()
    if (!isValid) {
      console.log('Form validation failed:', errors)
      return
    }

    try {
      setIsSubmitting(true)
      await onSubmit(values as T)
      
      if (resetOnSubmit) {
        reset()
      }
      
      console.log('Form submitted successfully')
    } catch (error) {
      console.error('Form submission error:', error)
      throw error
    } finally {
      setIsSubmitting(false)
    }
  }, [values, validate, onSubmit, resetOnSubmit, reset, errors])

  // Computed state
  const isValid = Object.keys(errors).length === 0
  const isDirty = Object.values(dirty).some(Boolean)

  // Field props helper
  const getFieldProps = useCallback((field: keyof T) => ({
    value: values[field] || '',
    onChange: (value: any) => setValue(field, value),
    onBlur: () => setFieldTouched(field, true),
    error: errors[field as string],
    touched: touched[field as string] || false,
    dirty: dirty[field as string] || false
  }), [values, errors, touched, dirty, setValue, setFieldTouched])

  // Form props helper
  const getFormProps = useCallback(() => ({
    onSubmit: (e: React.FormEvent) => {
      e.preventDefault()
      submit()
    }
  }), [submit])

  return {
    state: {
      values,
      errors,
      touched,
      dirty,
      isSubmitting,
      isValid,
      isDirty,
      submitCount
    },
    actions: {
      setValue,
      setValues: setValuesAction,
      setError,
      setErrors: setErrorsAction,
      setTouched: setFieldTouched,
      setFieldTouched,
      reset,
      submit,
      validate,
      validateField
    },
    getFieldProps,
    getFormProps
  }
}

export { useUniversalForm as default }

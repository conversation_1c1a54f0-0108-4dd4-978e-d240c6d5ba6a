import { useState, useEffect, useCallback } from 'react'
import { 
  TenantLogEntry, 
  TenantLogFilters, 
  TenantLogSearchCriteria,
  TenantActivitySummary,
  UserActivitySummary,
  RecordHistory,
  ComplianceReport
} from '@/lib/types'

interface UseTenantLogsResult {
  logs: TenantLogEntry[]
  activitySummary: TenantActivitySummary[]
  userSummary: UserActivitySummary[]
  complianceReport: ComplianceReport[]
  recordHistory: RecordHistory[]
  loading: boolean
  error: string | null
  pagination: {
    total: number
    limit: number
    offset: number
    has_more: boolean
  }
  
  // Actions
  fetchLogs: (filters?: TenantLogFilters) => Promise<void>
  searchLogs: (criteria: TenantLogSearchCriteria) => Promise<void>
  getActivitySummary: (startDate?: string, endDate?: string) => Promise<void>
  getUserSummary: (userId?: string, startDate?: string, endDate?: string) => Promise<void>
  getComplianceReport: (startDate?: string, endDate?: string) => Promise<void>
  getRecordHistory: (table: string, recordId: string, limit?: number) => Promise<void>
  clearLogs: () => void
  refresh: () => Promise<void>
}

export function useTenantLogs(initialFilters?: TenantLogFilters): UseTenantLogsResult {
  const [logs, setLogs] = useState<TenantLogEntry[]>([])
  const [activitySummary, setActivitySummary] = useState<TenantActivitySummary[]>([])
  const [userSummary, setUserSummary] = useState<UserActivitySummary[]>([])
  const [complianceReport, setComplianceReport] = useState<ComplianceReport[]>([])
  const [recordHistory, setRecordHistory] = useState<RecordHistory[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [currentFilters, setCurrentFilters] = useState<TenantLogFilters>(initialFilters || {})
  const [pagination, setPagination] = useState({
    total: 0,
    limit: 50,
    offset: 0,
    has_more: false
  })

  const fetchLogs = useCallback(async (filters?: TenantLogFilters) => {
    setLoading(true)
    setError(null)
    
    try {
      const queryFilters = { ...currentFilters, ...filters }
      setCurrentFilters(queryFilters)
      
      const params = new URLSearchParams()
      Object.entries(queryFilters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params.append(key, value.toString())
        }
      })

      console.log('[TENANT-LOGS-HOOK] Fetching logs with filters:', queryFilters)
      
      const response = await fetch(`/api/tenant-logs?${params.toString()}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch logs: ${response.statusText}`)
      }
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch logs')
      }
      
      setLogs(result.data || [])
      
      if (result.pagination) {
        setPagination(result.pagination)
      }
      
      console.log('[TENANT-LOGS-HOOK] Successfully fetched', result.data?.length || 0, 'log entries')
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      console.error('[TENANT-LOGS-HOOK] Error fetching logs:', errorMessage)
      setError(errorMessage)
      setLogs([])
    } finally {
      setLoading(false)
    }
  }, [currentFilters])

  const searchLogs = useCallback(async (criteria: TenantLogSearchCriteria) => {
    setLoading(true)
    setError(null)
    
    try {
      console.log('[TENANT-LOGS-HOOK] Searching logs with criteria:', criteria)
      
      const response = await fetch('/api/tenant-logs/search', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(criteria),
      })
      
      if (!response.ok) {
        throw new Error(`Failed to search logs: ${response.statusText}`)
      }
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to search logs')
      }
      
      setLogs(result.data || [])
      console.log('[TENANT-LOGS-HOOK] Search returned', result.data?.length || 0, 'results')
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      console.error('[TENANT-LOGS-HOOK] Error searching logs:', errorMessage)
      setError(errorMessage)
      setLogs([])
    } finally {
      setLoading(false)
    }
  }, [])

  const getActivitySummary = useCallback(async (startDate?: string, endDate?: string) => {
    setLoading(true)
    setError(null)
    
    try {
      const params = new URLSearchParams({ report_type: 'activity' })
      if (startDate) params.append('start_date', startDate)
      if (endDate) params.append('end_date', endDate)
      
      const response = await fetch(`/api/tenant-logs?${params.toString()}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch activity summary: ${response.statusText}`)
      }
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch activity summary')
      }
      
      setActivitySummary(result.data || [])
      console.log('[TENANT-LOGS-HOOK] Activity summary fetched:', result.data?.length || 0, 'entries')
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      console.error('[TENANT-LOGS-HOOK] Error fetching activity summary:', errorMessage)
      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }, [])

  const getUserSummary = useCallback(async (userId?: string, startDate?: string, endDate?: string) => {
    setLoading(true)
    setError(null)
    
    try {
      const params = new URLSearchParams({ report_type: 'user' })
      if (userId) params.append('user_id', userId)
      if (startDate) params.append('start_date', startDate)
      if (endDate) params.append('end_date', endDate)
      
      const response = await fetch(`/api/tenant-logs?${params.toString()}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch user summary: ${response.statusText}`)
      }
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch user summary')
      }
      
      setUserSummary(result.data || [])
      console.log('[TENANT-LOGS-HOOK] User summary fetched:', result.data?.length || 0, 'entries')
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      console.error('[TENANT-LOGS-HOOK] Error fetching user summary:', errorMessage)
      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }, [])

  const getComplianceReport = useCallback(async (startDate?: string, endDate?: string) => {
    setLoading(true)
    setError(null)
    
    try {
      const params = new URLSearchParams({ report_type: 'compliance' })
      if (startDate) params.append('start_date', startDate)
      if (endDate) params.append('end_date', endDate)
      
      const response = await fetch(`/api/tenant-logs?${params.toString()}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch compliance report: ${response.statusText}`)
      }
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch compliance report')
      }
      
      setComplianceReport(result.data || [])
      console.log('[TENANT-LOGS-HOOK] Compliance report fetched:', result.data?.length || 0, 'entries')
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      console.error('[TENANT-LOGS-HOOK] Error fetching compliance report:', errorMessage)
      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }, [])

  const getRecordHistory = useCallback(async (table: string, recordId: string, limit: number = 50) => {
    setLoading(true)
    setError(null)
    
    try {
      const params = new URLSearchParams({
        table,
        record_id: recordId,
        limit: limit.toString()
      })
      
      const response = await fetch(`/api/tenant-logs?${params.toString()}`)
      
      if (!response.ok) {
        throw new Error(`Failed to fetch record history: ${response.statusText}`)
      }
      
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to fetch record history')
      }
      
      setRecordHistory(result.data || [])
      console.log('[TENANT-LOGS-HOOK] Record history fetched:', result.data?.length || 0, 'entries')
      
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred'
      console.error('[TENANT-LOGS-HOOK] Error fetching record history:', errorMessage)
      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }, [])

  const clearLogs = useCallback(() => {
    setLogs([])
    setActivitySummary([])
    setUserSummary([])
    setComplianceReport([])
    setRecordHistory([])
    setError(null)
    setPagination({ total: 0, limit: 50, offset: 0, has_more: false })
  }, [])

  const refresh = useCallback(async () => {
    await fetchLogs(currentFilters)
  }, [fetchLogs, currentFilters])

  // Load initial data if filters are provided
  useEffect(() => {
    if (initialFilters && Object.keys(initialFilters).length > 0) {
      fetchLogs(initialFilters)
    }
  }, []) // Only run on mount

  return {
    logs,
    activitySummary,
    userSummary,
    complianceReport,
    recordHistory,
    loading,
    error,
    pagination,
    fetchLogs,
    searchLogs,
    getActivitySummary,
    getUserSummary,
    getComplianceReport,
    getRecordHistory,
    clearLogs,
    refresh,
  }
}

import { NextRequest } from 'next/server';
import { fetchAuthSession, fetchUserAttributes } from 'aws-amplify/auth';
import { getClientByEmailDomain } from '@/lib/tenant/clients';
import { executeQuery, schemaExists } from '@/lib/database';
import {
  createSuccessResponse,
  createErrorResponse,
  ApiErrorCode,
  HttpStatus,
  withErrorHandling
} from '@/lib/api/response';

// Department interface
interface Department {
  id: number;
  name: string;
  is_deleted: boolean;
}

// GET /api/tenant-departments - Get all departments for tenant
export const GET = withErrorHandling(async (request: NextRequest) => {
  // Verify authentication using Amplify
  try {
    const session = await fetchAuthSession();
    if (!session?.tokens?.idToken) {
      return createErrorResponse(
        'Authentication required',
        ApiErrorCode.UNAUTHORIZED,
        HttpStatus.UNAUTHORIZED
      );
    }
  } catch (error) {
    return createErrorResponse(
      'Authentication failed',
      ApiErrorCode.UNAUTHORIZED,
      HttpStatus.UNAUTHORIZED
    );
  }

  // Get user attributes for tenant context
  const userAttributes = await fetchUserAttributes();
  const userEmail = userAttributes.email;

  if (!userEmail) {
    return createErrorResponse(
      'User email not found',
      ApiErrorCode.UNAUTHORIZED,
      HttpStatus.UNAUTHORIZED
    );
  }

  // Get tenant context using direct authentication pattern
  const clientResult = await getClientByEmailDomain(userEmail);

  if (!clientResult.success) {
    return createErrorResponse(
      'Failed to fetch tenant information',
      ApiErrorCode.DATABASE_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }

  const tenant = clientResult.client!;

  // Check if tenant schema exists
  const schemaReady = await schemaExists(tenant.tenantSchema);

  if (!schemaReady) {
    console.log(`⚠️ Tenant schema ${tenant.tenantSchema} not ready yet`);
    return createSuccessResponse([], 'Tenant schema not ready, returning empty departments');
  }

  try {
    // Query tenant departments
    const query = `
      SELECT
        id,
        name
      FROM "${tenant.tenantSchema}".tenant_departments
      WHERE is_deleted = false
      ORDER BY name ASC
    `;

    const result = await executeQuery(query, [], { schema: tenant.tenantSchema });

    if (!result.success) {
      console.error('Error fetching tenant departments:', result.error);
      return createErrorResponse(
        'Failed to fetch departments',
        ApiErrorCode.DATABASE_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }

    const departments = result.data || [];
    console.log(`📋 [DEPARTMENTS] Found ${departments.length} departments for tenant ${tenant.tenantSchema}`);

    return createSuccessResponse(departments, 'Departments retrieved successfully');

  } catch (error) {
    console.error('Error in tenant departments API:', error);
    return createErrorResponse(
      'Internal server error',
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
});

// POST /api/tenant-departments - Create new department
export const POST = withErrorHandling(async (request: NextRequest) => {
  // Verify authentication using Amplify
  try {
    const session = await fetchAuthSession();
    if (!session?.tokens?.idToken) {
      return createErrorResponse(
        'Authentication required',
        ApiErrorCode.UNAUTHORIZED,
        HttpStatus.UNAUTHORIZED
      );
    }
  } catch (error) {
    return createErrorResponse(
      'Authentication failed',
      ApiErrorCode.UNAUTHORIZED,
      HttpStatus.UNAUTHORIZED
    );
  }

  // Get user attributes for tenant context
  const userAttributes = await fetchUserAttributes();
  const userEmail = userAttributes.email;

  if (!userEmail) {
    return createErrorResponse(
      'User email not found',
      ApiErrorCode.UNAUTHORIZED,
      HttpStatus.UNAUTHORIZED
    );
  }

  // Create session object for tenant context resolution
  const sessionObj = {
    isAuth: true,
    userId: userAttributes.sub || 'unknown',
    email: userEmail,
    roles: ['user']
  };

  // Get tenant context
  const clientResult = await getClientByEmailDomain(userEmail);
  if (!clientResult.success || !clientResult.client) {
    return createErrorResponse(
      clientResult.error || 'Failed to resolve tenant context',
      (clientResult.errorCode as ApiErrorCode) || ApiErrorCode.UNAUTHORIZED,
      HttpStatus.UNAUTHORIZED
    );
  }
  const tenant = clientResult.client;

  try {
    const body = await request.json();
    const { name } = body;

    if (!name || typeof name !== 'string' || name.trim().length === 0) {
      return createErrorResponse(
        'Department name is required',
        ApiErrorCode.INVALID_INPUT,
        HttpStatus.BAD_REQUEST
      );
    }

    // Check if tenant schema exists
    const schemaReady = await schemaExists(tenant.tenantSchema);

    if (!schemaReady) {
      return createErrorResponse(
        'Tenant schema not ready',
        ApiErrorCode.DATABASE_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }

    try {
        // Insert new department
        const query = `
          INSERT INTO "${tenant.tenantSchema}"."tenant_departments" (name)
          VALUES ($1)
          RETURNING id, name
        `;

        const result = await executeQuery(query, [name.trim()], { schema: tenant.tenantSchema });

        if (!result.success || !result.data || result.data.length === 0) {
          console.error('Error creating department:', result.error);
          return createErrorResponse(
            'Failed to create department',
            ApiErrorCode.DATABASE_ERROR,
            HttpStatus.INTERNAL_SERVER_ERROR
          );
        }

        const newDepartment = result.data[0];
        console.log(`✅ [DEPARTMENTS] Created new department: ${newDepartment.name}`);

        return createSuccessResponse(newDepartment, 'Department created successfully');

      } catch (error) {
        console.error('Error creating department:', error);
        return createErrorResponse(
          'Internal server error',
          ApiErrorCode.INTERNAL_ERROR,
          HttpStatus.INTERNAL_SERVER_ERROR
        );
      }
    } catch (error) {
      console.error('Error parsing request body:', error);
      return createErrorResponse(
        'Invalid request body',
        ApiErrorCode.INVALID_INPUT,
        HttpStatus.BAD_REQUEST
      );
    }
});

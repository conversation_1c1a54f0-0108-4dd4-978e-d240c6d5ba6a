-- =====================================================
-- LICENSE VALIDATION FUNCTION
-- =====================================================
-- Create a database function to validate client licenses

CREATE OR REPLACE FUNCTION metadata.check_client_license_validity(p_client_id INTEGER)
RETURNS JSON AS $$
DECLARE
    license_record RECORD;
    result JSON;
BEGIN
    -- Get the active license for the client
    SELECT 
        cl.client_license_id,
        cl.status,
        cl.activation_date,
        cl.expiry_date,
        cl.current_renewals,
        cl.max_renewals,
        lk.license_key,
        lt.type_name as license_type,
        lt.features,
        ac.name as client_name
    INTO license_record
    FROM metadata.admin_client_licenses cl
    JOIN metadata.admin_license_keys lk ON cl.license_key_id = lk.license_key_id
    JOIN metadata.admin_license_types lt ON cl.license_type_id = lt.id
    JOIN metadata.clients ac ON cl.client_id = ac.id
    WHERE cl.client_id = p_client_id 
      AND cl.status = 'ACTIVE'
    LIMIT 1;

    -- Check if license exists
    IF NOT FOUND THEN
        result := json_build_object(
            'is_valid', false,
            'reason', 'No active license found for client',
            'license_info', null
        );
        RETURN result;
    END IF;

    -- Check if license has expired
    IF license_record.expiry_date IS NOT NULL AND license_record.expiry_date < CURRENT_DATE THEN
        result := json_build_object(
            'is_valid', false,
            'reason', 'License has expired',
            'license_info', json_build_object(
                'license_type', license_record.license_type,
                'expiry_date', license_record.expiry_date,
                'client_name', license_record.client_name
            )
        );
        RETURN result;
    END IF;

    -- License is valid
    result := json_build_object(
        'is_valid', true,
        'reason', null,
        'license_info', json_build_object(
            'license_type', license_record.license_type,
            'activation_date', license_record.activation_date,
            'expiry_date', license_record.expiry_date,
            'current_renewals', license_record.current_renewals,
            'max_renewals', license_record.max_renewals,
            'features', license_record.features,
            'client_name', license_record.client_name,
            'license_key', license_record.license_key
        )
    );

    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Grant execute permission
GRANT
EXECUTE ON FUNCTION metadata.check_client_license_validity (INTEGER) TO postgres;

-- Test the function
SELECT metadata.check_client_license_validity (2) as test_result;
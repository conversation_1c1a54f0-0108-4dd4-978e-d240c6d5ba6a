/**
 * Unified Error Display Component
 * 
 * Provides consistent error display across the entire application
 * with different variants for different contexts
 */

import React from 'react';
import { Button } from '@/components/ui/Button';

export interface ErrorDisplayProps {
  error: Error | string | null;
  variant?: 'inline' | 'card' | 'page' | 'toast' | 'banner';
  size?: 'sm' | 'md' | 'lg';
  title?: string;
  description?: string;
  showDetails?: boolean;
  showRetry?: boolean;
  onRetry?: () => void;
  onDismiss?: () => void;
  className?: string;
  icon?: React.ReactNode;
}

const errorIcons = {
  default: '⚠️',
  network: '🌐',
  server: '🔧',
  validation: '📝',
  auth: '🔒',
  notFound: '🔍',
  permission: '🚫'
};

const getErrorType = (error: Error | string): keyof typeof errorIcons => {
  const message = typeof error === 'string' ? error : error.message;
  const lowerMessage = message.toLowerCase();
  
  if (lowerMessage.includes('network') || lowerMessage.includes('fetch')) return 'network';
  if (lowerMessage.includes('server') || lowerMessage.includes('internal')) return 'server';
  if (lowerMessage.includes('validation') || lowerMessage.includes('invalid')) return 'validation';
  if (lowerMessage.includes('auth') || lowerMessage.includes('unauthorized')) return 'auth';
  if (lowerMessage.includes('not found') || lowerMessage.includes('404')) return 'notFound';
  if (lowerMessage.includes('permission') || lowerMessage.includes('forbidden')) return 'permission';
  
  return 'default';
};

const getErrorMessage = (error: Error | string | null): string => {
  if (!error) return 'An unknown error occurred';
  if (typeof error === 'string') return error;
  return error.message || 'An unexpected error occurred';
};

const getErrorTitle = (error: Error | string | null, customTitle?: string): string => {
  if (customTitle) return customTitle;
  
  const errorType = error ? getErrorType(error) : 'default';
  
  const titles = {
    default: 'Error',
    network: 'Connection Error',
    server: 'Server Error',
    validation: 'Validation Error',
    auth: 'Authentication Error',
    notFound: 'Not Found',
    permission: 'Permission Denied'
  };
  
  return titles[errorType];
};

export function UnifiedErrorDisplay({
  error,
  variant = 'card',
  size = 'md',
  title,
  description,
  showDetails = process.env.NODE_ENV === 'development',
  showRetry = true,
  onRetry,
  onDismiss,
  className = '',
  icon
}: ErrorDisplayProps) {
  if (!error) return null;

  const errorMessage = getErrorMessage(error);
  const errorTitle = getErrorTitle(error, title);
  const errorType = getErrorType(error);
  const errorIcon = icon || errorIcons[errorType];

  const baseClasses = 'flex flex-col';
  const sizeClasses = {
    sm: 'text-sm p-3 gap-2',
    md: 'text-base p-4 gap-3',
    lg: 'text-lg p-6 gap-4'
  };

  const variantClasses = {
    inline: 'bg-red-50 border border-red-200 rounded-md',
    card: 'bg-red-50 border border-red-200 rounded-lg shadow-sm',
    page: 'bg-white border border-red-200 rounded-lg shadow-lg min-h-[200px] justify-center items-center text-center',
    toast: 'bg-red-600 text-white rounded-lg shadow-lg',
    banner: 'bg-red-100 border-l-4 border-red-500 p-4'
  };

  const textColorClasses = {
    inline: 'text-red-800',
    card: 'text-red-800',
    page: 'text-gray-800',
    toast: 'text-white',
    banner: 'text-red-800'
  };

  const iconColorClasses = {
    inline: 'text-red-600',
    card: 'text-red-600',
    page: 'text-red-500',
    toast: 'text-red-200',
    banner: 'text-red-600'
  };

  return (
    <div className={`${baseClasses} ${sizeClasses[size]} ${variantClasses[variant]} ${className}`}>
      <div className="flex items-start gap-3">
        <div className={`flex-shrink-0 ${iconColorClasses[variant]}`}>
          {errorIcon}
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h3 className={`font-medium ${textColorClasses[variant]}`}>
                {errorTitle}
              </h3>
              
              <div className={`mt-1 ${textColorClasses[variant]} opacity-90`}>
                {description || errorMessage}
              </div>
              
              {showDetails && error instanceof Error && error.stack && (
                <details className="mt-2">
                  <summary className={`cursor-pointer text-xs ${textColorClasses[variant]} opacity-75`}>
                    Technical Details
                  </summary>
                  <pre className={`mt-1 text-xs ${textColorClasses[variant]} opacity-75 whitespace-pre-wrap`}>
                    {error.stack}
                  </pre>
                </details>
              )}
            </div>
            
            {onDismiss && (
              <button
                onClick={onDismiss}
                className={`ml-2 ${textColorClasses[variant]} opacity-50 hover:opacity-75`}
                aria-label="Dismiss error"
              >
                ✕
              </button>
            )}
          </div>
          
          {(showRetry && onRetry) && (
            <div className="mt-3 flex gap-2">
              <Button
                size="sm"
                variant={variant === 'toast' ? 'secondary' : 'primary'}
                onClick={onRetry}
              >
                Try Again
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Convenience components for common use cases
export function InlineError(props: Omit<ErrorDisplayProps, 'variant'>) {
  return <UnifiedErrorDisplay {...props} variant="inline" />;
}

export function ErrorCard(props: Omit<ErrorDisplayProps, 'variant'>) {
  return <UnifiedErrorDisplay {...props} variant="card" />;
}

export function ErrorPage(props: Omit<ErrorDisplayProps, 'variant'>) {
  return <UnifiedErrorDisplay {...props} variant="page" size="lg" />;
}

export function ErrorToast(props: Omit<ErrorDisplayProps, 'variant'>) {
  return <UnifiedErrorDisplay {...props} variant="toast" />;
}

export function ErrorBanner(props: Omit<ErrorDisplayProps, 'variant'>) {
  return <UnifiedErrorDisplay {...props} variant="banner" />;
}

export default UnifiedErrorDisplay;

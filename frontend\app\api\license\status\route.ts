/**
 * License Status API
 * 
 * Endpoint for checking current user's license status
 * GET /api/license/status - Get license information for current user
 */

import { NextRequest } from 'next/server';
import { authenticateRequest } from '@/lib/api/auth-middleware';
import { validateClientLicense, getLicenseStats } from '@/lib/services/license-service';
import { getClientByEmailDomain } from '@/lib/tenant/clients';
import {
  createSuccessResponse,
  createErrorResponse,
  ApiErrorCode,
  HttpStatus
} from '@/lib/api/response';

/**
 * Get license status for current user
 */
export async function GET(request: NextRequest) {
  // Authenticate user
  const authResult = await authenticateRequest(request, {
    requireAuth: true
  });

  if (!authResult.success) {
    return authResult.response;
  }

  const { email } = authResult.session;

  try {
    // Get client from user's email domain
    const emailDomain = email.split('@')[1];
    const clientLookup = await getClientByEmailDomain(emailDomain);
    
    if (!clientLookup.success || !clientLookup.client) {
      return createErrorResponse(
        'Client not found for user domain',
        ApiErrorCode.NOT_FOUND,
        HttpStatus.NOT_FOUND
      );
    }

    const clientId = parseInt(clientLookup.client.clientId);

    // Validate license
    const licenseValidation = await validateClientLicense(clientId);
    
    if (!licenseValidation.isValid) {
      return createSuccessResponse({
        hasLicense: false,
        isValid: false,
        reason: licenseValidation.reason,
        client: {
          id: clientId,
          name: clientLookup.client.clientName
        }
      });
    }

    // Get license statistics
    const licenseStats = await getLicenseStats(clientId);

    return createSuccessResponse({
      hasLicense: true,
      isValid: true,
      client: {
        id: clientId,
        name: clientLookup.client.clientName
      },
      license: {
        type: licenseValidation.license!.licenseType,
        features: licenseValidation.license!.features,
        limits: {
          maxUsers: licenseValidation.license!.maxUsers,
          maxTenants: licenseValidation.license!.maxTenants,
          currentUsers: licenseStats?.currentUsers || 0,
          currentTenants: licenseStats?.currentTenants || 0
        },
        expiration: {
          expiresAt: licenseValidation.license!.expiresAt,
          daysRemaining: licenseValidation.license!.expiresAt 
            ? Math.ceil((licenseValidation.license!.expiresAt.getTime() - Date.now()) / (1000 * 60 * 60 * 24))
            : null
        },
        usage: {
          totalEvents: licenseStats?.totalEvents || 0,
          loginEvents: licenseStats?.loginEvents || 0,
          lastActivity: licenseStats?.lastActivity,
          userUtilization: licenseStats?.userUtilization || 0,
          tenantUtilization: licenseStats?.tenantUtilization || 0
        },
        activation: {
          activatedAt: licenseValidation.license!.activatedAt,
          lastUsage: licenseValidation.license!.lastUsage
        }
      }
    });

  } catch (error) {
    console.error('License status check error:', error);
    return createErrorResponse(
      'Failed to check license status',
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
}

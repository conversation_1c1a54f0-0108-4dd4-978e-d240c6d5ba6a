/**
 * Renewal Timeline Component
 * 
 * Displays a chronological timeline of renewal events and activities
 */

'use client'

import React, { useMemo, useState } from 'react'
import { BaseComponentProps } from '@/lib/types'
import { Renewal } from '@/lib/types'

interface TimelineEvent {
  id: string
  type: 'created' | 'renewed' | 'expired' | 'updated' | 'alert'
  title: string
  description: string
  date: Date
  renewal: Renewal
  icon: string
  color: string
}

interface RenewalTimelineProps extends BaseComponentProps {
  renewals: Renewal[]
  isLoading?: boolean
  maxEvents?: number
}

function TimelineEventItem({ event }: { event: TimelineEvent }) {
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getRelativeTime = (date: Date) => {
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
    
    if (diffDays === 0) return 'Today'
    if (diffDays === 1) return 'Yesterday'
    if (diffDays < 7) return `${diffDays} days ago`
    if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`
    if (diffDays < 365) return `${Math.floor(diffDays / 30)} months ago`
    return `${Math.floor(diffDays / 365)} years ago`
  }

  return (
    <div className="flex items-start space-x-3 pb-6">
      {/* Timeline dot */}
      <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center text-white text-sm ${event.color}`}>
        {event.icon}
      </div>
      
      {/* Timeline content */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-medium text-gray-900">{event.title}</h3>
          <span className="text-xs text-gray-500">{getRelativeTime(event.date)}</span>
        </div>
        <p className="text-sm text-gray-600 mt-1">{event.description}</p>
        <div className="flex items-center space-x-4 mt-2">
          <span className="text-xs text-gray-500">
            {formatDate(event.date)}
          </span>
          <span className="text-xs text-blue-600 hover:text-blue-800 cursor-pointer">
            View Renewal
          </span>
        </div>
      </div>
    </div>
  )
}

function LoadingSkeleton() {
  return (
    <div className="space-y-6">
      {[...Array(5)].map((_, index) => (
        <div key={index} className="flex items-start space-x-3 animate-pulse">
          <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
          <div className="flex-1">
            <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-full mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
      ))}
    </div>
  )
}

function EmptyState() {
  return (
    <div className="text-center py-8">
      <div className="text-4xl mb-4">📅</div>
      <h3 className="text-lg font-medium mb-2">No timeline events</h3>
      <p className="text-gray-600">
        Timeline events will appear here as renewals are created and updated.
      </p>
    </div>
  )
}

export default function RenewalTimeline({
  renewals,
  isLoading = false,
  maxEvents = 20,
  className = '',
  'data-testid': testId
}: RenewalTimelineProps) {
  const [currentMonth, setCurrentMonth] = useState(new Date())

  // Get renewals for the current month view
  const monthlyRenewals = useMemo(() => {
    const year = currentMonth.getFullYear()
    const month = currentMonth.getMonth()

    return renewals.filter(renewal => {
      if (!renewal.start_date) return false
      const start_date = new Date(renewal.start_date)
      return start_date.getFullYear() === year && start_date.getMonth() === month
    }).sort((a, b) => {
      const dateA = new Date(a.start_date || 0)
      const dateB = new Date(b.start_date || 0)
      return dateA.getTime() - dateB.getTime()
    })
  }, [renewals, currentMonth])

  const formatMonthYear = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long'
    })
  }

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentMonth(prev => {
      const newDate = new Date(prev)
      if (direction === 'prev') {
        newDate.setMonth(prev.getMonth() - 1)
      } else {
        newDate.setMonth(prev.getMonth() + 1)
      }
      return newDate
    })
  }

  return (
    <div
      className={`${className}`}
      data-testid={testId}
    >
      <div className="p-4">
        {/* Header with month navigation - matching reference exactly */}
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-base font-medium text-gray-900">Renewal Timeline</h3>
            <p className="text-sm text-gray-500">Upcoming renewals for this month and next month</p>
          </div>
          <div className="flex items-center space-x-1">
            <button
              onClick={() => navigateMonth('prev')}
              className="p-1 hover:bg-gray-100 rounded text-gray-400"
            >
              ‹
            </button>
            <span className="text-sm text-gray-900 px-2">
              {formatMonthYear(currentMonth)}
            </span>
            <button
              onClick={() => navigateMonth('next')}
              className="p-1 hover:bg-gray-100 rounded text-gray-400"
            >
              ›
            </button>
          </div>
        </div>

        {/* Current month section header */}
        <div className="mb-3">
          <h4 className="text-sm font-medium text-gray-900">
            {formatMonthYear(currentMonth)}
          </h4>
        </div>

        {/* Monthly renewals or empty state */}
        {isLoading ? (
          <LoadingSkeleton />
        ) : monthlyRenewals.length > 0 ? (
          <div className="space-y-1">
            {monthlyRenewals.map((renewal) => (
              <div key={renewal.id} className="flex items-center justify-between py-2 px-3 hover:bg-gray-50 rounded">
                <div className="flex-1">
                  <p className="text-sm text-gray-900">{renewal.name}</p>
                  <p className="text-xs text-gray-500">{renewal.vendor}</p>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-900">{renewal.start_date}</p>
                  <p className="text-xs text-gray-500">${renewal.cost?.toLocaleString()} {renewal.currency}</p>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <div className="text-3xl mb-3 text-gray-300">📅</div>
            <h4 className="text-sm font-medium text-gray-900 mb-1">No renewals scheduled</h4>
            <p className="text-xs text-gray-500">
              No renewals scheduled for this month
            </p>
          </div>
        )}
      </div>
    </div>
  )
}

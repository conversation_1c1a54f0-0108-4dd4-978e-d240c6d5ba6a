/**
 * Notification Settings Page
 * 
 * Allows users to configure their notification preferences
 */

'use client'

import React, { useState, useEffect } from 'react'
import { PageHeader } from '@/components/ui/PageHeader'
import { Form } from '@/components/ui/Form'
import { Button } from '@/components/ui/Button'
import { LoadingPage } from '@/components/common/LoadingStates'
import { ErrorBoundary } from '@/components/common/ErrorBoundary'
import { useData } from '@/lib/hooks/useData'
import { ALERTS } from '@/lib/constants/app-constants'

interface NotificationSettings {
  email_notifications: boolean
  renewal_reminders: boolean
  expiration_alerts: boolean
  weekly_reports: boolean
  reminder_days_before: number
  notification_frequency: 'immediate' | 'daily' | 'weekly'
}

export default function NotificationSettingsPage() {
  const [settings, setSettings] = useState<NotificationSettings>({
    email_notifications: true,
    renewal_reminders: true,
    expiration_alerts: true,
    weekly_reports: false,
    reminder_days_before: ALERTS.DEFAULT_DAYS_BEFORE,
    notification_frequency: 'immediate'
  })
  const [saving, setSaving] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null)

  const { data, loading, error } = useData<NotificationSettings>('/api/notifications/settings')

  useEffect(() => {
    if (data) {
      setSettings(data)
    }
  }, [data])

  const handleSave = async () => {
    setSaving(true)
    setMessage(null)

    try {
      const response = await fetch('/api/notifications/settings', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings),
      })

      if (response.ok) {
        setMessage({ type: 'success', text: 'Settings saved successfully!' })
      } else {
        throw new Error('Failed to save settings')
      }
    } catch (error) {
      setMessage({ type: 'error', text: 'Failed to save settings. Please try again.' })
    } finally {
      setSaving(false)
    }
  }

  const handleChange = (field: keyof NotificationSettings, value: any) => {
    setSettings(prev => ({ ...prev, [field]: value }))
  }

  if (loading) {
    return <LoadingPage title="Loading notification settings..." />
  }

  if (error) {
    return (
      <div className="p-6">
        <PageHeader 
          title="Notification Settings"
          subtitle="Configure your notification preferences"
        />
        <div className="mt-6 text-center">
          <p className="text-red-600 mb-4">Failed to load notification settings</p>
        </div>
      </div>
    )
  }

  return (
    <ErrorBoundary level="page">
      <div className="space-y-6">
        <PageHeader 
          title="Notification Settings"
          subtitle="Configure your notification preferences"
        />

        <div className="bg-white rounded-lg shadow p-6">
          <Form.Root onSubmit={(e) => { e.preventDefault(); handleSave(); }}>
            <div className="space-y-6">
              {/* Email Notifications */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Email Notifications</h3>
                <div className="space-y-4">
                  <Form.Field>
                    <Form.Checkbox
                      checked={settings.email_notifications}
                      onChange={(e) => handleChange('email_notifications', e.target.checked)}
                    />
                    <Form.Label>Enable email notifications</Form.Label>
                  </Form.Field>

                  <Form.Field>
                    <Form.Checkbox
                      checked={settings.renewal_reminders}
                      onChange={(e) => handleChange('renewal_reminders', e.target.checked)}
                      disabled={!settings.email_notifications}
                    />
                    <Form.Label>Renewal reminders</Form.Label>
                  </Form.Field>

                  <Form.Field>
                    <Form.Checkbox
                      checked={settings.expiration_alerts}
                      onChange={(e) => handleChange('expiration_alerts', e.target.checked)}
                      disabled={!settings.email_notifications}
                    />
                    <Form.Label>Expiration alerts</Form.Label>
                  </Form.Field>

                  <Form.Field>
                    <Form.Checkbox
                      checked={settings.weekly_reports}
                      onChange={(e) => handleChange('weekly_reports', e.target.checked)}
                      disabled={!settings.email_notifications}
                    />
                    <Form.Label>Weekly summary reports</Form.Label>
                  </Form.Field>
                </div>
              </div>

              {/* Reminder Settings */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Reminder Settings</h3>
                <div className="space-y-4">
                  <Form.Field>
                    <Form.Label>Days before renewal to send reminder</Form.Label>
                    <Form.Select
                      value={settings.reminder_days_before.toString()}
                      onChange={(e) => handleChange('reminder_days_before', parseInt(e.target.value))}
                      disabled={!settings.email_notifications || !settings.renewal_reminders}
                    >
                      <option value="7">7 days</option>
                      <option value="14">14 days</option>
                      <option value="30">30 days</option>
                      <option value="60">60 days</option>
                      <option value="90">90 days</option>
                    </Form.Select>
                  </Form.Field>

                  <Form.Field>
                    <Form.Label>Notification frequency</Form.Label>
                    <Form.Select
                      value={settings.notification_frequency}
                      onChange={(e) => handleChange('notification_frequency', e.target.value)}
                      disabled={!settings.email_notifications}
                    >
                      <option value="immediate">Immediate</option>
                      <option value="daily">Daily digest</option>
                      <option value="weekly">Weekly digest</option>
                    </Form.Select>
                  </Form.Field>
                </div>
              </div>

              {/* Message */}
              {message && (
                <div className={`p-4 rounded-md ${
                  message.type === 'success' 
                    ? 'bg-green-50 text-green-800 border border-green-200' 
                    : 'bg-red-50 text-red-800 border border-red-200'
                }`}>
                  {message.text}
                </div>
              )}

              {/* Actions */}
              <Form.Actions>
                <Button
                  type="submit"
                  variant="primary"
                  isLoading={saving}
                  disabled={saving}
                >
                  {saving ? 'Saving...' : 'Save Settings'}
                </Button>
              </Form.Actions>
            </div>
          </Form.Root>
        </div>
      </div>
    </ErrorBoundary>
  )
}

/**
 * Renewal Status Card Component
 * 
 * Displays renewal status information and timeline
 */

'use client'

import React from 'react'
import { Renewal } from '@/lib/hooks'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'

interface RenewalStatusCardProps {
  renewal: Renewal
}

export default function RenewalStatusCard({ renewal }: RenewalStatusCardProps) {
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'bg-green-100 text-green-800'
      case 'expired':
        return 'bg-red-100 text-red-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'cancelled':
        return 'bg-gray-100 text-gray-800'
      default:
        return 'bg-blue-100 text-blue-800'
    }
  }

  const getDaysUntilRenewal = () => {
    if (!renewal.start_date) return null
    
    const start_date = new Date(renewal.start_date)
    const today = new Date()
    const diffTime = start_date.getTime() - today.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    return diffDays
  }

  const formatRenewalTimeline = () => {
    const daysUntil = getDaysUntilRenewal()
    
    if (daysUntil === null) return 'Date not set'
    
    if (daysUntil < 0) {
      return `Expired ${Math.abs(daysUntil)} days ago`
    } else if (daysUntil === 0) {
      return 'Due today'
    } else if (daysUntil <= 30) {
      return `Due in ${daysUntil} days`
    } else {
      return `Due in ${Math.ceil(daysUntil / 30)} months`
    }
  }

  const getTimelineColor = () => {
    const daysUntil = getDaysUntilRenewal()
    
    if (daysUntil === null) return 'text-gray-600'
    
    if (daysUntil < 0) {
      return 'text-red-600'
    } else if (daysUntil <= 30) {
      return 'text-orange-600'
    } else {
      return 'text-green-600'
    }
  }

  return (
    <Card className="renewal-status-card">
      <CardHeader>
        <CardTitle>Renewal Status</CardTitle>
      </CardHeader>
      <CardContent>
      
      <div className="space-y-6">
        {/* Current Status */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Current Status
          </label>
          <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(renewal.status)}`}>
            {renewal.status}
          </span>
        </div>

        {/* Renewal Timeline */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Timeline
          </label>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Current Start Date</span>
              <span className="text-sm font-medium text-gray-900">
                {renewal.start_date ? new Date(renewal.start_date).toLocaleDateString() : 'Not set'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Time Remaining</span>
              <span className={`text-sm font-medium ${getTimelineColor()}`}>
                {formatRenewalTimeline()}
              </span>
            </div>
          </div>
        </div>

        {/* Next Start Date (if processed) */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            New Start Date
          </label>
          <p className="text-sm text-gray-600">
            If renewed
          </p>
          <p className="text-sm font-medium text-gray-900">
            {renewal.start_date ? 
              new Date(new Date(renewal.start_date).setFullYear(new Date(renewal.start_date).getFullYear() + 1)).toLocaleDateString() :
              'Not calculated'
            }
          </p>
        </div>

        {/* Alerts Count */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Active Alerts
          </label>
          <div className="flex items-center">
            <span className="text-2xl font-bold text-gray-900">{renewal.alerts}</span>
            <span className="text-sm text-gray-600 ml-2">
              {renewal.alerts === 1 ? 'alert' : 'alerts'} configured
            </span>
          </div>
        </div>

        {/* Progress Indicator */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Renewal Progress
          </label>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className={`h-2 rounded-full transition-all duration-300 ${
                renewal.status.toLowerCase() === 'active' ? 'bg-green-500' :
                renewal.status.toLowerCase() === 'expired' ? 'bg-red-500' :
                renewal.status.toLowerCase() === 'pending' ? 'bg-yellow-500' :
                'bg-gray-500'
              }`}
              style={{ 
                width: renewal.status.toLowerCase() === 'active' ? '100%' :
                       renewal.status.toLowerCase() === 'expired' ? '0%' :
                       renewal.status.toLowerCase() === 'pending' ? '50%' :
                       '25%'
              }}
            />
          </div>
          <div className="flex justify-between text-xs text-gray-500 mt-1">
            <span>Licensed</span>
            <span>Due</span>
            <span>Renewed</span>
          </div>
        </div>
      </div>
      </CardContent>
    </Card>
  )
}

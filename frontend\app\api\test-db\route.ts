/**
 * Test Database Connection API
 * Simple endpoint to test if database connection is working
 */

import { NextRequest } from 'next/server'
import { createSuccessResponse, createErrorResponse, ApiErrorCode, HttpStatus } from '@/lib/api/response'
import { executeQuery } from '@/lib/database'

/**
 * GET /api/test-db
 * Test database connection
 */
export async function GET(request: NextRequest) {
  try {
    console.log('🔍 [TEST-DB] Testing database connection...')

    // Simple test query
    const result = await executeQuery('SELECT 1 as test, NOW() as current_time')

    if (!result.success) {
      console.error('❌ [TEST-DB] Database query failed:', result.error)
      return createErrorResponse(
        'Database connection failed',
        ApiErrorCode.INTERNAL_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR,
        result.error
      )
    }

    console.log('✅ [TEST-DB] Database connection successful:', result.data)

    return createSuccessResponse({
      message: 'Database connection successful',
      data: result.data,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('❌ [TEST-DB] Error testing database:', error)
    return createErrorResponse(
      'Database test failed',
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR,
      error instanceof Error ? error.message : 'Unknown error'
    )
  }
}

/**
 * Edit Renewal Modal Component
 *
 * Uses the reusable RenewalModal component in edit mode.
 * Provides a consistent interface for editing existing renewals.
 */

'use client'

import React from 'react'
import { RenewalModal, RenewalFormData, AlertFormData } from './AddRenewalModal'
import { BaseComponentProps } from '@/lib/types'
import { Renewal } from '@/lib/types'

interface EditRenewalModalProps extends BaseComponentProps {
  isOpen: boolean
  renewal: Renewal | null
  onClose: () => void
  onSave?: (renewalData: RenewalFormData, alertData: AlertFormData[]) => Promise<void>
}

const EditRenewalModal: React.FC<EditRenewalModalProps> = ({
  isOpen,
  renewal,
  onClose,
  onSave,
  className = '',
  'data-testid': testId
}) => {
  // Convert renewal data to RenewalFormData format
  const convertRenewalToFormData = (renewal: Renewal): Partial<RenewalFormData> => {
    return {
      renewalName: renewal.name || '',
      vendorId: '', // Would need vendor ID lookup
      vendorName: renewal.vendor || '',
      renewalTypeId: null, // Would need type ID lookup
      start_date: renewal.start_date || '',
      department: renewal.department || '',
      purchaseTypeId: null, // Would need purchase type ID lookup
      assignedUsers: renewal.associated_emails ? [renewal.associated_emails] : [],
      reseller: renewal.reseller || '',
      currencyId: renewal.currency || '',
      costCode: '', // Not available in Renewal interface
      description: renewal.description || '',
      notes: [], // Not available in Renewal interface
      renewalItems: [], // Would need to be populated from renewal items
      totalCost: renewal.cost || 0
    }
  }

  const handleSubmit = async (renewalData: RenewalFormData, alertData: AlertFormData[]) => {
    if (onSave) {
      await onSave(renewalData, alertData)
    }
  }

  if (!renewal) {
    return null
  }

  return (
    <RenewalModal
      isOpen={isOpen}
      onClose={onClose}
      onSubmit={handleSubmit}
      mode="edit"
      initialData={convertRenewalToFormData(renewal)}
      title="Edit Renewal"
      className={className}
      data-testid={testId}
    />
  )
}

export default EditRenewalModal

/**
 * Background Job Service for Synchronization
 * 
 * Implements async processing for sync operations with:
 * 1. Job queue management
 * 2. Retry logic with exponential backoff
 * 3. Job status tracking
 * 4. Error handling and recovery
 * 5. Scheduled sync operations
 */

import { Pool, PoolClient } from 'pg'
import { Logger } from '../Logger'
import { SyncEngine } from './SyncEngine'

export interface SyncJob {
  id: string
  tenantId: string
  jobType: 'vendor_sync' | 'product_sync' | 'version_sync' | 'full_sync'
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'retrying'
  priority: number
  attempts: number
  maxAttempts: number
  scheduledAt: Date
  startedAt?: Date
  completedAt?: Date
  errorMessage?: string
  jobData?: any
  createdAt: Date
  updatedAt: Date
}

export interface JobResult {
  success: boolean
  message: string
  data?: any
  shouldRetry?: boolean
}

export class BackgroundJobService {
  private db: Pool
  private logger: Logger
  private syncEngine: SyncEngine
  private isProcessing: boolean = false
  private processingInterval?: NodeJS.Timeout

  constructor(db: Pool, logger: Logger) {
    this.db = db
    this.logger = logger
    this.syncEngine = new SyncEngine(db, logger)
  }

  /**
   * Start the background job processor
   */
  start(intervalMs: number = 30000): void {
    if (this.isProcessing) {
      this.logger.warn('Background job processor already running')
      return
    }

    this.isProcessing = true
    this.logger.info('Starting background job processor', { intervalMs })

    this.processingInterval = setInterval(async () => {
      try {
        await this.processJobs()
      } catch (error) {
        this.logger.error('Error in job processing cycle', { error })
      }
    }, intervalMs)
  }

  /**
   * Stop the background job processor
   */
  stop(): void {
    if (!this.isProcessing) {
      return
    }

    this.isProcessing = false
    if (this.processingInterval) {
      clearInterval(this.processingInterval)
      this.processingInterval = undefined
    }

    this.logger.info('Background job processor stopped')
  }

  /**
   * Queue a sync job
   */
  async queueSyncJob(
    tenantId: string,
    jobType: SyncJob['jobType'],
    options: {
      priority?: number
      maxAttempts?: number
      scheduledAt?: Date
      jobData?: any
    } = {}
  ): Promise<string> {
    const {
      priority = 5,
      maxAttempts = 3,
      scheduledAt = new Date(),
      jobData = {}
    } = options

    const client = await this.db.connect()

    try {
      const result = await client.query(`
        INSERT INTO metadata.sync_jobs (
          tenant_id, job_type, status, priority, attempts, max_attempts,
          scheduled_at, job_data, created_on, changed_on
        ) VALUES ($1, $2, 'pending', $3, 0, $4, $5, $6, NOW(), NOW())
        RETURNING id
      `, [tenantId, jobType, priority, maxAttempts, scheduledAt, JSON.stringify(jobData)])

      const jobId = result.rows[0].id

      this.logger.info(`Queued sync job`, {
        jobId,
        tenantId,
        jobType,
        priority,
        scheduledAt
      })

      return jobId

    } finally {
      client.release()
    }
  }

  /**
   * Get job status
   */
  async getJobStatus(jobId: string): Promise<SyncJob | null> {
    const client = await this.db.connect()

    try {
      const result = await client.query(`
        SELECT * FROM metadata.sync_jobs WHERE id = $1
      `, [jobId])

      return result.rows[0] || null

    } finally {
      client.release()
    }
  }

  /**
   * Get jobs for a tenant
   */
  async getTenantJobs(tenantId: string, options: {
    status?: string
    limit?: number
    offset?: number
  } = {}): Promise<{
    jobs: SyncJob[]
    total: number
  }> {
    const client = await this.db.connect()

    try {
      const { status, limit = 50, offset = 0 } = options

      let whereClause = 'WHERE tenant_id = $1'
      const params: any[] = [tenantId]
      let paramIndex = 2

      if (status) {
        whereClause += ` AND status = $${paramIndex}`
        params.push(status)
        paramIndex++
      }

      const result = await client.query(`
        SELECT * FROM metadata.sync_jobs
        ${whereClause}
        ORDER BY created_on DESC
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `, [...params, limit, offset])

      const countResult = await client.query(`
        SELECT COUNT(*) as total FROM metadata.sync_jobs ${whereClause}
      `, params)

      return {
        jobs: result.rows,
        total: parseInt(countResult.rows[0].total, 10)
      }

    } finally {
      client.release()
    }
  }

  /**
   * Process pending jobs
   */
  private async processJobs(): Promise<void> {
    const client = await this.db.connect()

    try {
      // Get next job to process
      const result = await client.query(`
        SELECT * FROM metadata.sync_jobs
        WHERE status IN ('pending', 'retrying')
          AND scheduled_at <= NOW()
        ORDER BY priority DESC, created_on ASC
        LIMIT 1
        FOR UPDATE SKIP LOCKED
      `)

      if (result.rows.length === 0) {
        return // No jobs to process
      }

      const job: SyncJob = result.rows[0]

      // Mark job as processing
      await client.query(`
        UPDATE metadata.sync_jobs
        SET status = 'processing', started_at = NOW(), changed_on = NOW()
        WHERE id = $1
      `, [job.id])

      this.logger.info(`Processing job ${job.id}`, {
        jobId: job.id,
        tenantId: job.tenantId,
        jobType: job.jobType,
        attempt: job.attempts + 1
      })

      // Process the job
      const jobResult = await this.executeJob(job)

      // Update job status based on result
      if (jobResult.success) {
        await client.query(`
          UPDATE metadata.sync_jobs
          SET status = 'completed',
              completed_at = NOW(),
              changed_on = NOW()
          WHERE id = $1
        `, [job.id])

        this.logger.info(`Job ${job.id} completed successfully`)
      } else {
        await this.handleJobFailure(client, job, jobResult)
      }

    } catch (error) {
      this.logger.error('Error processing job', { error })
    } finally {
      client.release()
    }
  }

  /**
   * Execute a specific job
   */
  private async executeJob(job: SyncJob): Promise<JobResult> {
    try {
      switch (job.jobType) {
        case 'vendor_sync':
          const vendorResult = await this.syncEngine.syncVendors(job.tenantId, job.jobData || {})
          return {
            success: vendorResult.success,
            message: `Vendor sync completed: ${vendorResult.matched} matched, ${vendorResult.conflicts} conflicts`,
            data: vendorResult
          }

        case 'product_sync':
          const productResult = await this.syncEngine.syncProducts(job.tenantId, job.jobData || {})
          return {
            success: productResult.success,
            message: `Product sync completed: ${productResult.matched} matched, ${productResult.conflicts} conflicts`,
            data: productResult
          }

        case 'version_sync':
          const versionResult = await this.syncEngine.syncProductVersions(job.tenantId, job.jobData || {})
          return {
            success: versionResult.success,
            message: `Version sync completed: ${versionResult.matched} matched, ${versionResult.conflicts} conflicts`,
            data: versionResult
          }

        case 'full_sync':
          const fullResult = await this.syncEngine.syncAll(job.tenantId, job.jobData || {})
          return {
            success: true,
            message: `Full sync completed in ${fullResult.totalTimeMs}ms`,
            data: fullResult
          }

        default:
          return {
            success: false,
            message: `Unknown job type: ${job.jobType}`,
            shouldRetry: false
          }
      }

    } catch (error) {
      this.logger.error(`Job execution failed`, { jobId: job.id, error })
      return {
        success: false,
        message: error instanceof Error ? error.message : 'Job execution failed',
        shouldRetry: true
      }
    }
  }

  /**
   * Handle job failure with retry logic
   */
  private async handleJobFailure(
    client: PoolClient,
    job: SyncJob,
    result: JobResult
  ): Promise<void> {
    const newAttempts = job.attempts + 1

    if (newAttempts >= job.maxAttempts || !result.shouldRetry) {
      // Max attempts reached or shouldn't retry
      await client.query(`
        UPDATE metadata.sync_jobs
        SET status = 'failed',
            attempts = $2,
            error_message = $3,
            changed_on = NOW()
        WHERE id = $1
      `, [job.id, newAttempts, result.message])

      this.logger.error(`Job ${job.id} failed permanently`, {
        jobId: job.id,
        attempts: newAttempts,
        error: result.message
      })
    } else {
      // Schedule retry with exponential backoff
      const retryDelayMs = Math.min(1000 * Math.pow(2, newAttempts), 300000) // Max 5 minutes
      const retryAt = new Date(Date.now() + retryDelayMs)

      await client.query(`
        UPDATE metadata.sync_jobs
        SET status = 'retrying',
            attempts = $2,
            scheduled_at = $3,
            error_message = $4,
            changed_on = NOW()
        WHERE id = $1
      `, [job.id, newAttempts, retryAt, result.message])

      this.logger.warn(`Job ${job.id} scheduled for retry`, {
        jobId: job.id,
        attempt: newAttempts,
        retryAt,
        error: result.message
      })
    }
  }

  /**
   * Schedule recurring sync jobs for all tenants
   */
  async scheduleRecurringSync(
    jobType: SyncJob['jobType'],
    intervalHours: number = 24
  ): Promise<void> {
    const client = await this.db.connect()

    try {
      // Get all active tenants
      const tenantsResult = await client.query(`
        SELECT DISTINCT tenant_id FROM metadata.sync_batches
      `)

      const scheduledAt = new Date(Date.now() + intervalHours * 60 * 60 * 1000)

      for (const tenant of tenantsResult.rows) {
        await this.queueSyncJob(tenant.tenant_id, jobType, {
          priority: 3, // Lower priority for scheduled jobs
          scheduledAt
        })
      }

      this.logger.info(`Scheduled recurring ${jobType} for ${tenantsResult.rows.length} tenants`, {
        jobType,
        intervalHours,
        scheduledAt
      })

    } finally {
      client.release()
    }
  }
}

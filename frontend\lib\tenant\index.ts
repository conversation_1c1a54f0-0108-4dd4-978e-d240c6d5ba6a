/**
 * Tenant Management Module
 * 
 * Centralized exports for all tenant-related functionality including
 * context resolution, database operations, middleware, security, and validation.
 */

// Core tenant context and types
export * from './context'

// Database operations with tenant isolation
export * from './database'

// Middleware for tenant resolution (selective exports to avoid conflicts)
export {
  withTenant,
  withAuthAndTenant,
  extractTenantContext,
  validateTenantAccess,
  requireTenant,
  type TenantSession
} from './middleware'

// Security and access control
export * from './security'

// Validation utilities
export * from './validation'

// Client management (selective exports to avoid conflicts)
export {
  getClientByDomain,
  getClientByEmailDomain,
  getTenantByDomain,
  getTenantById,
  getTenantByUserId,
  updateClient
} from './clients'

export type { ClientLookupResult } from './clients-api'

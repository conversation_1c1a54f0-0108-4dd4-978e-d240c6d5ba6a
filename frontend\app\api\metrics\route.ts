/**
 * Metrics Collection API Endpoint
 * 
 * Receives and processes metrics from the frontend
 */

import { createApiRoute } from '@/lib/api/route-factory'
import { z } from 'zod'
import { Metric, PerformanceMetric, UserMetric, SystemMetric } from '@/lib/monitoring/metrics'

const metricsSchema = z.object({
  sessionId: z.string(),
  timestamp: z.number(),
  metrics: z.array(z.any()),
  performanceMetrics: z.array(z.any()),
  userMetrics: z.array(z.any()),
  systemMetrics: z.array(z.any())
})

type MetricsPayload = z.infer<typeof metricsSchema>

// POST /api/metrics - Receive metrics from frontend
export const POST = createApiRoute({
  bodySchema: metricsSchema,
  handler: async (context) => {
    const { body: payload } = context

    if (!payload) {
      throw new Error('Invalid request data. Please try again.');
    }

    // Process different types of metrics
    await Promise.all([
      processMetrics(payload.metrics, payload.sessionId),
      processPerformanceMetrics(payload.performanceMetrics, payload.sessionId),
      processUserMetrics(payload.userMetrics, payload.sessionId),
      processSystemMetrics(payload.systemMetrics, payload.sessionId)
    ])

    const totalMetrics =
      payload.metrics.length +
      payload.performanceMetrics.length +
      payload.userMetrics.length +
      payload.systemMetrics.length

    return {
      processed: totalMetrics,
      sessionId: payload.sessionId,
      message: `Processed ${totalMetrics} metrics`
    }
  }
})

async function processMetrics(metrics: Metric[], sessionId: string): Promise<void> {
  if (metrics.length === 0) return
  
  try {
    // In development, log interesting metrics
    if (process.env.NODE_ENV === 'development') {
      const errorMetrics = metrics.filter(m => m.name.includes('error'))
      const performanceMetrics = metrics.filter(m => m.type === 'timer' && m.value > 1000)
      
      if (errorMetrics.length > 0) {
        console.warn('Error metrics:', errorMetrics)
      }
      
      if (performanceMetrics.length > 0) {
        console.warn('Slow operations:', performanceMetrics)
      }
    }
    
    // In production, send to metrics service
    if (process.env.NODE_ENV === 'production') {
      // Implementation note: Metrics would be sent to monitoring service
      // await sendToMetricsService(metrics, sessionId)
    }
    
    // Store critical metrics in database
    const criticalMetrics = metrics.filter(m => 
      m.name.includes('error') || 
      (m.type === 'timer' && m.value > 5000) ||
      (m.name.includes('memory') && m.value > 100 * 1024 * 1024) // 100MB
    )
    
    if (criticalMetrics.length > 0) {
      await storeCriticalMetrics(criticalMetrics, sessionId)
    }
    
  } catch (error) {
    console.error('Error processing metrics:', error)
  }
}

async function processPerformanceMetrics(metrics: PerformanceMetric[], sessionId: string): Promise<void> {
  if (metrics.length === 0) return
  
  try {
    // Identify slow operations
    const slowOperations = metrics.filter(m => m.duration > 2000) // 2 seconds
    
    if (slowOperations.length > 0) {
      // Store slow operations for analysis (logging disabled for cleaner console)
      await storeSlowOperations(slowOperations, sessionId)
    }
    
  } catch (error) {
    console.error('Error processing performance metrics:', error)
  }
}

async function processUserMetrics(metrics: UserMetric[], sessionId: string): Promise<void> {
  if (metrics.length === 0) return
  
  try {
    // Track user engagement
    const actions = metrics.map(m => m.action)
    const uniqueActions = new Set(actions)
    
    console.info(`User performed ${actions.length} actions (${uniqueActions.size} unique) in session ${sessionId}`)
    
    // Identify error-related user actions
    const errorActions = metrics.filter(m => m.action.includes('error'))
    if (errorActions.length > 0) {
      console.warn('User encountered errors:', errorActions)
      await storeUserErrors(errorActions, sessionId)
    }
    
  } catch (error) {
    console.error('Error processing user metrics:', error)
  }
}

async function processSystemMetrics(metrics: SystemMetric[], sessionId: string): Promise<void> {
  if (metrics.length === 0) return
  
  try {
    // Check for system resource issues
    const memoryMetrics = metrics.filter(m => m.system === 'memory')
    const networkMetrics = metrics.filter(m => m.system === 'network')
    
    // Alert on high memory usage
    const highMemoryUsage = memoryMetrics.filter(m => 
      m.name.includes('used') && m.value > 500 * 1024 * 1024 // 500MB
    )
    
    if (highMemoryUsage.length > 0) {
      console.warn('High memory usage detected:', highMemoryUsage)
    }
    
    // Alert on poor network conditions
    const poorNetwork = networkMetrics.filter(m => 
      (m.name.includes('rtt') && m.value > 1000) || // High latency
      (m.name.includes('downlink') && m.value < 1) // Slow connection
    )
    
    if (poorNetwork.length > 0) {
      console.warn('Poor network conditions detected:', poorNetwork)
    }
    
  } catch (error) {
    console.error('Error processing system metrics:', error)
  }
}

async function storeCriticalMetrics(metrics: Metric[], sessionId: string): Promise<void> {
  try {
    // Store critical metrics for alerting (logging disabled for cleaner console)
    // TODO: Implement actual storage to database or monitoring service
  } catch (error) {
    console.error('Failed to store critical metrics:', error)
  }
}

async function storeSlowOperations(operations: PerformanceMetric[], sessionId: string): Promise<void> {
  try {
    // Store slow operations for performance analysis (logging disabled for cleaner console)
    // TODO: Implement actual storage to database or monitoring service
  } catch (error) {
    console.error('Failed to store slow operations:', error)
  }
}

async function storeUserErrors(errors: UserMetric[], sessionId: string): Promise<void> {
  try {
    // Store user errors for UX analysis (logging disabled for cleaner console)
    // TODO: Implement actual storage to database or monitoring service
  } catch (error) {
    console.error('Failed to store user errors:', error)
  }
}

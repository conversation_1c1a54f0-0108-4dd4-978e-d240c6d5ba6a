/**
 * Renewal Details Step Component (Refactored with Design System)
 *
 * First step of the Add Renewal modal - collects renewal information.
 * Now uses the unified Form components for consistency.
 */

'use client'

import React, { useCallback, useEffect, useState, memo, useMemo } from 'react'
import { RenewalFormData, RenewalNote } from '@/components/modals/AddRenewalModal'
import { Form } from '@/components/ui/Form'
import { Button } from '@/components/ui/Button'
import { RenewalItemsManager, RenewalItem } from '@/components/ui/RenewalItemsManager'
// Note: Metadata services moved to server-side only
// Import types directly and use API endpoints for data
import type { Vendor, TenantUser } from '@/lib/types'

// Define MetadataOptions type locally
interface MetadataOptions {
  purchaseTypes: Array<{ id: number; name: string }>
  renewalTypes: Array<{ id: number; name: string }>
  currencies: Array<{ id: string; name: string; symbol?: string }>
  vendors: Array<{ id: number; name: string; display_name?: string }>
  products: Array<{ id: number; name: string; vendor_id: number }>
  versions: Array<{ id: number; name: string; product_id: number }>
  users: Array<{ id: number; name: string; email: string }>
  departments: Array<{ name: string }>
  resellers: Array<{ id: number; name: string }>
  tenantUsers: Array<{ id: number; name: string; email: string }>
}
import { formatCurrencyDisplay } from '@/lib/utils/format-utils'
import { TENANT_ENDPOINTS } from '@/lib/constants/api-endpoints'
import { useAuth } from '@/lib/hooks/useAuth'
import { useToast } from '@/components/ui/Toast'

interface RenewalDetailsStepProps {
  data: RenewalFormData
  onChange: (data: RenewalFormData) => void
}

const RenewalDetailsStep: React.FC<RenewalDetailsStepProps> = memo(({
  data,
  onChange
}) => {
  const { getDisplayName } = useAuth()
  const toast = useToast()
  const [metadataOptions, setMetadataOptions] = useState<MetadataOptions | null>(null)
  const [isLoadingMetadata, setIsLoadingMetadata] = useState(true)
  const [metadataError, setMetadataError] = useState<string | null>(null)
  const [isAddingVendor, setIsAddingVendor] = useState(false)
  const [isAddingDepartment, setIsAddingDepartment] = useState(false)
  const [isAddingReseller, setIsAddingReseller] = useState(false)
  const [newVendorName, setNewVendorName] = useState('')
  const [newDepartmentName, setNewDepartmentName] = useState('')
  const [newResellerName, setNewResellerName] = useState('')
  const [newNoteText, setNewNoteText] = useState('')
  const [noteValidationError, setNoteValidationError] = useState('')

  // Load metadata options on component mount
  useEffect(() => {
    const loadMetadata = async () => {
      try {
        setIsLoadingMetadata(true)
        setMetadataError(null)
        // Use API endpoint instead of direct service call
        const response = await fetch('/api/metadata')
        if (!response.ok) throw new Error('Failed to load metadata')
        const options = await response.json()
        console.log('🔍 Loaded metadata options:', {
          renewalTypes: options?.renewalTypes?.length || 0,
          vendors: options?.vendors?.length || 0,
          departments: options?.departments?.length || 0,
          currencies: options?.currencies?.length || 0,
          purchaseTypes: options?.purchaseTypes?.length || 0,
          resellers: options?.resellers?.length || 0,
          vendorDetails: options?.vendors,
          fullOptions: options
        })
        setMetadataOptions(options)
      } catch (error) {
        console.error('Error loading metadata options:', error)
        setMetadataError(error instanceof Error ? error.message : 'Failed to load dropdown options')
      } finally {
        setIsLoadingMetadata(false)
      }
    }

    loadMetadata()
  }, [])

  // Handle form field changes - optimized with useMemo for stable reference
  const handleChange = useCallback((field: keyof RenewalFormData, value: any) => {
    onChange({
      ...data,
      [field]: value
    })
  }, [data, onChange])

  // Handle email list changes
  const handleEmailsChange = useCallback((emailsString: string) => {
    const emails = emailsString.split(',').map(email => email.trim()).filter(email => email)
    handleChange('assignedUsers', emails)
  }, [handleChange])

  // Handle renewal items changes - memoized for performance
  const handleRenewalItemsChange = useCallback((items: RenewalItem[]) => {
    // Calculate total cost from all items
    const totalCost = items.reduce((sum, item) => sum + item.totalCost, 0);

    onChange({
      ...data,
      renewalItems: items,
      totalCost: totalCost
    })
  }, [data, onChange])

  // Memoized style objects to prevent re-renders
  const loadingStyle = useMemo(() => ({
    textAlign: 'center' as const,
    padding: '24px',
    color: 'var(--color-text-secondary)'
  }), [])

  const errorStyle = useMemo(() => ({
    padding: '16px',
    backgroundColor: 'var(--color-error-50)',
    border: '1px solid var(--color-error-200)',
    borderRadius: 'var(--border-radius-md)',
    marginBottom: '24px'
  }), [])

  const errorTextStyle = useMemo(() => ({
    color: 'var(--color-error-700)',
    margin: '0 0 8px 0'
  }), [])



  // Handle vendor creation
  const handleCreateVendor = useCallback(async () => {
    if (!newVendorName.trim()) return

    try {
      // Use API endpoint instead of direct service call
      const response = await fetch('/api/tenant-vendors', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: newVendorName.trim(),
          display_name: newVendorName.trim()
        })
      })

      if (!response.ok) throw new Error('Failed to create vendor')
      const newVendor = await response.json()

      // Refresh metadata to include new vendor
      const metadataResponse = await fetch('/api/metadata')
      if (!metadataResponse.ok) throw new Error('Failed to refresh metadata')
      const updatedOptions = await metadataResponse.json()
      setMetadataOptions(updatedOptions)

      // Select the new vendor
      handleChange('vendorId', newVendor.id)
      handleChange('vendorName', newVendor.display_name || newVendor.name)

      // Reset form
      setNewVendorName('')
      setIsAddingVendor(false)
    } catch (error) {
      console.error('Error creating vendor:', error)
      setMetadataError(error instanceof Error ? error.message : 'Failed to create vendor')
    }
  }, [newVendorName, handleChange])

  // Handle department creation
  const handleCreateDepartment = useCallback(async () => {
    if (!newDepartmentName.trim()) return

    try {
      const response = await fetch(TENANT_ENDPOINTS.DEPARTMENTS, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({ name: newDepartmentName.trim() })
      })

      if (!response.ok) {
        throw new Error('Failed to create department')
      }

      // Refresh metadata to include new department
      const metadataResponse = await fetch('/api/metadata')
      if (!metadataResponse.ok) throw new Error('Failed to refresh metadata')
      const updatedOptions = await metadataResponse.json()
      setMetadataOptions(updatedOptions)

      // Select the new department
      handleChange('department', newDepartmentName.trim())

      // Reset form
      setNewDepartmentName('')
      setIsAddingDepartment(false)
    } catch (error) {
      console.error('Error creating department:', error)
      setMetadataError(error instanceof Error ? error.message : 'Failed to create department')
    }
  }, [newDepartmentName, handleChange])

  // Handle reseller creation
  const handleCreateReseller = useCallback(async () => {
    if (!newResellerName.trim()) return

    try {
      console.log('Creating reseller with name:', newResellerName.trim());

      const response = await fetch(TENANT_ENDPOINTS.RESELLERS, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({ name: newResellerName.trim() })
      })

      console.log('Reseller creation response status:', response.status);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));
        console.error('Reseller creation failed:', errorData);
        throw new Error(errorData.message || `Failed to create reseller (${response.status})`);
      }

      const result = await response.json();
      console.log('Reseller created successfully:', result);

      // Refresh metadata to include new reseller
      const metadataResponse = await fetch('/api/metadata')
      if (!metadataResponse.ok) throw new Error('Failed to refresh metadata')
      const updatedOptions = await metadataResponse.json()
      setMetadataOptions(updatedOptions)

      // Select the new reseller
      handleChange('reseller', newResellerName.trim())

      // Reset form
      setNewResellerName('')
      setIsAddingReseller(false)

      toast.success('Reseller created successfully!');
    } catch (error) {
      console.error('Error creating reseller:', error)
      const errorMessage = error instanceof Error ? error.message : 'Failed to create reseller';
      setMetadataError(errorMessage);
      toast.error(errorMessage);
    }
  }, [newResellerName, handleChange, toast])

  // Helper function to determine if reseller should be shown
  const shouldShowReseller = useCallback(() => {
    const selectedPurchaseType = metadataOptions?.purchaseTypes.find((pt: any) => pt.id === data.purchaseTypeId);
    return selectedPurchaseType?.name === 'Through Reseller';
  }, [metadataOptions?.purchaseTypes, data.purchaseTypeId]);

  // Auto-close reseller form when purchase type changes away from "Through Reseller"
  useEffect(() => {
    if (!shouldShowReseller() && isAddingReseller) {
      setIsAddingReseller(false);
      setNewResellerName('');
    }
  }, [shouldShowReseller, isAddingReseller]);

  // Handle adding new note
  const handleAddNote = useCallback(() => {
    // Clear previous validation error
    setNoteValidationError('')

    // Validate note text
    if (!newNoteText.trim()) {
      setNoteValidationError('Note text is required')
      toast.error('Please enter note text before adding')
      return
    }

    const displayName = getDisplayName();
    console.log('🔍 Creating note with display name:', displayName);

    const newNote: RenewalNote = {
      id: `note_${Date.now()}`,
      text: newNoteText.trim(),
      createdBy: displayName,
      createdAt: new Date().toISOString()
    }

    console.log('🔍 Created note object:', newNote);

    const updatedNotes = [...data.notes, newNote]
    handleChange('notes', updatedNotes)
    setNewNoteText('')
  }, [newNoteText, data.notes, handleChange, getDisplayName, toast])

  // Handle removing a note
  const handleRemoveNote = useCallback((noteId: string) => {
    const updatedNotes = data.notes.filter(note => note.id !== noteId)
    handleChange('notes', updatedNotes)
  }, [data.notes, handleChange])

  // Validate required fields
  const isValid = data.renewalName &&
                  data.vendorId &&
                  data.renewalTypeId &&
                  data.start_date &&
                  data.department &&
                  data.purchaseTypeId &&
                  data.currencyId &&
                  data.renewalItems.length > 0;

  return (
    <div className="renewal-details-step">
      {/* Loading state */}
      {isLoadingMetadata && (
        <div style={loadingStyle}>
          <p>Loading form options...</p>
        </div>
      )}

      {/* Error state */}
      {metadataError && (
        <div style={errorStyle}>
          <p style={errorTextStyle}>
            Error loading form options. Some dropdowns may not work properly.
          </p>
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.location.reload()}
          >
            Refresh Page
          </Button>
        </div>
      )}

      {/* RENEWAL-LEVEL FIELDS (shown first) */}
      <div className="renewal-level-fields">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Renewal Details</h3>

        <Form.Grid columns={2}>
          {/* Renewal Type */}
          <Form.Field>
            <Form.Label htmlFor="renewalTypeId" required>
              Renewal Type
            </Form.Label>
            <Form.Select
              id="renewalTypeId"
              value={data.renewalTypeId || ''}
              onChange={(e) => handleChange('renewalTypeId', e.target.value ? parseInt(e.target.value) : null)}
              disabled={isLoadingMetadata}
              placeholder="Select renewal type..."
            >
              {metadataOptions?.renewalTypes?.map((type: any) => {
                console.log('🔍 Rendering renewal type option:', type);
                return (
                  <option key={type.id} value={type.id}>
                    {type.name}
                  </option>
                );
              })}
            </Form.Select>
          </Form.Field>

          {/* Renewal Date */}
          <Form.Field>
            <Form.Label htmlFor="start_date" required>
              Renewal Date
            </Form.Label>
            <Form.Input
              id="start_date"
              type="date"
              value={data.start_date}
              onChange={(e) => handleChange('start_date', e.target.value)}
            />
          </Form.Field>
        </Form.Grid>

        {/* Renewal Name */}
        <Form.Field>
          <Form.Label htmlFor="renewalName" required>
            Renewal Name
          </Form.Label>
          <Form.Input
            id="renewalName"
            type="text"
            placeholder="Enter renewal name..."
            value={data.renewalName}
            onChange={(e) => handleChange('renewalName', e.target.value)}
          />
          <Form.Help>
            A descriptive name for this renewal (e.g., "Office 365 Annual Subscription")
          </Form.Help>
        </Form.Field>

        {/* Description */}
        <Form.Field>
          <Form.Label htmlFor="description">
            Description
          </Form.Label>
          <Form.Textarea
            id="description"
            placeholder="Enter description..."
            value={data.description}
            onChange={(e) => handleChange('description', e.target.value)}
            rows={3}
          />
          <Form.Help>
            Provide additional details about this renewal
          </Form.Help>
        </Form.Field>

        {/* Vendor Selection with Add New Option */}
        <Form.Field>
          <Form.Label htmlFor="vendorId" required>
            Vendor
          </Form.Label>
          <div className="flex gap-2">
            <Form.Select
              id="vendorId"
              value={data.vendorId || ''}
              onChange={(e) => {
                handleChange('vendorId', e.target.value)
                // Clear vendorName - it will be retrieved from database when needed
                handleChange('vendorName', '')
              }}
              disabled={isLoadingMetadata}
              placeholder="Select vendor..."
              className="flex-1"
            >
              {metadataOptions?.vendors?.map((vendor: any) => (
                <option key={vendor.id} value={vendor.id}>
                  {vendor.display_name || vendor.name}
                </option>
              ))}
            </Form.Select>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => setIsAddingVendor(true)}
              disabled={isLoadingMetadata}
            >
              + Add New
            </Button>
          </div>
          <Form.Help>
            Select the vendor for this renewal. Products will be filtered based on this selection.
          </Form.Help>
        </Form.Field>

        {/* Add New Vendor Modal/Inline Form */}
        {isAddingVendor && (
          <div className="bg-gray-50 p-4 rounded-md border mb-4">
            <h4 className="font-medium mb-2">Add New Vendor</h4>
            <div className="flex gap-2">
              <Form.Input
                type="text"
                placeholder="Vendor name..."
                value={newVendorName}
                onChange={(e) => setNewVendorName(e.target.value)}
                className="flex-1"
              />
              <Button
                type="button"
                variant="primary"
                size="sm"
                onClick={handleCreateVendor}
                disabled={!newVendorName.trim()}
              >
                Add
              </Button>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => {
                  setIsAddingVendor(false)
                  setNewVendorName('')
                }}
              >
                Cancel
              </Button>
            </div>
          </div>
        )}

        {/* Department */}
        <Form.Field>
          <Form.Label htmlFor="department">
            Department
          </Form.Label>
          <div className="flex gap-2">
            <Form.Select
              id="department"
              value={data.department || ''}
              onChange={(e) => handleChange('department', e.target.value)}
              disabled={isLoadingMetadata}
              placeholder="Select department..."
              className="flex-1"
            >
              {metadataOptions?.departments?.map((dept: any) => (
                <option key={dept.name} value={dept.name}>
                  {dept.name}
                </option>
              ))}
            </Form.Select>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => setIsAddingDepartment(true)}
              disabled={isLoadingMetadata}
            >
              + Add New
            </Button>
          </div>
        </Form.Field>

        {/* Add New Department Modal/Inline Form */}
        {isAddingDepartment && (
          <div className="bg-gray-50 p-4 rounded-md border mb-4">
            <h4 className="font-medium mb-2">Add New Department</h4>
            <div className="flex gap-2">
              <Form.Input
                type="text"
                placeholder="Department name..."
                value={newDepartmentName}
                onChange={(e) => setNewDepartmentName(e.target.value)}
                className="flex-1"
              />
              <Button
                type="button"
                variant="primary"
                size="sm"
                onClick={handleCreateDepartment}
                disabled={!newDepartmentName.trim()}
              >
                Add
              </Button>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => {
                  setIsAddingDepartment(false)
                  setNewDepartmentName('')
                }}
              >
                Cancel
              </Button>
            </div>
          </div>
        )}



        {/* Purchase Type */}
        <Form.Field>
          <Form.Label htmlFor="purchaseTypeId" required>
            Purchase Type
          </Form.Label>
          <Form.Select
            id="purchaseTypeId"
            value={data.purchaseTypeId || ''}
            onChange={(e) => handleChange('purchaseTypeId', e.target.value ? parseInt(e.target.value) : null)}
            disabled={isLoadingMetadata}
            placeholder="Select purchase type..."
          >
            {metadataOptions?.purchaseTypes.map((type) => (
              <option key={type.id} value={type.id}>
                {type.name}
              </option>
            ))}
          </Form.Select>
        </Form.Field>

        {/* Reseller - Only show if "Through Reseller" is selected */}
        {shouldShowReseller() && (
          <Form.Field>
            <Form.Label htmlFor="reseller">
              Reseller
            </Form.Label>
            <div className="flex gap-2">
              <Form.Select
                id="reseller"
                value={data.reseller || ''}
                onChange={(e) => {
                  console.log('🔍 Reseller selection changed:', e.target.value);
                  handleChange('reseller', e.target.value)
                }}
                disabled={isLoadingMetadata}
                placeholder="Select reseller..."
                className="flex-1"
              >
                {metadataOptions?.resellers?.map((reseller) => {
                  console.log('🔍 Rendering reseller option:', reseller);
                  return (
                    <option key={reseller.id} value={reseller.name}>
                      {reseller.name}
                    </option>
                  );
                })}
              </Form.Select>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => setIsAddingReseller(true)}
                disabled={isLoadingMetadata}
              >
                + Add New
              </Button>
            </div>
          </Form.Field>
        )}

        {/* Add New Reseller Modal/Inline Form - Only show if reseller should be shown */}
        {shouldShowReseller() && isAddingReseller && (
          <div className="bg-gray-50 p-4 rounded-md border mb-4">
            <h4 className="font-medium mb-2">Add New Reseller</h4>
            <div className="flex gap-2">
              <Form.Input
                type="text"
                placeholder="Reseller name..."
                value={newResellerName}
                onChange={(e) => setNewResellerName(e.target.value)}
                className="flex-1"
              />
              <Button
                type="button"
                variant="primary"
                size="sm"
                onClick={handleCreateReseller}
                disabled={!newResellerName.trim()}
              >
                Add
              </Button>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => {
                  setIsAddingReseller(false)
                  setNewResellerName('')
                }}
              >
                Cancel
              </Button>
            </div>
          </div>
        )}

        {/* Assigned Users - Multi-select */}
        <Form.Field>
          <Form.Label htmlFor="assignedUsers">
            Assigned Users
          </Form.Label>
          <div className="space-y-2">
            {data.assignedUsers.map((userId, index) => (
              <div key={index} className="flex gap-2">
                <Form.Select
                  value={userId}
                  onChange={(e) => {
                    const newUsers = [...data.assignedUsers]
                    newUsers[index] = e.target.value
                    handleChange('assignedUsers', newUsers)
                  }}
                  disabled={isLoadingMetadata}
                  placeholder="Select user..."
                  className="flex-1"
                >
                  {metadataOptions?.tenantUsers.map((user) => (
                    <option key={user.id} value={user.id}>
                      {user.name} ({user.email})
                    </option>
                  ))}
                </Form.Select>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const newUsers = data.assignedUsers.filter((_, i) => i !== index)
                    handleChange('assignedUsers', newUsers)
                  }}
                >
                  Remove
                </Button>
              </div>
            ))}
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => {
                handleChange('assignedUsers', [...data.assignedUsers, ''])
              }}
            >
              + Add User
            </Button>
          </div>
          <Form.Help>
            Select users who should be assigned to this renewal
          </Form.Help>
        </Form.Field>



        <Form.Grid columns={1}>
        {/* Currency */}
        <Form.Field>
          <Form.Label htmlFor="currencyId" required>
            Currency
          </Form.Label>
          <Form.Select
            id="currencyId"
            value={data.currencyId || ''}
            onChange={(e) => handleChange('currencyId', e.target.value)}
            disabled={isLoadingMetadata}
            placeholder="Select currency..."
          >
            {metadataOptions?.currencies.map((currency) => (
              <option key={currency.id} value={currency.id}>
                {formatCurrencyDisplay(currency)}
              </option>
            ))}
          </Form.Select>
          </Form.Field>
        </Form.Grid>

        <Form.Grid columns={2}>
          {/* Cost Code */}
          <Form.Field>
            <Form.Label htmlFor="costCode">
              Cost Code
            </Form.Label>
            <Form.Input
              id="costCode"
              type="text"
              placeholder="Enter cost code..."
              value={data.costCode}
              onChange={(e) => handleChange('costCode', e.target.value)}
            />
          </Form.Field>
        </Form.Grid>

        {/* Notes - Multiple notes with user/timestamp */}
        <Form.Field>
          <Form.Label>
            Notes
          </Form.Label>

          {/* Existing notes */}
          {data.notes.length > 0 && (
            <div className="space-y-3 mb-4">
              {data.notes.map((note) => {
                console.log('🔍 Rendering note:', note);
                // Check if createdBy looks like a user ID (UUID pattern) and try to resolve it
                const displayName = note.createdBy;
                console.log('🔍 Note display name:', displayName);

                return (
                  <div key={note.id} className="bg-gray-50 p-3 rounded-md border">
                    <div className="flex justify-between items-start mb-2">
                      <div className="text-sm text-gray-600">
                        <span className="font-medium">{displayName}</span>
                        <span className="mx-2">•</span>
                        <span>{new Date(note.createdAt).toLocaleString()}</span>
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveNote(note.id!)}
                        className="text-red-600 hover:text-red-800"
                      >
                        Remove
                      </Button>
                    </div>
                    <p className="text-gray-900">{note.text}</p>
                  </div>
                );
              })}
            </div>
          )}

          {/* Add new note */}
          <div className="space-y-2">
            <Form.Textarea
              placeholder="Add a new note..."
              value={newNoteText}
              onChange={(e) => {
                setNewNoteText(e.target.value)
                // Clear validation error when user starts typing
                if (noteValidationError) {
                  setNoteValidationError('')
                }
              }}
              rows={3}
              error={noteValidationError}
            />
            {noteValidationError && (
              <Form.Error>{noteValidationError}</Form.Error>
            )}
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleAddNote}
            >
              Add Note
            </Button>
          </div>
        </Form.Field>
      </div>

      {/* ITEM-LEVEL FIELDS (shown after renewal fields) */}
      <div className="item-level-fields mt-8">
        <RenewalItemsManager
          items={data.renewalItems}
          onChange={handleRenewalItemsChange}
          disabled={isLoadingMetadata}
          vendorId={data.vendorId}
        />
      </div>

    </div>
  )
})

RenewalDetailsStep.displayName = 'RenewalDetailsStep'

export default RenewalDetailsStep

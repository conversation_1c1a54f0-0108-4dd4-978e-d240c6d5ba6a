/**
 * Universal Form Components
 * 
 * Consolidates all form implementations into reusable, type-safe components.
 * Replaces scattered FormInput, FormSelect, and other form components.
 */

'use client'

import React, { forwardRef, ReactNode } from 'react'
import { BaseComponentProps } from '@/lib/types'

// Base form field props
interface BaseFieldProps extends BaseComponentProps {
  label?: string
  error?: string
  required?: boolean
  disabled?: boolean
  helperText?: string
  size?: 'sm' | 'md' | 'lg'
}

// Form root container
export const FormRoot = forwardRef<HTMLFormElement, React.FormHTMLAttributes<HTMLFormElement>>(
  ({ className = '', ...props }, ref) => (
    <form ref={ref} className={`space-y-6 ${className}`} {...props} />
  )
)
FormRoot.displayName = 'FormRoot'

// Form section for grouping related fields
export const FormSection: React.FC<BaseComponentProps & {
  title?: string
  description?: string
}> = ({ title, description, children, className = '' }) => (
  <div className={`space-y-4 ${className}`}>
    {title && (
      <div className="border-b border-gray-200 pb-2">
        <h3 className="text-lg font-medium text-gray-900">{title}</h3>
        {description && (
          <p className="mt-1 text-sm text-gray-600">{description}</p>
        )}
      </div>
    )}
    <div className="space-y-4">
      {children}
    </div>
  </div>
)

// Form field wrapper
export const FormField: React.FC<BaseFieldProps> = ({
  label,
  error,
  required,
  helperText,
  children,
  className = ''
}) => (
  <div className={`space-y-1 ${className}`}>
    {label && (
      <label className="block text-sm font-medium text-gray-700">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>
    )}
    {children}
    {error && (
      <p className="text-sm text-red-600">{error}</p>
    )}
    {helperText && !error && (
      <p className="text-sm text-gray-500">{helperText}</p>
    )}
  </div>
)

// Input component
export const FormInput = forwardRef<HTMLInputElement, 
  React.InputHTMLAttributes<HTMLInputElement> & BaseFieldProps
>(({ label, error, required, helperText, size = 'md', className = '', ...props }, ref) => {
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-3 py-2 text-sm',
    lg: 'px-4 py-3 text-base'
  }

  const inputClasses = `
    block w-full rounded-md border-gray-300 shadow-sm
    focus:border-blue-500 focus:ring-blue-500
    disabled:bg-gray-50 disabled:text-gray-500
    ${error ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}
    ${sizeClasses[size]}
    ${className}
  `

  if (label || error || helperText) {
    return (
      <FormField label={label} error={error} required={required} helperText={helperText}>
        <input ref={ref} className={inputClasses} {...props} />
      </FormField>
    )
  }

  return <input ref={ref} className={inputClasses} {...props} />
})
FormInput.displayName = 'FormInput'

// Select component
export const FormSelect = forwardRef<HTMLSelectElement,
  React.SelectHTMLAttributes<HTMLSelectElement> & BaseFieldProps & {
    options: Array<{ value: string | number; label: string; disabled?: boolean }>
    placeholder?: string
  }
>(({ label, error, required, helperText, options, placeholder, size = 'md', className = '', ...props }, ref) => {
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-3 py-2 text-sm',
    lg: 'px-4 py-3 text-base'
  }

  const selectClasses = `
    block w-full rounded-md border-gray-300 shadow-sm
    focus:border-blue-500 focus:ring-blue-500
    disabled:bg-gray-50 disabled:text-gray-500
    ${error ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}
    ${sizeClasses[size]}
    ${className}
  `

  const selectElement = (
    <select ref={ref} className={selectClasses} {...props}>
      {placeholder && (
        <option value="" disabled>
          {placeholder}
        </option>
      )}
      {options.map((option) => (
        <option 
          key={option.value} 
          value={option.value}
          disabled={option.disabled}
        >
          {option.label}
        </option>
      ))}
    </select>
  )

  if (label || error || helperText) {
    return (
      <FormField label={label} error={error} required={required} helperText={helperText}>
        {selectElement}
      </FormField>
    )
  }

  return selectElement
})
FormSelect.displayName = 'FormSelect'

// Textarea component
export const FormTextarea = forwardRef<HTMLTextAreaElement,
  React.TextareaHTMLAttributes<HTMLTextAreaElement> & BaseFieldProps
>(({ label, error, required, helperText, size = 'md', className = '', ...props }, ref) => {
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-3 py-2 text-sm',
    lg: 'px-4 py-3 text-base'
  }

  const textareaClasses = `
    block w-full rounded-md border-gray-300 shadow-sm
    focus:border-blue-500 focus:ring-blue-500
    disabled:bg-gray-50 disabled:text-gray-500
    ${error ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}
    ${sizeClasses[size]}
    ${className}
  `

  const textareaElement = <textarea ref={ref} className={textareaClasses} {...props} />

  if (label || error || helperText) {
    return (
      <FormField label={label} error={error} required={required} helperText={helperText}>
        {textareaElement}
      </FormField>
    )
  }

  return textareaElement
})
FormTextarea.displayName = 'FormTextarea'

// Checkbox component
export const FormCheckbox = forwardRef<HTMLInputElement,
  Omit<React.InputHTMLAttributes<HTMLInputElement>, 'type'> & BaseFieldProps & {
    description?: string
  }
>(({ label, error, required, helperText, description, className = '', ...props }, ref) => {
  const checkboxElement = (
    <div className="flex items-start">
      <div className="flex items-center h-5">
        <input
          ref={ref}
          type="checkbox"
          className={`
            h-4 w-4 text-blue-600 border-gray-300 rounded
            focus:ring-blue-500 focus:ring-2
            ${error ? 'border-red-300' : ''}
            ${className}
          `}
          {...props}
        />
      </div>
      {(label || description) && (
        <div className="ml-3 text-sm">
          {label && (
            <label className="font-medium text-gray-700">
              {label}
              {required && <span className="text-red-500 ml-1">*</span>}
            </label>
          )}
          {description && (
            <p className="text-gray-500">{description}</p>
          )}
        </div>
      )}
    </div>
  )

  if (error || helperText) {
    return (
      <div className="space-y-1">
        {checkboxElement}
        {error && <p className="text-sm text-red-600">{error}</p>}
        {helperText && !error && <p className="text-sm text-gray-500">{helperText}</p>}
      </div>
    )
  }

  return checkboxElement
})
FormCheckbox.displayName = 'FormCheckbox'

// Radio group component
export const FormRadioGroup: React.FC<BaseFieldProps & {
  options: Array<{ value: string | number; label: string; description?: string; disabled?: boolean }>
  value?: string | number
  onChange?: (value: string | number) => void
  name: string
}> = ({ label, error, required, helperText, options, value, onChange, name, className = '' }) => {
  const radioGroup = (
    <div className="space-y-3">
      {options.map((option) => (
        <div key={option.value} className="flex items-start">
          <div className="flex items-center h-5">
            <input
              type="radio"
              name={name}
              value={option.value}
              checked={value === option.value}
              onChange={() => onChange?.(option.value)}
              disabled={option.disabled}
              className={`
                h-4 w-4 text-blue-600 border-gray-300
                focus:ring-blue-500 focus:ring-2
                ${error ? 'border-red-300' : ''}
              `}
            />
          </div>
          <div className="ml-3 text-sm">
            <label className="font-medium text-gray-700">
              {option.label}
            </label>
            {option.description && (
              <p className="text-gray-500">{option.description}</p>
            )}
          </div>
        </div>
      ))}
    </div>
  )

  return (
    <FormField label={label} error={error} required={required} helperText={helperText} className={className}>
      {radioGroup}
    </FormField>
  )
}

// Export compound component
export const Form = {
  Root: FormRoot,
  Section: FormSection,
  Field: FormField,
  Input: FormInput,
  Select: FormSelect,
  Textarea: FormTextarea,
  Checkbox: FormCheckbox,
  RadioGroup: FormRadioGroup
}

# Database Schema Standardization

## Overview

This document outlines the comprehensive database schema standardization that converts all tables from UUID to integer primary keys, following PostgreSQL best practices for optimal performance and maintainability.

## Key Changes

### ✅ **Primary Key Standardization**

- **Before**: Mixed UUID and integer primary keys across tables
- **After**: Consistent integer primary keys (SERIAL/BIGSERIAL) for all tables
- **Benefit**: Better performance, smaller indexes, easier joins

### ✅ **Foreign Key Consistency**

- **Before**: Foreign keys referencing mixed UUID/integer types
- **After**: All foreign keys reference integer primary keys
- **Benefit**: Referential integrity, better query performance

### ✅ **Naming Convention Standardization**

- **Before**: Mixed naming conventions (snake_case, PascalCase)
- **After**: Consistent PascalCase for tenant tables, snake_case for metadata
- **Benefit**: Clear distinction between schemas, consistent codebase

## Schema Structure

### **Metadata Schema** (Reference Data)

```sql
-- All reference tables use integer primary keys
metadata.PurchaseTypes (PurchaseTypeID SERIAL)
metadata.RenewalTypes (RenewalTypeID SERIAL)
metadata.Currencies (CurrencyID CHAR(3)) -- ISO standard
metadata.Departments (DepartmentID SERIAL)
metadata.global_vendors (VendorID SERIAL)
metadata.global_products (ProductID SERIAL)
metadata.global_product_versions (VersionID SERIAL)
```

### **Tenant Management Schema**

```sql
-- Tenant infrastructure tables
tenant_management.tenants (TenantID SERIAL)
tenant_management.clients (ClientID SERIAL)
tenant_management.domains (DomainID SERIAL)
```

### **Tenant Schema Tables** (per tenant)

```sql
-- Core business tables
tenant_renewals (renewal_id SERIAL)
tenant_alerts (alert_id SERIAL)
tenant_vendors (vendor_id SERIAL)
tenant_products (product_id SERIAL)
tenant_product_versions (version_id SERIAL)
```

### **Logging and Audit Tables**

```sql
-- Logging tables with integer record references
tenant_log (LogID BIGSERIAL, RecordID INTEGER)
audit_log (LogID BIGSERIAL)
```

## Migration Strategy

### **1. New Installations**

Use the complete setup script:

```bash
psql -d renewtrack -f database/setup_complete_database.sql
```

### **2. Existing Database Migration**

Follow the migration process:

```bash
# 1. Backup existing data
pg_dump renewtrack > backup_before_migration.sql

# 2. Run migration script
psql -d renewtrack -f database/migrations/migration_uuid_to_integer.sql

# 3. Verify data integrity
# 4. Update application code
```

### **3. New Tenant Setup**

For each new tenant:

```bash
# Edit tenant details in script, then run:
psql -d renewtrack -f database/setup_new_tenant.sql
```

## File Structure

### **Core Schema Files**

- `database/migrations/metadata_schema.sql` - Reference data tables
- `database/migrations/tenant_management_schema.sql` - Tenant infrastructure
- `database/migrations/tenant_schema_standardized.sql` - Tenant business tables

### **Setup Scripts**

- `database/setup_complete_database.sql` - Complete new installation
- `database/setup_new_tenant.sql` - New tenant creation
- `database/tenant-logging-setup.sql` - Tenant logging system

### **Migration Scripts**

- `database/migrations/migration_uuid_to_integer.sql` - UUID to integer migration
- `frontend/scripts/create-renewals-table.sql` - Updated Renewals table

## Key Benefits

### **Performance Improvements**

- **Smaller Indexes**: Integer keys are 4 bytes vs 16 bytes for UUIDs
- **Faster Joins**: Integer comparisons are faster than UUID comparisons
- **Better Caching**: More records fit in memory with smaller keys

### **Development Benefits**

- **Consistent Patterns**: All tables follow same primary key pattern
- **Easier Debugging**: Sequential IDs are human-readable
- **Better Tooling**: Most database tools work better with integer keys

### **Maintenance Benefits**

- **Simplified Relationships**: Clear foreign key relationships
- **Easier Migrations**: Integer sequences are easier to manage
- **Better Monitoring**: Easier to track record counts and growth

## Foreign Key Relationships

### **Metadata References**

```sql
-- Tenant tables reference metadata
tenant_renewals.renewal_type_id → metadata.global_renewal_types.renewal_type_id
tenant_renewals.department_id → metadata.global_departments.department_id
tenant_renewals.purchase_type_id → metadata.global_purchase_types.purchase_type_id
tenant_renewals.currency_id → metadata.global_currencies.currency_id
```

### **Tenant Relationships**

```sql
-- Within tenant schema
tenant_alerts.renewal_id → tenant_renewals.renewal_id
tenant_products.vendor_id → tenant_vendors.vendor_id
tenant_product_versions.product_id → tenant_products.product_id
```

### **Global Sync References**

```sql
-- Tenant to global master data
tenant_vendors.GlobalVendorID → metadata.global_vendors.VendorID
tenant_products.GlobalProductID → metadata.global_products.ProductID
tenant_product_versions.GlobalVersionID → metadata.global_product_versions.VersionID
```

## Logging System Updates

### **Enhanced Tenant Logging**

- **Integer Record IDs**: All logged changes reference integer primary keys
- **UUID Compatibility**: Optional RecordUUID field for migration support
- **Table-Specific Logic**: Smart record ID extraction based on table name
- **Business Impact**: Automatic impact calculation based on changed fields

### **Trigger Updates**

```sql
-- Updated triggers handle integer primary keys
CASE
    WHEN TG_TABLE_NAME = 'tenant_renewals' THEN NEW.renewal_id
    WHEN TG_TABLE_NAME = 'tenant_alerts' THEN NEW.alert_id
    WHEN TG_TABLE_NAME = 'tenant_vendors' THEN NEW.vendor_id
    -- etc.
END
```

## Data Integrity Features

### **Check Constraints**

- Status field validation
- Email format validation
- Confidence score ranges
- Required field validation

### **Unique Constraints**

- Vendor + Product name uniqueness
- Product + Version uniqueness
- Domain name uniqueness

### **Soft Delete Support**

- `IsDeleted` flags instead of hard deletes
- Maintains referential integrity
- Supports audit trails

## Next Steps

1. **✅ Schema Standardization Complete**
2. **🔄 Application Code Updates** (in progress)
   - Update TypeScript interfaces
   - Modify API endpoints
   - Update service layers
3. **📋 Testing and Validation**
   - Unit tests for new schema
   - Integration tests for migrations
   - Performance benchmarking

## Support

For questions or issues with the database standardization:

1. Review this documentation
2. Check the migration logs
3. Verify foreign key relationships
4. Test with sample data

The standardized schema provides a solid foundation for scalable, maintainable, and performant database operations.

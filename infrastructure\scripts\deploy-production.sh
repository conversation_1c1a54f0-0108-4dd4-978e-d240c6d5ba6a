#!/bin/bash

# RenewTrack Production Deployment Script
# This script deploys the complete production infrastructure and application

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT="${ENVIRONMENT:-prod}"
APPLICATION_NAME="${APPLICATION_NAME:-renewtrack}"
AWS_REGION="${AWS_REGION:-ca-central-1}"
DEPLOYMENT_BUCKET="${DEPLOYMENT_BUCKET:-renewtrack-deployments-${AWS_REGION}}"

# Stack names
SECRETS_STACK="${APPLICATION_NAME}-secrets-${ENVIRONMENT}"
INFRASTRUCTURE_STACK="${APPLICATION_NAME}-infrastructure-${ENVIRONMENT}"
APPLICATION_STACK="${APPLICATION_NAME}-application-${ENVIRONMENT}"
DATABASE_STACK="${APPLICATION_NAME}-database-${ENVIRONMENT}"

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}$1${NC}"
}

# Function to check if AWS CLI is installed and configured
check_prerequisites() {
    print_header "Checking Prerequisites"
    echo "========================"
    
    if ! command -v aws &> /dev/null; then
        print_error "AWS CLI is not installed. Please install it first."
        exit 1
    fi

    if ! aws sts get-caller-identity &> /dev/null; then
        print_error "AWS CLI is not configured. Please run 'aws configure' first."
        exit 1
    fi

    if ! command -v jq &> /dev/null; then
        print_warning "jq is not installed. Some features may not work properly."
    fi

    # Check required environment variables
    if [ -z "$KEY_PAIR_NAME" ]; then
        print_error "KEY_PAIR_NAME environment variable is required"
        exit 1
    fi

    print_status "Prerequisites check passed"
    echo ""
}

# Function to create S3 bucket for deployments
create_deployment_bucket() {
    print_status "Creating deployment bucket: $DEPLOYMENT_BUCKET"
    
    if aws s3 ls "s3://$DEPLOYMENT_BUCKET" 2>/dev/null; then
        print_status "Deployment bucket already exists"
    else
        aws s3 mb "s3://$DEPLOYMENT_BUCKET" --region "$AWS_REGION"
        
        # Enable versioning
        aws s3api put-bucket-versioning \
            --bucket "$DEPLOYMENT_BUCKET" \
            --versioning-configuration Status=Enabled
        
        # Enable encryption
        aws s3api put-bucket-encryption \
            --bucket "$DEPLOYMENT_BUCKET" \
            --server-side-encryption-configuration '{
                "Rules": [
                    {
                        "ApplyServerSideEncryptionByDefault": {
                            "SSEAlgorithm": "AES256"
                        }
                    }
                ]
            }'
        
        print_status "Deployment bucket created and configured"
    fi
}

# Function to upload CloudFormation templates
upload_templates() {
    print_status "Uploading CloudFormation templates to S3"
    
    local templates=(
        "../templates/secrets-infrastructure.yaml"
        "../templates/production-infrastructure.yaml"
        "../templates/application-deployment.yaml"
        "../templates/database-infrastructure.yaml"
    )
    
    for template in "${templates[@]}"; do
        if [ -f "$template" ]; then
            local key="templates/$(basename "$template")"
            aws s3 cp "$template" "s3://$DEPLOYMENT_BUCKET/$key"
            print_status "Uploaded $template"
        else
            print_warning "Template not found: $template"
        fi
    done
}

# Function to check if stack exists
stack_exists() {
    aws cloudformation describe-stacks --stack-name "$1" --region "$AWS_REGION" &> /dev/null
}

# Function to wait for stack operation
wait_for_stack() {
    local stack_name="$1"
    local operation="$2"
    
    print_status "Waiting for stack $operation to complete: $stack_name"
    
    case "$operation" in
        "create")
            aws cloudformation wait stack-create-complete --stack-name "$stack_name" --region "$AWS_REGION"
            ;;
        "update")
            aws cloudformation wait stack-update-complete --stack-name "$stack_name" --region "$AWS_REGION"
            ;;
        "delete")
            aws cloudformation wait stack-delete-complete --stack-name "$stack_name" --region "$AWS_REGION"
            ;;
    esac
    
    if [ $? -eq 0 ]; then
        print_status "Stack $operation completed successfully: $stack_name"
    else
        print_error "Stack $operation failed: $stack_name"
        exit 1
    fi
}

# Function to deploy secrets infrastructure
deploy_secrets() {
    print_header "Deploying Secrets Infrastructure"
    echo "================================="
    
    local template_url="https://$DEPLOYMENT_BUCKET.s3.$AWS_REGION.amazonaws.com/templates/secrets-infrastructure.yaml"
    
    local parameters=(
        "ParameterKey=Environment,ParameterValue=$ENVIRONMENT"
        "ParameterKey=ApplicationName,ParameterValue=$APPLICATION_NAME"
        "ParameterKey=DatabaseHost,ParameterValue=${DATABASE_HOST:-renewtrack-prod-ca-central-1-rds.cpy6ukqgy9s3.ca-central-1.rds.amazonaws.com}"
        "ParameterKey=DatabaseName,ParameterValue=${DATABASE_NAME:-renewtrack}"
        "ParameterKey=DatabaseUsername,ParameterValue=${DATABASE_USERNAME:-renewtrack_admin}"
        "ParameterKey=DatabasePassword,ParameterValue=${DATABASE_PASSWORD}"
        "ParameterKey=UseIAMAuth,ParameterValue=${USE_IAM_AUTH:-true}"
        "ParameterKey=JWTSecret,ParameterValue=${JWT_SECRET}"
        "ParameterKey=EncryptionKey,ParameterValue=${ENCRYPTION_KEY}"
    )
    
    if stack_exists "$SECRETS_STACK"; then
        print_status "Updating secrets stack..."
        aws cloudformation update-stack \
            --stack-name "$SECRETS_STACK" \
            --template-url "$template_url" \
            --parameters "${parameters[@]}" \
            --capabilities CAPABILITY_NAMED_IAM \
            --region "$AWS_REGION"
        wait_for_stack "$SECRETS_STACK" "update"
    else
        print_status "Creating secrets stack..."
        aws cloudformation create-stack \
            --stack-name "$SECRETS_STACK" \
            --template-url "$template_url" \
            --parameters "${parameters[@]}" \
            --capabilities CAPABILITY_NAMED_IAM \
            --region "$AWS_REGION"
        wait_for_stack "$SECRETS_STACK" "create"
    fi
    
    echo ""
}

# Function to deploy infrastructure
deploy_infrastructure() {
    print_header "Deploying Core Infrastructure"
    echo "=============================="
    
    local template_url="https://$DEPLOYMENT_BUCKET.s3.$AWS_REGION.amazonaws.com/templates/production-infrastructure.yaml"
    
    local parameters=(
        "ParameterKey=Environment,ParameterValue=$ENVIRONMENT"
        "ParameterKey=ApplicationName,ParameterValue=$APPLICATION_NAME"
        "ParameterKey=VpcCidr,ParameterValue=${VPC_CIDR:-10.0.0.0/16}"
        "ParameterKey=InstanceType,ParameterValue=${INSTANCE_TYPE:-t3.medium}"
        "ParameterKey=MinSize,ParameterValue=${MIN_SIZE:-2}"
        "ParameterKey=MaxSize,ParameterValue=${MAX_SIZE:-10}"
        "ParameterKey=DesiredCapacity,ParameterValue=${DESIRED_CAPACITY:-2}"
    )
    
    if stack_exists "$INFRASTRUCTURE_STACK"; then
        print_status "Updating infrastructure stack..."
        aws cloudformation update-stack \
            --stack-name "$INFRASTRUCTURE_STACK" \
            --template-url "$template_url" \
            --parameters "${parameters[@]}" \
            --capabilities CAPABILITY_IAM \
            --region "$AWS_REGION"
        wait_for_stack "$INFRASTRUCTURE_STACK" "update"
    else
        print_status "Creating infrastructure stack..."
        aws cloudformation create-stack \
            --stack-name "$INFRASTRUCTURE_STACK" \
            --template-url "$template_url" \
            --parameters "${parameters[@]}" \
            --capabilities CAPABILITY_IAM \
            --region "$AWS_REGION"
        wait_for_stack "$INFRASTRUCTURE_STACK" "create"
    fi
    
    echo ""
}

# Function to deploy application
deploy_application() {
    print_header "Deploying Application"
    echo "===================="
    
    local template_url="https://$DEPLOYMENT_BUCKET.s3.$AWS_REGION.amazonaws.com/templates/application-deployment.yaml"
    
    local parameters=(
        "ParameterKey=Environment,ParameterValue=$ENVIRONMENT"
        "ParameterKey=ApplicationName,ParameterValue=$APPLICATION_NAME"
        "ParameterKey=InstanceType,ParameterValue=${INSTANCE_TYPE:-t3.medium}"
        "ParameterKey=MinSize,ParameterValue=${MIN_SIZE:-2}"
        "ParameterKey=MaxSize,ParameterValue=${MAX_SIZE:-10}"
        "ParameterKey=DesiredCapacity,ParameterValue=${DESIRED_CAPACITY:-2}"
        "ParameterKey=KeyPairName,ParameterValue=$KEY_PAIR_NAME"
        "ParameterKey=DomainName,ParameterValue=${DOMAIN_NAME:-renewtrack.com}"
    )
    
    # Add certificate ARN if provided
    if [ -n "$CERTIFICATE_ARN" ]; then
        parameters+=("ParameterKey=CertificateArn,ParameterValue=$CERTIFICATE_ARN")
    fi
    
    if stack_exists "$APPLICATION_STACK"; then
        print_status "Updating application stack..."
        aws cloudformation update-stack \
            --stack-name "$APPLICATION_STACK" \
            --template-url "$template_url" \
            --parameters "${parameters[@]}" \
            --capabilities CAPABILITY_NAMED_IAM \
            --region "$AWS_REGION"
        wait_for_stack "$APPLICATION_STACK" "update"
    else
        print_status "Creating application stack..."
        aws cloudformation create-stack \
            --stack-name "$APPLICATION_STACK" \
            --template-url "$template_url" \
            --parameters "${parameters[@]}" \
            --capabilities CAPABILITY_NAMED_IAM \
            --region "$AWS_REGION"
        wait_for_stack "$APPLICATION_STACK" "create"
    fi
    
    echo ""
}

# Function to get stack outputs
get_stack_outputs() {
    print_header "Deployment Outputs"
    echo "=================="
    
    print_status "Infrastructure Stack Outputs:"
    aws cloudformation describe-stacks \
        --stack-name "$INFRASTRUCTURE_STACK" \
        --region "$AWS_REGION" \
        --query 'Stacks[0].Outputs[*].[OutputKey,OutputValue]' \
        --output table
    
    echo ""
    
    print_status "Application Stack Outputs:"
    aws cloudformation describe-stacks \
        --stack-name "$APPLICATION_STACK" \
        --region "$AWS_REGION" \
        --query 'Stacks[0].Outputs[*].[OutputKey,OutputValue]' \
        --output table
    
    echo ""
    
    # Get load balancer DNS name
    local lb_dns=$(aws cloudformation describe-stacks \
        --stack-name "$APPLICATION_STACK" \
        --region "$AWS_REGION" \
        --query 'Stacks[0].Outputs[?OutputKey==`LoadBalancerDNS`].OutputValue' \
        --output text)
    
    if [ -n "$lb_dns" ]; then
        print_status "Application URL: http://$lb_dns"
        if [ -n "$CERTIFICATE_ARN" ]; then
            print_status "Secure Application URL: https://$lb_dns"
        fi
    fi
}

# Function to run health checks
run_health_checks() {
    print_header "Running Health Checks"
    echo "====================="
    
    # Get load balancer DNS
    local lb_dns=$(aws cloudformation describe-stacks \
        --stack-name "$APPLICATION_STACK" \
        --region "$AWS_REGION" \
        --query 'Stacks[0].Outputs[?OutputKey==`LoadBalancerDNS`].OutputValue' \
        --output text)
    
    if [ -n "$lb_dns" ]; then
        print_status "Testing application health endpoint..."
        
        # Wait for load balancer to be ready
        sleep 60
        
        local health_url="http://$lb_dns/api/health"
        local max_attempts=10
        local attempt=1
        
        while [ $attempt -le $max_attempts ]; do
            print_status "Health check attempt $attempt/$max_attempts"
            
            if curl -f -s "$health_url" > /dev/null; then
                print_status "✅ Application is healthy!"
                break
            else
                if [ $attempt -eq $max_attempts ]; then
                    print_warning "⚠️ Health check failed after $max_attempts attempts"
                    print_warning "The application may still be starting up. Check the instances manually."
                else
                    print_status "Health check failed, retrying in 30 seconds..."
                    sleep 30
                fi
            fi
            
            ((attempt++))
        done
    fi
}

# Main execution
main() {
    print_header "RenewTrack Production Deployment"
    echo "================================="
    echo ""
    
    print_status "Environment: $ENVIRONMENT"
    print_status "Application: $APPLICATION_NAME"
    print_status "AWS Region: $AWS_REGION"
    print_status "Deployment Bucket: $DEPLOYMENT_BUCKET"
    echo ""
    
    # Check prerequisites
    check_prerequisites
    
    # Create deployment bucket
    create_deployment_bucket
    
    # Upload templates
    upload_templates
    
    # Deploy stacks in order
    deploy_secrets
    deploy_infrastructure
    deploy_application
    
    # Get outputs
    get_stack_outputs
    
    # Run health checks
    run_health_checks
    
    echo ""
    print_status "Deployment completed successfully!"
    echo ""
    print_status "Next steps:"
    echo "1. Configure DNS to point to the load balancer"
    echo "2. Deploy your application code to the instances"
    echo "3. Configure monitoring and alerting"
    echo "4. Set up backup and disaster recovery procedures"
    echo ""
    print_warning "Remember to:"
    echo "- Update security groups to restrict SSH access"
    echo "- Configure SSL certificates for HTTPS"
    echo "- Set up proper monitoring and logging"
    echo "- Test all functionality thoroughly"
}

# Run main function
main "$@"

'use client'

import { useState, useEffect } from 'react'
import { useData } from '@/lib/hooks'
import PageAccessGuard from '@/components/auth/PageAccessGuard'

interface AdminPage {
  id: number
  name: string
  header: string
  description: string | null
  sidebar: boolean
  status: string
  display_order: number
  icon_svg: string | null
  route_path: string
  groups: string[]
  packages?: string[]
}

export default function PageManagementPage() {
  const [selectedPage, setSelectedPage] = useState<AdminPage | null>(null)
  const [showAddModal, setShowAddModal] = useState(false)

  const {
    data: pages = [],
    loading: isLoading,
    error,
    refetch
  } = useData<AdminPage[]>({
    endpoint: '/api/admin-pages',
    cache: {
      key: 'admin-pages-management'
    }
  })

  const handleToggleSidebar = async (page: AdminPage) => {
    try {
      const response = await fetch(`/api/admin-pages/${page.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sidebar: !page.sidebar
        })
      })

      if (response.ok) {
        refetch()
      } else {
        console.error('Failed to update page')
      }
    } catch (error) {
      console.error('Error updating page:', error)
    }
  }

  const handleToggleStatus = async (page: AdminPage) => {
    try {
      const newStatus = page.status === 'A' ? 'I' : 'A'
      const response = await fetch(`/api/admin-pages/${page.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: newStatus
        })
      })

      if (response.ok) {
        refetch()
      } else {
        console.error('Failed to update page status')
      }
    } catch (error) {
      console.error('Error updating page status:', error)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <h3 className="text-red-800 font-medium">Error Loading Pages</h3>
          <p className="text-red-600 mt-1">{error?.message || 'An error occurred'}</p>
        </div>
      </div>
    )
  }

  return (
    <PageAccessGuard pageName="page-management" redirectTo="/overview">
      <div className="p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900">Page Management</h1>
          <p className="text-gray-600 mt-1">
            Manage application pages and their access permissions
          </p>
        </div>

        <div className="bg-white shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Application Pages</h2>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Page
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Route
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Groups
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Packages
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Sidebar
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Order
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {pages?.map((page) => (
                  <tr key={page.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {page.header}
                        </div>
                        <div className="text-sm text-gray-500">
                          {page.description}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {page.route_path}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex flex-wrap gap-1">
                        {page.groups.map((group) => (
                          <span
                            key={group}
                            className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                          >
                            {group}
                          </span>
                        ))}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex flex-wrap gap-1">
                        {(page.packages || []).map((pkg) => (
                          <span
                            key={pkg}
                            className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
                          >
                            {pkg}
                          </span>
                        ))}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <button
                        onClick={() => handleToggleSidebar(page)}
                        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                          page.sidebar ? 'bg-blue-600' : 'bg-gray-200'
                        }`}
                      >
                        <span
                          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                            page.sidebar ? 'translate-x-6' : 'translate-x-1'
                          }`}
                        />
                      </button>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <button
                        onClick={() => handleToggleStatus(page)}
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          page.status === 'A'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}
                      >
                        {page.status === 'A' ? 'Active' : 'Inactive'}
                      </button>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {page.display_order}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button
                        onClick={() => setSelectedPage(page)}
                        className="text-blue-600 hover:text-blue-900 mr-3"
                      >
                        Edit
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </PageAccessGuard>
  )
}

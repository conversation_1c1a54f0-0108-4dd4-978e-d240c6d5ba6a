/**
 * AWS Configuration and Credentials Management
 * 
 * This module handles AWS service configuration, credential management,
 * and secure parameter retrieval from AWS Parameter Store.
 */

import { STSClient, AssumeRoleCommand } from "@aws-sdk/client-sts";
import { SSMClient, GetParameterCommand } from "@aws-sdk/client-ssm";

/**
 * Get temporary credentials by assuming a role
 * In development, uses local credentials; in production, assumes IAM role
 */
async function getCredentials() {
  if (process.env.NODE_ENV !== 'production') {
    return {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      region: process.env.NEXT_PUBLIC_AWS_REGION
    };
  }

  const stsClient = new STSClient({ region: process.env.NEXT_PUBLIC_AWS_REGION });
  
  const command = new AssumeRoleCommand({
    RoleArn: process.env.PARAMETER_STORE_ROLE_ARN,
    RoleSessionName: 'RenewTrackParameterStoreAccess',
    DurationSeconds: 900 // 15 minutes
  });
  
  const response = await stsClient.send(command);
  
  return {
    accessKeyId: response.Credentials?.AccessKeyId,
    secretAccessKey: response.Credentials?.SecretAccessKey,
    sessionToken: response.Credentials?.SessionToken,
    region: process.env.NEXT_PUBLIC_AWS_REGION
  };
}

/**
 * Get parameter from AWS Parameter Store
 */
async function getParameter(parameterName: string, decrypt: boolean = true) {
  const credentials = await getCredentials();
  
  const ssmClient = new SSMClient({
    region: credentials.region,
    credentials: {
      accessKeyId: credentials.accessKeyId!,
      secretAccessKey: credentials.secretAccessKey!,
      sessionToken: credentials.sessionToken
    }
  });
  
  const command = new GetParameterCommand({
    Name: parameterName,
    WithDecryption: decrypt
  });
  
  const response = await ssmClient.send(command);
  return response.Parameter?.Value;
}

export { getCredentials, getParameter };

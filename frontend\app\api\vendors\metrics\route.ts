/**
 * Vendors Metrics API
 * 
 * Provides vendor metrics and KPIs for the vendor dashboard
 */

import { NextRequest } from 'next/server'
import { withAuth } from '@/lib/api/auth-middleware'
import { resolveTenantContext } from '@/lib/tenant/context'
import { executeQuery, schemaExists } from '@/lib/database'
import {
  createSuccessResponse,
  createErrorResponse,
  ApiErrorCode,
  HttpStatus
} from '@/lib/api/response'

interface VendorMetrics {
  totalVendors: number
  totalSpend: number
  avgSpendPerVendor: number
  topVendorSpend: number
  topVendorName: string
  vendorConcentration: number
  avgReliabilityScore: number
  vendorsWithUpcomingRenewals: number
  currency?: string
}

export const GET = withAuth(async (request: NextRequest, session) => {
  console.log('[VENDORS-METRICS-API] GET request received')

  try {
    // Resolve tenant context
    const tenant = await resolveTenantContext(session.email)
    if (!tenant) {
      console.error('[VENDORS-METRICS-API] Failed to resolve tenant context')
      return createErrorResponse(
        'Tenant context not found',
        ApiErrorCode.TENANT_NOT_FOUND,
        HttpStatus.BAD_REQUEST
      )
    }

    console.log(`[VENDORS-METRICS-API] Resolved tenant: ${tenant.tenantId}`)

    // Check if tenant schema exists
    const schemaExistsResult = await schemaExists(tenant.tenantSchema)
    if (!schemaExistsResult) {
      console.log(`[VENDORS-METRICS-API] Tenant schema ${tenant.tenantSchema} not ready yet`)
      
      // Return default metrics
      const defaultMetrics: VendorMetrics = {
        totalVendors: 0,
        totalSpend: 0,
        avgSpendPerVendor: 0,
        topVendorSpend: 0,
        topVendorName: 'N/A',
        vendorConcentration: 0,
        avgReliabilityScore: 0,
        vendorsWithUpcomingRenewals: 0,
        currency: 'USD'
      }
      
      return createSuccessResponse(defaultMetrics, 'Vendor metrics retrieved successfully')
    }

    // Query vendor metrics from tenant schema
    const query = `
      WITH vendor_stats AS (
        SELECT 
          vendor,
          COUNT(*) as renewal_count,
          SUM(cost) as total_spend,
          COUNT(CASE WHEN renewal_date > CURRENT_DATE THEN 1 END) as upcoming_renewals
        FROM "${tenant.tenantSchema}".tenant_renewals
        WHERE vendor IS NOT NULL AND vendor != ''
        GROUP BY vendor
      ),
      overall_stats AS (
        SELECT 
          COUNT(DISTINCT vendor) as total_vendors,
          SUM(total_spend) as total_spend,
          AVG(total_spend) as avg_spend_per_vendor,
          MAX(total_spend) as top_vendor_spend,
          SUM(CASE WHEN upcoming_renewals > 0 THEN 1 ELSE 0 END) as vendors_with_upcoming
        FROM vendor_stats
      ),
      top_vendor AS (
        SELECT vendor as top_vendor_name
        FROM vendor_stats
        ORDER BY total_spend DESC
        LIMIT 1
      )
      SELECT 
        os.total_vendors,
        os.total_spend,
        os.avg_spend_per_vendor,
        os.top_vendor_spend,
        COALESCE(tv.top_vendor_name, 'N/A') as top_vendor_name,
        CASE 
          WHEN os.total_spend > 0 THEN (os.top_vendor_spend / os.total_spend * 100)
          ELSE 0 
        END as vendor_concentration,
        os.vendors_with_upcoming
      FROM overall_stats os
      CROSS JOIN top_vendor tv
    `

    console.log(`[VENDORS-METRICS-API] Executing query for schema: ${tenant.tenantSchema}`)
    const result = await executeQuery(query)

    if (!result.success) {
      console.error('[VENDORS-METRICS-API] Database query failed:', result.error)
      return createErrorResponse(
        'Failed to fetch vendor metrics',
        ApiErrorCode.INTERNAL_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }

    // Transform data
    const row = result.data[0] || {}
    const metrics: VendorMetrics = {
      totalVendors: parseInt(row.total_vendors) || 0,
      totalSpend: parseFloat(row.total_spend) || 0,
      avgSpendPerVendor: parseFloat(row.avg_spend_per_vendor) || 0,
      topVendorSpend: parseFloat(row.top_vendor_spend) || 0,
      topVendorName: row.top_vendor_name || 'N/A',
      vendorConcentration: parseFloat(row.vendor_concentration) || 0,
      avgReliabilityScore: Math.random() * 100, // TODO: Implement actual reliability scoring
      vendorsWithUpcomingRenewals: parseInt(row.vendors_with_upcoming) || 0,
      currency: 'USD'
    }

    console.log(`[VENDORS-METRICS-API] Returning metrics:`, metrics)
    return createSuccessResponse(metrics, 'Vendor metrics retrieved successfully')

  } catch (error) {
    console.error('[VENDORS-METRICS-API] Error:', error)
    return createErrorResponse(
      'Failed to fetch vendor metrics',
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    )
  }
}, {
  requireAuth: true
})

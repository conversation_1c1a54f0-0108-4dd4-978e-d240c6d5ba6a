/**
 * License Validation Hook
 * 
 * Provides page-level license validation with proper error handling
 * and user experience. This replaces the middleware-based validation
 * with a more robust, user-friendly approach.
 */

import { useState, useEffect } from 'react'
import { useAuth } from '@/lib/hooks/useAuth'
import { useLicense } from '@/lib/hooks/useLicense'
import { useRouter } from 'next/navigation'

interface LicenseValidationResult {
  isValid: boolean
  isLoading: boolean
  error: string | null
  licenseInfo?: {
    clientId: number
    clientName: string
    expiresAt?: string
    features: string[]
  }
}

interface LicenseValidationOptions {
  required?: boolean
  redirectOnFailure?: boolean
  exemptForAdmins?: boolean
  requiredFeatures?: string[]
}

/**
 * Hook for page-level license validation with caching
 */
export function useLicenseValidation(options: LicenseValidationOptions = {}): LicenseValidationResult {
  const {
    required = true,
    redirectOnFailure = true,
    exemptForAdmins = true,
    requiredFeatures = []
  } = options

  const { user, isAuthenticated, isLoading: authLoading } = useAuth()
  const router = useRouter()

  // Use the cached license hook instead of making direct API calls
  const { licenseStatus, isLoading: licenseLoading, error: licenseError } = useLicense()

  const [isValid, setIsValid] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [licenseInfo, setLicenseInfo] = useState<any>(null)

  useEffect(() => {
    async function validateLicense() {
      // Don't validate if auth is still loading
      if (authLoading) {
        return
      }

      // Don't validate if not authenticated - set as valid to avoid blocking
      if (!isAuthenticated || !user) {
        setIsValid(true) // Allow access when not authenticated (let auth guards handle this)
        setError(null)
        return
      }

      // Check if user is exempt from license validation
      if (exemptForAdmins && user.groups) {
        const userGroups = Array.isArray(user.groups)
          ? user.groups
          : typeof user.groups === 'string'
            ? (user.groups as string).split(',')
            : []
        const exemptGroups = ['admin', 'super-admin']
        if (userGroups.some(group => exemptGroups.includes(group.toLowerCase()))) {
          setIsValid(true)
          return
        }
      }

      // Skip validation if not required
      if (!required) {
        setIsValid(true)
        return
      }

      // Use cached license status instead of making direct API call
      if (licenseError) {
        setIsValid(false)
        setError(String(licenseError))
        if (redirectOnFailure) {
          router.push('/license-error')
        }
        return
      }

      if (licenseStatus) {
        const hasRequiredFeatures = requiredFeatures.length === 0 ||
          requiredFeatures.every(feature =>
            licenseStatus.license?.features?.includes(feature)
          )

        if (licenseStatus.isValid && hasRequiredFeatures) {
          setIsValid(true)
          setLicenseInfo(licenseStatus.license)
        } else {
          setIsValid(false)
          setError(licenseStatus.reason || 'License validation failed')
          if (redirectOnFailure) {
            router.push('/license-error')
          }
        }
      }
    }

    validateLicense()
  }, [isAuthenticated, user, authLoading, licenseLoading, required, exemptForAdmins, redirectOnFailure, router, licenseStatus, licenseError, requiredFeatures])

  return {
    isValid,
    isLoading: authLoading || licenseLoading,
    error,
    licenseInfo
  }
}

/**
 * Higher-order component for license validation
 */
export function withLicenseValidation<P extends object>(
  Component: React.ComponentType<P>,
  options: LicenseValidationOptions = {}
) {
  return function LicenseValidatedComponent(props: P) {
    const { isValid, isLoading, error } = useLicenseValidation(options)

    if (isLoading) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Validating license...</p>
          </div>
        </div>
      )
    }

    if (!isValid && options.required !== false) {
      return (
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center max-w-md">
            <div className="text-red-600 text-6xl mb-4">⚠️</div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">License Required</h1>
            <p className="text-gray-600 mb-4">
              {error || 'A valid license is required to access this feature.'}
            </p>
            <button
              onClick={() => window.location.href = '/license-error'}
              className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
            >
              Learn More
            </button>
          </div>
        </div>
      )
    }

    return <Component {...props} />
  }
}

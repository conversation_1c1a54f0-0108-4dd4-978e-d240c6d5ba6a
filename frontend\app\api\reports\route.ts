/**
 * Reports API Route
 * 
 * API endpoint for fetching renewal data for reports
 */

import { NextRequest, NextResponse } from 'next/server'
import { fetchAuthSession, fetchUserAttributes } from 'aws-amplify/auth'
import { cookies } from 'next/headers'
import { getClientByEmailDomain } from '@/lib/tenant/clients'
import { executeQuery } from '@/lib/database'
import { createSuccessResponse, createErrorResponse, ApiErrorCode, HttpStatus, withErrorHandling } from '@/lib/api/response'

export const GET = withErrorHandling(async (request: NextRequest) => {
  // Get authenticated user
  const session = await fetchAuthSession()
  if (!session.tokens) {
    return createErrorResponse(
      'Authentication required',
      ApiErrorCode.UNAUTHORIZED,
      HttpStatus.UNAUTHORIZED
    );
  }

  // Get user attributes
  const userAttributes = await fetchUserAttributes();
  const userEmail = userAttributes.email;
  if (!userEmail) {
    return createErrorResponse(
      'User email not found',
      ApiErrorCode.UNAUTHORIZED,
      HttpStatus.UNAUTHORIZED
    );
  }

  // Get tenant context using the same pattern as other routes
  const clientResult = await getClientByEmailDomain(userEmail);

  if (!clientResult.success) {
    return createErrorResponse(
      'Failed to fetch tenant information',
      ApiErrorCode.DATABASE_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }

  const tenantContext = clientResult.client!
        if (!tenantContext) {
          return createErrorResponse(
            'Tenant context not found',
            ApiErrorCode.NOT_FOUND,
            HttpStatus.NOT_FOUND
          );
        }

        // Parse query parameters
        const { searchParams } = new URL(request.url)
        const vendor = searchParams.get('vendor')
        const renewalType = searchParams.get('renewalType')
        const renewalFrom = searchParams.get('renewalFrom')
        const renewalTo = searchParams.get('renewalTo')
        const includeArchived = searchParams.get('includeArchived') === 'true'

        // Build query conditions
        const conditions: string[] = []
        const params: any[] = [tenantContext.tenantId]
        let paramIndex = 2

        if (vendor && vendor !== 'All Vendors') {
          conditions.push(`r.vendor = $${paramIndex}`)
          params.push(vendor)
          paramIndex++
        }

        if (renewalType && renewalType !== 'All Types') {
          conditions.push(`r.type = $${paramIndex}`)
          params.push(renewalType)
          paramIndex++
        }

        if (renewalFrom) {
          conditions.push(`r.start_date >= $${paramIndex}`)
          params.push(renewalFrom)
          paramIndex++
        }

        if (renewalTo) {
          conditions.push(`r.start_date <= $${paramIndex}`)
          params.push(renewalTo)
          paramIndex++
        }

        if (!includeArchived) {
          conditions.push(`r.status != 'Archived'`)
        }

        const whereClause = conditions.length > 0 ? `AND ${conditions.join(' AND ')}` : ''

        // Query renewals with items
        const renewalsQuery = `
          SELECT 
            r.id,
            r.name,
            r.vendor,
            r.type,
            r.start_date,
            r.currency,
            r.cost,
            r.status,
            r.contact_emails,
            r.created_on,
            r.changed_on,
            -- Aggregate renewal items
            COALESCE(
              JSON_AGG(
                JSON_BUILD_OBJECT(
                  'id', ri.id,
                  'product_name', ri.product_name,
                  'version', ri.version,
                  'quantity', ri.quantity,
                  'unitCost', ri.unit_cost
                ) ORDER BY ri.id
              ) FILTER (WHERE ri.id IS NOT NULL),
              '[]'::json
            ) as items
          FROM ${tenantContext.schemaName}.tenant_renewals r
          LEFT JOIN ${tenantContext.schemaName}.tenant_renewal_items ri ON r.id = ri.renewal_id
          WHERE r.tenant_id = $1 ${whereClause}
          GROUP BY r.id, r.name, r.vendor, r.type, r.start_date, r.currency, r.cost, r.status, r.contact_emails, r.created_on, r.changed_on
          ORDER BY r.start_date ASC
        `

        const result = await executeQuery(renewalsQuery, params)

        // Transform data for frontend
        const renewals = (result.data || []).map(row => ({
          id: row.id,
          name: row.name,
          vendor: row.vendor,
          type: row.type,
          start_date: row.start_date,
          currency: row.currency,
          cost: parseFloat(row.cost) || 0,
          status: row.status,
          contactEmails: row.contact_emails,
          items: row.items || [],
          // Derived fields for reports
          product_name: row.items?.[0]?.product_name || row.name,
          version: row.items?.[0]?.version || '',
          annualCost: parseFloat(row.cost) || 0,
          createdAt: row.created_on,
          updatedAt: row.changed_on
        }))

        // Calculate summary statistics
        const stats = {
          totalRenewals: renewals.length,
          totalValue: renewals.reduce((sum, r) => sum + r.cost, 0),
          averageValue: renewals.length > 0 ? renewals.reduce((sum, r) => sum + r.cost, 0) / renewals.length : 0,
          upcomingRenewals: renewals.filter(r => {
            const start_date = new Date(r.start_date)
            const now = new Date()
            const thirtyDaysFromNow = new Date(now.getTime() + (30 * 24 * 60 * 60 * 1000))
            return start_date >= now && start_date <= thirtyDaysFromNow
          }).length,
          vendorCount: new Set(renewals.map(r => r.vendor)).size,
          typeBreakdown: renewals.reduce((acc, r) => {
            acc[r.type] = (acc[r.type] || 0) + 1
            return acc
          }, {} as Record<string, number>)
        }

        return createSuccessResponse({
          renewals,
          stats,
          filters: {
            vendor,
            renewalType,
            renewalFrom,
            renewalTo,
            includeArchived
          }
        });
});

/**
 * Vendor Analytics Caching Service
 * 
 * Provides high-performance caching for vendor analytics data with:
 * - Multi-level caching (memory + localStorage)
 * - Intelligent cache invalidation
 * - Background data refresh
 * - Query optimization
 */

import { AdvancedCache } from '@/lib/utils/cache'

// Cache configuration for vendor analytics
const VENDOR_CACHE_CONFIG = {
  maxSize: 1000,
  defaultTTL: 5 * 60 * 1000, // 5 minutes
  cleanupInterval: 60 * 1000, // 1 minute
  enableMetrics: true
}

// Cache keys
const CACHE_KEYS = {
  VENDOR_LIST: 'vendor:list',
  VENDOR_ANALYTICS: 'vendor:analytics',
  VENDOR_COMPARISON: 'vendor:comparison',
  VENDOR_METRICS: 'vendor:metrics',
  VENDOR_TRENDS: 'vendor:trends'
} as const

// Cache tags for invalidation
const CACHE_TAGS = {
  VENDOR_DATA: 'vendor-data',
  ANALYTICS: 'analytics',
  METRICS: 'metrics',
  TRENDS: 'trends'
} as const

interface VendorAnalyticsQuery {
  vendor_id?: string
  period?: string
  include_trends?: boolean
  include_risk_analysis?: boolean
  limit?: number
}

interface CachedVendorData {
  data: any
  query: VendorAnalyticsQuery
  timestamp: number
  tenantId: string
}

class VendorAnalyticsCacheService {
  private cache: AdvancedCache<any>
  private backgroundRefreshQueue: Set<string> = new Set()
  private refreshInProgress: Set<string> = new Set()

  constructor() {
    this.cache = new AdvancedCache(VENDOR_CACHE_CONFIG)
    this.setupBackgroundRefresh()
  }

  /**
   * Get vendor analytics with caching
   */
  async getVendorAnalytics(
    query: VendorAnalyticsQuery,
    tenantId: string,
    fetchFn: () => Promise<any>
  ): Promise<any> {
    const cacheKey = this.generateCacheKey(CACHE_KEYS.VENDOR_ANALYTICS, query, tenantId)
    
    // Try to get from cache first
    const cached = this.cache.get(cacheKey)
    if (cached) {
      // Schedule background refresh if data is getting stale
      this.scheduleBackgroundRefresh(cacheKey, query, tenantId, fetchFn)
      return cached
    }

    // Cache miss - fetch fresh data
    const data = await fetchFn()
    
    // Cache the result with appropriate tags
    const tags = [
      CACHE_TAGS.VENDOR_DATA,
      CACHE_TAGS.ANALYTICS,
      ...(query.vendor_id ? [`vendor:${query.vendor_id}`] : []),
      `tenant:${tenantId}`
    ]
    
    this.cache.set(cacheKey, data, this.getTTLForQuery(query), tags)

    return data
  }

  /**
   * Get vendor comparison data with caching
   */
  async getVendorComparison(
    vendorIds: string[],
    tenantId: string,
    fetchFn: () => Promise<any>
  ): Promise<any> {
    const cacheKey = this.generateComparisonCacheKey(vendorIds, tenantId)
    
    const cached = this.cache.get(cacheKey)
    if (cached) {
      return cached
    }

    const data = await fetchFn()
    
    const tags = [
      CACHE_TAGS.VENDOR_DATA,
      CACHE_TAGS.ANALYTICS,
      ...vendorIds.map(id => `vendor:${id}`),
      `tenant:${tenantId}`
    ]
    
    this.cache.set(cacheKey, data, 10 * 60 * 1000, tags) // 10 minutes for comparison data

    return data
  }

  /**
   * Get vendor metrics with aggressive caching
   */
  async getVendorMetrics(
    tenantId: string,
    fetchFn: () => Promise<any>
  ): Promise<any> {
    const cacheKey = `${CACHE_KEYS.VENDOR_METRICS}:${tenantId}`
    
    const cached = this.cache.get(cacheKey)
    if (cached) {
      // Metrics can be served from cache more aggressively
      this.scheduleBackgroundRefresh(cacheKey, {}, tenantId, fetchFn)
      return cached
    }

    const data = await fetchFn()
    
    this.cache.set(cacheKey, data, 15 * 60 * 1000, [CACHE_TAGS.VENDOR_DATA, CACHE_TAGS.METRICS, `tenant:${tenantId}`]) // 15 minutes for metrics

    return data
  }

  /**
   * Invalidate cache when vendor data changes
   */
  invalidateVendorData(vendorId?: string, tenantId?: string): void {
    const tags: string[] = [CACHE_TAGS.VENDOR_DATA]

    if (vendorId) {
      tags.push(`vendor:${vendorId}`)
    }

    if (tenantId) {
      tags.push(`tenant:${tenantId}`)
    }
    
    this.cache.clearByTags(tags)
  }

  /**
   * Invalidate all analytics cache
   */
  invalidateAnalytics(tenantId?: string): void {
    const tags: string[] = [CACHE_TAGS.ANALYTICS]

    if (tenantId) {
      tags.push(`tenant:${tenantId}`)
    }
    
    this.cache.clearByTags(tags)
  }

  /**
   * Get cache statistics
   */
  getCacheStats() {
    return {
      ...this.cache.getMetrics(),
      backgroundRefreshQueue: this.backgroundRefreshQueue.size,
      refreshInProgress: this.refreshInProgress.size
    }
  }

  /**
   * Preload vendor data for better performance
   */
  async preloadVendorData(tenantId: string, vendorIds: string[]): Promise<void> {
    const preloadPromises = vendorIds.map(async (vendorId) => {
      const query = { vendor_id: vendorId, include_trends: true, include_risk_analysis: true }
      const cacheKey = this.generateCacheKey(CACHE_KEYS.VENDOR_ANALYTICS, query, tenantId)
      
      // Only preload if not already cached
      if (!this.cache.has(cacheKey)) {
        try {
          // This would need to be connected to the actual API
          // For now, we just mark it for background loading
          this.backgroundRefreshQueue.add(cacheKey)
        } catch (error) {
          console.warn(`Failed to preload vendor data for ${vendorId}:`, error)
        }
      }
    })

    await Promise.allSettled(preloadPromises)
  }

  /**
   * Generate cache key for vendor analytics
   */
  private generateCacheKey(
    baseKey: string,
    query: VendorAnalyticsQuery,
    tenantId: string
  ): string {
    const queryString = Object.entries(query)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([key, value]) => `${key}:${value}`)
      .join('|')
    
    return `${baseKey}:${tenantId}:${queryString}`
  }

  /**
   * Generate cache key for vendor comparison
   */
  private generateComparisonCacheKey(vendorIds: string[], tenantId: string): string {
    const sortedIds = [...vendorIds].sort().join(',')
    return `${CACHE_KEYS.VENDOR_COMPARISON}:${tenantId}:${sortedIds}`
  }

  /**
   * Get TTL based on query complexity
   */
  private getTTLForQuery(query: VendorAnalyticsQuery): number {
    let baseTTL = 5 * 60 * 1000 // 5 minutes

    // Longer TTL for simpler queries
    if (!query.include_trends && !query.include_risk_analysis) {
      baseTTL = 10 * 60 * 1000 // 10 minutes
    }

    // Shorter TTL for specific vendor queries (more likely to change)
    if (query.vendor_id) {
      baseTTL = Math.max(baseTTL * 0.7, 2 * 60 * 1000) // Reduce by 30%, min 2 minutes
    }

    return baseTTL
  }

  /**
   * Schedule background refresh for stale data
   */
  private scheduleBackgroundRefresh(
    cacheKey: string,
    query: VendorAnalyticsQuery,
    tenantId: string,
    fetchFn: () => Promise<any>
  ): void {
    // Only refresh if data is more than 70% of TTL old
    const entry = this.cache.get(cacheKey)
    if (!entry) return

    const age = Date.now() - entry.timestamp
    const stalenessThreshold = entry.ttl * 0.7

    if (age > stalenessThreshold && !this.refreshInProgress.has(cacheKey)) {
      this.backgroundRefreshQueue.add(cacheKey)
    }
  }

  /**
   * Setup background refresh worker
   */
  private setupBackgroundRefresh(): void {
    setInterval(() => {
      this.processBackgroundRefresh()
    }, 30 * 1000) // Process every 30 seconds
  }

  /**
   * Process background refresh queue
   */
  private async processBackgroundRefresh(): Promise<void> {
    if (this.backgroundRefreshQueue.size === 0) return

    // Process up to 3 items at a time to avoid overwhelming the system
    const itemsToProcess = Array.from(this.backgroundRefreshQueue).slice(0, 3)
    
    for (const cacheKey of itemsToProcess) {
      this.backgroundRefreshQueue.delete(cacheKey)
      this.refreshInProgress.add(cacheKey)

      try {
        // This would need to be implemented with actual refresh logic
        // For now, we just remove from the in-progress set
        setTimeout(() => {
          this.refreshInProgress.delete(cacheKey)
        }, 5000)
      } catch (error) {
        console.warn(`Background refresh failed for ${cacheKey}:`, error)
        this.refreshInProgress.delete(cacheKey)
      }
    }
  }
}

// Export singleton instance
export const vendorAnalyticsCache = new VendorAnalyticsCacheService()
export default vendorAnalyticsCache

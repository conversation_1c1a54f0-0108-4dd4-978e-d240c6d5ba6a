/**
 * Notifications API Endpoint
 * 
 * Handles notification-related operations
 * POST /api/notifications/check - Manually trigger alert checks
 * POST /api/notifications/test - Send test email
 */

import { createApiRoute } from '@/lib/api/route-factory';
import { NotificationService } from '@/lib/services/notification-service';
import { z } from 'zod';

// Validation schemas
const testEmailSchema = z.object({
  email: z.string().email('Invalid email address')
});

const checkAlertsSchema = z.object({
  force: z.boolean().optional().default(false)
});

const notificationBodySchema = z.union([
  testEmailSchema,
  checkAlertsSchema
]);

const notificationQuerySchema = z.object({
  action: z.enum(['check', 'test'])
});

// POST /api/notifications
export const POST = createApiRoute('POST', {
  requireAuth: true,
  requireTenant: true,
  bodySchema: notificationBodySchema,
  querySchema: notificationQuerySchema,
  handler: async (context) => {
    const { tenant, body, query } = context;
    if (!tenant || !query) {
      throw new Error('Tenant context and query parameters are required');
    }
    const { action } = query;

    switch (action) {
      case 'check':
        // Manually trigger alert checks
        const checkData = checkAlertsSchema.parse(body);

        try {
          await NotificationService.checkAndSendAlerts(tenant.tenant);
          
          return {
            message: 'Alert check completed successfully',
            timestamp: new Date().toISOString()
          };
        } catch (error) {
          console.error('Error checking alerts:', error);
          throw new Error('Failed to check alerts');
        }

      case 'test':
        // Send test email
        const testData = testEmailSchema.parse(body);
        
        try {
          const success = await NotificationService.sendTestEmail(
            testData.email,
            tenant.tenant
          );
          
          if (!success) {
            throw new Error('Email service is temporarily unavailable. Please try again later.');
          }

          return {
            message: `Test email sent successfully to ${testData.email}`,
            timestamp: new Date().toISOString()
          };
        } catch (error) {
          console.error('Error sending test email:', error);
          throw new Error('Email service is temporarily unavailable. Please try again later.');
        }

      default:
        throw new Error('Invalid request. Please check your parameters and try again.');
    }
  }
});

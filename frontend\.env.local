
# Separate credentials for local development
DB_USER=postgres
DB_PASSWORD=admin

# AWS Configuration
AWS_REGION=ca-central-1


# AWS Amplify Configuration
NEXT_PUBLIC_AWS_REGION=ca-central-1
NEXT_PUBLIC_AWS_USER_POOLS_ID=ca-central-1_uwPuGUhLc
NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID=6fc4ks4poom3mqk5icavr7np1k

NEXT_PUBLIC_AWS_COGNITO_DOMAIN=auth.renewtrack.com
NEXT_PUBLIC_REDIRECT_SIGN_IN=http://localhost:3000/callback
NEXT_PUBLIC_REDIRECT_SIGN_OUT=http://localhost:3000/signout

# Session Management Configuration
# Cookie expiration (in seconds) - 7 days = 604800 seconds
SESSION_COOKIE_MAX_AGE=604800
# Token refresh threshold (in seconds) - refresh when token expires in 5 minutes
TOKEN_REFRESH_THRESHOLD=300
# Enable automatic token refresh
AUTO_REFRESH_TOKENS=true

# JWT Configuration
JWT_EXPIRATION=604800

# Logging Configuration
NEXT_PUBLIC_LOG_LEVEL=ERROR
NEXT_PUBLIC_ENABLE_LOGGING=false
NEXT_PUBLIC_SHOW_LOGS_IN_PRODUCTION=false
NEXT_PUBLIC_LOG_CATEGORIES=

# Performance Configuration
API_TIMEOUT=30000
DATABASE_TIMEOUT=10000

# Feature Flags
ENABLE_DEBUG_MODE=false
ENABLE_PERFORMANCE_MONITORING=false
ENABLE_ERROR_TRACKING=true

# Security Configuration
ENCRYPTION_ALGORITHM=aes-256-gcm

# Database Configuration
# For production RDS (uncomment when using RDS)
# DATABASE_URL='postgresql://postgres:<EMAIL>:5432/renewtrack'
# DATABASE_SSL='true'

# For local development (currently active)
DATABASE_URL='postgresql://postgres:admin@127.0.0.1:5432/Renewtrack'
DATABASE_SSL='false'

DB_HOST='127.0.0.1'
DB_NAME='Renewtrack'
DB_PORT=5432

# Database Authentication
USE_IAM_DB_AUTH=false

# JWT and Security Configuration
JWT_SECRET=dev-jwt-secret-change-in-production-12345678901234567890
ENCRYPTION_KEY=dev-encryption-key-change-in-production-12345678901234567890

# Application URLs
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_API_URL=http://localhost:3000/api

# Cron and Background Jobs
CRON_SECRET=dev-cron-secret-change-in-production

# Email Configuration
EMAIL_FROM=<EMAIL>
EMAIL_REPLY_TO=<EMAIL>

# Rate Limiting
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX_REQUESTS=100

# Analytics and Monitoring
NEXT_PUBLIC_ENABLE_ANALYTICS=false
NEXT_PUBLIC_ANALYTICS_ID=

# Additional JWT Configuration
REFRESH_TOKEN_EXPIRATION=604800

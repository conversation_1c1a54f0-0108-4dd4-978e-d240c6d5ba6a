import { getClientByEmailDomain } from '@/lib/tenant/clients';
import { NextRequest } from 'next/server';
import {
  createSuccessResponse,
  createErrorResponse,
  ApiErrorCode,
  HttpStatus,
  withErrorHandling
} from '@/lib/api/response';

export const GET = withErrorHandling(async (request: NextRequest) => {
  try {
    // Get email from query parameter instead of server-side Amplify calls
    const { searchParams } = new URL(request.url);
    const email = searchParams.get('email');

    if (!email) {
      return createErrorResponse(
        'Email parameter is required',
        ApiErrorCode.VALIDATION_ERROR,
        HttpStatus.BAD_REQUEST
      );
    }

    // Get client by email domain
    const result = await getClientByEmailDomain(email);

    if (!result.success) {
      const statusCode = result.errorCode === 'NOT_FOUND' ? HttpStatus.NOT_FOUND : HttpStatus.INTERNAL_SERVER_ERROR;
      const apiErrorCode = result.errorCode === 'NOT_FOUND' ? ApiErrorCode.NOT_FOUND : ApiErrorCode.DATABASE_ERROR;

      return createErrorResponse(
        result.error || 'Failed to fetch client',
        apiErrorCode,
        statusCode
      );
    }

    // Return tenant context
    return createSuccessResponse({
      client: result.client
    }, 'Client information retrieved successfully');
  } catch (error) {
    console.error('Error in clients/domain API:', error);
    return createErrorResponse(
      'Internal server error',
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
});






/**
 * Vendor Synchronization Processor
 * 
 * Handles the synchronization of vendor data between global canonical tables
 * and tenant-specific tables using sophisticated matching algorithms.
 * 
 * Matching Algorithm Priority:
 * 1. Exact Tax ID match (95% confidence)
 * 2. Domain + Name match (90% confidence)  
 * 3. Fuzzy Name + Address match (70-85% confidence)
 * 
 * Auto-match threshold: >85%
 * Manual review: 50-85%
 * Reject: <50%
 */

import { Pool, PoolClient } from 'pg'
import { Logger } from '../../Logger'
import { VendorMatcher } from '../matchers/VendorMatcher'
import { SyncOptions, SyncResult } from '../SyncEngine'

export interface TenantVendor {
  id: string
  name: string
  taxId?: string
  domain?: string
  address?: string
  city?: string
  state?: string
  country?: string
  contactEmail?: string
  contactPhone?: string
  website?: string
  createdAt: Date
  updatedAt: Date
}

export interface GlobalVendor {
  id: string
  name: string
  taxId?: string
  domain?: string
  address?: string
  city?: string
  state?: string
  country?: string
  website?: string
  confidence: number
  sourceCount: number
  lastUpdated: Date
}

export interface VendorMatch {
  tenantVendorId: string
  globalVendorId: string
  confidence: number
  matchType: 'tax_id' | 'domain_name' | 'fuzzy_name_address'
  matchDetails: Record<string, any>
}

export class VendorSyncProcessor {
  private db: Pool
  private logger: Logger
  private matcher: VendorMatcher

  constructor(db: Pool, logger: Logger) {
    this.db = db
    this.logger = logger
    this.matcher = new VendorMatcher(logger)
  }

  /**
   * Process vendor synchronization for a tenant
   */
  async process(tenantId: string, batchId: string, options: SyncOptions = {}): Promise<Omit<SyncResult, 'batchId' | 'processingTimeMs'>> {
    const {
      batchSize = 100,
      maxRetries = 3,
      autoResolveThreshold = 85,
      dryRun = false
    } = options

    const client = await this.db.connect()
    
    try {
      // Get tenant schema name
      const tenantSchema = `tenant_${tenantId.padStart(16, '0')}`
      
      // Get all tenant vendors that need synchronization
      const tenantVendors = await this.getTenantVendors(client, tenantSchema)
      
      this.logger.info(`Processing ${tenantVendors.length} vendors for tenant ${tenantId}`, { batchId })
      
      let totalProcessed = 0
      let matched = 0
      let conflicts = 0
      const errors: string[] = []
      
      // Process in batches
      for (let i = 0; i < tenantVendors.length; i += batchSize) {
        const batch = tenantVendors.slice(i, i + batchSize)
        
        for (const tenantVendor of batch) {
          try {
            const result = await this.processVendor(
              client,
              tenantId,
              tenantSchema,
              tenantVendor,
              batchId,
              autoResolveThreshold,
              dryRun
            )
            
            totalProcessed++
            if (result.matched) matched++
            if (result.conflict) conflicts++
            
          } catch (error) {
            const errorMsg = `Failed to process vendor ${tenantVendor.id}: ${error instanceof Error ? error.message : 'Unknown error'}`
            errors.push(errorMsg)
            this.logger.error(errorMsg, { batchId, tenantVendorId: tenantVendor.id })
          }
        }
        
        // Update batch progress
        await this.updateBatchProgress(client, batchId, totalProcessed, matched, conflicts)
      }
      
      return {
        success: errors.length === 0,
        totalProcessed,
        matched,
        conflicts,
        errors
      }
      
    } finally {
      client.release()
    }
  }

  /**
   * Process a single vendor
   */
  private async processVendor(
    client: PoolClient,
    tenantId: string,
    tenantSchema: string,
    tenantVendor: TenantVendor,
    batchId: string,
    autoResolveThreshold: number,
    dryRun: boolean
  ): Promise<{ matched: boolean; conflict: boolean }> {
    
    // Check if vendor is already synchronized
    const existingSync = await this.getExistingSync(client, tenantSchema, tenantVendor.id)
    if (existingSync) {
      this.logger.debug(`Vendor ${tenantVendor.id} already synchronized`, { globalVendorId: existingSync.globalVendorId })
      return { matched: true, conflict: false }
    }
    
    // Find potential matches in global vendors
    const globalVendors = await this.getGlobalVendors(client)
    const matches = await this.matcher.findMatches(tenantVendor, globalVendors)
    
    if (matches.length === 0) {
      // No matches found - create new global vendor
      if (!dryRun) {
        const globalVendorId = await this.createGlobalVendor(client, tenantVendor)
        await this.createTenantVendorSync(client, tenantSchema, tenantVendor.id, globalVendorId, 100, 'exact_match')
      }
      
      this.logger.info(`Created new global vendor for tenant vendor ${tenantVendor.id}`)
      return { matched: true, conflict: false }
    }
    
    // Get best match
    const bestMatch = matches[0]
    
    if (bestMatch.confidence >= autoResolveThreshold) {
      // Auto-resolve high confidence matches
      if (!dryRun) {
        await this.createTenantVendorSync(
          client,
          tenantSchema,
          tenantVendor.id,
          bestMatch.globalVendorId,
          bestMatch.confidence,
          bestMatch.matchType
        )
        
        // Update global vendor with additional data
        await this.updateGlobalVendor(client, bestMatch.globalVendorId, tenantVendor)
      }
      
      this.logger.info(`Auto-matched vendor ${tenantVendor.id} to global vendor ${bestMatch.globalVendorId}`, {
        confidence: bestMatch.confidence,
        matchType: bestMatch.matchType
      })
      
      return { matched: true, conflict: false }
    } else {
      // Create conflict for manual review
      if (!dryRun) {
        await this.createSyncConflict(client, batchId, tenantVendor, matches)
      }
      
      this.logger.info(`Created conflict for vendor ${tenantVendor.id}`, {
        matchCount: matches.length,
        bestConfidence: bestMatch.confidence
      })
      
      return { matched: false, conflict: true }
    }
  }

  /**
   * Get tenant vendors that need synchronization
   */
  private async getTenantVendors(client: PoolClient, tenantSchema: string): Promise<TenantVendor[]> {
    const result = await client.query(`
      SELECT tv.* 
      FROM ${tenantSchema}.tenant_vendors tv
      LEFT JOIN ${tenantSchema}.tenant_vendor_sync tvs ON tv.id = tvs.tenant_vendor_id
      WHERE tvs.tenant_vendor_id IS NULL
      ORDER BY tv.created_on
    `)
    
    return result.rows
  }

  /**
   * Get all global vendors for matching
   */
  private async getGlobalVendors(client: PoolClient): Promise<GlobalVendor[]> {
    const result = await client.query(`
      SELECT * FROM metadata.global_vendors
      ORDER BY confidence DESC, source_count DESC
    `)
    
    return result.rows
  }

  /**
   * Check if vendor is already synchronized
   */
  private async getExistingSync(client: PoolClient, tenantSchema: string, tenantVendorId: string) {
    const result = await client.query(`
      SELECT * FROM ${tenantSchema}.tenant_vendor_sync
      WHERE tenant_vendor_id = $1
    `, [tenantVendorId])
    
    return result.rows[0] || null
  }

  /**
   * Create new global vendor
   */
  private async createGlobalVendor(client: PoolClient, tenantVendor: TenantVendor): Promise<string> {
    const result = await client.query(`
      INSERT INTO metadata.global_vendors (
        name, tax_id, domain, address, city, state, country, website,
        confidence, source_count, last_updated
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, 100, 1, NOW())
      RETURNING id
    `, [
      tenantVendor.name,
      tenantVendor.taxId,
      tenantVendor.domain,
      tenantVendor.address,
      tenantVendor.city,
      tenantVendor.state,
      tenantVendor.country,
      tenantVendor.website
    ])
    
    return result.rows[0].id
  }

  /**
   * Create tenant vendor sync record
   */
  private async createTenantVendorSync(
    client: PoolClient,
    tenantSchema: string,
    tenantVendorId: string,
    globalVendorId: string,
    confidence: number,
    matchType: string
  ): Promise<void> {
    await client.query(`
      INSERT INTO ${tenantSchema}.tenant_vendor_sync (
        tenant_vendor_id, global_vendor_id, confidence, match_type,
        sync_status, created_on, changed_on
      ) VALUES ($1, $2, $3, $4, 'synced', NOW(), NOW())
    `, [tenantVendorId, globalVendorId, confidence, matchType])
  }

  /**
   * Update global vendor with additional data
   */
  private async updateGlobalVendor(client: PoolClient, globalVendorId: string, tenantVendor: TenantVendor): Promise<void> {
    await client.query(`
      UPDATE metadata.global_vendors 
      SET source_count = source_count + 1,
          last_updated = NOW(),
          -- Update fields if they're empty in global but present in tenant
          tax_id = COALESCE(tax_id, $2),
          domain = COALESCE(domain, $3),
          address = COALESCE(address, $4),
          city = COALESCE(city, $5),
          state = COALESCE(state, $6),
          country = COALESCE(country, $7),
          website = COALESCE(website, $8)
      WHERE id = $1
    `, [
      globalVendorId,
      tenantVendor.taxId,
      tenantVendor.domain,
      tenantVendor.address,
      tenantVendor.city,
      tenantVendor.state,
      tenantVendor.country,
      tenantVendor.website
    ])
  }

  /**
   * Create sync conflict for manual review
   */
  private async createSyncConflict(
    client: PoolClient,
    batchId: string,
    tenantVendor: TenantVendor,
    matches: VendorMatch[]
  ): Promise<void> {
    await client.query(`
      INSERT INTO metadata.sync_conflicts (
        batch_id, entity_type, entity_id, conflict_data, status, created_on
      ) VALUES ($1, 'vendor', $2, $3, 'pending', NOW())
    `, [
      batchId,
      tenantVendor.id,
      JSON.stringify({
        tenantVendor,
        potentialMatches: matches
      })
    ])
  }

  /**
   * Update batch progress
   */
  private async updateBatchProgress(
    client: PoolClient,
    batchId: string,
    processed: number,
    matched: number,
    conflicts: number
  ): Promise<void> {
    await client.query(`
      UPDATE metadata.sync_batches 
      SET processed_records = $2,
          matched_records = $3,
          conflict_records = $4,
          changed_on = NOW()
      WHERE id = $1
    `, [batchId, processed, matched, conflicts])
  }
}

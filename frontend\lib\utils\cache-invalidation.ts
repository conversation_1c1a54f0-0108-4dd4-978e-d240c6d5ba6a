/**
 * Cache Invalidation Utilities
 * 
 * Provides utilities to invalidate related cache entries when data changes.
 * This helps maintain data consistency across components.
 */

import { dataCacheService } from '@/lib/services/data-cache-service';

/**
 * Cache invalidation patterns for different data types
 */
export const CACHE_TAGS = {
  // User-related data
  USER: 'user',
  USER_PROFILE: 'user-profile',
  USER_PREFERENCES: 'user-preferences',
  
  // Tenant-related data
  TENANT: 'tenant',
  TENANT_USERS: 'tenant-users',
  TENANT_SETTINGS: 'tenant-settings',
  
  // Renewals data
  RENEWALS: 'renewals',
  RENEWAL_FILTERS: 'renewal-filters',
  RENEWAL_STATS: 'renewal-stats',
  
  // Overview/Dashboard data
  OVERVIEW: 'overview',
  DASHBOARD_STATS: 'dashboard-stats',
  
  // Admin/Pages data
  ADMIN_PAGES: 'admin-pages',
  SIDEBAR_PAGES: 'sidebar-pages',
  
  // Vendor data
  VENDORS: 'vendors',
  VENDOR_PRODUCTS: 'vendor-products',
  
  // License data
  LICENSES: 'licenses',
  LICENSE_USAGE: 'license-usage',
} as const;

/**
 * Invalidation rules - when one type of data changes, what else should be invalidated
 */
const INVALIDATION_RULES: Record<string, string[]> = {
  [CACHE_TAGS.USER]: [CACHE_TAGS.USER_PROFILE, CACHE_TAGS.TENANT],
  [CACHE_TAGS.TENANT]: [CACHE_TAGS.TENANT_USERS, CACHE_TAGS.RENEWALS, CACHE_TAGS.OVERVIEW],
  [CACHE_TAGS.RENEWALS]: [CACHE_TAGS.RENEWAL_STATS, CACHE_TAGS.OVERVIEW, CACHE_TAGS.DASHBOARD_STATS],
  [CACHE_TAGS.ADMIN_PAGES]: [CACHE_TAGS.SIDEBAR_PAGES],
  [CACHE_TAGS.VENDORS]: [CACHE_TAGS.VENDOR_PRODUCTS, CACHE_TAGS.RENEWALS],
  [CACHE_TAGS.LICENSES]: [CACHE_TAGS.LICENSE_USAGE],
};

/**
 * Invalidate cache entries by tags with cascading invalidation
 */
export function invalidateByTags(tags: string[]): void {
  const allTagsToInvalidate = new Set(tags);
  
  // Add cascading invalidations
  for (const tag of tags) {
    const relatedTags = INVALIDATION_RULES[tag] || [];
    relatedTags.forEach(relatedTag => allTagsToInvalidate.add(relatedTag));
  }
  
  const finalTags = Array.from(allTagsToInvalidate);
  console.log('🗑️ [CACHE] Invalidating tags:', finalTags);
  
  dataCacheService.invalidateByTags(finalTags);
}

/**
 * Invalidate user-related data
 */
export function invalidateUserData(): void {
  invalidateByTags([CACHE_TAGS.USER]);
}

/**
 * Invalidate tenant-related data
 */
export function invalidateTenantData(): void {
  invalidateByTags([CACHE_TAGS.TENANT]);
}

/**
 * Invalidate renewals-related data
 */
export function invalidateRenewalsData(): void {
  invalidateByTags([CACHE_TAGS.RENEWALS]);
}

/**
 * Invalidate overview/dashboard data
 */
export function invalidateOverviewData(): void {
  invalidateByTags([CACHE_TAGS.OVERVIEW]);
}

/**
 * Invalidate admin pages data
 */
export function invalidateAdminPagesData(): void {
  invalidateByTags([CACHE_TAGS.ADMIN_PAGES]);
}

/**
 * Invalidate vendor-related data
 */
export function invalidateVendorData(): void {
  invalidateByTags([CACHE_TAGS.VENDORS]);
}

/**
 * Invalidate license-related data
 */
export function invalidateLicenseData(): void {
  invalidateByTags([CACHE_TAGS.LICENSES]);
}

/**
 * Get cache statistics for debugging
 */
export function getCacheStats() {
  const stats = dataCacheService.getStats();
  const hitRate = dataCacheService.getHitRate();
  
  return {
    ...stats,
    hitRate: Math.round(hitRate * 100),
    keys: dataCacheService.getKeys(),
  };
}

/**
 * Clear all cache data
 */
export function clearAllCache(): void {
  console.log('🗑️ [CACHE] Clearing all cache data');
  dataCacheService.clear();
}

/**
 * Hook to get cache statistics (for debugging/monitoring)
 */
export function useCacheStats() {
  return getCacheStats();
}

/**
 * Utility to create cache keys with consistent formatting
 */
export function createCacheKey(prefix: string, ...parts: (string | number)[]): string {
  return [prefix, ...parts.filter(Boolean)].join('-');
}

/**
 * Utility to create tenant-specific cache keys
 */
export function createTenantCacheKey(tenantId: string, prefix: string, ...parts: (string | number)[]): string {
  return createCacheKey(prefix, tenantId, ...parts);
}

/**
 * Utility to create user-specific cache keys
 */
export function createUserCacheKey(userId: string, prefix: string, ...parts: (string | number)[]): string {
  return createCacheKey(prefix, userId, ...parts);
}

// Export cache tags for use in components
export { CACHE_TAGS as CacheTags };

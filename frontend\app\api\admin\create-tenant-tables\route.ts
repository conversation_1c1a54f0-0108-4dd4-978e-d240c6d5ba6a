/**
 * Admin API endpoint to create tenant-specific master data tables
 */

import { NextRequest } from 'next/server';
import { executeQuery } from '@/lib/database';
import { resolveTenantContext } from '@/lib/tenant/context';
import { requireSuperAdmin } from '@/lib/api/auth-middleware';
import {
  createSuccessResponse,
  createErrorResponse,
  ApiErrorCode,
  HttpStatus
} from '@/lib/api/response';

export async function POST(request: NextRequest) {
  try {
    console.log('Starting tenant tables creation...');

    // Require super admin authentication
    const authResult = await requireSuperAdmin(request);
    if (!authResult.success) {
      return authResult.response;
    }

    // Get tenant context from session
    const tenantResult = await resolveTenantContext(authResult.session);
    if (!tenantResult.success) {
      return tenantResult.response!;
    }
    const tenantContext = tenantResult.tenant!;

    if (!tenantContext) {
      return createErrorResponse(
        'Unable to determine tenant context',
        ApiErrorCode.TENANT_NOT_FOUND,
        HttpStatus.BAD_REQUEST
      );
    }

    const tenantSchema = tenantContext.tenantSchema;
    console.log(`Creating tables for tenant schema: ${tenantSchema}`);

    // Create enum type for tenant schema
    await executeQuery(`
      DO $$ BEGIN
          CREATE TYPE "${tenantSchema}".sync_status AS ENUM ('pending', 'matched', 'manual_review', 'rejected');
      EXCEPTION
          WHEN duplicate_object THEN null;
      END $$;
    `);
    console.log('✓ Tenant sync_status enum created');
    
    // Create tenant_vendors table
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS "${tenantSchema}".tenant_vendors (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          name VARCHAR(255) NOT NULL,
          display_name VARCHAR(255),
          contact_email VARCHAR(255),
          phone VARCHAR(50),
          address_line1 VARCHAR(255),
          address_line2 VARCHAR(255),
          city VARCHAR(100),
          state VARCHAR(100),
          postal_code VARCHAR(20),
          country VARCHAR(100),
          tax_id VARCHAR(100),
          website VARCHAR(255),
          notes TEXT,
          custom_fields JSONB DEFAULT '{}',
          global_vendor_id UUID,
          sync_status "${tenantSchema}".sync_status DEFAULT 'pending',
          sync_confidence DECIMAL(5,4) DEFAULT 0.0000 CHECK (sync_confidence >= 0 AND sync_confidence <= 1),
          last_sync_attempt TIMESTAMP,
          created_by UUID,
          changed_by UUID,
          created_on TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          changed_on TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          CONSTRAINT tenant_vendors_name_not_empty CHECK (LENGTH(TRIM(name)) > 0)
      )
    `);
    console.log('✓ tenant_vendors table created');

    // Create tenant_products table
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS "${tenantSchema}".tenant_products (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          vendor_id UUID NOT NULL REFERENCES "${tenantSchema}".tenant_vendors(id),
          name VARCHAR(255) NOT NULL,
          description TEXT,
          category VARCHAR(100),
          sku VARCHAR(100),
          barcode VARCHAR(50),
          unit_of_measure VARCHAR(50),
          custom_fields JSONB DEFAULT '{}',
          global_product_id UUID,
          sync_status "${tenantSchema}".sync_status DEFAULT 'pending',
          sync_confidence DECIMAL(5,4) DEFAULT 0.0000 CHECK (sync_confidence >= 0 AND sync_confidence <= 1),
          last_sync_attempt TIMESTAMP,
          created_by UUID,
          changed_by UUID,
          created_on TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          changed_on TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          CONSTRAINT tenant_products_name_not_empty CHECK (LENGTH(TRIM(name)) > 0)
      )
    `);
    console.log('✓ tenant_products table created');
    
    // Create tenant_product_versions table
    await executeQuery(`
      CREATE TABLE IF NOT EXISTS "${tenantSchema}".tenant_product_versions (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          product_id UUID NOT NULL REFERENCES "${tenantSchema}".tenant_products(id),
          version VARCHAR(50) NOT NULL,
          release_date DATE,
          notes TEXT,
          is_current BOOLEAN DEFAULT false,
          custom_fields JSONB DEFAULT '{}',
          global_product_version_id UUID,
          sync_status "${tenantSchema}".sync_status DEFAULT 'pending',
          sync_confidence DECIMAL(5,4) DEFAULT 0.0000 CHECK (sync_confidence >= 0 AND sync_confidence <= 1),
          last_sync_attempt TIMESTAMP,
          created_by UUID,
          changed_by UUID,
          created_on TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          changed_on TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          CONSTRAINT tenant_product_versions_version_not_empty CHECK (LENGTH(TRIM(version)) > 0),
          CONSTRAINT tenant_product_versions_product_version_unique UNIQUE (product_id, version)
      )
    `);
    console.log('✓ tenant_product_versions table created');

    // Create indexes for tenant tables
    await executeQuery(`CREATE INDEX IF NOT EXISTS idx_tenant_vendors_name ON "${tenantSchema}".tenant_vendors(name)`);
    await executeQuery(`CREATE INDEX IF NOT EXISTS idx_tenant_vendors_global_id ON "${tenantSchema}".tenant_vendors(global_vendor_id)`);
    await executeQuery(`CREATE INDEX IF NOT EXISTS idx_tenant_vendors_sync_status ON "${tenantSchema}".tenant_vendors(sync_status)`);

    await executeQuery(`CREATE INDEX IF NOT EXISTS idx_tenant_products_vendor_id ON "${tenantSchema}".tenant_products(vendor_id)`);
    await executeQuery(`CREATE INDEX IF NOT EXISTS idx_tenant_products_name ON "${tenantSchema}".tenant_products(name)`);
    await executeQuery(`CREATE INDEX IF NOT EXISTS idx_tenant_products_global_id ON "${tenantSchema}".tenant_products(global_product_id)`);

    await executeQuery(`CREATE INDEX IF NOT EXISTS idx_tenant_product_versions_product_id ON "${tenantSchema}".tenant_product_versions(product_id)`);
    await executeQuery(`CREATE INDEX IF NOT EXISTS idx_tenant_product_versions_global_id ON "${tenantSchema}".tenant_product_versions(global_product_version_id)`);
    console.log('✓ Tenant table indexes created');
    
    // Create update triggers for changed_on timestamps
    await executeQuery(`
      CREATE OR REPLACE FUNCTION "${tenantSchema}".update_updated_at_column()
      RETURNS TRIGGER AS $$
      BEGIN
          NEW.changed_on = CURRENT_TIMESTAMP;
          RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);

    await executeQuery(`
      DROP TRIGGER IF EXISTS update_tenant_vendors_updated_at ON "${tenantSchema}".tenant_vendors;
      CREATE TRIGGER update_tenant_vendors_updated_at
          BEFORE UPDATE ON "${tenantSchema}".tenant_vendors
          FOR EACH ROW
          EXECUTE FUNCTION "${tenantSchema}".update_updated_at_column();
    `);

    await executeQuery(`
      DROP TRIGGER IF EXISTS update_tenant_products_updated_at ON "${tenantSchema}".tenant_products;
      CREATE TRIGGER update_tenant_products_updated_at
          BEFORE UPDATE ON "${tenantSchema}".tenant_products
          FOR EACH ROW
          EXECUTE FUNCTION "${tenantSchema}".update_updated_at_column();
    `);

    await executeQuery(`
      DROP TRIGGER IF EXISTS update_tenant_product_versions_updated_at ON "${tenantSchema}".tenant_product_versions;
      CREATE TRIGGER update_tenant_product_versions_updated_at
          BEFORE UPDATE ON "${tenantSchema}".tenant_product_versions
          FOR EACH ROW
          EXECUTE FUNCTION "${tenantSchema}".update_updated_at_column();
    `);
    console.log('✓ Update triggers created');
    
    console.log('✅ All tenant-specific tables created successfully!');
    
    return createSuccessResponse(
      { message: 'Tenant tables created successfully' },
      'Tenant-specific master data tables have been created'
    );
    
  } catch (error) {
    console.error('❌ Error creating tenant tables:', error);
    return createErrorResponse(
      'Failed to create tenant tables',
      ApiErrorCode.DATABASE_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
}

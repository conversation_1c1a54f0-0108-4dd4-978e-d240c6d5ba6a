/**
 * Debug Sidebar API
 * 
 * This endpoint helps debug why the sidebar is not showing records
 */

import { NextRequest } from 'next/server'
import { fetchAuthSession } from 'aws-amplify/auth'
import { createSuccessResponse, createErrorResponse, ApiErrorCode, HttpStatus } from '@/lib/api/response'
import { databaseService } from '@/lib/services/database-service'

export async function GET(request: NextRequest) {
  try {
    console.log('[DEBUG-SIDEBAR] Starting debug...')
    
    // Get authentication session
    const session = await fetchAuthSession()
    if (!session?.tokens?.idToken) {
      return createErrorResponse('Authentication required', ApiErrorCode.UNAUTHORIZED, HttpStatus.UNAUTHORIZED)
    }

    // Extract user groups and email from token
    const idToken = session.tokens.idToken
    const payload = idToken.payload as any

    let userGroups: string[] = []
    const userEmail = payload.email || payload['cognito:username'] || ''

    // Check various possible locations for groups in the token
    if (payload['cognito:groups']) {
      userGroups = Array.isArray(payload['cognito:groups'])
        ? payload['cognito:groups']
        : [payload['cognito:groups']]
    } else if (payload.groups) {
      userGroups = Array.isArray(payload.groups)
        ? payload.groups
        : [payload.groups]
    }

    // Normalize groups to lowercase
    const normalizedGroups = userGroups
      .filter(group => typeof group === 'string')
      .map(group => group.toLowerCase().trim())

    console.log('[DEBUG-SIDEBAR] User groups:', normalizedGroups)
    console.log('[DEBUG-SIDEBAR] User email:', userEmail)

    // Extract client domain from email
    const clientDomain = userEmail.includes('@') ? userEmail.split('@')[1] : ''
    console.log('[DEBUG-SIDEBAR] Client domain:', clientDomain)

    const debugInfo: any = {
      userEmail,
      userGroups: normalizedGroups,
      clientDomain,
      steps: []
    }

    // Get database connection
    const db = databaseService

    // Step 1: Test basic database connection
    try {
      const testResult = await db.query('SELECT 1 as test')
      debugInfo.steps.push({
        step: 'Database Connection',
        success: true,
        result: testResult.rows
      })
    } catch (error) {
      debugInfo.steps.push({
        step: 'Database Connection',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      return createSuccessResponse(debugInfo)
    }

    // Step 2: Check clients table structure and data
    try {
      const clientsResult = await db.query(`
        SELECT id, name, domain, status
        FROM metadata.clients
        WHERE status = 'A'
      `)
      debugInfo.steps.push({
        step: 'Admin Clients Query',
        success: true,
        result: clientsResult.rows
      })
    } catch (error) {
      debugInfo.steps.push({
        step: 'Admin Clients Query',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }

    // Step 3: Check if client domain exists
    if (clientDomain) {
      try {
        const domainCheckResult = await db.query(`
          SELECT client_id, name, domain, status 
          FROM metadata.clients 
          WHERE domain = $1 AND status = 'A'
        `, [clientDomain])
        debugInfo.steps.push({
          step: 'Domain Check (VARCHAR)',
          success: true,
          result: domainCheckResult.rows
        })
      } catch (error) {
        debugInfo.steps.push({
          step: 'Domain Check (VARCHAR)',
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }

      // Try array version
      try {
        const domainArrayCheckResult = await db.query(`
          SELECT client_id, name, domain, status 
          FROM metadata.clients 
          WHERE $1 = ANY(domain) AND status = 'A'
        `, [clientDomain])
        debugInfo.steps.push({
          step: 'Domain Check (Array)',
          success: true,
          result: domainArrayCheckResult.rows
        })
      } catch (error) {
        debugInfo.steps.push({
          step: 'Domain Check (Array)',
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    // Step 4: Check admin_pages
    try {
      const pagesResult = await db.query(`
        SELECT id, name, header, display_order, route_path, sidebar, status
        FROM metadata.admin_pages 
        WHERE status = 'A' AND sidebar = true
        ORDER BY display_order
      `)
      debugInfo.steps.push({
        step: 'Admin Pages Query',
        success: true,
        result: pagesResult.rows
      })
    } catch (error) {
      debugInfo.steps.push({
        step: 'Admin Pages Query',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }

    // Step 5: Check admin_pages_groups
    try {
      const groupsResult = await db.query(`
        SELECT pg.page_id, pg.group_name, p.name as page_name
        FROM metadata.admin_pages_groups pg
        JOIN metadata.admin_pages p ON pg.page_id = p.id
        WHERE pg.group_name = ANY($1)
      `, [normalizedGroups])
      debugInfo.steps.push({
        step: 'Page Groups Query',
        success: true,
        result: groupsResult.rows
      })
    } catch (error) {
      debugInfo.steps.push({
        step: 'Page Groups Query',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }

    // Step 6: Check addon packages
    try {
      const packagesResult = await db.query(`
        SELECT ap.id, ap.name, ap.display_name, ap.status
        FROM metadata.addon_packages ap
        WHERE ap.status = 'A'
      `)
      debugInfo.steps.push({
        step: 'Addon Packages Query',
        success: true,
        result: packagesResult.rows
      })
    } catch (error) {
      debugInfo.steps.push({
        step: 'Addon Packages Query',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }

    return createSuccessResponse(debugInfo)

  } catch (error) {
    console.error('[DEBUG-SIDEBAR] Error:', error)
    return createErrorResponse(
      'Debug failed',
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR,
      error instanceof Error ? error.message : 'Unknown error'
    )
  }
}

/**
 * AddRenewalModal Component Tests
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import AddRenewalModal, { RenewalFormData, AlertFormData } from '@/components/modals/AddRenewalModal'

// Mock the step components
jest.mock('@/components/modals/steps/RenewalDetailsStep', () => {
  return function MockRenewalDetailsStep({ data, onChange, onNext, onCancel }: any) {
    return (
      <div data-testid="renewal-details-step">
        <input
          data-testid="product-name"
          value={data.product_name}
          onChange={(e) => onChange({ ...data, product_name: e.target.value })}
        />
        <button data-testid="next-button" onClick={onNext}>
          Next
        </button>
        <button data-testid="cancel-button" onClick={onCancel}>
          Cancel
        </button>
      </div>
    )
  }
})

jest.mock('@/components/modals/steps/SetupAlertsStep', () => {
  return function MockSetupAlertsStep({ data, onChange, onBack, onSubmit, isSubmitting }: any) {
    return (
      <div data-testid="setup-alerts-step">
        <input
          data-testid="days-before"
          type="number"
          value={data[0]?.daysBeforeRenewal || 30}
          onChange={(e) => {
            const newData = [...data]
            newData[0] = { ...newData[0], daysBeforeRenewal: parseInt(e.target.value) || 30 }
            onChange(newData)
          }}
        />
        <button data-testid="back-button" onClick={onBack}>
          Back
        </button>
        <button data-testid="submit-button" onClick={onSubmit} disabled={isSubmitting}>
          {isSubmitting ? 'Saving...' : 'Save & Finish'}
        </button>
      </div>
    )
  }
})

describe('AddRenewalModal', () => {
  const mockOnClose = jest.fn()
  const mockOnSubmit = jest.fn()

  const defaultProps = {
    isOpen: true,
    onClose: mockOnClose,
    onSubmit: mockOnSubmit
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders the modal when open', () => {
    render(<AddRenewalModal {...defaultProps} />)
    
    expect(screen.getByText('Add New Renewal')).toBeInTheDocument()
    expect(screen.getByText('Enter the details of the renewal you want to track.')).toBeInTheDocument()
  })

  it('does not render when closed', () => {
    render(<AddRenewalModal {...defaultProps} isOpen={false} />)
    
    expect(screen.queryByText('Add New Renewal')).not.toBeInTheDocument()
  })

  it('shows step 1 initially', () => {
    render(<AddRenewalModal {...defaultProps} />)
    
    expect(screen.getByTestId('renewal-details-step')).toBeInTheDocument()
    expect(screen.queryByTestId('setup-alerts-step')).not.toBeInTheDocument()
  })

  it('navigates to step 2 when next is clicked', async () => {
    const user = userEvent.setup()
    render(<AddRenewalModal {...defaultProps} />)
    
    const nextButton = screen.getByTestId('next-button')
    await user.click(nextButton)
    
    expect(screen.getByTestId('setup-alerts-step')).toBeInTheDocument()
    expect(screen.queryByTestId('renewal-details-step')).not.toBeInTheDocument()
    expect(screen.getByText('Edit Renewal')).toBeInTheDocument()
  })

  it('navigates back to step 1 from step 2', async () => {
    const user = userEvent.setup()
    render(<AddRenewalModal {...defaultProps} />)
    
    // Go to step 2
    await user.click(screen.getByTestId('next-button'))
    
    // Go back to step 1
    await user.click(screen.getByTestId('back-button'))
    
    expect(screen.getByTestId('renewal-details-step')).toBeInTheDocument()
    expect(screen.queryByTestId('setup-alerts-step')).not.toBeInTheDocument()
    expect(screen.getByText('Add New Renewal')).toBeInTheDocument()
  })

  it('calls onClose when cancel is clicked', async () => {
    const user = userEvent.setup()
    render(<AddRenewalModal {...defaultProps} />)
    
    await user.click(screen.getByTestId('cancel-button'))
    
    expect(mockOnClose).toHaveBeenCalledTimes(1)
  })

  it('calls onClose when close button is clicked', async () => {
    const user = userEvent.setup()
    render(<AddRenewalModal {...defaultProps} />)
    
    const closeButton = screen.getByLabelText('Close modal')
    await user.click(closeButton)
    
    expect(mockOnClose).toHaveBeenCalledTimes(1)
  })

  it('calls onSubmit with correct data when form is submitted', async () => {
    const user = userEvent.setup()
    mockOnSubmit.mockResolvedValue(undefined)
    
    render(<AddRenewalModal {...defaultProps} />)
    
    // Fill in some data in step 1
    const productNameInput = screen.getByTestId('product-name')
    await user.type(productNameInput, 'Test Product')
    
    // Go to step 2
    await user.click(screen.getByTestId('next-button'))
    
    // Modify alert data
    const daysBeforeInput = screen.getByTestId('days-before')
    fireEvent.change(daysBeforeInput, { target: { value: '60' } })
    
    // Submit
    await user.click(screen.getByTestId('submit-button'))
    
    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledTimes(1)
    })
    
    const [renewalData, alertsData] = mockOnSubmit.mock.calls[0]
    expect(renewalData.product_name).toBe('Test Product')
    expect(alertsData[0].daysBeforeRenewal).toBe(60)
  })

  it('shows loading state during submission', async () => {
    const user = userEvent.setup()
    mockOnSubmit.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 1000)))
    
    render(<AddRenewalModal {...defaultProps} />)
    
    // Go to step 2
    await user.click(screen.getByTestId('next-button'))
    
    // Submit
    await user.click(screen.getByTestId('submit-button'))
    
    expect(screen.getByText('Saving...')).toBeInTheDocument()
    expect(screen.getByTestId('submit-button')).toBeDisabled()
  })

  it('resets form data after successful submission', async () => {
    const user = userEvent.setup()
    mockOnSubmit.mockResolvedValue(undefined)
    
    render(<AddRenewalModal {...defaultProps} />)
    
    // Fill in data
    const productNameInput = screen.getByTestId('product-name')
    await user.type(productNameInput, 'Test Product')
    
    // Go to step 2 and submit
    await user.click(screen.getByTestId('next-button'))
    await user.click(screen.getByTestId('submit-button'))
    
    await waitFor(() => {
      expect(mockOnClose).toHaveBeenCalledTimes(1)
    })
  })

  it('handles submission errors gracefully', async () => {
    const user = userEvent.setup()
    const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {})
    mockOnSubmit.mockRejectedValue(new Error('Submission failed'))
    
    render(<AddRenewalModal {...defaultProps} />)
    
    // Go to step 2 and submit
    await user.click(screen.getByTestId('next-button'))
    await user.click(screen.getByTestId('submit-button'))
    
    await waitFor(() => {
      expect(consoleErrorSpy).toHaveBeenCalledWith('Error submitting renewal:', expect.any(Error))
    })
    
    // Modal should still be open
    expect(screen.getByTestId('setup-alerts-step')).toBeInTheDocument()
    
    consoleErrorSpy.mockRestore()
  })

  it('shows correct step indicators', async () => {
    const user = userEvent.setup()
    render(<AddRenewalModal {...defaultProps} />)
    
    // Step 1 should be active
    const step1 = screen.getByText('1')
    const step2 = screen.getByText('2')
    
    expect(step1.closest('.step')).toHaveClass('active')
    expect(step2.closest('.step')).not.toHaveClass('active')
    
    // Go to step 2
    await user.click(screen.getByTestId('next-button'))
    
    expect(step1.closest('.step')).toHaveClass('completed')
    expect(step2.closest('.step')).toHaveClass('active')
  })
})

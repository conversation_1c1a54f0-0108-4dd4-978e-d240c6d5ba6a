/**
 * Metadata API Endpoint
 *
 * Provides access to all metadata tables for form dropdowns
 * GET /api/metadata - Returns all metadata options (purchase types, renewal types, currencies)
 */

import { createApiRoute } from '@/lib/api/route-factory';
import { executeQuery } from '@/lib/database';

// Interfaces for metadata types
export interface PurchaseType {
  id: number; // Standardized to camelCase 'id' for API responses
  name: string;
  active: boolean;
  displayOrder?: number; // Standardized to camelCase
}

export interface RenewalType {
  id: number; // Standardized to camelCase 'id' for API responses
  name: string;
  active: boolean;
  displayOrder?: number; // Standardized to camelCase
}

export interface Currency {
  id: string; // Standardized to camelCase 'id' for API responses
  name: string;
  symbol: string;
  active: boolean;
  displayOrder: number; // Standardized to camelCase
}

export interface MetadataResponse {
  purchaseTypes: PurchaseType[];
  renewalTypes: RenewalType[];
  currencies: Currency[];
}

// GET /api/metadata - Get all metadata options
export const GET = createApiRoute('GET', {
  requireAuth: true,
  handler: async (context) => {
    // Note: All data should come from database. If metadata schema doesn't exist,
    // the database migration should be run to create the proper tables with default data.
    // Hardcoded fallbacks are removed to enforce proper database setup.

    // Try to execute all metadata queries in parallel for better performance
    let purchaseTypesResult, renewalTypesResult, currenciesResult;

    try {
      [purchaseTypesResult, renewalTypesResult, currenciesResult] = await Promise.all([
        // Purchase Types query - map database fields to API format
        executeQuery(
          `SELECT
            "purchase_type_id" as id,
            "name",
            "active",
            "display_order" as "displayOrder"
          FROM metadata."global_purchase_types"
          WHERE "active" = true
          ORDER BY "name" ASC`,
          []
        ),

        // Renewal Types query - map database fields to API format
        executeQuery(
          `SELECT
            "renewal_type_id" as id,
            "name",
            "active",
            "display_order" as "displayOrder"
          FROM metadata."global_renewal_types"
          WHERE "active" = true
          ORDER BY "name" ASC`,
          []
        ),

        // Currencies query - map database fields to API format
        executeQuery(
          `SELECT
            "currency_id" as id,
            "name",
            "symbol",
            "active",
            "display_order" as "displayOrder"
          FROM metadata."global_currencies"
          WHERE "active" = true
          ORDER BY "display_order" ASC, "name" ASC`,
          []
        )
      ]);
    } catch (dbError) {
      console.error('Metadata schema not available:', dbError);
      throw new Error('Metadata schema not found. Please run database migrations to set up metadata tables.');
    }

    // Extract data from successful queries, return empty arrays if queries fail
    const purchaseTypes = purchaseTypesResult.success ? purchaseTypesResult.data || [] : [];
    const renewalTypes = renewalTypesResult.success ? renewalTypesResult.data || [] : [];
    const currencies = currenciesResult.success ? currenciesResult.data || [] : [];

    if (!purchaseTypesResult.success) {
      console.log('Using default purchase types due to database error:', purchaseTypesResult.error);
    }
    if (!renewalTypesResult.success) {
      console.log('Using default renewal types due to database error:', renewalTypesResult.error);
    }
    if (!currenciesResult.success) {
      console.log('Using default currencies due to database error:', currenciesResult.error);
    }

    // Prepare response data
    const responseData: MetadataResponse = {
      purchaseTypes,
      renewalTypes,
      currencies
    };

    // Log successful fetch for monitoring
    console.log('Metadata fetched successfully:', {
      purchaseTypes: responseData.purchaseTypes.length,
      renewalTypes: responseData.renewalTypes.length,
      currencies: responseData.currencies.length
    });

    return responseData;
  }
});

// Types are already exported above in the interface definitions

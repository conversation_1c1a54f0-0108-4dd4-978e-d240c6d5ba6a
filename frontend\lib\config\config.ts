/**
 * Centralized Configuration Management
 * 
 * This module provides a secure, centralized way to manage all application configuration.
 * It validates environment variables and provides type-safe access to configuration values.
 */

import { z } from 'zod';
import { DATABASE, ENVIRONMENT } from '@/lib/constants/app-constants';
import {
  getEnvVar,
  getEnvNumber,
  getEnvBoolean,
  getDatabaseConfig as getEnvDatabaseConfig,
  getAWSConfig,
  isDevelopment,
  isProduction
} from './environment';
import { getRuntimeConfig } from './env-init';
import { getServerConfig, getClientSafeConfig } from './env-validator';

// Client-side environment validation schema (only NEXT_PUBLIC_ variables)
const clientEnvSchema = z.object({
  // AWS Configuration
  NEXT_PUBLIC_AWS_REGION: z.string().min(1, 'AWS region is required'),
  NEXT_PUBLIC_AWS_USER_POOLS_ID: z.string().min(1, 'User pool ID is required'),
  NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID: z.string().min(1, 'User pool client ID is required'),
  NEXT_PUBLIC_AWS_COGNITO_DOMAIN: z.string().min(1, 'Cognito domain is required'),

  // Redirect URLs
  NEXT_PUBLIC_REDIRECT_SIGN_IN: z.string().url('Invalid sign-in redirect URL'),
  NEXT_PUBLIC_REDIRECT_SIGN_OUT: z.string().url('Invalid sign-out redirect URL'),
});

// Server-side environment validation schema (includes all variables)
const serverEnvSchema = clientEnvSchema.extend({
  // Database Configuration (server-side only)
  DB_USER: z.string().optional(),
  DB_PASSWORD: z.string().optional(),
  DB_HOST: z.string().optional(),
  DB_PORT: z.string().optional(),
  DB_NAME: z.string().optional(),
  DATABASE_URL: z.string().optional(),
  DATABASE_SSL: z.string().optional(),

  // Environment
  NODE_ENV: z.enum([ENVIRONMENT.DEVELOPMENT, ENVIRONMENT.PRODUCTION, ENVIRONMENT.TEST]).default(ENVIRONMENT.DEVELOPMENT),
});

// Validate environment variables based on context
function validateEnv() {
  try {
    const isServer = typeof window === 'undefined';

    if (isServer) {
      // Server-side: validate all environment variables
      return serverEnvSchema.parse(process.env);
    } else {
      // Client-side: only validate NEXT_PUBLIC_ variables that should be available
      const clientEnv = {
        NEXT_PUBLIC_AWS_REGION: process.env.NEXT_PUBLIC_AWS_REGION,
        NEXT_PUBLIC_AWS_USER_POOLS_ID: process.env.NEXT_PUBLIC_AWS_USER_POOLS_ID,
        NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID: process.env.NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID,
        NEXT_PUBLIC_AWS_COGNITO_DOMAIN: process.env.NEXT_PUBLIC_AWS_COGNITO_DOMAIN,
        NEXT_PUBLIC_REDIRECT_SIGN_IN: process.env.NEXT_PUBLIC_REDIRECT_SIGN_IN,
        NEXT_PUBLIC_REDIRECT_SIGN_OUT: process.env.NEXT_PUBLIC_REDIRECT_SIGN_OUT,
      };

      return clientEnvSchema.parse(clientEnv);
    }
  } catch (error) {
    console.error('❌ Invalid environment configuration:', error);

    // If it's a Zod error, show detailed validation issues
    if (error instanceof z.ZodError) {
      console.error('Validation errors:');
      error.issues.forEach((issue) => {
        console.error(`  - ${issue.path.join('.')}: ${issue.message}`);
      });
    }

    throw new Error('Application configuration error. Please contact support.');
  }
}

// Separate caches for client and server
let _serverEnv: z.infer<typeof serverEnvSchema> | null = null;
let _clientEnv: z.infer<typeof clientEnvSchema> | null = null;

function getEnv() {
  const isServer = typeof window === 'undefined';

  if (isServer) {
    if (_serverEnv === null) {
      _serverEnv = validateEnv() as z.infer<typeof serverEnvSchema>;
    }
    return _serverEnv;
  } else {
    if (_clientEnv === null) {
      _clientEnv = validateEnv() as z.infer<typeof clientEnvSchema>;
    }
    return _clientEnv;
  }
}

// Client-safe configuration that doesn't require validation
const getClientConfig = () => {
  // Direct access to process.env for client-side NEXT_PUBLIC_ variables
  return {
    aws: {
      region: process.env.NEXT_PUBLIC_AWS_REGION || '',
      userPoolId: process.env.NEXT_PUBLIC_AWS_USER_POOLS_ID || '',
      userPoolClientId: process.env.NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID || '',
      cognitoDomain: process.env.NEXT_PUBLIC_AWS_COGNITO_DOMAIN || '',
    },
    auth: {
      redirectSignIn: process.env.NEXT_PUBLIC_REDIRECT_SIGN_IN || '',
      redirectSignOut: process.env.NEXT_PUBLIC_REDIRECT_SIGN_OUT || '',
    },
    app: {
      environment: process.env.NODE_ENV || 'development',
      isDevelopment: process.env.NODE_ENV === 'development',
      isProduction: process.env.NODE_ENV === 'production',
    },
  };
};

// Public configuration (safe to expose to client) - consistent between server and client
// Uses direct process.env access for NEXT_PUBLIC_ variables which are available on both sides
export const publicConfig = {
  aws: {
    region: process.env.NEXT_PUBLIC_AWS_REGION || '',
    userPoolId: process.env.NEXT_PUBLIC_AWS_USER_POOLS_ID || '',
    userPoolClientId: process.env.NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID || '',
    cognitoDomain: process.env.NEXT_PUBLIC_AWS_COGNITO_DOMAIN || '',
  },
  auth: {
    redirectSignIn: process.env.NEXT_PUBLIC_REDIRECT_SIGN_IN || '',
    redirectSignOut: process.env.NEXT_PUBLIC_REDIRECT_SIGN_OUT || '',
  },
  app: {
    environment: process.env.NODE_ENV || 'development',
    isDevelopment: process.env.NODE_ENV === 'development',
    isProduction: process.env.NODE_ENV === 'production',
  },
} as const;

// Server-side configuration (never expose to client) - using getters for lazy evaluation
export const serverConfig = {
  get database() {
    // Use centralized environment configuration
    return getEnvDatabaseConfig();
  },
} as const;

// Type exports for better TypeScript support
export type PublicConfig = typeof publicConfig;
export type ServerConfig = typeof serverConfig;

// Utility functions
export const getPublicConfig = () => publicConfig;
export const getAuthConfig = () => publicConfig.auth;
export const getAwsConfig = () => publicConfig.aws;
export const getServerDatabaseConfig = () => {
  if (typeof window !== 'undefined') {
    throw new Error('Database configuration is not available on the client side');
  }
  return serverConfig.database;
};

// Configuration validation for runtime checks
export const validateConfig = () => {
  // Only validate on server-side to avoid client-side hydration issues
  if (typeof window === 'undefined') {
    const requiredPublicVars = [
      'NEXT_PUBLIC_AWS_REGION',
      'NEXT_PUBLIC_AWS_USER_POOLS_ID',
      'NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID',
      'NEXT_PUBLIC_AWS_COGNITO_DOMAIN',
    ];

    const missing = requiredPublicVars.filter(key => !process.env[key]);

    if (missing.length > 0) {
      console.error(`Missing required environment variables: ${missing.join(', ')}`);
      // Don't throw error to prevent build failures, just log
      return false;
    }
  }

  return true;
};

// Export environment for backward compatibility (to be removed)
export const env = getEnv;

// Main config getter function
export const getConfig = () => ({
  ...publicConfig,
  server: typeof window === 'undefined' ? serverConfig : undefined
});

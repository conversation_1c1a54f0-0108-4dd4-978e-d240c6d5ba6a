# Tenant Logging System

## Overview

The Tenant Logging System provides comprehensive monitoring and auditing of all changes made to tenant data. It automatically tracks INSERT, UPDATE, and DELETE operations across all monitored tables and provides detailed analytics through the admin interface.

## Features

- **Automatic Change Tracking**: Monitors all changes to tenant tables
- **Business Impact Assessment**: Automatically calculates impact levels (low, medium, high, critical)
- **User Attribution**: Tracks which user made each change
- **Field-Level Tracking**: Records exactly which fields changed and their before/after values
- **Performance Optimized**: Includes indexes for fast querying
- **Admin Interface**: Web-based interface for viewing and analyzing logs

## Setup Instructions

### 1. Run the Setup Script

For each tenant schema, run the tenant logging setup script:

```sql
-- Replace 'tenant_0000000000000001' with your actual tenant schema
\i database/tenant-logging-setup.sql
```

### 2. Set User Context (Application Level)

Before making database changes, set the current user context:

```sql
-- Set user context for logging
SELECT set_config('app.current_user_id', 'user-uuid-here', false);
SELECT set_config('app.current_user_email', '<EMAIL>', false);
```

### 3. Access the Admin Interface

1. Log in as an admin user (must be in 'admin' Cognito group)
2. Navigate to **Admin > Tenant Logs** in the sidebar
3. Use filters to analyze specific changes

## Monitored Tables

The system automatically monitors these tables:

- **Renewals**: All renewal record changes
- **tenant_vendors**: Vendor management changes  
- **tenant_products**: Product catalog changes
- **tenant_product_versions**: Product version changes

## Business Impact Levels

- **Critical**: DELETE operations (data loss)
- **High**: Changes to cost, renewal dates, status fields
- **Medium**: Changes to other important fields
- **Low**: Minor field updates

## Admin Interface Features

### Filters
- **Table**: Filter by specific table
- **Operation**: Filter by INSERT/UPDATE/DELETE/SYSTEM
- **Business Impact**: Filter by impact level
- **User**: Filter by user email
- **Date Range**: Filter by time period

### Field Change Search
- Search for specific field changes
- Find records where fields changed from/to specific values
- Useful for tracking specific data modifications

### Summary Views
- **Activity Summary**: Aggregated statistics by table and operation
- **User Summary**: User activity analysis with impact metrics

## Database Queries

### Recent Changes
```sql
SELECT * FROM tenant_log 
ORDER BY timestamp DESC 
LIMIT 100;
```

### High Impact Changes
```sql
SELECT * FROM tenant_log 
WHERE business_impact IN ('high', 'critical')
ORDER BY timestamp DESC;
```

### User Activity
```sql
SELECT user_email, COUNT(*) as total_changes,
       COUNT(*) FILTER (WHERE business_impact = 'high') as high_impact_changes
FROM tenant_log 
WHERE user_email IS NOT NULL
GROUP BY user_email
ORDER BY total_changes DESC;
```

### Field Change Analysis
```sql
SELECT table_name, 
       unnest(changed_fields) as field_name,
       COUNT(*) as change_count
FROM tenant_log 
WHERE operation = 'UPDATE'
GROUP BY table_name, field_name
ORDER BY change_count DESC;
```

## Performance Considerations

- Logs are indexed for fast querying
- Old logs can be archived/purged as needed
- Consider partitioning for high-volume tenants
- Monitor log table size and implement retention policies

## Security

- Only admin users can access the logging interface
- User context is set at application level
- Sensitive data changes are tracked but not exposed in UI
- All access is logged and auditable

## Troubleshooting

### No Logs Appearing
1. Verify triggers are installed: `\d+ table_name`
2. Check user context is set before operations
3. Verify schema search path includes tenant schema

### Performance Issues
1. Check index usage: `EXPLAIN ANALYZE SELECT ...`
2. Consider log retention policies
3. Monitor log table size

### Missing User Information
1. Ensure `app.current_user_id` and `app.current_user_email` are set
2. Verify application sets context before database operations

## Maintenance

### Log Retention
Consider implementing log retention policies:

```sql
-- Delete logs older than 1 year
DELETE FROM tenant_log 
WHERE timestamp < NOW() - INTERVAL '1 year';
```

### Performance Monitoring
Monitor log table size and query performance regularly.

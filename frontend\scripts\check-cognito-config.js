#!/usr/bin/env node

/**
 * Check Cognito User Pool Configuration
 * 
 * This script checks the current Cognito User Pool settings including
 * token expiration times to verify they match the 7-day requirement.
 */

const { CognitoIdentityProviderClient, DescribeUserPoolCommand, DescribeUserPoolClientCommand } = require('@aws-sdk/client-cognito-identity-provider');

async function checkCognitoConfig() {
  console.log('🔍 Checking Cognito User Pool Configuration...\n');

  try {
    // Get configuration from environment
    const userPoolId = process.env.NEXT_PUBLIC_AWS_USER_POOLS_ID || 'ca-central-1_uwPuGUhLc';
    const clientId = process.env.NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID || '6fc4ks4poom3mqk5icavr7np1k';
    const region = process.env.NEXT_PUBLIC_AWS_REGION || 'ca-central-1';

    console.log(`User Pool ID: ${userPoolId}`);
    console.log(`Client ID: ${clientId}`);
    console.log(`Region: ${region}\n`);

    // Initialize Cognito client
    const cognitoClient = new CognitoIdentityProviderClient({ 
      region: region,
      // Use default credential provider chain (environment variables, IAM roles, etc.)
    });

    // Get User Pool details
    console.log('📋 User Pool Configuration:');
    console.log('=' .repeat(50));
    
    try {
      const userPoolCommand = new DescribeUserPoolCommand({
        UserPoolId: userPoolId
      });
      
      const userPoolResponse = await cognitoClient.send(userPoolCommand);
      const userPool = userPoolResponse.UserPool;

      console.log(`Name: ${userPool.Name}`);
      console.log(`Status: ${userPool.Status}`);
      console.log(`Creation Date: ${userPool.CreationDate}`);
      console.log(`Last Modified: ${userPool.LastModifiedDate}`);
      
      if (userPool.Policies?.PasswordPolicy) {
        const policy = userPool.Policies.PasswordPolicy;
        console.log('\n🔐 Password Policy:');
        console.log(`  Minimum Length: ${policy.MinimumLength}`);
        console.log(`  Require Uppercase: ${policy.RequireUppercase}`);
        console.log(`  Require Lowercase: ${policy.RequireLowercase}`);
        console.log(`  Require Numbers: ${policy.RequireNumbers}`);
        console.log(`  Require Symbols: ${policy.RequireSymbols}`);
      }

    } catch (error) {
      console.error(`❌ Error getting User Pool details: ${error.message}`);
    }

    // Get User Pool Client details (this contains token expiration settings)
    console.log('\n📱 User Pool Client Configuration:');
    console.log('=' .repeat(50));
    
    try {
      const clientCommand = new DescribeUserPoolClientCommand({
        UserPoolId: userPoolId,
        ClientId: clientId
      });
      
      const clientResponse = await cognitoClient.send(clientCommand);
      const client = clientResponse.UserPoolClient;

      console.log(`Client Name: ${client.ClientName}`);
      console.log(`Client ID: ${client.ClientId}`);
      
      // Token expiration settings
      console.log('\n⏰ Token Expiration Settings:');
      if (client.AccessTokenValidity) {
        console.log(`  Access Token Validity: ${client.AccessTokenValidity} ${client.TokenValidityUnits?.AccessToken || 'hours'}`);
      }
      if (client.IdTokenValidity) {
        console.log(`  ID Token Validity: ${client.IdTokenValidity} ${client.TokenValidityUnits?.IdToken || 'hours'}`);
      }
      if (client.RefreshTokenValidity) {
        const unit = client.TokenValidityUnits?.RefreshToken || 'days';
        const value = client.RefreshTokenValidity;
        console.log(`  Refresh Token Validity: ${value} ${unit}`);
        
        // Check if it's 7 days
        let daysValue = value;
        if (unit === 'hours') {
          daysValue = value / 24;
        } else if (unit === 'minutes') {
          daysValue = value / (24 * 60);
        }
        
        if (Math.abs(daysValue - 7) < 0.1) {
          console.log('  ✅ Refresh token expiration is set to 7 days (as required)');
        } else {
          console.log(`  ⚠️  Refresh token expiration is ${daysValue} days (should be 7 days)`);
        }
      }

      // OAuth settings
      if (client.SupportedIdentityProviders) {
        console.log(`\n🔗 Supported Identity Providers: ${client.SupportedIdentityProviders.join(', ')}`);
      }
      
      if (client.CallbackURLs) {
        console.log(`\n🔄 Callback URLs:`);
        client.CallbackURLs.forEach(url => console.log(`  - ${url}`));
      }
      
      if (client.LogoutURLs) {
        console.log(`\n🚪 Logout URLs:`);
        client.LogoutURLs.forEach(url => console.log(`  - ${url}`));
      }

      if (client.AllowedOAuthScopes) {
        console.log(`\n🔐 OAuth Scopes: ${client.AllowedOAuthScopes.join(', ')}`);
      }

    } catch (error) {
      console.error(`❌ Error getting User Pool Client details: ${error.message}`);
      
      if (error.name === 'UnauthorizedOperation' || error.name === 'AccessDenied') {
        console.log('\n💡 Note: You may need AWS credentials with Cognito read permissions to view these settings.');
        console.log('   You can configure credentials using:');
        console.log('   - AWS CLI: aws configure');
        console.log('   - Environment variables: AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY');
        console.log('   - IAM roles (if running on EC2)');
      }
    }

    console.log('\n✅ Cognito configuration check completed.');

  } catch (error) {
    console.error('❌ Error checking Cognito configuration:', error.message);
    process.exit(1);
  }
}

// Run the check
checkCognitoConfig().catch(console.error);

/**
 * Top Vendors Widget Component
 * 
 * Displays top vendors by spending with quick stats and actions
 */

'use client'

import React, { useMemo } from 'react'
import { BaseComponentProps } from '@/lib/types'

interface VendorData {
  vendor: string
  totalSpend: number
  renewalCount: number
  avgSpend: number
  lastRenewal?: string
  upcomingRenewals?: number
  reliabilityScore?: number
  currency?: string
}

interface TopVendorsWidgetProps extends BaseComponentProps {
  data: VendorData[]
  showCount?: number
  currency?: string
  onVendorClick?: (vendor: string) => void
  onViewAll?: () => void
}

export default function TopVendorsWidget({
  data,
  showCount = 5,
  currency = 'USD',
  onVendorClick,
  onViewAll,
  className = '',
  'data-testid': testId
}: TopVendorsWidgetProps) {
  // Process and sort data
  const topVendors = useMemo(() => {
    if (!data.length) return []
    
    return data
      .sort((a, b) => b.totalSpend - a.totalSpend)
      .slice(0, showCount)
  }, [data, showCount])

  const totalSpend = useMemo(() => {
    return data.reduce((sum, item) => sum + item.totalSpend, 0)
  }, [data])

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  const getReliabilityColor = (score?: number) => {
    if (!score) return 'text-gray-400'
    if (score >= 90) return 'text-green-600'
    if (score >= 75) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getReliabilityIcon = (score?: number) => {
    if (!score) return '❓'
    if (score >= 90) return '✅'
    if (score >= 75) return '⚠️'
    return '❌'
  }

  if (!topVendors.length) {
    return (
      <div className={`bg-white border rounded-lg p-6 ${className}`} data-testid={testId}>
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Top Vendors</h3>
        </div>
        <div className="text-center py-8">
          <div className="text-4xl mb-2">🏢</div>
          <p className="text-gray-600">No vendor data available</p>
          <p className="text-sm text-gray-500 mt-1">Add renewals with vendor information to see top vendors</p>
        </div>
      </div>
    )
  }

  return (
    <div className={`bg-white border rounded-lg p-6 ${className}`} data-testid={testId}>
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Top Vendors</h3>
        {onViewAll && (
          <button
            onClick={onViewAll}
            className="text-sm text-blue-600 hover:text-blue-800 font-medium"
          >
            View All
          </button>
        )}
      </div>

      <div className="space-y-4">
        {topVendors.map((vendor, index) => {
          const percentage = totalSpend > 0 ? (vendor.totalSpend / totalSpend) * 100 : 0
          
          return (
            <div
              key={vendor.vendor}
              className={`group p-4 rounded-lg border border-gray-100 ${
                onVendorClick ? 'cursor-pointer hover:border-blue-200 hover:bg-blue-50' : ''
              } transition-all duration-200`}
              onClick={() => onVendorClick?.(vendor.vendor)}
            >
              {/* Vendor Header */}
              <div className="flex items-start justify-between mb-3">
                <div className="flex items-center gap-3">
                  <div className="flex items-center justify-center w-8 h-8 bg-blue-100 text-blue-600 rounded-full font-semibold text-sm">
                    #{index + 1}
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900 truncate" title={vendor.vendor}>
                      {vendor.vendor}
                    </h4>
                    <div className="flex items-center gap-2 mt-1">
                      <span className="text-sm text-gray-500">
                        {vendor.renewalCount} renewal{vendor.renewalCount !== 1 ? 's' : ''}
                      </span>
                      {vendor.reliabilityScore && (
                        <div className="flex items-center gap-1">
                          <span className={`text-sm ${getReliabilityColor(vendor.reliabilityScore)}`}>
                            {getReliabilityIcon(vendor.reliabilityScore)}
                          </span>
                          <span className={`text-xs ${getReliabilityColor(vendor.reliabilityScore)}`}>
                            {vendor.reliabilityScore}%
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                
                <div className="text-right">
                  <div className="font-semibold text-gray-900">
                    {formatCurrency(vendor.totalSpend)}
                  </div>
                  <div className="text-xs text-gray-500">
                    {percentage.toFixed(1)}% of total
                  </div>
                </div>
              </div>

              {/* Vendor Stats */}
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-500">Avg Spend:</span>
                  <span className="ml-1 font-medium text-gray-900">
                    {formatCurrency(vendor.avgSpend)}
                  </span>
                </div>
                
                {vendor.upcomingRenewals !== undefined && (
                  <div>
                    <span className="text-gray-500">Upcoming:</span>
                    <span className="ml-1 font-medium text-gray-900">
                      {vendor.upcomingRenewals}
                    </span>
                  </div>
                )}
              </div>

              {/* Progress Bar */}
              <div className="mt-3">
                <div className="w-full bg-gray-200 rounded-full h-1.5">
                  <div 
                    className="bg-blue-500 h-1.5 rounded-full transition-all duration-300"
                    style={{ width: `${Math.max(percentage, 2)}%` }}
                  />
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* Summary Footer */}
      <div className="mt-6 pt-4 border-t border-gray-100">
        <div className="flex justify-between items-center text-sm">
          <span className="text-gray-500">
            Showing top {topVendors.length} of {data.length} vendors
          </span>
          <span className="font-medium text-gray-900">
            Total: {formatCurrency(totalSpend)}
          </span>
        </div>
      </div>
    </div>
  )
}

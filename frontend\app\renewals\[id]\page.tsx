/**
 * Renewal Details Page
 * 
 * Displays comprehensive renewal information including details, status, history, and alerts
 */

'use client'

import { useState, useEffect } from 'react'
import { useParams, useRouter } from 'next/navigation'
import { useRenewals, Renewal } from '@/lib/hooks'
// Note: usePerformanceMonitor moved to server-side only

// Components
import { PageHeader } from '@/components/ui'
import { usePageInfoByName } from '@/lib/hooks/usePageInfo'
import RenewalDetailsCard from '@/components/renewals/RenewalDetailsCard'
import RenewalStatusCard from '@/components/renewals/RenewalStatusCard'
import RenewalSummaryCard from '@/components/renewals/RenewalSummaryCard'
import RenewalHistoryCard from '@/components/renewals/RenewalHistoryCard'
import RenewalAlertsCard from '@/components/renewals/RenewalAlertsCard'
import QuickActionsCard from '@/components/renewals/QuickActionsCard'

export default function RenewalDetailsPage() {
  // Performance monitoring - removed for now
  // usePerformanceMonitor('RenewalDetailsPage')

  // Get page info for the header
  const { pageInfo } = usePageInfoByName('renewal-details')

  // Hooks
  const params = useParams()
  const router = useRouter()
  const { fetchRenewalById, selectedRenewal, setSelectedRenewal, isLoading } = useRenewals()

  // State
  const [renewal, setRenewal] = useState<Renewal | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const renewalId = params.id as string

  // Load renewal data
  useEffect(() => {
    const loadRenewal = async () => {
      if (!renewalId) {
        setError('Invalid renewal ID')
        setLoading(false)
        return
      }

      try {
        setLoading(true)
        setError(null)

        const renewalData = await fetchRenewalById(renewalId)
        if (renewalData) {
          setRenewal(renewalData)
          setSelectedRenewal(renewalData)
        } else {
          setError('Renewal not found')
        }
      } catch (err) {
        setError('Failed to load renewal details')
        console.error('Error loading renewal:', err)
      } finally {
        setLoading(false)
      }
    }

    loadRenewal()
  }, [renewalId, fetchRenewalById, setSelectedRenewal])

  // Handle back navigation
  const handleBack = () => {
    router.push('/renewals')
  }

  // Handle edit action
  const handleEdit = () => {
    // This will be handled by the actions menu component
    console.log('Edit renewal:', renewalId)
  }

  // Handle process action
  const handleProcess = () => {
    // This will be handled by the actions menu component
    console.log('Process renewal:', renewalId)
  }

  // Loading state
  if (loading || isLoading) {
    return (
      <div className="overview-container">
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading renewal details...</p>
          </div>
        </div>
      </div>
    )
  }

  // Error state
  if (error || !renewal) {
    return (
      <div className="overview-container">
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="text-red-500 text-6xl mb-4">⚠️</div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              {error || 'Renewal not found'}
            </h2>
            <p className="text-gray-600 mb-4">
              The renewal you're looking for could not be loaded.
            </p>
            <button
              onClick={handleBack}
              className="btn-primary"
            >
              Back to Renewals
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="overview-container">
      {/* Header */}
      <PageHeader
        title={renewal?.name || pageInfo?.header || 'Renewal Details'}
        subtitle={pageInfo?.description || 'View and manage renewal details'}
        actions={[
          {
            label: 'Back',
            onClick: handleBack,
            variant: 'outline',
            leftIcon: '←'
          },
          {
            label: 'Edit',
            onClick: handleEdit,
            variant: 'secondary',
            leftIcon: '✏️'
          },
          {
            label: 'Process',
            onClick: handleProcess,
            variant: 'primary',
            leftIcon: '⚡'
          }
        ]}
      />

      {/* Main Content */}
      <div className="renewal-details-content">
        {/* Top Row - Details and Status */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
          {/* Renewal Details */}
          <div className="lg:col-span-2">
            <RenewalDetailsCard renewal={renewal} />
          </div>

          {/* Renewal Status */}
          <div>
            <RenewalStatusCard renewal={renewal} />
          </div>
        </div>

        {/* Middle Row - Summary and Quick Actions */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
          {/* Renewal Summary */}
          <div className="lg:col-span-2">
            <RenewalSummaryCard renewal={renewal} />
          </div>

          {/* Quick Actions */}
          <div>
            <QuickActionsCard 
              renewal={renewal}
              onEdit={handleEdit}
              onProcess={handleProcess}
            />
          </div>
        </div>

        {/* Bottom Row - History and Alerts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Renewal History */}
          <div>
            <RenewalHistoryCard renewal={renewal} />
          </div>

          {/* Renewal Alerts */}
          <div>
            <RenewalAlertsCard renewal={renewal} />
          </div>
        </div>
      </div>
    </div>
  )
}

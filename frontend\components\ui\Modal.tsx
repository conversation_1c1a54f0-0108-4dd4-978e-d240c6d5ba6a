/**
 * Universal Modal Component
 * 
 * Replaces all scattered modal implementations with a single, consistent component.
 * Provides unified modal behavior, styling, and accessibility features.
 */

'use client';

import React, { useEffect, useRef, useState } from 'react';
import { createPortal } from 'react-dom';
import { designTokens } from '@/lib/utils/design-tokens';
import { Button, IconButton } from './Button';

export type ModalSize = 'sm' | 'md' | 'lg' | 'xl' | 'full';

export interface ModalProps {
  /**
   * Whether the modal is open
   */
  isOpen: boolean;
  
  /**
   * Function to call when the modal should be closed
   */
  onClose: () => void;
  
  /**
   * Modal title
   */
  title: string;
  
  /**
   * Optional subtitle
   */
  subtitle?: string;
  
  /**
   * Size of the modal
   */
  size?: ModalSize;
  
  /**
   * Whether to show the close button
   */
  showCloseButton?: boolean;
  
  /**
   * Whether clicking the overlay should close the modal
   */
  closeOnOverlayClick?: boolean;
  
  /**
   * Whether pressing Escape should close the modal
   */
  closeOnEscape?: boolean;
  
  /**
   * Custom header content (replaces title/subtitle)
   */
  header?: React.ReactNode;
  
  /**
   * Modal content
   */
  children: React.ReactNode;
  
  /**
   * Footer content (typically buttons)
   */
  footer?: React.ReactNode;
  
  /**
   * Custom class name
   */
  className?: string;
  
  /**
   * Test ID for testing
   */
  'data-testid'?: string;
  
  /**
   * Z-index override
   */
  zIndex?: number;
}

/**
 * Get modal styles based on size
 */
const getModalStyles = (size: ModalSize, zIndex: number) => {
  const overlayStyles = {
    position: 'fixed' as const,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: designTokens.colors.background.overlay,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    padding: designTokens.spacing[4],
    zIndex,
    backdropFilter: 'blur(4px)',
  };

  const sizeStyles = {
    sm: {
      maxWidth: designTokens.components.modal.maxWidth.sm,
      width: '100%',
    },
    md: {
      maxWidth: designTokens.components.modal.maxWidth.md,
      width: '100%',
    },
    lg: {
      maxWidth: designTokens.components.modal.maxWidth.lg,
      width: '100%',
    },
    xl: {
      maxWidth: designTokens.components.modal.maxWidth.xl,
      width: '100%',
    },
    full: {
      width: '100vw',
      height: '100vh',
      maxWidth: 'none',
      maxHeight: 'none',
      margin: 0,
    },
  };

  const contentStyles = {
    backgroundColor: designTokens.colors.background.primary,
    borderRadius: size === 'full' ? 0 : designTokens.borderRadius.lg,
    boxShadow: designTokens.shadows.xl,
    maxHeight: size === 'full' ? '100vh' : '90vh',
    overflow: 'hidden',
    display: 'flex',
    flexDirection: 'column' as const,
    ...sizeStyles[size],
  };

  return { overlayStyles, contentStyles };
};

/**
 * Universal Modal Component
 */
export function Modal({
  isOpen,
  onClose,
  title,
  subtitle,
  size = 'md',
  showCloseButton = true,
  closeOnOverlayClick = true,
  closeOnEscape = true,
  header,
  children,
  footer,
  className = '',
  'data-testid': testId,
  zIndex = designTokens.zIndex.modal,
}: ModalProps) {
  const modalRef = useRef<HTMLDivElement>(null);
  const previousActiveElement = useRef<HTMLElement | null>(null);

  // Prevent hydration mismatch by only rendering on client after mount
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Handle escape key
  useEffect(() => {
    if (!isOpen || !closeOnEscape) return;

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, closeOnEscape, onClose]);

  // Handle focus management
  useEffect(() => {
    if (isOpen) {
      // Store the currently focused element
      previousActiveElement.current = document.activeElement as HTMLElement;
      
      // Focus the modal
      if (modalRef.current) {
        modalRef.current.focus();
      }
      
      // Prevent body scroll
      document.body.style.overflow = 'hidden';
    } else {
      // Restore focus to the previously focused element
      if (previousActiveElement.current) {
        previousActiveElement.current.focus();
      }
      
      // Restore body scroll
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [isOpen]);

  // Handle overlay click
  const handleOverlayClick = (event: React.MouseEvent) => {
    if (closeOnOverlayClick && event.target === event.currentTarget) {
      onClose();
    }
  };

  if (!isOpen) return null;

  const { overlayStyles, contentStyles } = getModalStyles(size, zIndex);

  const modalContent = (
    <div
      style={overlayStyles}
      className="modal-overlay"
      onClick={handleOverlayClick}
      data-testid={testId}
    >
      <div
        ref={modalRef}
        style={contentStyles}
        className={`modal-content modal-${size} ${className}`}
        role="dialog"
        aria-modal="true"
        aria-labelledby={header ? undefined : 'modal-title'}
        aria-describedby={subtitle ? 'modal-subtitle' : undefined}
        tabIndex={-1}
      >
        {/* Header */}
        {header || (title && (
          <div
            style={{
              padding: `${designTokens.spacing[6]} ${designTokens.spacing[6]} 0`,
              borderBottom: `1px solid ${designTokens.colors.border.primary}`,
              marginBottom: designTokens.spacing[6],
              position: 'relative',
            }}
            className="modal-header"
          >
            <h2
              id="modal-title"
              style={{
                fontSize: designTokens.typography.fontSize.xl,
                fontWeight: designTokens.typography.fontWeight.semibold,
                color: designTokens.colors.text.primary,
                margin: `0 0 ${designTokens.spacing[1]} 0`,
              }}
              className="modal-title"
            >
              {title}
            </h2>
            
            {subtitle && (
              <p
                id="modal-subtitle"
                style={{
                  fontSize: designTokens.typography.fontSize.sm,
                  color: designTokens.colors.text.secondary,
                  margin: `0 0 ${designTokens.spacing[5]} 0`,
                }}
                className="modal-subtitle"
              >
                {subtitle}
              </p>
            )}
            
            {showCloseButton && (
              <div
                style={{
                  position: 'absolute',
                  top: designTokens.spacing[5],
                  right: designTokens.spacing[5],
                }}
              >
                <IconButton
                  variant="ghost"
                  size="sm"
                  icon={
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                      <path
                        d="M18 6L6 18M6 6l12 12"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  }
                  onClick={onClose}
                  aria-label="Close modal"
                />
              </div>
            )}
          </div>
        ))}

        {/* Body */}
        <div
          style={{
            flex: 1,
            overflowY: 'auto',
            padding: `0 ${designTokens.spacing[6]} ${designTokens.spacing[6]}`,
          }}
          className="modal-body"
        >
          {children}
        </div>

        {/* Footer */}
        {footer && (
          <div
            style={{
              padding: `${designTokens.spacing[4]} ${designTokens.spacing[6]} ${designTokens.spacing[6]}`,
              borderTop: `1px solid ${designTokens.colors.border.primary}`,
              display: 'flex',
              justifyContent: 'flex-end',
              gap: designTokens.spacing[3],
            }}
            className="modal-footer"
          >
            {footer}
          </div>
        )}
      </div>
    </div>
  );

  // Render modal in portal - only after client-side hydration to prevent mismatch
  if (!isMounted) {
    return null;
  }

  return createPortal(modalContent, document.body);
}

/**
 * Modal Header Component for custom headers
 */
export interface ModalHeaderProps {
  title: string;
  subtitle?: string;
  actions?: React.ReactNode;
  className?: string;
}

export function ModalHeader({ title, subtitle, actions, className = '' }: ModalHeaderProps) {
  return (
    <div
      style={{
        padding: `${designTokens.spacing[6]} ${designTokens.spacing[6]} 0`,
        borderBottom: `1px solid ${designTokens.colors.border.primary}`,
        marginBottom: designTokens.spacing[6],
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
      }}
      className={`modal-header ${className}`}
    >
      <div>
        <h2
          style={{
            fontSize: designTokens.typography.fontSize.xl,
            fontWeight: designTokens.typography.fontWeight.semibold,
            color: designTokens.colors.text.primary,
            margin: `0 0 ${designTokens.spacing[1]} 0`,
          }}
          className="modal-title"
        >
          {title}
        </h2>
        
        {subtitle && (
          <p
            style={{
              fontSize: designTokens.typography.fontSize.sm,
              color: designTokens.colors.text.secondary,
              margin: 0,
            }}
            className="modal-subtitle"
          >
            {subtitle}
          </p>
        )}
      </div>
      
      {actions && (
        <div className="modal-header-actions">
          {actions}
        </div>
      )}
    </div>
  );
}

/**
 * Modal Footer Component for consistent footer layouts
 */
export interface ModalFooterProps {
  children: React.ReactNode;
  justify?: 'start' | 'center' | 'end' | 'between';
  className?: string;
}

export function ModalFooter({ children, justify = 'end', className = '' }: ModalFooterProps) {
  const justifyContent = {
    start: 'flex-start',
    center: 'center',
    end: 'flex-end',
    between: 'space-between',
  };

  return (
    <div
      style={{
        padding: `${designTokens.spacing[4]} ${designTokens.spacing[6]} ${designTokens.spacing[6]}`,
        borderTop: `1px solid ${designTokens.colors.border.primary}`,
        display: 'flex',
        justifyContent: justifyContent[justify],
        gap: designTokens.spacing[3],
      }}
      className={`modal-footer ${className}`}
    >
      {children}
    </div>
  );
}



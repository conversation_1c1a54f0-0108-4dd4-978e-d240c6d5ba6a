AWSTemplateFormatVersion: "2010-09-09"
Description: "RenewTrack Application Deployment - Load Balancer, Auto Scaling, and Application Servers"

Parameters:
  Environment:
    Type: String
    Default: "prod"
    AllowedValues: ["dev", "staging", "prod"]
    Description: "Environment name"

  ApplicationName:
    Type: String
    Default: "renewtrack"
    Description: "Application name for resource naming"

  InstanceType:
    Type: String
    Default: "t3.medium"
    AllowedValues:
      ["t3.small", "t3.medium", "t3.large", "t3.xlarge", "t3.2xlarge"]
    Description: "EC2 instance type for application servers"

  MinSize:
    Type: Number
    Default: 2
    Description: "Minimum number of instances in Auto Scaling Group"

  MaxSize:
    Type: Number
    Default: 10
    Description: "Maximum number of instances in Auto Scaling Group"

  DesiredCapacity:
    Type: Number
    Default: 2
    Description: "Desired number of instances in Auto Scaling Group"

  KeyPairName:
    Type: AWS::EC2::KeyPair::KeyName
    Description: "EC2 Key Pair for SSH access"

  CertificateArn:
    Type: String
    Description: "ARN of SSL certificate for HTTPS"
    Default: ""

  DomainName:
    Type: String
    Description: "Domain name for the application"
    Default: "renewtrack.com"

Conditions:
  HasSSLCertificate: !Not [!Equals [!Ref CertificateArn, ""]]

Resources:
  # Application Load Balancer
  ApplicationLoadBalancer:
    Type: AWS::ElasticLoadBalancingV2::LoadBalancer
    Properties:
      Name: !Sub "${ApplicationName}-alb-${Environment}"
      Scheme: internet-facing
      Type: application
      IpAddressType: ipv4
      Subnets:
        - Fn::ImportValue: !Sub "${ApplicationName}-public-subnet-1-${Environment}"
        - Fn::ImportValue: !Sub "${ApplicationName}-public-subnet-2-${Environment}"
      SecurityGroups:
        - Fn::ImportValue: !Sub "${ApplicationName}-alb-sg-${Environment}"
      Tags:
        - Key: Name
          Value: !Sub "${ApplicationName}-alb-${Environment}"
        - Key: Environment
          Value: !Ref Environment

  # Target Group
  ApplicationTargetGroup:
    Type: AWS::ElasticLoadBalancingV2::TargetGroup
    Properties:
      Name: !Sub "${ApplicationName}-tg-${Environment}"
      Port: 3000
      Protocol: HTTP
      VpcId:
        Fn::ImportValue: !Sub "${ApplicationName}-vpc-id-${Environment}"
      TargetType: instance
      HealthCheckEnabled: true
      HealthCheckIntervalSeconds: 30
      HealthCheckPath: /api/health
      HealthCheckPort: traffic-port
      HealthCheckProtocol: HTTP
      HealthCheckTimeoutSeconds: 5
      HealthyThresholdCount: 2
      UnhealthyThresholdCount: 3
      Matcher:
        HttpCode: "200"
      TargetGroupAttributes:
        - Key: deregistration_delay.timeout_seconds
          Value: "30"
        - Key: stickiness.enabled
          Value: "true"
        - Key: stickiness.type
          Value: "lb_cookie"
        - Key: stickiness.lb_cookie.duration_seconds
          Value: "86400"
      Tags:
        - Key: Name
          Value: !Sub "${ApplicationName}-tg-${Environment}"

  # HTTP Listener (redirect to HTTPS if certificate available)
  HTTPListener:
    Type: AWS::ElasticLoadBalancingV2::Listener
    Properties:
      DefaultActions:
        - !If
          - HasSSLCertificate
          - Type: redirect
            RedirectConfig:
              Protocol: HTTPS
              Port: 443
              StatusCode: HTTP_301
          - Type: forward
            TargetGroupArn: !Ref ApplicationTargetGroup
      LoadBalancerArn: !Ref ApplicationLoadBalancer
      Port: 80
      Protocol: HTTP

  # HTTPS Listener (only if certificate is provided)
  HTTPSListener:
    Type: AWS::ElasticLoadBalancingV2::Listener
    Condition: HasSSLCertificate
    Properties:
      DefaultActions:
        - Type: forward
          TargetGroupArn: !Ref ApplicationTargetGroup
      LoadBalancerArn: !Ref ApplicationLoadBalancer
      Port: 443
      Protocol: HTTPS
      Certificates:
        - CertificateArn: !Ref CertificateArn
      SslPolicy: ELBSecurityPolicy-TLS-1-2-2017-01

  # IAM Role for EC2 instances
  EC2Role:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub "${ApplicationName}-ec2-role-${Environment}"
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service: ec2.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/CloudWatchAgentServerPolicy
        - arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore
      Policies:
        - PolicyName: SecretsAccess
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action:
                  - ssm:GetParameter
                  - ssm:GetParameters
                  - ssm:GetParametersByPath
                Resource:
                  - !Sub "arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/${ApplicationName}/*"
              - Effect: Allow
                Action:
                  - secretsmanager:GetSecretValue
                Resource:
                  - !Sub "arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:/${ApplicationName}/*"
              - Effect: Allow
                Action:
                  - logs:CreateLogGroup
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                  - logs:DescribeLogStreams
                Resource:
                  - !Sub "arn:aws:logs:${AWS::Region}:${AWS::AccountId}:log-group:/aws/ec2/${ApplicationName}/*"

  EC2InstanceProfile:
    Type: AWS::IAM::InstanceProfile
    Properties:
      Roles:
        - !Ref EC2Role

  # Launch Template
  LaunchTemplate:
    Type: AWS::EC2::LaunchTemplate
    Properties:
      LaunchTemplateName: !Sub "${ApplicationName}-lt-${Environment}"
      LaunchTemplateData:
        ImageId: ami-0c02fb55956c7d316 # Amazon Linux 2 AMI (update as needed)
        InstanceType: !Ref InstanceType
        KeyName: !Ref KeyPairName
        IamInstanceProfile:
          Arn: !GetAtt EC2InstanceProfile.Arn
        SecurityGroupIds:
          - Fn::ImportValue: !Sub "${ApplicationName}-app-sg-${Environment}"
        UserData:
          Fn::Base64: !Sub |
            #!/bin/bash
            yum update -y

            # Install Node.js 18
            curl -fsSL https://rpm.nodesource.com/setup_18.x | bash -
            yum install -y nodejs git

            # Install PM2 for process management
            npm install -g pm2

            # Install CloudWatch agent
            yum install -y amazon-cloudwatch-agent

            # Create application user
            useradd -m -s /bin/bash renewtrack

            # Create application directory
            mkdir -p /opt/renewtrack
            chown renewtrack:renewtrack /opt/renewtrack

            # Create systemd service for the application
            cat > /etc/systemd/system/renewtrack.service << 'EOF'
            [Unit]
            Description=RenewTrack Application
            After=network.target

            [Service]
            Type=simple
            User=renewtrack
            WorkingDirectory=/opt/renewtrack
            ExecStart=/usr/bin/node server.js
            Restart=always
            RestartSec=10
            Environment=NODE_ENV=production
            Environment=PORT=3000

            [Install]
            WantedBy=multi-user.target
            EOF

            # Enable and start the service
            systemctl enable renewtrack

            # Configure CloudWatch agent
            cat > /opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.json << 'EOF'
            {
              "agent": {
                "metrics_collection_interval": 60,
                "run_as_user": "cwagent"
              },
              "logs": {
                "logs_collected": {
                  "files": {
                    "collect_list": [
                      {
                        "file_path": "/opt/renewtrack/logs/application.log",
                        "log_group_name": "/aws/ec2/${ApplicationName}/application",
                        "log_stream_name": "{instance_id}"
                      },
                      {
                        "file_path": "/var/log/messages",
                        "log_group_name": "/aws/ec2/${ApplicationName}/system",
                        "log_stream_name": "{instance_id}"
                      }
                    ]
                  }
                }
              },
              "metrics": {
                "namespace": "${ApplicationName}/${Environment}",
                "metrics_collected": {
                  "cpu": {
                    "measurement": [
                      "cpu_usage_idle",
                      "cpu_usage_iowait",
                      "cpu_usage_user",
                      "cpu_usage_system"
                    ],
                    "metrics_collection_interval": 60
                  },
                  "disk": {
                    "measurement": [
                      "used_percent"
                    ],
                    "metrics_collection_interval": 60,
                    "resources": [
                      "*"
                    ]
                  },
                  "diskio": {
                    "measurement": [
                      "io_time"
                    ],
                    "metrics_collection_interval": 60,
                    "resources": [
                      "*"
                    ]
                  },
                  "mem": {
                    "measurement": [
                      "mem_used_percent"
                    ],
                    "metrics_collection_interval": 60
                  }
                }
              }
            }
            EOF

            # Start CloudWatch agent
            /opt/aws/amazon-cloudwatch-agent/bin/amazon-cloudwatch-agent-ctl \
              -a fetch-config \
              -m ec2 \
              -c file:/opt/aws/amazon-cloudwatch-agent/etc/amazon-cloudwatch-agent.json \
              -s

            # Signal that the instance is ready
            /opt/aws/bin/cfn-signal -e $? --stack ${AWS::StackName} --resource AutoScalingGroup --region ${AWS::Region}
        TagSpecifications:
          - ResourceType: instance
            Tags:
              - Key: Name
                Value: !Sub "${ApplicationName}-instance-${Environment}"
              - Key: Environment
                Value: !Ref Environment
          - ResourceType: volume
            Tags:
              - Key: Name
                Value: !Sub "${ApplicationName}-volume-${Environment}"
              - Key: Environment
                Value: !Ref Environment

  # Auto Scaling Group
  AutoScalingGroup:
    Type: AWS::AutoScaling::AutoScalingGroup
    Properties:
      AutoScalingGroupName: !Sub "${ApplicationName}-asg-${Environment}"
      VPCZoneIdentifier:
        - Fn::ImportValue: !Sub "${ApplicationName}-private-subnet-1-${Environment}"
        - Fn::ImportValue: !Sub "${ApplicationName}-private-subnet-2-${Environment}"
      LaunchTemplate:
        LaunchTemplateId: !Ref LaunchTemplate
        Version: !GetAtt LaunchTemplate.LatestVersionNumber
      MinSize: !Ref MinSize
      MaxSize: !Ref MaxSize
      DesiredCapacity: !Ref DesiredCapacity
      TargetGroupARNs:
        - !Ref ApplicationTargetGroup
      HealthCheckType: ELB
      HealthCheckGracePeriod: 300
      DefaultCooldown: 300
      Tags:
        - Key: Name
          Value: !Sub "${ApplicationName}-asg-${Environment}"
          PropagateAtLaunch: false
        - Key: Environment
          Value: !Ref Environment
          PropagateAtLaunch: true
    CreationPolicy:
      ResourceSignal:
        Count: !Ref DesiredCapacity
        Timeout: PT15M
    UpdatePolicy:
      AutoScalingRollingUpdate:
        MinInstancesInService: 1
        MaxBatchSize: 1
        PauseTime: PT15M
        WaitOnResourceSignals: true

  # Auto Scaling Policies
  ScaleUpPolicy:
    Type: AWS::AutoScaling::ScalingPolicy
    Properties:
      AdjustmentType: ChangeInCapacity
      AutoScalingGroupName: !Ref AutoScalingGroup
      Cooldown: 300
      ScalingAdjustment: 1
      PolicyType: SimpleScaling

  ScaleDownPolicy:
    Type: AWS::AutoScaling::ScalingPolicy
    Properties:
      AdjustmentType: ChangeInCapacity
      AutoScalingGroupName: !Ref AutoScalingGroup
      Cooldown: 300
      ScalingAdjustment: -1
      PolicyType: SimpleScaling

  # CloudWatch Alarms
  CPUAlarmHigh:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub "${ApplicationName}-cpu-high-${Environment}"
      AlarmDescription: "Scale up on high CPU"
      MetricName: CPUUtilization
      Namespace: AWS/EC2
      Statistic: Average
      Period: 300
      EvaluationPeriods: 2
      Threshold: 70
      ComparisonOperator: GreaterThanThreshold
      Dimensions:
        - Name: AutoScalingGroupName
          Value: !Ref AutoScalingGroup
      AlarmActions:
        - !Ref ScaleUpPolicy

  CPUAlarmLow:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub "${ApplicationName}-cpu-low-${Environment}"
      AlarmDescription: "Scale down on low CPU"
      MetricName: CPUUtilization
      Namespace: AWS/EC2
      Statistic: Average
      Period: 300
      EvaluationPeriods: 2
      Threshold: 30
      ComparisonOperator: LessThanThreshold
      Dimensions:
        - Name: AutoScalingGroupName
          Value: !Ref AutoScalingGroup
      AlarmActions:
        - !Ref ScaleDownPolicy

Outputs:
  LoadBalancerDNS:
    Description: "DNS name of the load balancer"
    Value: !GetAtt ApplicationLoadBalancer.DNSName
    Export:
      Name: !Sub "${ApplicationName}-alb-dns-${Environment}"

  LoadBalancerArn:
    Description: "ARN of the load balancer"
    Value: !Ref ApplicationLoadBalancer
    Export:
      Name: !Sub "${ApplicationName}-alb-arn-${Environment}"

  TargetGroupArn:
    Description: "ARN of the target group"
    Value: !Ref ApplicationTargetGroup
    Export:
      Name: !Sub "${ApplicationName}-tg-arn-${Environment}"

  AutoScalingGroupName:
    Description: "Name of the Auto Scaling Group"
    Value: !Ref AutoScalingGroup
    Export:
      Name: !Sub "${ApplicationName}-asg-name-${Environment}"

/**
 * Universal Button Component
 * 
 * Replaces all scattered button implementations with a single, consistent component.
 * Provides all button variants, sizes, and states used throughout the application.
 */

'use client';

import React, { forwardRef } from 'react';
import { designTokens } from '@/lib/utils/design-tokens';

// Button variant types
export type ButtonVariant = 
  | 'primary' 
  | 'secondary' 
  | 'outline' 
  | 'ghost' 
  | 'danger' 
  | 'success' 
  | 'warning';

export type ButtonSize = 'sm' | 'md' | 'lg';

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /**
   * Visual style variant
   */
  variant?: ButtonVariant;
  
  /**
   * Size of the button
   */
  size?: ButtonSize;
  
  /**
   * Whether the button is in a loading state
   */
  isLoading?: boolean;
  
  /**
   * Icon to display before the text
   */
  leftIcon?: React.ReactNode;
  
  /**
   * Icon to display after the text
   */
  rightIcon?: React.ReactNode;
  
  /**
   * Whether the button should take full width
   */
  fullWidth?: boolean;
  
  /**
   * Custom class name
   */
  className?: string;
  
  /**
   * Test ID for testing
   */
  'data-testid'?: string;
  
  /**
   * Button content
   */
  children?: React.ReactNode;
}

/**
 * Get button styles based on variant and size
 */
const getButtonStyles = (variant: ButtonVariant, size: ButtonSize, isLoading: boolean, fullWidth: boolean) => {
  const baseStyles = {
    display: 'inline-flex',
    alignItems: 'center',
    justifyContent: 'center',
    gap: designTokens.spacing[2],
    fontFamily: designTokens.typography.fontFamily.sans,
    fontWeight: designTokens.typography.fontWeight.medium,
    borderRadius: designTokens.borderRadius.md,
    border: '1px solid transparent',
    cursor: isLoading ? 'not-allowed' : 'pointer',
    transition: `all ${designTokens.duration.normal} ${designTokens.easing.inOut}`,
    textDecoration: 'none',
    userSelect: 'none' as const,
    outline: 'none',
    width: fullWidth ? '100%' : 'auto',
    opacity: isLoading ? 0.7 : 1,
  };

  // Size-specific styles
  const sizeStyles = {
    sm: {
      height: designTokens.components.button.height.sm,
      padding: designTokens.components.button.padding.sm,
      fontSize: designTokens.typography.fontSize.sm,
    },
    md: {
      height: designTokens.components.button.height.md,
      padding: designTokens.components.button.padding.md,
      fontSize: designTokens.typography.fontSize.base,
    },
    lg: {
      height: designTokens.components.button.height.lg,
      padding: designTokens.components.button.padding.lg,
      fontSize: designTokens.typography.fontSize.lg,
    },
  };

  // Variant-specific styles
  const variantStyles = {
    primary: {
      backgroundColor: designTokens.colors.primary[500],
      color: designTokens.colors.white,
      borderColor: designTokens.colors.primary[500],
      '&:hover': {
        backgroundColor: designTokens.colors.primary[600],
        borderColor: designTokens.colors.primary[600],
      },
      '&:focus': {
        boxShadow: `0 0 0 3px ${designTokens.colors.primary[200]}`,
      },
      '&:active': {
        backgroundColor: designTokens.colors.primary[700],
      },
    },
    secondary: {
      backgroundColor: designTokens.colors.secondary[500],
      color: designTokens.colors.white,
      borderColor: designTokens.colors.secondary[500],
      '&:hover': {
        backgroundColor: designTokens.colors.secondary[600],
        borderColor: designTokens.colors.secondary[600],
      },
      '&:focus': {
        boxShadow: `0 0 0 3px ${designTokens.colors.secondary[200]}`,
      },
    },
    outline: {
      backgroundColor: 'transparent',
      color: designTokens.colors.primary[500],
      borderColor: designTokens.colors.border.primary,
      '&:hover': {
        backgroundColor: designTokens.colors.primary[50],
        borderColor: designTokens.colors.primary[300],
      },
      '&:focus': {
        boxShadow: `0 0 0 3px ${designTokens.colors.primary[200]}`,
      },
    },
    ghost: {
      backgroundColor: 'transparent',
      color: designTokens.colors.text.primary,
      borderColor: 'transparent',
      '&:hover': {
        backgroundColor: designTokens.colors.secondary[100],
      },
      '&:focus': {
        boxShadow: `0 0 0 3px ${designTokens.colors.secondary[200]}`,
      },
    },
    danger: {
      backgroundColor: designTokens.colors.error[500],
      color: designTokens.colors.white,
      borderColor: designTokens.colors.error[500],
      '&:hover': {
        backgroundColor: designTokens.colors.error[600],
        borderColor: designTokens.colors.error[600],
      },
      '&:focus': {
        boxShadow: `0 0 0 3px ${designTokens.colors.error[200]}`,
      },
    },
    success: {
      backgroundColor: designTokens.colors.success[500],
      color: designTokens.colors.white,
      borderColor: designTokens.colors.success[500],
      '&:hover': {
        backgroundColor: designTokens.colors.success[600],
        borderColor: designTokens.colors.success[600],
      },
      '&:focus': {
        boxShadow: `0 0 0 3px ${designTokens.colors.success[200]}`,
      },
    },
    warning: {
      backgroundColor: designTokens.colors.warning[500],
      color: designTokens.colors.white,
      borderColor: designTokens.colors.warning[500],
      '&:hover': {
        backgroundColor: designTokens.colors.warning[600],
        borderColor: designTokens.colors.warning[600],
      },
      '&:focus': {
        boxShadow: `0 0 0 3px ${designTokens.colors.warning[200]}`,
      },
    },
  };

  return {
    ...baseStyles,
    ...sizeStyles[size],
    ...variantStyles[variant],
  };
};

/**
 * Universal Button Component
 */
export const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      variant = 'primary',
      size = 'md',
      isLoading = false,
      leftIcon,
      rightIcon,
      fullWidth = false,
      className = '',
      disabled,
      children,
      'data-testid': testId,
      ...props
    },
    ref
  ) => {
    const styles = getButtonStyles(variant, size, isLoading, fullWidth);
    const isDisabled = disabled || isLoading;

    return (
      <button
        ref={ref}
        style={styles}
        className={`btn btn-${variant} btn-${size} ${fullWidth ? 'btn-full-width' : ''} ${className}`}
        disabled={isDisabled}
        data-testid={testId}
        {...props}
      >
        {isLoading ? (
          <>
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              style={{
                animation: 'spin 1s linear infinite',
              }}
            >
              <circle
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="4"
                strokeOpacity="0.25"
              />
              <path
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              />
            </svg>
            <span>Loading...</span>
          </>
        ) : (
          <>
            {leftIcon && <span className="btn-icon-left">{leftIcon}</span>}
            {children && <span className="btn-text">{children}</span>}
            {rightIcon && <span className="btn-icon-right">{rightIcon}</span>}
          </>
        )}
      </button>
    );
  }
);

Button.displayName = 'Button';

/**
 * Button Group Component for related actions
 */
export interface ButtonGroupProps {
  children: React.ReactNode;
  orientation?: 'horizontal' | 'vertical';
  spacing?: keyof typeof designTokens.spacing;
  className?: string;
}

export function ButtonGroup({
  children,
  orientation = 'horizontal',
  spacing = 2,
  className = ''
}: ButtonGroupProps) {
  const styles = {
    display: 'flex',
    flexDirection: orientation === 'vertical' ? 'column' as const : 'row' as const,
    gap: designTokens.spacing[spacing],
    alignItems: orientation === 'horizontal' ? 'center' : 'stretch',
  };

  return (
    <div style={styles} className={`btn-group btn-group-${orientation} ${className}`}>
      {children}
    </div>
  );
}

/**
 * Icon Button Component for icon-only buttons
 */
export interface IconButtonProps extends Omit<ButtonProps, 'leftIcon' | 'rightIcon' | 'children'> {
  icon: React.ReactNode;
  'aria-label': string;
}

export const IconButton = forwardRef<HTMLButtonElement, IconButtonProps>(
  ({ icon, ...props }, ref) => {
    return (
      <Button ref={ref} {...props}>
        {icon}
      </Button>
    );
  }
);

IconButton.displayName = 'IconButton';



/**
 * Fix clients domain column schema
 * This script fixes the mismatch between the domain column structure and API expectations
 */

const { Pool } = require('pg');
require('dotenv').config({ path: '../frontend/.env.local' });

const pool = new Pool({
  user: process.env.DB_USER,
  host: process.env.DB_HOST,
  database: process.env.DB_NAME,
  password: process.env.DB_PASSWORD,
  port: process.env.DB_PORT || 5432,
  ssl: process.env.DATABASE_SSL === 'true' ? { rejectUnauthorized: false } : false,
});

async function fixDomainSchema() {
  const client = await pool.connect();
  
  try {
    console.log('🔍 Checking current clients structure...');
    
    // Check current structure
    const structureResult = await client.query(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_schema = 'metadata' 
        AND table_name = 'clients' 
        AND column_name = 'domain'
    `);
    
    console.log('Current domain column structure:', structureResult.rows);
    
    // Check current data
    const dataResult = await client.query(`
      SELECT client_id, name, domain, status 
      FROM metadata.clients 
      WHERE status = 'A'
    `);
    
    console.log('Current clients data:', dataResult.rows);
    
    // Check if domain is already an array
    const isArray = structureResult.rows[0]?.data_type === 'ARRAY';
    
    if (isArray) {
      console.log('✅ Domain column is already an array, no migration needed');
      return;
    }
    
    console.log('🔄 Converting domain column to array...');
    
    // Begin transaction
    await client.query('BEGIN');
    
    try {
      // Step 1: Add temporary array column
      await client.query(`
        ALTER TABLE metadata.clients 
        ADD COLUMN IF NOT EXISTS domain_temp VARCHAR(255)[]
      `);
      
      // Step 2: Migrate data to array format
      await client.query(`
        UPDATE metadata.clients 
        SET domain_temp = ARRAY[domain]
        WHERE domain_temp IS NULL AND domain IS NOT NULL
      `);
      
      // Step 3: Drop old column
      await client.query(`
        ALTER TABLE metadata.clients 
        DROP COLUMN IF EXISTS domain
      `);
      
      // Step 4: Rename new column
      await client.query(`
        ALTER TABLE metadata.clients 
        RENAME COLUMN domain_temp TO domain
      `);
      
      // Step 5: Add constraints
      await client.query(`
        ALTER TABLE metadata.clients 
        ADD CONSTRAINT domain_not_empty 
        CHECK (array_length(domain, 1) > 0)
      `);
      
      // Step 6: Create index for performance
      await client.query(`
        CREATE INDEX IF NOT EXISTS idx_admin_clients_domain_gin 
        ON metadata.clients USING GIN (domain)
      `);
      
      // Commit transaction
      await client.query('COMMIT');
      
      console.log('✅ Domain column successfully converted to array');
      
      // Verify the changes
      const verifyResult = await client.query(`
        SELECT client_id, name, domain, status 
        FROM metadata.clients 
        WHERE status = 'A'
      `);
      
      console.log('Updated clients data:', verifyResult.rows);
      
      // Test the sidebar query
      console.log('🧪 Testing sidebar query...');
      const testResult = await client.query(`
        SELECT DISTINCT
          p.id,
          p.name,
          p.header,
          p.display_order,
          p.route_path
        FROM metadata.admin_pages p
        INNER JOIN metadata.admin_pages_groups pg ON p.id = pg.page_id
        INNER JOIN metadata.page_addon_packages pap ON p.id = pap.page_id
        INNER JOIN metadata.addon_packages ap ON pap.package_id = ap.id
        INNER JOIN metadata.client_addon_packages cap ON ap.id = cap.package_id
        INNER JOIN metadata.clients c ON cap.client_id = c.client_id
        WHERE p.status = 'A'
          AND p.sidebar = true
          AND pg.group_name = ANY($1)
          AND $2 = ANY(c.domain)
          AND c.status = 'A'
          AND ap.status = 'A'
          AND cap.status = 'A'
        ORDER BY p.display_order ASC, p.name ASC
      `, [['admin', 'user'], 'renewtrack.com']);
      
      console.log('✅ Sidebar query test results:', testResult.rows);
      
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    }
    
  } catch (error) {
    console.error('❌ Error fixing domain schema:', error);
    throw error;
  } finally {
    client.release();
  }
}

async function main() {
  try {
    await fixDomainSchema();
    console.log('🎉 Database schema fix completed successfully');
  } catch (error) {
    console.error('💥 Database schema fix failed:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

if (require.main === module) {
  main();
}

module.exports = { fixDomainSchema };

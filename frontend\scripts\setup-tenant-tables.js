#!/usr/bin/env node

/**
 * Setup Tenant Tables Script
 * 
 * This script creates the necessary tenant tables for the application
 */

import { Pool } from 'pg';
import fs from 'fs';
import path from 'path';

// Database configuration
const dbConfig = {
  user: 'postgres',
  host: '127.0.0.1',
  database: 'Renewtrack',
  password: 'admin',
  port: 5432,
  ssl: false
};

async function setupTenantTables() {
  console.log('🔧 Setting up tenant tables...\n');

  const pool = new Pool(dbConfig);
  
  try {
    // Test connection
    const client = await pool.connect();
    console.log('✅ Connected to database');
    
    // Create metadata schema if it doesn't exist
    await client.query('CREATE SCHEMA IF NOT EXISTS metadata');
    console.log('✅ Metadata schema ready');
    
    // Create tenant_management schema if it doesn't exist
    await client.query('CREATE SCHEMA IF NOT EXISTS tenant_management');
    console.log('✅ Tenant management schema ready');
    
    // Create tenant schema if it doesn't exist
    const tenantSchema = 'tenant_0000000000000001';
    await client.query(`CREATE SCHEMA IF NOT EXISTS "${tenantSchema}"`);
    console.log(`✅ Tenant schema "${tenantSchema}" ready`);
    
    // Set search path
    await client.query(`SET search_path TO "${tenantSchema}", metadata, public`);
    
    // Create tenant_vendors table
    const createVendorsTable = `
      CREATE TABLE IF NOT EXISTS "${tenantSchema}".tenant_vendors (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        display_name VARCHAR(255),
        contact_email VARCHAR(255),
        phone VARCHAR(50),
        address_line1 VARCHAR(255),
        address_line2 VARCHAR(255),
        city VARCHAR(100),
        state VARCHAR(100),
        postal_code VARCHAR(20),
        country VARCHAR(100),
        tax_id VARCHAR(100),
        website VARCHAR(255),
        notes TEXT,
        global_vendor_id INTEGER,
        sync_status VARCHAR(20) DEFAULT 'pending',
        sync_confidence DECIMAL(5, 4) DEFAULT 0.0000,
        last_sync_attempt TIMESTAMP WITH TIME ZONE,
        is_deleted BOOLEAN DEFAULT false,
        created_by VARCHAR(255),
        changed_by VARCHAR(255),
        created_on TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        changed_on TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT tenant_vendors_name_not_empty CHECK (LENGTH(TRIM(name)) > 0),
        CONSTRAINT tenant_vendors_email_format CHECK (
          contact_email IS NULL
          OR contact_email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$'
        )
      );
    `;
    
    await client.query(createVendorsTable);
    console.log('✅ tenant_vendors table created');
    
    // Create tenant_products table
    const createProductsTable = `
      CREATE TABLE IF NOT EXISTS "${tenantSchema}".tenant_products (
        id SERIAL PRIMARY KEY,
        vendor_id INTEGER NOT NULL REFERENCES "${tenantSchema}".tenant_vendors (id),
        name VARCHAR(255) NOT NULL,
        description TEXT,
        category VARCHAR(100),
        sku VARCHAR(100),
        barcode VARCHAR(50),
        unit_of_measure VARCHAR(50),
        global_product_id INTEGER,
        sync_status VARCHAR(20) DEFAULT 'pending',
        sync_confidence DECIMAL(5, 4) DEFAULT 0.0000,
        last_sync_attempt TIMESTAMP WITH TIME ZONE,
        is_deleted BOOLEAN DEFAULT false,
        created_by VARCHAR(255),
        changed_by VARCHAR(255),
        created_on TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        changed_on TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT tenant_products_name_not_empty CHECK (LENGTH(TRIM(name)) > 0)
      );
    `;
    
    await client.query(createProductsTable);
    console.log('✅ tenant_products table created');
    
    // Create tenant_product_versions table
    const createVersionsTable = `
      CREATE TABLE IF NOT EXISTS "${tenantSchema}".tenant_product_versions (
        id SERIAL PRIMARY KEY,
        product_id INTEGER NOT NULL REFERENCES "${tenantSchema}".tenant_products(id),
        version VARCHAR(50) NOT NULL,
        release_date DATE,
        notes TEXT,
        is_current BOOLEAN DEFAULT false,
        custom_fields JSONB DEFAULT '{}',
        global_version_id INTEGER,
        sync_status VARCHAR(20) DEFAULT 'pending',
        sync_confidence DECIMAL(5, 4) DEFAULT 0.0000,
        last_sync_attempt TIMESTAMP WITH TIME ZONE,
        is_deleted BOOLEAN DEFAULT false,
        created_by VARCHAR(255),
        changed_by VARCHAR(255),
        created_on TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        changed_on TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        CONSTRAINT tenant_product_versions_version_not_empty CHECK (LENGTH(TRIM(version)) > 0)
      );
    `;
    
    await client.query(createVersionsTable);
    console.log('✅ tenant_product_versions table created');
    
    // Create indexes
    try {
      await client.query(`CREATE INDEX IF NOT EXISTS idx_tenant_vendors_name ON "${tenantSchema}".tenant_vendors (name)`);
      await client.query(`CREATE INDEX IF NOT EXISTS idx_tenant_vendors_deleted ON "${tenantSchema}".tenant_vendors (is_deleted)`);
      await client.query(`CREATE INDEX IF NOT EXISTS idx_tenant_products_vendor ON "${tenantSchema}".tenant_products (vendor_id)`);
      await client.query(`CREATE INDEX IF NOT EXISTS idx_tenant_products_name ON "${tenantSchema}".tenant_products (name)`);
      await client.query(`CREATE INDEX IF NOT EXISTS idx_tenant_products_deleted ON "${tenantSchema}".tenant_products (is_deleted)`);
      await client.query(`CREATE INDEX IF NOT EXISTS idx_tenant_product_versions_product ON "${tenantSchema}".tenant_product_versions (product_id)`);
      await client.query(`CREATE INDEX IF NOT EXISTS idx_tenant_product_versions_deleted ON "${tenantSchema}".tenant_product_versions (is_deleted)`);
      console.log('✅ Indexes created');
    } catch (indexError) {
      console.log('⚠️ Some indexes may already exist or have issues:', indexError.message);
    }
    
    // Test the tables by inserting a sample vendor
    const testVendor = await client.query(`
      INSERT INTO "${tenantSchema}".tenant_vendors (name, display_name, created_by)
      VALUES ('Microsoft Corporation', 'Microsoft', 'system')
      ON CONFLICT DO NOTHING
      RETURNING id, name
    `);
    
    if (testVendor.rows.length > 0) {
      console.log('✅ Sample vendor created:', testVendor.rows[0]);
    } else {
      console.log('✅ Sample vendor already exists');
    }
    
    client.release();
    console.log('\n🎉 Tenant tables setup completed successfully!');
    
  } catch (error) {
    console.error('❌ Error setting up tenant tables:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Run the setup
setupTenantTables().catch(console.error);

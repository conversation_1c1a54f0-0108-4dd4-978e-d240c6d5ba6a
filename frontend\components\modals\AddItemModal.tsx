/**
 * Add Item Modal Component
 * 
 * A modal for adding new vendors, products, or versions
 * with proper form validation and tenant context
 */

'use client';

import React, { useState, useCallback } from 'react';
import { z } from 'zod';
import { Mo<PERSON>, ModalFooter } from '@/components/ui/Modal';
import { Button } from '@/components/ui/Button';
import { Form } from '@/components/ui/Form';
import { useToast } from '@/components/ui/Toast';

// Validation schemas
const vendorSchema = z.object({
  name: z.string().min(1, 'Vendor name is required').max(255),
  display_name: z.string().max(255).optional(),
  contact_email: z.string().email('Invalid email').optional().or(z.literal('')),
  phone: z.string().max(50).optional(),
  website: z.string().url('Invalid URL').optional().or(z.literal('')),
  notes: z.string().optional(),
});

const productSchema = z.object({
  name: z.string().min(1, 'Product name is required').max(255),
  description: z.string().optional(),
  category: z.string().max(100).optional(),
  sku: z.string().max(100).optional(),
});

const versionSchema = z.object({
  version: z.string().min(1, 'Version is required').max(50),
  release_date: z.string().optional(),
  notes: z.string().optional(),
  is_current: z.boolean().optional().default(false),
});

export type ItemType = 'vendor' | 'product' | 'version';

export interface AddItemModalProps {
  isOpen: boolean;
  onClose: () => void;
  itemType: ItemType;
  parentId?: string; // vendor_id for products, product_id for versions
  onSuccess: (item: any) => void;
}

interface FormData {
  [key: string]: any;
}

export function AddItemModal({
  isOpen,
  onClose,
  itemType,
  parentId,
  onSuccess
}: AddItemModalProps) {
  const [formData, setFormData] = useState<FormData>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const toast = useToast();

  const getSchema = useCallback(() => {
    switch (itemType) {
      case 'vendor':
        return vendorSchema;
      case 'product':
        return productSchema;
      case 'version':
        return versionSchema;
      default:
        throw new Error(`Unknown item type: ${itemType}`);
    }
  }, [itemType]);

  const getTitle = useCallback(() => {
    switch (itemType) {
      case 'vendor':
        return 'Add New Vendor';
      case 'product':
        return 'Add New Product';
      case 'version':
        return 'Add New Version';
      default:
        return 'Add New Item';
    }
  }, [itemType]);

  const getApiEndpoint = useCallback(() => {
    switch (itemType) {
      case 'vendor':
        return '/api/tenant-vendors';
      case 'product':
        return '/api/tenant-products';
      case 'version':
        return '/api/tenant-product-versions';
      default:
        throw new Error(`Unknown item type: ${itemType}`);
    }
  }, [itemType]);

  const handleInputChange = useCallback((field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  }, [errors]);

  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    
    const schema = getSchema();
    
    // Add parent ID if needed
    const dataToValidate = { ...formData };
    if (itemType === 'product' && parentId) {
      dataToValidate.vendor_id = parentId;
    } else if (itemType === 'version' && parentId) {
      dataToValidate.product_id = parentId;
    }

    // Validate form data
    const validationResult = schema.safeParse(dataToValidate);
    if (!validationResult.success) {
      const newErrors: Record<string, string> = {};
      validationResult.error.errors.forEach(error => {
        if (error.path.length > 0) {
          newErrors[error.path[0] as string] = error.message;
        }
      });
      setErrors(newErrors);
      return;
    }

    setIsSubmitting(true);
    setErrors({});

    try {
      const response = await fetch(getApiEndpoint(), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(validationResult.data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create item');
      }

      const result = await response.json();
      toast.success(`${getTitle().replace('Add New ', '')} created successfully!`);
      onSuccess(result.data);
      handleClose();
    } catch (error) {
      console.error('Error creating item:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to create item');
    } finally {
      setIsSubmitting(false);
    }
  }, [formData, parentId, itemType, getSchema, getApiEndpoint, getTitle, onSuccess, toast]);

  const handleClose = useCallback(() => {
    if (!isSubmitting) {
      setFormData({});
      setErrors({});
      onClose();
    }
  }, [isSubmitting, onClose]);

  const renderVendorForm = () => (
    <>
      <Form.Field>
        <Form.Label htmlFor="name" required>
          Vendor Name
        </Form.Label>
        <Form.Input
          id="name"
          type="text"
          placeholder="Enter vendor name"
          value={formData.name || ''}
          onChange={(e) => handleInputChange('name', e.target.value)}
          error={errors.name}
          required
        />
        {errors.name && <Form.Error>{errors.name}</Form.Error>}
      </Form.Field>

      <Form.Field>
        <Form.Label htmlFor="display_name">
          Display Name
        </Form.Label>
        <Form.Input
          id="display_name"
          type="text"
          placeholder="Display name (optional)"
          value={formData.display_name || ''}
          onChange={(e) => handleInputChange('display_name', e.target.value)}
        />
        <Form.Help>
          A user-friendly name for display purposes
        </Form.Help>
      </Form.Field>

      <Form.Grid columns={2}>
        <Form.Field>
          <Form.Label htmlFor="contact_email">
            Contact Email
          </Form.Label>
          <Form.Input
            id="contact_email"
            type="email"
            placeholder="<EMAIL>"
            value={formData.contact_email || ''}
            onChange={(e) => handleInputChange('contact_email', e.target.value)}
            error={errors.contact_email}
          />
          {errors.contact_email && <Form.Error>{errors.contact_email}</Form.Error>}
        </Form.Field>

        <Form.Field>
          <Form.Label htmlFor="phone">
            Phone
          </Form.Label>
          <Form.Input
            id="phone"
            type="tel"
            placeholder="+****************"
            value={formData.phone || ''}
            onChange={(e) => handleInputChange('phone', e.target.value)}
          />
        </Form.Field>
      </Form.Grid>

      <Form.Field>
        <Form.Label htmlFor="website">
          Website
        </Form.Label>
        <Form.Input
          id="website"
          type="url"
          placeholder="https://vendor.com"
          value={formData.website || ''}
          onChange={(e) => handleInputChange('website', e.target.value)}
          error={errors.website}
        />
        {errors.website && <Form.Error>{errors.website}</Form.Error>}
      </Form.Field>

      <Form.Field>
        <Form.Label htmlFor="notes">
          Notes
        </Form.Label>
        <Form.Textarea
          id="notes"
          placeholder="Additional notes about this vendor"
          rows={3}
          value={formData.notes || ''}
          onChange={(e) => handleInputChange('notes', e.target.value)}
        />
      </Form.Field>
    </>
  );

  const renderProductForm = () => (
    <>
      <Form.Field>
        <Form.Label htmlFor="name" required>
          Product
        </Form.Label>
        <Form.Input
          id="name"
          type="text"
          placeholder="Enter product name"
          value={formData.name || ''}
          onChange={(e) => handleInputChange('name', e.target.value)}
          error={errors.name}
          required
        />
        {errors.name && <Form.Error>{errors.name}</Form.Error>}
      </Form.Field>

      <Form.Field>
        <Form.Label htmlFor="description">
          Description
        </Form.Label>
        <Form.Textarea
          id="description"
          placeholder="Brief description of the product"
          rows={3}
          value={formData.description || ''}
          onChange={(e) => handleInputChange('description', e.target.value)}
        />
      </Form.Field>

      <Form.Grid columns={2}>
        <Form.Field>
          <Form.Label htmlFor="category">
            Category
          </Form.Label>
          <Form.Input
            id="category"
            type="text"
            placeholder="e.g., Software, Hardware"
            value={formData.category || ''}
            onChange={(e) => handleInputChange('category', e.target.value)}
          />
        </Form.Field>

        <Form.Field>
          <Form.Label htmlFor="sku">
            SKU
          </Form.Label>
          <Form.Input
            id="sku"
            type="text"
            placeholder="Product SKU"
            value={formData.sku || ''}
            onChange={(e) => handleInputChange('sku', e.target.value)}
          />
        </Form.Field>
      </Form.Grid>
    </>
  );

  const renderVersionForm = () => (
    <>
      <Form.Field>
        <Form.Label htmlFor="version" required>
          Version
        </Form.Label>
        <Form.Input
          id="version"
          type="text"
          placeholder="e.g., 1.0, v2.1, 2024"
          value={formData.version || ''}
          onChange={(e) => handleInputChange('version', e.target.value)}
          error={errors.version}
          required
        />
        {errors.version && <Form.Error>{errors.version}</Form.Error>}
      </Form.Field>

      <Form.Field>
        <Form.Label htmlFor="release_date">
          Release Date
        </Form.Label>
        <Form.Input
          id="release_date"
          type="date"
          value={formData.release_date || ''}
          onChange={(e) => handleInputChange('release_date', e.target.value)}
        />
      </Form.Field>

      <Form.Field>
        <Form.Label htmlFor="is_current">
          <Form.Checkbox
            id="is_current"
            checked={formData.is_current || false}
            onChange={(e) => handleInputChange('is_current', e.target.checked)}
          />
          Mark as current version
        </Form.Label>
        <Form.Help>
          If checked, this will become the current version for this product
        </Form.Help>
      </Form.Field>

      <Form.Field>
        <Form.Label htmlFor="notes">
          Notes
        </Form.Label>
        <Form.Textarea
          id="notes"
          placeholder="Additional notes about this version"
          rows={3}
          value={formData.notes || ''}
          onChange={(e) => handleInputChange('notes', e.target.value)}
        />
      </Form.Field>
    </>
  );

  const renderForm = () => {
    switch (itemType) {
      case 'vendor':
        return renderVendorForm();
      case 'product':
        return renderProductForm();
      case 'version':
        return renderVersionForm();
      default:
        return null;
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title={getTitle()}
      subtitle={`Add a new ${itemType} to your inventory`}
      size="md"
    >
      <Form.Root onSubmit={handleSubmit}>
        {renderForm()}
        
        <ModalFooter>
          <Button
            type="button"
            variant="ghost"
            onClick={handleClose}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="primary"
            isLoading={isSubmitting}
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Creating...' : `Create ${itemType.charAt(0).toUpperCase() + itemType.slice(1)}`}
          </Button>
        </ModalFooter>
      </Form.Root>
    </Modal>
  );
}

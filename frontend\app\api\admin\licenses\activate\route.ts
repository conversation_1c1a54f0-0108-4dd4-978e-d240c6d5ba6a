/**
 * License Activation API
 * 
 * Admin endpoint for activating license keys for clients
 * POST /api/admin/licenses/activate - Activate license for client
 */

import { NextRequest } from 'next/server';
import { fetchAuthSession, fetchUserAttributes } from 'aws-amplify/auth';
import { executeQuery, executeQuerySingle } from '@/lib/database';
import {
  createSuccessResponse,
  createErrorResponse,
  ApiErrorCode,
  HttpStatus,
  withErrorHandling
} from '@/lib/api/response';

interface LicenseActivationRequest {
  licenseKey: string;
  clientId: number;
  customExpiryDays?: number;
}

interface LicenseValidation {
  id: number;
  licenseKey: string;
  licenseType: string;
  maxUsers: number;
  maxTenants: number;
  features: any;
  validUntil: string | null;
  isActive: boolean;
  usageCount: number;
  maxUsage: number;
}

interface ClientInfo {
  client_id: number;
  name: string;
  status: string;
}

/**
 * Activate license key for a client (Admin only)
 */
export const POST = withErrorHandling(async (request: NextRequest) => {
  try {
    // Verify authentication
    const session = await fetchAuthSession();
    if (!session?.tokens?.idToken) {
      return createErrorResponse('Authentication required', ApiErrorCode.UNAUTHORIZED, HttpStatus.UNAUTHORIZED);
    }

    // Get user attributes to check admin status
    const attributes = await fetchUserAttributes();
    const userGroups = attributes['cognito:groups']?.split(',') || [];
    const adminGroups = ['admin', 'super-admin']

    if (!userGroups.some(group => adminGroups.includes(group.toLowerCase()))) {
      return createErrorResponse('Admin access required', ApiErrorCode.FORBIDDEN, HttpStatus.FORBIDDEN);
    }

    // Parse request body
    const body: LicenseActivationRequest = await request.json();
    const { licenseKey, clientId, customExpiryDays } = body;

    // Validate input
    if (!licenseKey || !clientId) {
      return createErrorResponse('License key and client ID are required', ApiErrorCode.VALIDATION_ERROR, HttpStatus.BAD_REQUEST);
    }

    // 1. Validate license key exists and is available
    const licenseQuery = `
      SELECT * FROM licensing.license_keys
      WHERE license_key = $1 AND is_active = true
    `;

    const licenseResult = await executeQuerySingle<LicenseValidation>(licenseQuery, [licenseKey]);

    if (!licenseResult.success || !licenseResult.data) {
      return createErrorResponse('Invalid or inactive license key', ApiErrorCode.NOT_FOUND, HttpStatus.NOT_FOUND);
    }

    const license = licenseResult.data;

    // 2. Check if license key has expired
    if (license.validUntil && new Date(license.validUntil) < new Date()) {
      return createErrorResponse('License key has expired', ApiErrorCode.VALIDATION_ERROR, HttpStatus.BAD_REQUEST);
    }

    // 3. Check if license key usage limit exceeded
    if (license.usageCount >= license.maxUsage) {
      return createErrorResponse('License key usage limit exceeded', ApiErrorCode.VALIDATION_ERROR, HttpStatus.BAD_REQUEST);
    }

    // 4. Validate client exists and is active
    const clientQuery = `
      SELECT client_id, name, status
      FROM metadata.clients
      WHERE id = $1 AND status = 'A'
    `;

    const clientResult = await executeQuerySingle<ClientInfo>(clientQuery, [clientId]);

    if (!clientResult.success || !clientResult.data) {
      return createErrorResponse('Client not found or inactive', ApiErrorCode.NOT_FOUND, HttpStatus.NOT_FOUND);
    }

    const client = clientResult.data;

    // 5. Check if client already has an active license
    const existingActivationQuery = `
      SELECT id FROM licensing.client_activations
      WHERE client_id = $1 AND activation_status = 'active'
    `;

    const existingResult = await executeQuerySingle(existingActivationQuery, [clientId]);

    if (existingResult.success && existingResult.data) {
      return createErrorResponse('Client already has an active license', ApiErrorCode.CONFLICT, HttpStatus.CONFLICT);
    }

    // 6. Calculate expiration date
    let expiresAt = null;
    if (customExpiryDays) {
      expiresAt = new Date(Date.now() + customExpiryDays * 24 * 60 * 60 * 1000).toISOString();
    } else if (license.validUntil) {
      expiresAt = license.validUntil;
    }

    // 7. Create activation record
    const activationQuery = `
      INSERT INTO licensing.client_activations (
        client_id,
        license_key_id,
        license_key,
        activated_by,
        expires_at,
        created_by,
        changed_by
      ) VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING *
    `;

    const activationResult = await executeQuerySingle(activationQuery, [
      clientId,
      license.id,
      licenseKey,
      attributes.email,
      expiresAt,
      attributes.email,
      attributes.email
    ]);

    if (!activationResult.success) {
      return createErrorResponse('Failed to activate license', ApiErrorCode.INTERNAL_ERROR, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    // 8. Update license key usage count
    const updateUsageQuery = `
      UPDATE licensing.license_keys
      SET usage_count = usage_count + 1,
          changed_on = CURRENT_TIMESTAMP,
          changed_by = $2
      WHERE id = $1
    `;

    await executeQuery(updateUsageQuery, [license.id, attributes.email]);

    // 9. Log the activation
    const logQuery = `
      INSERT INTO licensing.usage_logs (
        client_id,
        license_key,
        event_type,
        user_email
      ) VALUES ($1, $2, 'license_activated', $3)
    `;

    await executeQuery(logQuery, [clientId, licenseKey, attributes.email]);

    return createSuccessResponse({
      activation: activationResult.data,
      client: client,
      license: {
        key: license.licenseKey,
        type: license.licenseType,
        maxUsers: license.maxUsers,
        maxTenants: license.maxTenants,
        features: license.features,
        expiresAt
      },
      message: `License successfully activated for ${client.name}`
    });

  } catch (error) {
    console.error('License activation error:', error);
    return createErrorResponse('Failed to activate license', ApiErrorCode.INTERNAL_ERROR, HttpStatus.INTERNAL_SERVER_ERROR);
  }
});

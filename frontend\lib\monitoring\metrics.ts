/**
 * Metrics Collection System
 * 
 * Comprehensive metrics collection for performance monitoring and analytics
 */

interface Metric {
  name: string
  value: number
  timestamp: number
  tags?: Record<string, string>
  type: 'counter' | 'gauge' | 'histogram' | 'timer'
}

interface PerformanceMetric {
  name: string
  duration: number
  timestamp: number
  tags?: Record<string, string>
}

interface UserMetric {
  userId?: string
  sessionId: string
  action: string
  timestamp: number
  context?: Record<string, any>
}

interface SystemMetric {
  name: string
  value: number
  timestamp: number
  system: 'memory' | 'network' | 'storage' | 'cpu'
}

class MetricsCollector {
  private static instance: MetricsCollector
  private metrics: Metric[] = []
  private performanceMetrics: PerformanceMetric[] = []
  private userMetrics: UserMetric[] = []
  private systemMetrics: SystemMetric[] = []
  private timers: Map<string, number> = new Map()
  private counters: Map<string, number> = new Map()
  private gauges: Map<string, number> = new Map()
  private sessionId: string
  private flushTimer?: NodeJS.Timeout

  private constructor() {
    this.sessionId = this.generateSessionId()
    this.startPerformanceMonitoring()
    this.startFlushTimer()
  }

  static getInstance(): MetricsCollector {
    if (!MetricsCollector.instance) {
      MetricsCollector.instance = new MetricsCollector()
    }
    return MetricsCollector.instance
  }

  private generateSessionId(): string {
    return `metrics_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private startPerformanceMonitoring(): void {
    if (typeof window === 'undefined') return

    // Monitor page load performance
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
        
        if (navigation) {
          this.recordPerformance('page_load_time', navigation.loadEventEnd - navigation.fetchStart)
          this.recordPerformance('dom_content_loaded', navigation.domContentLoadedEventEnd - navigation.fetchStart)
          this.recordPerformance('first_paint', navigation.responseEnd - navigation.fetchStart)
        }
      }, 0)
    })

    // Monitor resource loading
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'resource') {
          const resourceEntry = entry as PerformanceResourceTiming
          this.recordPerformance('resource_load_time', resourceEntry.duration, {
            resource_type: resourceEntry.initiatorType,
            resource_name: resourceEntry.name.split('/').pop() || 'unknown'
          })
        }
      }
    })

    try {
      observer.observe({ entryTypes: ['resource'] })
    } catch (error) {
      console.warn('Performance observer not supported:', error)
    }
  }

  private startFlushTimer(): void {
    this.flushTimer = setInterval(() => {
      this.flush()
    }, 60000) // Flush every minute
  }

  // Counter methods
  increment(name: string, value: number = 1, tags?: Record<string, string>): void {
    const current = this.counters.get(name) || 0
    this.counters.set(name, current + value)
    
    this.addMetric({
      name,
      value: current + value,
      timestamp: Date.now(),
      tags,
      type: 'counter'
    })
  }

  decrement(name: string, value: number = 1, tags?: Record<string, string>): void {
    this.increment(name, -value, tags)
  }

  // Gauge methods
  setGauge(name: string, value: number, tags?: Record<string, string>): void {
    this.gauges.set(name, value)
    
    this.addMetric({
      name,
      value,
      timestamp: Date.now(),
      tags,
      type: 'gauge'
    })
  }

  // Timer methods
  startTimer(name: string): void {
    this.timers.set(name, performance.now())
  }

  endTimer(name: string, tags?: Record<string, string>): number {
    const startTime = this.timers.get(name)
    if (!startTime) {
      console.warn(`Timer ${name} was not started`)
      return 0
    }

    const duration = performance.now() - startTime
    this.timers.delete(name)

    this.addMetric({
      name,
      value: duration,
      timestamp: Date.now(),
      tags,
      type: 'timer'
    })

    return duration
  }

  // Performance metrics
  recordPerformance(name: string, duration: number, tags?: Record<string, string>): void {
    this.performanceMetrics.push({
      name,
      duration,
      timestamp: Date.now(),
      tags
    })
  }

  // User action metrics
  recordUserAction(action: string, context?: Record<string, any>): void {
    this.userMetrics.push({
      userId: this.getCurrentUserId(),
      sessionId: this.sessionId,
      action,
      timestamp: Date.now(),
      context
    })
  }

  // System metrics
  recordSystemMetric(name: string, value: number, system: SystemMetric['system']): void {
    this.systemMetrics.push({
      name,
      value,
      timestamp: Date.now(),
      system
    })
  }

  // Web Vitals
  recordWebVital(name: string, value: number, rating: 'good' | 'needs-improvement' | 'poor'): void {
    this.addMetric({
      name: `web_vital_${name}`,
      value,
      timestamp: Date.now(),
      tags: { rating },
      type: 'gauge'
    })
  }

  // Error metrics
  recordError(errorType: string, errorMessage: string, context?: Record<string, any>): void {
    this.increment('errors_total', 1, { 
      error_type: errorType,
      error_message: errorMessage.substring(0, 100) // Truncate long messages
    })

    this.recordUserAction('error_occurred', {
      errorType,
      errorMessage,
      ...context
    })
  }

  // API metrics
  recordApiCall(method: string, endpoint: string, status: number, duration: number): void {
    this.increment('api_calls_total', 1, {
      method,
      endpoint: endpoint.replace(/\/\d+/g, '/:id'), // Normalize dynamic segments
      status: status.toString(),
      status_class: Math.floor(status / 100) + 'xx'
    })

    this.recordPerformance('api_call_duration', duration, {
      method,
      endpoint,
      status: status.toString()
    })
  }

  // Component metrics
  recordComponentRender(componentName: string, renderTime: number): void {
    this.recordPerformance('component_render_time', renderTime, {
      component: componentName
    })
  }

  recordComponentMount(componentName: string): void {
    this.increment('component_mounts_total', 1, {
      component: componentName
    })
  }

  recordComponentUnmount(componentName: string): void {
    this.increment('component_unmounts_total', 1, {
      component: componentName
    })
  }

  // Memory metrics
  recordMemoryUsage(): void {
    if (typeof window !== 'undefined' && 'memory' in performance) {
      const memory = (performance as any).memory
      
      this.recordSystemMetric('memory_used_bytes', memory.usedJSHeapSize, 'memory')
      this.recordSystemMetric('memory_total_bytes', memory.totalJSHeapSize, 'memory')
      this.recordSystemMetric('memory_limit_bytes', memory.jsHeapSizeLimit, 'memory')
    }
  }

  // Network metrics
  recordNetworkInfo(): void {
    if (typeof navigator !== 'undefined' && 'connection' in navigator) {
      const connection = (navigator as any).connection
      
      if (connection) {
        this.setGauge('network_downlink_mbps', connection.downlink || 0, {
          effective_type: connection.effectiveType || 'unknown'
        })
        
        this.setGauge('network_rtt_ms', connection.rtt || 0, {
          effective_type: connection.effectiveType || 'unknown'
        })
      }
    }
  }

  // Helper methods
  private addMetric(metric: Metric): void {
    this.metrics.push(metric)
  }

  private getCurrentUserId(): string | undefined {
    try {
      if (typeof window !== 'undefined') {
        const userSession = sessionStorage.getItem('user-session')
        if (userSession) {
          const parsed = JSON.parse(userSession)
          return parsed.userId
        }
      }
    } catch {
      // Ignore errors
    }
    return undefined
  }

  // Data export methods
  getMetrics(): Metric[] {
    return [...this.metrics]
  }

  getPerformanceMetrics(): PerformanceMetric[] {
    return [...this.performanceMetrics]
  }

  getUserMetrics(): UserMetric[] {
    return [...this.userMetrics]
  }

  getSystemMetrics(): SystemMetric[] {
    return [...this.systemMetrics]
  }

  // Summary methods
  getSummary(): {
    totalMetrics: number
    totalPerformanceMetrics: number
    totalUserActions: number
    totalSystemMetrics: number
    sessionId: string
    uptime: number
  } {
    return {
      totalMetrics: this.metrics.length,
      totalPerformanceMetrics: this.performanceMetrics.length,
      totalUserActions: this.userMetrics.length,
      totalSystemMetrics: this.systemMetrics.length,
      sessionId: this.sessionId,
      uptime: Date.now() - parseInt(this.sessionId.split('_')[1])
    }
  }

  // Flush metrics to server
  private async flush(): Promise<void> {
    if (this.metrics.length === 0 && this.performanceMetrics.length === 0 && 
        this.userMetrics.length === 0 && this.systemMetrics.length === 0) {
      return
    }

    const payload = {
      sessionId: this.sessionId,
      timestamp: Date.now(),
      metrics: this.getMetrics(),
      performanceMetrics: this.getPerformanceMetrics(),
      userMetrics: this.getUserMetrics(),
      systemMetrics: this.getSystemMetrics()
    }

    try {
      await fetch('/api/metrics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(payload),
        credentials: 'include'
      })

      // Clear metrics after successful flush
      this.metrics = []
      this.performanceMetrics = []
      this.userMetrics = []
      this.systemMetrics = []
    } catch (error) {
      console.error('Failed to flush metrics:', error)
    }
  }

  // Cleanup
  destroy(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer)
    }
    this.flush()
  }
}

// Export singleton instance
export const metrics = MetricsCollector.getInstance()

// Export types
export type { Metric, PerformanceMetric, UserMetric, SystemMetric }

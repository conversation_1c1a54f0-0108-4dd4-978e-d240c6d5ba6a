/**
 * Simplified Application Context
 *
 * Uses focused hooks for authentication and tenant management to follow
 * single responsibility principle and improve maintainability.
 */

'use client';

import React, { createContext, useContext, ReactNode } from 'react';
import { TenantContext as TenantContextType, User } from '@/lib/types';

export interface AppState {
  // Authentication state
  isAuthenticated: boolean;
  isLoading: boolean;
  user: User | null;

  // Tenant/Client state
  tenant: TenantContextType | null;
  tenantLoading: boolean;
  tenantError: string | null;

  // Actions
  logout: () => Promise<boolean>;
  refreshTenant: () => Promise<void>;
}

const AppContext = createContext<AppState | undefined>(undefined);

interface AppProviderProps {
  children: ReactNode;
}

export function AppProvider({ children }: AppProviderProps) {
  // Provide minimal default state to prevent blocking
  const value: AppState = {
    // Authentication state
    isAuthenticated: false,
    isLoading: false,
    user: null,

    // Tenant state
    tenant: null,
    tenantLoading: false,
    tenantError: null,

    // Actions - provide no-op functions
    logout: async () => {
      console.log('🔄 [APP-CONTEXT] Logout called')
      return true
    },
    refreshTenant: async () => {
      console.log('🔄 [APP-CONTEXT] Refresh tenant called')
    },
  };

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
}

// Custom hook for using app context
export const useApp = () => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};

// Backward compatibility hooks - DEPRECATED
// Use the useAuth hook from @/lib/hooks/useAuth instead

// DEPRECATED: Use useAuth from @/lib/hooks/useAuth instead
export const useAuthDeprecated = () => {
  const { isAuthenticated, isLoading, user, logout } = useApp();
  return { isAuthenticated, isLoading, user, logout };
};

// DEPRECATED: Use useAuth from @/lib/hooks/useAuth instead
export const useTenantDeprecated = () => {
  const { tenant, tenantLoading, tenantError, refreshTenant } = useApp();
  return { tenant, loading: tenantLoading, error: tenantError, refreshTenant };
};

// DEPRECATED: Use useAuth from @/lib/hooks/useAuth instead
export const useUserDeprecated = () => {
  const { user } = useApp();
  return user;
};

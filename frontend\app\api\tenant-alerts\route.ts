/**
 * Tenant Alerts API Endpoint
 * 
 * Provides CRUD operations for tenant-specific renewal alerts
 * GET /api/tenant-alerts?renewal_id=xxx - Returns alerts for a specific renewal
 * POST /api/tenant-alerts - Creates a new alert for the tenant
 */

import { NextRequest } from 'next/server';
import { fetchAuthSession, fetchUserAttributes } from 'aws-amplify/auth';
import { getClientByEmailDomain } from '@/lib/tenant/clients';
import { executeTenantQuery, executeTenantQuerySingle } from '@/lib/tenant/database';
import {
  createSuccessResponse,
  createErrorResponse,
  ApiErrorCode,
  HttpStatus,
  withErrorHandling
} from '@/lib/api/response';
import { z } from 'zod';

// Alert interface
export interface TenantAlert {
  alert_id: number;
  renewal_id: number;
  alert_name: string;
  days_before_renewal: number;
  email_recipients: string[];
  is_active: boolean;
  last_sent?: string;
  created_on: string;
  changed_on: string;
  created_by?: string;
  modified_by?: string;
}

// Validation schemas
const createAlertSchema = z.object({
  renewal_id: z.number().int().positive(),
  alert_name: z.string().min(1).max(255),
  days_before_renewal: z.number().int().min(1).max(365),
  email_recipients: z.array(z.string().email()).min(1),
  is_active: z.boolean().optional().default(true)
});

// GET /api/tenant-alerts
export const GET = withErrorHandling(async (request: NextRequest) => {
  try {
    // Get authentication session and user attributes
    const session = await fetchAuthSession();
    if (!session.tokens?.accessToken) {
      return createErrorResponse(
        'Authentication required',
        ApiErrorCode.UNAUTHORIZED,
        HttpStatus.UNAUTHORIZED
      );
    }

    const userAttributes = await fetchUserAttributes();
    if (!userAttributes.email) {
      return createErrorResponse(
        'User email not found',
        ApiErrorCode.UNAUTHORIZED,
        HttpStatus.UNAUTHORIZED
      );
    }

    // Get tenant context using direct authentication pattern
    const clientResult = await getClientByEmailDomain(userAttributes.email);

    if (!clientResult.success) {
      return createErrorResponse(
        'Failed to fetch tenant information',
        ApiErrorCode.DATABASE_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }

    const tenantContext = clientResult.client!;

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const renewalId = searchParams.get('renewal_id');

    let query: string;
    let params: any[];

    if (renewalId) {
      // Get alerts for specific renewal
      query = `
        SELECT 
          alert_id,
          renewal_id,
          alert_name,
          days_before_renewal,
          email_recipients,
          is_active,
          last_sent,
          created_on,
          changed_on,
          created_by,
          modified_by
        FROM tenant_alerts 
        WHERE renewal_id = $1
        ORDER BY days_before_renewal ASC
      `;
      params = [parseInt(renewalId)];
    } else {
      // Get all alerts for tenant
      query = `
        SELECT 
          a.alert_id,
          a.renewal_id,
          a.alert_name,
          a.days_before_renewal,
          a.email_recipients,
          a.is_active,
          a.last_sent,
          a.created_on,
          a.changed_on,
          a.created_by,
          a.modified_by,
          r.renewal_type,
          r.vendor_name,
          r.product_name,
          r.start_date
        FROM tenant_alerts a
        JOIN tenant_renewals r ON a.renewal_id = r.id
        WHERE a.is_active = true
        ORDER BY r.start_date ASC, a.days_before_renewal ASC
      `;
      params = [];
    }

    const result = await executeTenantQuery<TenantAlert>(
      query,
      params,
      tenantContext
    );

    if (!result.success) {
      return createErrorResponse(
        result.error || 'Failed to fetch alerts',
        ApiErrorCode.DATABASE_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }

    return createSuccessResponse(result.data || []);

  } catch (error) {
    console.error('Error in tenant-alerts GET:', error);
    return createErrorResponse(
      'Internal server error',
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
});

// POST /api/tenant-alerts
export const POST = withErrorHandling(async (request: NextRequest) => {
  try {
    // Get authentication session
    const session = await fetchAuthSession();
    if (!session.tokens?.accessToken) {
      return createErrorResponse(
        'Authentication required',
        ApiErrorCode.UNAUTHORIZED,
        HttpStatus.UNAUTHORIZED
      );
    }

    // Get user attributes
    const userAttributes = await fetchUserAttributes();
    if (!userAttributes.email) {
      return createErrorResponse(
        'User email not found',
        ApiErrorCode.UNAUTHORIZED,
        HttpStatus.UNAUTHORIZED
      );
    }

    // Get tenant context using direct authentication pattern
    const clientResult = await getClientByEmailDomain(userAttributes.email);

    if (!clientResult.success) {
      return createErrorResponse(
        'Failed to fetch tenant information',
        ApiErrorCode.DATABASE_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }

    const tenantContext = clientResult.client!;

    // Parse and validate request body
    const body = await request.json();
    const validatedData = createAlertSchema.parse(body);

    // Verify renewal exists and belongs to tenant
    const renewalCheck = await executeTenantQuerySingle(
      'SELECT id FROM tenant_renewals WHERE id = $1',
      [validatedData.renewal_id],
      tenantContext
    );

    if (!renewalCheck.success || !renewalCheck.data) {
      return createErrorResponse(
        'Renewal not found',
        ApiErrorCode.NOT_FOUND,
        HttpStatus.NOT_FOUND
      );
    }

    // Create the alert
    const insertQuery = `
      INSERT INTO tenant_alerts (
        renewal_id,
        alert_name,
        days_before_renewal,
        email_recipients,
        is_active,
        created_by,
        modified_by
      ) VALUES ($1, $2, $3, $4, $5, $6, $7)
      RETURNING alert_id, renewal_id, alert_name, days_before_renewal, 
                email_recipients, is_active, created_on, changed_on
    `;

    const result = await executeTenantQuerySingle<TenantAlert>(
      insertQuery,
      [
        validatedData.renewal_id,
        validatedData.alert_name,
        validatedData.days_before_renewal,
        validatedData.email_recipients,
        validatedData.is_active,
        userAttributes.email,
        userAttributes.email
      ],
      tenantContext
    );

    if (!result.success || !result.data) {
      return createErrorResponse(
        result.error || 'Failed to create alert',
        ApiErrorCode.DATABASE_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }

    return createSuccessResponse(result.data, 'Alert created successfully', HttpStatus.CREATED);

  } catch (error) {
    if (error instanceof z.ZodError) {
      return createErrorResponse(
        'Invalid request data',
        ApiErrorCode.VALIDATION_ERROR,
        HttpStatus.BAD_REQUEST,
        error.errors
      );
    }

    console.error('Error in tenant-alerts POST:', error);
    return createErrorResponse(
      'Internal server error',
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
});

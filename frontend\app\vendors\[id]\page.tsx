/**
 * Vendor Detail Page
 * 
 * Displays comprehensive vendor information including:
 * - Vendor profile and contact information
 * - Renewal history and upcoming renewals
 * - Product portfolio
 * - Performance metrics and analytics
 * - Risk assessment
 */

'use client'

import { useState, useCallback, useMemo } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { useData } from '@/lib/hooks'
import { useTenant } from '@/lib/hooks/useTenant'

// Components
import { PageErrorBoundary, ChartErrorBoundary } from '@/components/common'
import { LoadingSpinner } from '@/components/common/LoadingStates'
import { <PERSON>Header, <PERSON><PERSON>, Card, CardHeader, CardContent } from '@/components/ui'
import { 
  VendorSpendChart,
  VendorMetricsSummary
} from '@/components/vendor-dashboard'

// Types
interface VendorDetail {
  id: string
  name: string
  display_name?: string
  contact_email?: string
  phone?: string
  website?: string
  address_line1?: string
  address_line2?: string
  city?: string
  state?: string
  postal_code?: string
  country?: string
  tax_id?: string
  notes?: string
  created_on: string
  changed_on: string
  is_deleted: boolean
}

interface VendorAnalytics {
  total_renewals: number
  unique_products: number
  total_annual_cost: number
  avg_renewal_cost: number
  upcoming_renewals_90d: number
  overdue_renewals: number
  avg_reliability_score: number
  first_renewal_date: string
  last_activity_date: string
  relationship_days: number
  currencies: string[]
  categories: string[]
}

interface VendorRenewal {
  id: string
  name: string
  product_name: string
  annual_cost: number
  currency: string
  due_date: string
  status: string
  reliability_score: number
  category?: string
}

interface VendorDetailData {
  vendor: VendorDetail
  analytics: VendorAnalytics
  renewals: VendorRenewal[]
  risk_analysis?: {
    overall_risk_score: number
    risk_level: string
    risk_factors: {
      concentration: string
      reliability: string
      overdue_renewals: string
    }
    recommendations: string[]
  }
}

export default function VendorDetailPage() {
  const params = useParams()
  const router = useRouter()
  const vendorId = params.id as string

  // App state
  const { tenant, tenantLoading: isTenantLoading } = useTenant()

  // Local state
  const [activeTab, setActiveTab] = useState<'overview' | 'renewals' | 'analytics' | 'risk'>('overview')

  // Data fetching
  const {
    data,
    loading: isLoading,
    error,
    refetch
  } = useData<VendorDetailData>({
    endpoint: '/api/vendor-analytics',
    params: {
      vendor_id: vendorId,
      include_trends: true,
      include_risk_analysis: true
    },
    dependencies: [vendorId],
    options: {
      enabled: !isTenantLoading && !!tenant && !!vendorId,
      refetchOnMount: true
    }
  })

  // Event handlers
  const handleRefresh = useCallback(() => {
    refetch()
  }, [refetch])

  const handleEdit = useCallback(() => {
    // Navigate to vendor edit page
    router.push(`/vendor-management?edit=${vendorId}`)
  }, [router, vendorId])

  const handleBack = useCallback(() => {
    router.back()
  }, [router])

  // Process vendor data for charts
  const vendorChartData = useMemo(() => {
    if (!data?.analytics) return []
    
    return [{
      vendor: data.vendor.name,
      totalSpend: data.analytics.total_annual_cost,
      renewalCount: data.analytics.total_renewals,
      avgSpend: data.analytics.avg_renewal_cost,
      reliabilityScore: data.analytics.avg_reliability_score,
      currency: data.analytics.currencies?.[0] || 'USD'
    }]
  }, [data])

  // Calculate vendor metrics for summary
  const vendorMetrics = useMemo(() => {
    if (!data?.analytics) {
      return {
        totalVendors: 1,
        totalSpend: 0,
        avgSpendPerVendor: 0,
        topVendorSpend: 0,
        topVendorName: '',
        vendorConcentration: 100,
        avgReliabilityScore: 0,
        vendorsWithUpcomingRenewals: 0
      }
    }

    return {
      totalVendors: 1,
      totalSpend: data.analytics.total_annual_cost,
      avgSpendPerVendor: data.analytics.avg_renewal_cost,
      topVendorSpend: data.analytics.total_annual_cost,
      topVendorName: data.vendor.name,
      vendorConcentration: 100, // Single vendor view
      avgReliabilityScore: data.analytics.avg_reliability_score,
      vendorsWithUpcomingRenewals: data.analytics.upcoming_renewals_90d > 0 ? 1 : 0
    }
  }, [data])

  // Loading state
  if (isTenantLoading || isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  // Error state
  if (error || !data) {
    return (
      <PageErrorBoundary>
        <div className="p-6">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Error Loading Vendor Details</h1>
          <p className="text-gray-600 mb-4">{error?.message || 'Vendor not found'}</p>
          <div className="space-x-4">
            <Button onClick={handleRefresh} variant="secondary">
              Try Again
            </Button>
            <Button onClick={handleBack} variant="primary">
              Go Back
            </Button>
          </div>
        </div>
      </PageErrorBoundary>
    )
  }

  const { vendor, analytics, renewals, risk_analysis } = data

  // Header actions
  const headerActions = [
    {
      label: 'Edit Vendor',
      onClick: handleEdit,
      variant: 'secondary' as const,
      leftIcon: '✏️',
      'data-testid': 'edit-vendor-button'
    },
    {
      label: 'Refresh',
      onClick: handleRefresh,
      variant: 'primary' as const,
      leftIcon: isLoading ? '🔄' : '↻',
      isLoading: isLoading,
      disabled: isLoading,
      'data-testid': 'refresh-button'
    }
  ]

  return (
    <PageErrorBoundary>
      <div className="vendor-detail-container">
        {/* Header Section */}
        <PageHeader
          title={vendor.display_name || vendor.name}
          subtitle={`Vendor Details • ${analytics.total_renewals} renewals • ${analytics.relationship_days} days relationship`}
          clientName={tenant?.clientName}
          actions={headerActions}
          backButton={{
            label: 'Back',
            onClick: handleBack
          }}
          className="mb-6"
        />

        {/* Vendor Metrics Summary */}
        <div className="mb-6">
          <VendorMetricsSummary
            metrics={vendorMetrics}
            isLoading={isLoading}
            currency={analytics.currencies?.[0] || 'USD'}
          />
        </div>

        {/* Tab Navigation */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              {[
                { id: 'overview', label: 'Overview', icon: '📋' },
                { id: 'renewals', label: 'Renewals', icon: '🔄' },
                { id: 'analytics', label: 'Analytics', icon: '📊' },
                { id: 'risk', label: 'Risk Assessment', icon: '⚠️' }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <span className="mr-2">{tab.icon}</span>
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        <div className="space-y-6">
          {activeTab === 'overview' && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Vendor Information */}
              <Card>
                <CardHeader>
                  <h3 className="text-lg font-semibold">Vendor Information</h3>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium text-gray-500">Name</label>
                      <p className="text-gray-900">{vendor.name}</p>
                    </div>
                    {vendor.display_name && (
                      <div>
                        <label className="text-sm font-medium text-gray-500">Display Name</label>
                        <p className="text-gray-900">{vendor.display_name}</p>
                      </div>
                    )}
                    {vendor.contact_email && (
                      <div>
                        <label className="text-sm font-medium text-gray-500">Contact Email</label>
                        <p className="text-gray-900">{vendor.contact_email}</p>
                      </div>
                    )}
                    {vendor.website && (
                      <div>
                        <label className="text-sm font-medium text-gray-500">Website</label>
                        <a 
                          href={vendor.website} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800"
                        >
                          {vendor.website}
                        </a>
                      </div>
                    )}
                    {(vendor.city || vendor.state || vendor.country) && (
                      <div>
                        <label className="text-sm font-medium text-gray-500">Location</label>
                        <p className="text-gray-900">
                          {[vendor.city, vendor.state, vendor.country].filter(Boolean).join(', ')}
                        </p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Quick Stats */}
              <Card>
                <CardHeader>
                  <h3 className="text-lg font-semibold">Quick Stats</h3>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-4 bg-blue-50 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">{analytics.total_renewals}</div>
                      <div className="text-sm text-gray-600">Total Renewals</div>
                    </div>
                    <div className="text-center p-4 bg-green-50 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">{analytics.unique_products}</div>
                      <div className="text-sm text-gray-600">Products</div>
                    </div>
                    <div className="text-center p-4 bg-yellow-50 rounded-lg">
                      <div className="text-2xl font-bold text-yellow-600">{analytics.upcoming_renewals_90d}</div>
                      <div className="text-sm text-gray-600">Upcoming (90d)</div>
                    </div>
                    <div className="text-center p-4 bg-red-50 rounded-lg">
                      <div className="text-2xl font-bold text-red-600">{analytics.overdue_renewals}</div>
                      <div className="text-sm text-gray-600">Overdue</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {activeTab === 'renewals' && (
            <Card>
              <CardHeader>
                <h3 className="text-lg font-semibold">Renewal History</h3>
              </CardHeader>
              <CardContent>
                {renewals.length > 0 ? (
                  <div className="space-y-3">
                    {renewals.map((renewal) => (
                      <div key={renewal.id} className="border rounded-lg p-4 hover:bg-gray-50">
                        <div className="flex justify-between items-start">
                          <div>
                            <h4 className="font-medium">{renewal.name}</h4>
                            <p className="text-sm text-gray-600">{renewal.product_name}</p>
                            {renewal.category && (
                              <span className="inline-block bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded mt-1">
                                {renewal.category}
                              </span>
                            )}
                          </div>
                          <div className="text-right">
                            <div className="font-semibold">
                              {new Intl.NumberFormat('en-US', {
                                style: 'currency',
                                currency: renewal.currency || 'USD'
                              }).format(renewal.annual_cost)}
                            </div>
                            <div className="text-sm text-gray-600">Due: {renewal.due_date}</div>
                            <div className="text-sm">
                              Reliability: {renewal.reliability_score}%
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    No renewals found for this vendor
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {activeTab === 'analytics' && (
            <div>
              <ChartErrorBoundary>
                <VendorSpendChart
                  data={vendorChartData}
                  chartType="bar"
                  showTop={1}
                  currency={analytics.currencies?.[0] || 'USD'}
                />
              </ChartErrorBoundary>
            </div>
          )}

          {activeTab === 'risk' && risk_analysis && (
            <Card>
              <CardHeader>
                <h3 className="text-lg font-semibold">Risk Assessment</h3>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="flex items-center justify-between">
                    <span className="text-lg font-medium">Overall Risk Score</span>
                    <div className={`text-2xl font-bold ${
                      risk_analysis.risk_level === 'high' ? 'text-red-600' :
                      risk_analysis.risk_level === 'medium' ? 'text-yellow-600' : 'text-green-600'
                    }`}>
                      {risk_analysis.overall_risk_score}/100
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {Object.entries(risk_analysis.risk_factors).map(([factor, level]) => (
                      <div key={factor} className="text-center p-4 border rounded-lg">
                        <div className={`text-lg font-semibold ${
                          level === 'high' ? 'text-red-600' :
                          level === 'medium' ? 'text-yellow-600' : 'text-green-600'
                        }`}>
                          {level.toUpperCase()}
                        </div>
                        <div className="text-sm text-gray-600 capitalize">
                          {factor.replace('_', ' ')}
                        </div>
                      </div>
                    ))}
                  </div>

                  <div>
                    <h4 className="font-medium mb-3">Recommendations</h4>
                    <ul className="space-y-2">
                      {risk_analysis.recommendations.map((rec, index) => (
                        <li key={index} className="flex items-start">
                          <span className="text-blue-500 mr-2">•</span>
                          <span className="text-gray-700">{rec}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </PageErrorBoundary>
  )
}

/**
 * Health Check System
 *
 * Comprehensive health monitoring for application components and services
 */

import { HEALTH_ENDPOINTS } from '@/lib/constants/api-endpoints'

interface HealthCheckResult {
  name: string
  status: 'healthy' | 'degraded' | 'unhealthy'
  message?: string
  duration: number
  timestamp: number
  details?: Record<string, any>
}

interface HealthCheckConfig {
  timeout: number
  retries: number
  interval: number
  enabled: boolean
}

type HealthCheckFunction = () => Promise<Omit<HealthCheckResult, 'duration' | 'timestamp'>>

class HealthCheckManager {
  private static instance: HealthCheckManager
  private checks: Map<string, { fn: HealthCheckFunction; config: HealthCheckConfig }> = new Map()
  private results: Map<string, HealthCheckResult> = new Map()
  private intervals: Map<string, NodeJS.Timeout> = new Map()
  private isRunning = false

  private constructor() {}

  static getInstance(): HealthCheckManager {
    if (!HealthCheckManager.instance) {
      HealthCheckManager.instance = new HealthCheckManager()
    }
    return HealthCheckManager.instance
  }

  // Register a health check
  register(
    name: string,
    checkFunction: HealthCheckFunction,
    config: Partial<HealthCheckConfig> = {}
  ): void {
    const defaultConfig: HealthCheckConfig = {
      timeout: 5000,
      retries: 2,
      interval: 30000, // 30 seconds
      enabled: true
    }

    this.checks.set(name, {
      fn: checkFunction,
      config: { ...defaultConfig, ...config }
    })

    if (this.isRunning && config.enabled !== false) {
      this.startCheck(name)
    }
  }

  // Unregister a health check
  unregister(name: string): void {
    this.stopCheck(name)
    this.checks.delete(name)
    this.results.delete(name)
  }

  // Start all health checks
  start(): void {
    this.isRunning = true
    
    for (const [name, { config }] of this.checks) {
      if (config.enabled) {
        this.startCheck(name)
      }
    }
  }

  // Stop all health checks
  stop(): void {
    this.isRunning = false
    
    for (const name of this.checks.keys()) {
      this.stopCheck(name)
    }
  }

  // Start a specific health check
  private startCheck(name: string): void {
    const check = this.checks.get(name)
    if (!check) return

    // Run immediately
    this.runCheck(name)

    // Schedule periodic runs
    const interval = setInterval(() => {
      this.runCheck(name)
    }, check.config.interval)

    this.intervals.set(name, interval)
  }

  // Stop a specific health check
  private stopCheck(name: string): void {
    const interval = this.intervals.get(name)
    if (interval) {
      clearInterval(interval)
      this.intervals.delete(name)
    }
  }

  // Run a specific health check
  private async runCheck(name: string): Promise<HealthCheckResult> {
    const check = this.checks.get(name)
    if (!check) {
      throw new Error(`Health check '${name}' not found`)
    }

    const startTime = performance.now()
    let result: HealthCheckResult

    try {
      // Run with timeout
      const checkPromise = this.runWithRetries(check.fn, check.config.retries)
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Health check timeout')), check.config.timeout)
      })

      const checkResult = await Promise.race([checkPromise, timeoutPromise])
      const duration = performance.now() - startTime

      result = {
        ...checkResult,
        duration,
        timestamp: Date.now()
      }
    } catch (error) {
      const duration = performance.now() - startTime
      
      result = {
        name,
        status: 'unhealthy',
        message: error instanceof Error ? error.message : 'Unknown error',
        duration,
        timestamp: Date.now(),
        details: { error: error instanceof Error ? error.stack : String(error) }
      }
    }

    this.results.set(name, result)
    return result
  }

  // Run function with retries
  private async runWithRetries(
    fn: HealthCheckFunction,
    retries: number
  ): Promise<Omit<HealthCheckResult, 'duration' | 'timestamp'>> {
    let lastError: Error | undefined

    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        return await fn()
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error))
        
        if (attempt < retries) {
          // Wait before retry (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000))
        }
      }
    }

    throw lastError
  }

  // Get all health check results
  getResults(): Record<string, HealthCheckResult> {
    const results: Record<string, HealthCheckResult> = {}
    
    for (const [name, result] of this.results) {
      results[name] = result
    }
    
    return results
  }

  // Get overall health status
  getOverallStatus(): {
    status: 'healthy' | 'degraded' | 'unhealthy'
    healthy: number
    degraded: number
    unhealthy: number
    total: number
  } {
    let healthy = 0
    let degraded = 0
    let unhealthy = 0

    for (const result of this.results.values()) {
      switch (result.status) {
        case 'healthy':
          healthy++
          break
        case 'degraded':
          degraded++
          break
        case 'unhealthy':
          unhealthy++
          break
      }
    }

    const total = healthy + degraded + unhealthy
    let overallStatus: 'healthy' | 'degraded' | 'unhealthy'

    if (unhealthy > 0) {
      overallStatus = 'unhealthy'
    } else if (degraded > 0) {
      overallStatus = 'degraded'
    } else {
      overallStatus = 'healthy'
    }

    return {
      status: overallStatus,
      healthy,
      degraded,
      unhealthy,
      total
    }
  }

  // Run all checks once
  async runAll(): Promise<Record<string, HealthCheckResult>> {
    const promises = Array.from(this.checks.keys()).map(name => 
      this.runCheck(name).catch(error => ({
        name,
        status: 'unhealthy' as const,
        message: error.message,
        duration: 0,
        timestamp: Date.now()
      }))
    )

    const results = await Promise.all(promises)
    return results.reduce((acc, result) => {
      acc[result.name] = result
      return acc
    }, {} as Record<string, HealthCheckResult>)
  }
}

// Pre-defined health checks
export const healthChecks = {
  // Database connectivity check
  database: async (): Promise<Omit<HealthCheckResult, 'duration' | 'timestamp'>> => {
    try {
      const response = await fetch(HEALTH_ENDPOINTS.DATABASE, {
        method: 'GET',
        credentials: 'include'
      })

      if (!response.ok) {
        return {
          name: 'database',
          status: 'unhealthy',
          message: `Database check failed: ${response.status}`
        }
      }

      const data = await response.json()
      
      return {
        name: 'database',
        status: data.connected ? 'healthy' : 'unhealthy',
        message: data.message,
        details: {
          connectionCount: data.connectionCount,
          responseTime: data.responseTime
        }
      }
    } catch (error) {
      return {
        name: 'database',
        status: 'unhealthy',
        message: error instanceof Error ? error.message : 'Database check failed'
      }
    }
  },

  // API health check
  api: async (): Promise<Omit<HealthCheckResult, 'duration' | 'timestamp'>> => {
    try {
      const response = await fetch('/api/health', {
        method: 'GET',
        credentials: 'include'
      })

      if (!response.ok) {
        return {
          name: 'api',
          status: 'unhealthy',
          message: `API health check failed: ${response.status}`
        }
      }

      return {
        name: 'api',
        status: 'healthy',
        message: 'API is responding'
      }
    } catch (error) {
      return {
        name: 'api',
        status: 'unhealthy',
        message: error instanceof Error ? error.message : 'API check failed'
      }
    }
  },

  // Authentication service check
  auth: async (): Promise<Omit<HealthCheckResult, 'duration' | 'timestamp'>> => {
    try {
      const response = await fetch('/api/auth/health', {
        method: 'GET',
        credentials: 'include'
      })

      if (!response.ok) {
        return {
          name: 'auth',
          status: 'degraded',
          message: 'Authentication service may be experiencing issues'
        }
      }

      return {
        name: 'auth',
        status: 'healthy',
        message: 'Authentication service is healthy'
      }
    } catch (error) {
      return {
        name: 'auth',
        status: 'degraded',
        message: 'Authentication service check failed'
      }
    }
  },

  // Memory usage check
  memory: async (): Promise<Omit<HealthCheckResult, 'duration' | 'timestamp'>> => {
    if (typeof window === 'undefined' || !('memory' in performance)) {
      return {
        name: 'memory',
        status: 'healthy',
        message: 'Memory monitoring not available'
      }
    }

    const memory = (performance as any).memory
    const usedMB = memory.usedJSHeapSize / 1024 / 1024
    const totalMB = memory.totalJSHeapSize / 1024 / 1024
    const limitMB = memory.jsHeapSizeLimit / 1024 / 1024
    
    const usagePercent = (usedMB / limitMB) * 100

    let status: 'healthy' | 'degraded' | 'unhealthy'
    let message: string

    if (usagePercent > 90) {
      status = 'unhealthy'
      message = `High memory usage: ${usagePercent.toFixed(1)}%`
    } else if (usagePercent > 75) {
      status = 'degraded'
      message = `Elevated memory usage: ${usagePercent.toFixed(1)}%`
    } else {
      status = 'healthy'
      message = `Memory usage normal: ${usagePercent.toFixed(1)}%`
    }

    return {
      name: 'memory',
      status,
      message,
      details: {
        usedMB: Math.round(usedMB),
        totalMB: Math.round(totalMB),
        limitMB: Math.round(limitMB),
        usagePercent: Math.round(usagePercent)
      }
    }
  }
}

// Export singleton instance
export const healthCheck = HealthCheckManager.getInstance()

// Export types
export type { HealthCheckResult, HealthCheckConfig, HealthCheckFunction }

/**
 * Notification Service
 * 
 * Handles sending notifications for renewal alerts
 */

import { SESClient, SendEmailCommand } from '@aws-sdk/client-ses'
import { TenantContext } from '@/lib/tenant/context'
import { executeTenantQuery, executeTenantQuerySingle } from '@/lib/tenant/database'

// AWS SES Client
const sesClient = new SESClient({
  region: process.env.AWS_REGION || 'us-east-1',
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
  },
})

export interface RenewalAlert {
  alert_id: number
  renewal_id: number
  alert_name: string
  days_before_renewal: number
  email_recipients: string[]
  is_active: boolean
  last_sent?: string
}

export interface RenewalInfo {
  id: number
  renewal_type: string
  vendor_name: string
  product_name: string
  product_version?: string
  start_date: string
  cost?: number
  currency?: string
  department_name?: string
}

export class NotificationService {
  /**
   * Check for alerts that need to be sent
   */
  static async checkAndSendAlerts(tenantContext: TenantContext): Promise<void> {
    try {
      // Get all active alerts with their renewal information
      const query = `
        SELECT 
          a.alert_id,
          a.renewal_id,
          a.alert_name,
          a.days_before_renewal,
          a.email_recipients,
          a.last_sent,
          r.renewal_type,
          r.vendor_name,
          r.product_name,
          r.product_version,
          r.start_date,
          r.cost,
          r.currency,
          d.department_name
        FROM tenant_alerts a
        JOIN tenant_renewals r ON a.renewal_id = r.id
        LEFT JOIN tenant_departments d ON r.department_id = d.id
        WHERE a.is_active = true
          AND r.start_date > CURRENT_DATE
          AND (
            a.last_sent IS NULL 
            OR a.last_sent < CURRENT_DATE - INTERVAL '1 day'
          )
          AND r.start_date <= CURRENT_DATE + INTERVAL '1 day' * a.days_before_renewal
          AND r.start_date > CURRENT_DATE + INTERVAL '1 day' * (a.days_before_renewal - 1)
      `

      const result = await executeTenantQuery<RenewalAlert & RenewalInfo>(
        query,
        [],
        tenantContext
      )

      if (!result.success || !result.data) {
        console.log('No alerts to send or query failed')
        return
      }

      // Send alerts
      for (const alert of result.data) {
        await this.sendRenewalAlert(alert, tenantContext)
      }

    } catch (error) {
      console.error('Error checking and sending alerts:', error)
    }
  }

  /**
   * Send a renewal alert email
   */
  private static async sendRenewalAlert(
    alert: RenewalAlert & RenewalInfo,
    tenantContext: TenantContext
  ): Promise<void> {
    try {
      const start_date = new Date(alert.start_date)
      const daysUntilRenewal = Math.ceil(
        (start_date.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
      )

      // Prepare email content
      const subject = `Renewal Alert: ${alert.product_name} - ${daysUntilRenewal} days remaining`
      
      const htmlBody = this.generateEmailHTML(alert, daysUntilRenewal)
      const textBody = this.generateEmailText(alert, daysUntilRenewal)

      // Send email to each recipient
      for (const recipient of alert.email_recipients) {
        const command = new SendEmailCommand({
          Source: process.env.FROM_EMAIL || '<EMAIL>',
          Destination: {
            ToAddresses: [recipient],
          },
          Message: {
            Subject: {
              Data: subject,
              Charset: 'UTF-8',
            },
            Body: {
              Html: {
                Data: htmlBody,
                Charset: 'UTF-8',
              },
              Text: {
                Data: textBody,
                Charset: 'UTF-8',
              },
            },
          },
        })

        await sesClient.send(command)
        console.log(`Alert email sent to ${recipient} for renewal ${alert.renewal_id}`)
      }

      // Update last_sent timestamp
      await executeTenantQuerySingle(
        'UPDATE tenant_alerts SET last_sent = CURRENT_TIMESTAMP WHERE alert_id = $1',
        [alert.alert_id],
        tenantContext
      )

    } catch (error) {
      console.error(`Error sending alert ${alert.alert_id}:`, error)
    }
  }

  /**
   * Generate HTML email content
   */
  private static generateEmailHTML(alert: RenewalAlert & RenewalInfo, daysUntilRenewal: number): string {
    const start_date = new Date(alert.start_date).toLocaleDateString()
    const costInfo = alert.cost ? `$${alert.cost.toFixed(2)} ${alert.currency || 'CAD'}` : 'Not specified'

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Renewal Alert</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
          .alert-badge { background-color: #ffc107; color: #000; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold; }
          .renewal-details { background-color: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0; }
          .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6; font-size: 12px; color: #6c757d; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h2>🔔 Renewal Alert</h2>
            <p><span class="alert-badge">${daysUntilRenewal} DAYS REMAINING</span></p>
          </div>
          
          <p>This is an automated reminder that the following renewal is approaching:</p>
          
          <div class="renewal-details">
            <h3>${alert.product_name}</h3>
            <p><strong>Vendor:</strong> ${alert.vendor_name}</p>
            ${alert.product_version ? `<p><strong>Version:</strong> ${alert.product_version}</p>` : ''}
            <p><strong>Renewal Date:</strong> ${start_date}</p>
            <p><strong>Type:</strong> ${alert.renewal_type}</p>
            ${alert.department_name ? `<p><strong>Department:</strong> ${alert.department_name}</p>` : ''}
            <p><strong>Cost:</strong> ${costInfo}</p>
          </div>
          
          <p>Please take the necessary action to ensure this renewal is processed on time.</p>
          
          <div class="footer">
            <p>This alert was configured as: ${alert.alert_name}</p>
            <p>You are receiving this email because you are listed as a recipient for renewal alerts.</p>
            <p>This is an automated message from RenewTrack. Please do not reply to this email.</p>
          </div>
        </div>
      </body>
      </html>
    `
  }

  /**
   * Generate plain text email content
   */
  private static generateEmailText(alert: RenewalAlert & RenewalInfo, daysUntilRenewal: number): string {
    const start_date = new Date(alert.start_date).toLocaleDateString()
    const costInfo = alert.cost ? `$${alert.cost.toFixed(2)} ${alert.currency || 'CAD'}` : 'Not specified'

    return `
RENEWAL ALERT - ${daysUntilRenewal} DAYS REMAINING

This is an automated reminder that the following renewal is approaching:

Product: ${alert.product_name}
Vendor: ${alert.vendor_name}
${alert.product_version ? `Version: ${alert.product_version}\n` : ''}Renewal Date: ${start_date}
Type: ${alert.renewal_type}
${alert.department_name ? `Department: ${alert.department_name}\n` : ''}Cost: ${costInfo}

Please take the necessary action to ensure this renewal is processed on time.

Alert Configuration: ${alert.alert_name}

This is an automated message from RenewTrack. Please do not reply to this email.
    `.trim()
  }

  /**
   * Test email functionality
   */
  static async sendTestEmail(
    recipient: string,
    tenantContext: TenantContext
  ): Promise<boolean> {
    try {
      const command = new SendEmailCommand({
        Source: process.env.FROM_EMAIL || '<EMAIL>',
        Destination: {
          ToAddresses: [recipient],
        },
        Message: {
          Subject: {
            Data: 'RenewTrack - Test Email',
            Charset: 'UTF-8',
          },
          Body: {
            Html: {
              Data: `
                <h2>Test Email from RenewTrack</h2>
                <p>This is a test email to verify that email notifications are working correctly.</p>
                <p>If you received this email, your notification system is configured properly.</p>
                <p>Tenant: ${tenantContext.tenantId}</p>
              `,
              Charset: 'UTF-8',
            },
            Text: {
              Data: `
Test Email from RenewTrack

This is a test email to verify that email notifications are working correctly.
If you received this email, your notification system is configured properly.

Tenant: ${tenantContext.tenantId}
              `.trim(),
              Charset: 'UTF-8',
            },
          },
        },
      })

      await sesClient.send(command)
      return true
    } catch (error) {
      console.error('Error sending test email:', error)
      return false
    }
  }
}

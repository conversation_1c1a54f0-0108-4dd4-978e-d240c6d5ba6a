# Dynamic Sidebar System

## Overview

The Dynamic Sidebar System provides database-driven navigation and role-based access control for the RenewTrack application. Instead of hardcoded navigation links, the sidebar is dynamically generated based on user permissions and database configuration.

## Architecture

### Database Schema

#### `metadata.admin_pages`
Defines all available pages in the application:

```sql
CREATE TABLE metadata.admin_pages (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,           -- Internal page identifier
    header VARCHAR(50) NOT NULL,                -- Display name in sidebar
    description VARCHAR(100),                   -- Page description
    sidebar BOOLEAN DEFAULT false,              -- Show in sidebar navigation
    status CHAR(1) DEFAULT 'A',                -- A=Active, I=Inactive
    display_order INTEGER DEFAULT 0,            -- Sort order in sidebar
    icon_svg TEXT,                              -- SVG icon paths
    route_path VARCHAR(100) NOT NULL,           -- Next.js route
    created_on TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    changed_on TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

#### `metadata.admin_pages_groups`
Maps pages to Cognito user groups for access control:

```sql
CREATE TABLE metadata.admin_pages_groups (
    id SERIAL PRIMARY KEY,
    page_id INTEGER NOT NULL REFERENCES metadata.admin_pages(id) ON DELETE CASCADE,
    group_name VARCHAR(50) NOT NULL,            -- Cognito group name
    created_on TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(page_id, group_name)
);
```

### Components

#### `DynamicSidebar`
- Replaces the hardcoded `Sidebar` component
- Fetches user-accessible pages via `useSidebarPages` hook
- Renders navigation dynamically with proper icons and badges
- Handles loading states and error conditions

#### `PageAccessGuard`
- Protects individual pages based on user group membership
- Can redirect unauthorized users or show fallback content
- Provides HOC pattern with `withPageAccess`

### Hooks

#### `useSidebarPages`
- Fetches sidebar pages accessible to current user
- Caches results for 5 minutes
- Automatically refetches when user changes

#### `usePageAccess`
- Checks if user has access to a specific page
- Used by `PageAccessGuard` for authorization

#### `usePageInfo`
- Retrieves page information by name
- Useful for dynamic page titles and metadata

### API Endpoints

#### `GET /api/admin-pages`
Returns all pages accessible to the current user with their group mappings.

#### `GET /api/admin-pages/sidebar`
Returns only pages that should appear in the sidebar for the current user.

#### `PATCH /api/admin-pages/[id]`
Updates page properties (admin only).

#### `DELETE /api/admin-pages/[id]`
Deletes a page (admin only).

## Usage

### Basic Page Protection

```tsx
import { PageAccessGuard } from '@/components/auth'

export default function MyPage() {
  return (
    <PageAccessGuard pageName="my-page" redirectTo="/overview">
      <div>Protected content</div>
    </PageAccessGuard>
  )
}
```

### HOC Pattern

```tsx
import { withPageAccess } from '@/components/auth'

function MyPage() {
  return <div>Protected content</div>
}

export default withPageAccess('my-page', '/overview')(MyPage)
```

### Check Access in Components

```tsx
import { usePageAccess } from '@/lib/hooks'

function MyComponent() {
  const { hasAccess, isLoading } = usePageAccess('admin-features')
  
  if (isLoading) return <div>Loading...</div>
  if (!hasAccess) return null
  
  return <div>Admin features</div>
}
```

## Configuration

### Adding New Pages

1. **Database Entry**: Add page to `admin_pages` table
```sql
INSERT INTO metadata.admin_pages (name, header, description, sidebar, route_path, icon_svg)
VALUES ('new-page', 'New Page', 'Description', true, '/new-page', '<svg>...</svg>');
```

2. **Group Permissions**: Map page to user groups
```sql
INSERT INTO metadata.admin_pages_groups (page_id, group_name)
VALUES 
  ((SELECT id FROM metadata.admin_pages WHERE name = 'new-page'), 'admin'),
  ((SELECT id FROM metadata.admin_pages WHERE name = 'new-page'), 'user');
```

3. **Page Component**: Protect the page component
```tsx
export default function NewPage() {
  return (
    <PageAccessGuard pageName="new-page">
      <div>New page content</div>
    </PageAccessGuard>
  )
}
```

### Managing Page Access

Use the admin interface at `/admin/page-management` to:
- Toggle sidebar visibility
- Enable/disable pages
- View group assignments
- Modify page properties

## Security

- All API endpoints verify user authentication via AWS Cognito
- Group membership is extracted from JWT tokens
- Database queries filter results based on user groups
- Pages without group assignments are inaccessible to all users
- Admin-only endpoints require 'admin' group membership

## Performance

- Sidebar pages are cached for 5 minutes per user
- Database queries use indexes on frequently filtered columns
- Component re-renders are minimized through selective state updates
- SVG icons are stored as text to avoid additional HTTP requests

## Migration

To migrate from the hardcoded sidebar:

1. **Run Migration**: Execute `database/migrations/admin_pages_system.sql`
2. **Update Layout**: `MainLayout` now uses `DynamicSidebar`
3. **Protect Pages**: Add `PageAccessGuard` to sensitive pages
4. **Test Access**: Verify users see appropriate navigation items

## Troubleshooting

### Users Can't See Pages
- Check user's Cognito group membership
- Verify page has `status='A'` and `sidebar=true`
- Ensure group mapping exists in `admin_pages_groups`

### API Errors
- Check database connection and schema
- Verify JWT token contains group information
- Review server logs for detailed error messages

### Performance Issues
- Monitor cache hit rates
- Check database query performance
- Consider increasing cache duration for stable environments

/**
 * Overview Upcoming Renewals API
 * 
 * Provides upcoming renewals data for the overview dashboard
 */

import { NextRequest } from 'next/server'
import { withAuth } from '@/lib/api/auth-middleware'
import { resolveTenantContext } from '@/lib/tenant/context'
import { executeQuery, schemaExists } from '@/lib/database'
import {
  createSuccessResponse,
  createErrorResponse,
  ApiErrorCode,
  HttpStatus
} from '@/lib/api/response'

export const GET = withAuth(async (request: NextRequest, session) => {
  console.log('[OVERVIEW-UPCOMING-RENEWALS-API] GET request received')

  try {
    // Resolve tenant context
    const tenant = await resolveTenantContext(session.email)
    if (!tenant) {
      console.error('[OVERVIEW-UPCOMING-RENEWALS-API] Failed to resolve tenant context')
      return createErrorResponse(
        'Tenant context not found',
        ApiErrorCode.TENANT_NOT_FOUND,
        HttpStatus.BAD_REQUEST
      )
    }

    console.log(`[OVERVIEW-UPCOMING-RENEWALS-API] Resolved tenant: ${tenant.tenantId}`)

    // Check if tenant schema exists
    const schemaExistsResult = await schemaExists(tenant.tenantSchema)
    if (!schemaExistsResult) {
      console.log(`[OVERVIEW-UPCOMING-RENEWALS-API] Tenant schema ${tenant.tenantSchema} not ready yet`)
      return createSuccessResponse([], 'Upcoming renewals retrieved successfully')
    }

    // Query upcoming renewals from tenant schema
    const query = `
      SELECT 
        id,
        product_name,
        vendor,
        cost,
        renewal_date,
        renewal_type,
        status,
        EXTRACT(DAYS FROM (renewal_date - CURRENT_DATE)) as days_until_renewal
      FROM "${tenant.tenantSchema}".tenant_renewals
      WHERE vendor IS NOT NULL 
        AND vendor != ''
        AND renewal_date > CURRENT_DATE
        AND renewal_date <= CURRENT_DATE + INTERVAL '90 days'
      ORDER BY renewal_date ASC
      LIMIT 10
    `

    console.log(`[OVERVIEW-UPCOMING-RENEWALS-API] Executing query for schema: ${tenant.tenantSchema}`)
    const result = await executeQuery(query)

    if (!result.success) {
      console.error('[OVERVIEW-UPCOMING-RENEWALS-API] Database query failed:', result.error)
      return createErrorResponse(
        'Failed to fetch upcoming renewals',
        ApiErrorCode.INTERNAL_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }

    // Transform data
    const renewals = result.data.map((row: any) => ({
      id: row.id,
      product_name: row.product_name,
      vendor: row.vendor,
      cost: parseFloat(row.cost) || 0,
      renewal_date: row.renewal_date,
      renewal_type: row.renewal_type,
      status: row.status,
      days_until_renewal: parseInt(row.days_until_renewal) || 0
    }))

    console.log(`[OVERVIEW-UPCOMING-RENEWALS-API] Returning ${renewals.length} upcoming renewals`)
    return createSuccessResponse(renewals, 'Upcoming renewals retrieved successfully')

  } catch (error) {
    console.error('[OVERVIEW-UPCOMING-RENEWALS-API] Error:', error)
    return createErrorResponse(
      'Failed to fetch upcoming renewals',
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    )
  }
}, {
  requireAuth: true
})

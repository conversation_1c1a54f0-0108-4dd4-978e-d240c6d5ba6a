/**
 * License Status Hook
 * 
 * Client-side hook for checking and managing license status
 */

'use client'

import { useState, useEffect } from 'react'
import { useData } from '@/lib/hooks/useData'

export interface LicenseStatus {
  hasLicense: boolean
  isValid: boolean
  reason?: string
  client?: {
    id: number
    name: string
  }
  license?: {
    type: string
    features: string[]
    limits: {
      maxUsers: number
      maxTenants: number
      currentUsers: number
      currentTenants: number
    }
    expiration: {
      expiresAt: Date | null
      daysRemaining: number | null
    }
    usage: {
      totalEvents: number
      loginEvents: number
      lastActivity: Date | null
      userUtilization: number
      tenantUtilization: number
    }
    activation: {
      activatedAt: Date
      lastUsage: Date | null
    }
  }
}

export interface UseLicenseResult {
  licenseStatus: LicenseStatus | null
  isLoading: boolean
  error: string | null
  refetch: () => void
  hasFeature: (featureCode: string) => boolean
  isNearExpiry: (days?: number) => boolean
  isOverLimit: (type: 'users' | 'tenants') => boolean
}

/**
 * Hook to check current user's license status
 */
export function useLicense(): UseLicenseResult {
  const {
    data: licenseStatus,
    loading: isLoading,
    error: dataError,
    refetch
  } = useData<LicenseStatus>({
    endpoint: '/api/license/status',
    cache: {
      key: 'license-status',
      ttl: 300000 // 5 minutes
    }
  })

  const error = dataError ? String(dataError) : null

  /**
   * Check if a specific feature is available
   */
  const hasFeature = (featureCode: string): boolean => {
    if (!licenseStatus?.isValid || !licenseStatus.license) {
      return false
    }
    return licenseStatus.license.features.includes(featureCode)
  }

  /**
   * Check if license is near expiry
   */
  const isNearExpiry = (days: number = 30): boolean => {
    if (!licenseStatus?.isValid || !licenseStatus.license?.expiration.daysRemaining) {
      return false
    }
    return licenseStatus.license.expiration.daysRemaining <= days
  }

  /**
   * Check if usage is over limit
   */
  const isOverLimit = (type: 'users' | 'tenants'): boolean => {
    if (!licenseStatus?.isValid || !licenseStatus.license) {
      return false
    }

    const { limits } = licenseStatus.license
    
    if (type === 'users') {
      return limits.currentUsers >= limits.maxUsers
    } else {
      return limits.currentTenants >= limits.maxTenants
    }
  }

  return {
    licenseStatus,
    isLoading,
    error,
    refetch,
    hasFeature,
    isNearExpiry,
    isOverLimit
  }
}

/**
 * Hook for license management (admin only)
 */
export function useLicenseManagement() {
  const [isGenerating, setIsGenerating] = useState(false)
  const [isActivating, setIsActivating] = useState(false)

  const generateLicense = async (licenseData: any) => {
    setIsGenerating(true)
    try {
      const response = await fetch('/api/admin/licenses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(licenseData)
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to generate license')
      }

      return await response.json()
    } finally {
      setIsGenerating(false)
    }
  }

  const activateLicense = async (activationData: any) => {
    setIsActivating(true)
    try {
      const response = await fetch('/api/admin/licenses/activate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(activationData)
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Failed to activate license')
      }

      return await response.json()
    } finally {
      setIsActivating(false)
    }
  }

  return {
    generateLicense,
    activateLicense,
    isGenerating,
    isActivating
  }
}

/**
 * License validation utilities
 */
export const LicenseUtils = {
  /**
   * Check if a feature is available based on license status
   */
  hasFeature: (licenseStatus: LicenseStatus | null, featureCode: string): boolean => {
    if (!licenseStatus?.isValid || !licenseStatus.license) {
      return false
    }
    return licenseStatus.license.features.includes(featureCode)
  },

  /**
   * Check if license is near expiry
   */
  isNearExpiry: (licenseStatus: LicenseStatus | null, days: number = 30): boolean => {
    if (!licenseStatus?.isValid || !licenseStatus.license?.expiration.daysRemaining) {
      return false
    }
    return licenseStatus.license.expiration.daysRemaining <= days
  },

  /**
   * Check if usage is over limit
   */
  isOverLimit: (licenseStatus: LicenseStatus | null, type: 'users' | 'tenants'): boolean => {
    if (!licenseStatus?.isValid || !licenseStatus.license) {
      return false
    }

    const { limits } = licenseStatus.license

    if (type === 'users') {
      return limits.currentUsers >= limits.maxUsers
    } else {
      return limits.currentTenants >= limits.maxTenants
    }
  },

  /**
   * Get license status display text
   */
  getStatusText: (licenseStatus: LicenseStatus | null): string => {
    if (!licenseStatus) return 'Checking license...'
    if (!licenseStatus.isValid) return 'Invalid license'

    const { license } = licenseStatus
    const isNearExpiry = license?.expiration.daysRemaining && license.expiration.daysRemaining <= 30

    let text = `${license?.type} license`
    if (license?.expiration.daysRemaining) {
      text += ` (${license.expiration.daysRemaining} days remaining)`
    }

    return text
  },

  /**
   * Get license status color class
   */
  getStatusColor: (licenseStatus: LicenseStatus | null): string => {
    if (!licenseStatus?.isValid) return 'text-red-600'

    const isNearExpiry = licenseStatus.license?.expiration.daysRemaining &&
                        licenseStatus.license.expiration.daysRemaining <= 30

    return isNearExpiry ? 'text-yellow-600' : 'text-green-600'
  }
}

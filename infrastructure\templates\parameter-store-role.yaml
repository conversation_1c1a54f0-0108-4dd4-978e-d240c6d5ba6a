Resources:
  ParameterStoreAccessRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: RenewTrackParameterStoreAccess
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - !Ref ParameterStoreAccessPolicy

  ParameterStoreAccessPolicy:
    Type: AWS::IAM::ManagedPolicy
    Properties:
      ManagedPolicyName: RenewTrackParameterStorePolicy
      PolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Action:
              - ssm:GetParameter
              - ssm:GetParameters
            Resource:
              - !Sub arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/renewtrack/db/*
          - Effect: Allow
            Action:
              - kms:Decrypt
            Resource:
              - !Sub arn:aws:kms:${AWS::Region}:${AWS::AccountId}:key/${KMSKeyId}
            Condition:
              StringEquals:
                kms:ViaService: !Sub ssm.${AWS::Region}.amazonaws.com
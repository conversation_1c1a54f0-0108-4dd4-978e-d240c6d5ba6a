/**
 * Debug Tenant Tables API Endpoint
 * 
 * Checks if tenant tables exist and provides diagnostic information
 */

import { NextRequest } from 'next/server';
import { fetchAuthSession } from 'aws-amplify/auth';
import { resolveTenantContext, TenantContext } from '@/lib/tenant/context';
import { AuthSession } from '@/lib/api/auth-middleware';
import { executeTenantQuery } from '@/lib/tenant/database';
import {
  createSuccessResponse,
  createErrorResponse,
  ApiErrorCode,
  HttpStatus,
  withErrorHandling
} from '@/lib/api/response';

// GET /api/debug/tenant-tables - Check tenant table status
export const GET = withErrorHandling(async (request: NextRequest) => {
  console.log('[DEBUG-TENANT-TABLES] GET request received');

  // Verify authentication using Amplify
  try {
    const amplifySession = await fetchAuthSession();
    if (!amplifySession?.tokens?.idToken) {
      return createErrorResponse(
        'Authentication required',
        ApiErrorCode.UNAUTHORIZED,
        HttpStatus.UNAUTHORIZED
      );
    }
  } catch (error) {
    return createErrorResponse(
      'Authentication failed',
      ApiErrorCode.UNAUTHORIZED,
      HttpStatus.UNAUTHORIZED
    );
  }

  // Create session object for tenant context resolution
  const session: AuthSession = {
    isAuthenticated: true,
    userId: 'debug-user', // This is a debug endpoint
    email: '<EMAIL>',
    roles: ['user'],
    groups: [],
    tokens: {
      idToken: 'debug-token',
      accessToken: 'debug-token'
    }
  };

  // Get tenant context
  const tenantResult = await resolveTenantContext(session);
  if (!tenantResult.success) {
    return tenantResult.response! as any;
  }

  const tenant = tenantResult.tenant!;

  try {
    const diagnostics = {
      tenant: {
        clientId: tenant.clientId,
        clientName: tenant.clientName,
        tenantSchema: tenant.tenantSchema,
        isActive: tenant.isActive
      },
      tables: {} as Record<string, { exists: boolean; rowCount?: number | string; error?: string }>,
      schema: {} as { name: string; exists: boolean; error?: string },
      errors: [] as string[]
    };

      // List of tables to check
      const tablesToCheck = [
        'tenant_renewals',
        'tenant_alerts',
        'tenant_vendors',
        'tenant_products',
        'tenant_product_versions',
        'tenant_renewal_items',
        'tenant_users'
      ];

      // Check if each table exists
      for (const tableName of tablesToCheck) {
        try {
          const checkQuery = `
            SELECT EXISTS (
              SELECT FROM information_schema.tables 
              WHERE table_schema = $1 AND table_name = $2
            );
          `;
          
          const existsResult = await executeTenantQuery(
            checkQuery, 
            [tenant.tenantSchema, tableName], 
            tenant
          );

          if (existsResult.success && existsResult.data && existsResult.data.length > 0) {
            const exists = existsResult.data[0].exists;
            
            if (exists) {
              // Table exists, get row count
              const countQuery = `SELECT COUNT(*) as count FROM "${tenant.tenantSchema}".${tableName}`;
              const countResult = await executeTenantQuery(countQuery, [], tenant);
              
              diagnostics.tables[tableName] = {
                exists: true,
                rowCount: countResult.success && countResult.data && countResult.data[0] ? countResult.data[0].count : 'Unknown'
              };
            } else {
              diagnostics.tables[tableName] = {
                exists: false,
                rowCount: 0
              };
            }
          } else {
            diagnostics.tables[tableName] = {
              exists: false,
              error: 'Failed to check table existence'
            };
          }
        } catch (error) {
          diagnostics.tables[tableName] = {
            exists: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          };
          diagnostics.errors.push(`Error checking ${tableName}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      // Check if main tenant_renewals table exists
      try {
        const renewalsQuery = `
          SELECT EXISTS (
            SELECT FROM information_schema.tables
            WHERE table_schema = $1 AND table_name = 'tenant_renewals'
          );
        `;

        const renewalsResult = await executeTenantQuery(
          renewalsQuery,
          [tenant.tenantSchema],
          tenant
        );

        if (renewalsResult.success && renewalsResult.data && renewalsResult.data.length > 0) {
          const exists = renewalsResult.data[0].exists;
          
          if (exists) {
            const countQuery = `SELECT COUNT(*) as count FROM "${tenant.tenantSchema}"."tenant_renewals"`;
            const countResult = await executeTenantQuery(countQuery, [], tenant);

            diagnostics.tables['tenant_renewals'] = {
              exists: true,
              rowCount: countResult.success && countResult.data && countResult.data.length > 0 ? countResult.data[0].count : 'Unknown'
            };
          } else {
            diagnostics.tables['tenant_renewals'] = {
              exists: false,
              rowCount: 0
            };
          }
        }
      } catch (error) {
        diagnostics.tables['tenant_renewals'] = {
          exists: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        };
        diagnostics.errors.push(`Error checking Renewals table: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }

      // Check schema existence
      try {
        const schemaQuery = `
          SELECT EXISTS (
            SELECT FROM information_schema.schemata 
            WHERE schema_name = $1
          );
        `;
        
        const schemaResult = await executeTenantQuery(
          schemaQuery, 
          [tenant.tenantSchema], 
          tenant
        );

        diagnostics.schema = {
          name: tenant.tenantSchema,
          exists: schemaResult.success && schemaResult.data && schemaResult.data.length > 0 ? schemaResult.data[0].exists : false
        };
      } catch (error) {
        diagnostics.schema = {
          name: tenant.tenantSchema,
          exists: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        };
        diagnostics.errors.push(`Error checking schema: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }

      console.log('[DEBUG-TENANT-TABLES] Diagnostics completed:', diagnostics);

      return createSuccessResponse(
        diagnostics,
        'Tenant table diagnostics completed'
      );

  } catch (error) {
    console.error('[DEBUG-TENANT-TABLES] Error:', error);
    return createErrorResponse(
      'Failed to run tenant table diagnostics',
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
});

/**
 * API Endpoints Constants
 * 
 * Centralized API endpoint definitions to avoid hardcoding URLs throughout the codebase
 */

// Base API paths
export const API_BASE = '/api' as const

// Authentication endpoints
export const AUTH_ENDPOINTS = {
  LOGIN: `${API_BASE}/auth/login`,
  LOGOUT: `${API_BASE}/auth/logout`,
  SET_TOKEN: `${API_BASE}/auth/set-token`,
  REFRESH: `${API_BASE}/auth/refresh`,
  CALLBACK: `${API_BASE}/auth/callback`,
} as const

// Client management endpoints
export const CLIENT_ENDPOINTS = {
  DOMAIN: `${API_BASE}/clients/domain`,
  LIST: `${API_BASE}/clients`,
  DETAILS: (id: string) => `${API_BASE}/clients/${id}`,
} as const

// Metadata endpoints
export const METADATA_ENDPOINTS = {
  RENEWAL_TYPES: `${API_BASE}/metadata/renewal-types`,
  PURCHASE_TYPES: `${API_BASE}/metadata/purchase-types`,
  CURRENCIES: `${API_BASE}/metadata/currencies`,
  INDUSTRIES: `${API_BASE}/metadata/industries`,
  STATUSES: `${API_BASE}/metadata/statuses`,
} as const

// Tenant-specific endpoints
export const TENANT_ENDPOINTS = {
  USERS: `${API_BASE}/tenant-users`,
  VENDORS: `${API_BASE}/tenant-vendors`,
  PRODUCTS: `${API_BASE}/tenant-products`,
  PRODUCT_VERSIONS: `${API_BASE}/tenant-product-versions`,
  DEPARTMENTS: `${API_BASE}/tenant-departments`,
  RESELLERS: `${API_BASE}/tenant-resellers`,
  RENEWALS: `${API_BASE}/tenant-renewals`,
  ALERTS: `${API_BASE}/tenant-alerts`,
} as const

// Renewals endpoints
export const RENEWALS_ENDPOINTS = {
  LIST: `${API_BASE}/renewals`,
  FILTER_OPTIONS: `${API_BASE}/renewals/filter-options`,
  DETAILS: (id: string) => `${API_BASE}/renewals/${id}`,
} as const

// Dashboard endpoints
export const DASHBOARD_ENDPOINTS = {
  STATS: `${API_BASE}/dashboard/stats`,
  RENEWALS: `${API_BASE}/dashboard/renewals`,
  OVERVIEW: `${API_BASE}/dashboard/overview`,
  SCAN_RESULTS: `${API_BASE}/dashboard/scan-results`,
} as const

// Vendor Analytics endpoints
export const VENDOR_ANALYTICS_ENDPOINTS = {
  ANALYTICS: `${API_BASE}/vendor-analytics`,
  COMPARISON: `${API_BASE}/vendor-comparison`,
  DASHBOARD: `${API_BASE}/vendor-dashboard`,
} as const

// Admin endpoints
export const ADMIN_ENDPOINTS = {
  USERS: `${API_BASE}/admin/users`,
  TENANTS: `${API_BASE}/admin/tenants`,
  SYSTEM: `${API_BASE}/admin/system`,
  LOGS: `${API_BASE}/admin/logs`,
  MONITORING: `${API_BASE}/admin/monitoring`,
  PAGES: `${API_BASE}/admin-pages`,
  PAGES_SIDEBAR: `${API_BASE}/admin-pages/sidebar`,
} as const

// Health and monitoring endpoints
export const HEALTH_ENDPOINTS = {
  HEALTH: `${API_BASE}/health`,
  DATABASE: `${API_BASE}/health/database`,
  METRICS: `${API_BASE}/health/metrics`,
} as const

// Utility function to build query string
export const buildQueryString = (params: Record<string, any>): string => {
  const searchParams = new URLSearchParams()
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      if (Array.isArray(value)) {
        value.forEach(v => searchParams.append(key, String(v)))
      } else {
        searchParams.append(key, String(value))
      }
    }
  })
  
  const queryString = searchParams.toString()
  return queryString ? `?${queryString}` : ''
}

// Utility function to build endpoint with query params
export const buildEndpoint = (endpoint: string, params?: Record<string, any>): string => {
  if (!params) return endpoint
  return `${endpoint}${buildQueryString(params)}`
}

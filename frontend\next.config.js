/** @type {import('next').NextConfig} */
const nextConfig = {
  // Keep existing configuration but ensure no symbolic links are created
  experimental: {
    // Add any experimental features you need
    // But avoid features that might create symlinks
  },
  // Environment variables are now managed through the centralized config system
  // No need to explicitly expose them here as they're handled by the config module

  // Webpack configuration to handle Node.js modules in browser
  webpack: (config, { isServer }) => {
    if (!isServer) {
      // Don't resolve 'fs' module on the client to prevent this error on build
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        dns: false,
        child_process: false,
        tls: false,
        'cloudflare:sockets': false,
      };
    }

    // Add externals to prevent bundling server-only modules
    config.externals = config.externals || [];
    config.externals.push({
      'cloudflare:sockets': 'commonjs cloudflare:sockets',
      'pg-native': 'commonjs pg-native',
      'pg-cloudflare': 'commonjs pg-cloudflare',
    });

    // Add module resolution rules to ignore problematic modules
    config.module.rules.push({
      test: /pg-cloudflare/,
      use: 'null-loader',
    });

    return config;
  },

  // Add comprehensive security headers
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-DNS-Prefetch-Control',
            value: 'on'
          },
          {
            key: 'Strict-Transport-Security',
            value: 'max-age=63072000; includeSubDomains; preload'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block'
          },
          {
            key: 'Referrer-Policy',
            value: 'strict-origin-when-cross-origin'
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=(), payment=(), usb=(), bluetooth=(), magnetometer=(), gyroscope=(), accelerometer=()'
          },
          {
            key: 'Content-Security-Policy',
            value: process.env.NODE_ENV === 'development'
              ? "default-src 'self' 'unsafe-eval' 'unsafe-inline'; script-src 'self' 'unsafe-eval' 'unsafe-inline' https://*.amazonaws.com https://auth.renewtrack.com; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https://*.amazonaws.com https://auth.renewtrack.com http://localhost:*; frame-src 'self' https://*.amazonaws.com https://auth.renewtrack.com; frame-ancestors 'none'; base-uri 'self'; form-action 'self' https://*.amazonaws.com;"
              : "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline' https://*.amazonaws.com https://auth.renewtrack.com; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https://*.amazonaws.com https://auth.renewtrack.com; frame-src 'self' https://*.amazonaws.com https://auth.renewtrack.com; frame-ancestors 'none'; base-uri 'self'; form-action 'self' https://*.amazonaws.com;"
          },
          ...(process.env.NODE_ENV === 'production' ? [
            {
              key: 'Cross-Origin-Embedder-Policy',
              value: 'require-corp'
            },
            {
              key: 'Cross-Origin-Opener-Policy',
              value: 'same-origin'
            },
            {
              key: 'Cross-Origin-Resource-Policy',
              value: 'same-origin'
            }
          ] : [])
        ]
      },
      {
        source: '/api/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'no-store, no-cache, must-revalidate, proxy-revalidate'
          },
          {
            key: 'Pragma',
            value: 'no-cache'
          },
          {
            key: 'Expires',
            value: '0'
          },
          // CORS headers for API routes
          {
            key: 'Access-Control-Allow-Origin',
            value: process.env.NODE_ENV === 'production'
              ? process.env.NEXT_PUBLIC_APP_URL || 'https://renewtrack.com'
              : 'http://localhost:3000'
          },
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET, POST, PUT, DELETE, OPTIONS'
          },
          {
            key: 'Access-Control-Allow-Headers',
            value: 'Content-Type, Authorization, X-Requested-With'
          },
          {
            key: 'Access-Control-Allow-Credentials',
            value: 'true'
          },
          {
            key: 'Access-Control-Max-Age',
            value: '86400' // 24 hours
          }
        ]
      }
    ]
  },
  // Restrict image sources
  images: {
    domains: [], // Configure with actual CDN domains during deployment
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**.amazonaws.com',
      },
    ],
  },
  // Add CSP
  poweredByHeader: false,
}

module.exports = nextConfig




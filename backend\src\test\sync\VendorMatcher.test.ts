/**
 * Unit Tests for Vendor Matching Algorithm
 * 
 * Tests the sophisticated vendor matching logic including:
 * - Tax ID matching (95% confidence)
 * - Domain + Name matching (90% confidence)
 * - Fuzzy Name + Address matching (70-85% confidence)
 */

import { VendorMatcher } from '../../services/sync/matchers/VendorMatcher'
import { Logger } from '../../services/Logger'
import { TenantVendor, GlobalVendor } from '../../services/sync/processors/VendorSyncProcessor'

describe('VendorMatcher', () => {
  let matcher: VendorMatcher
  let mockLogger: jest.Mocked<Logger>

  beforeEach(() => {
    mockLogger = {
      info: jest.fn(),
      error: jest.fn(),
      warn: jest.fn(),
      debug: jest.fn()
    } as any

    matcher = new VendorMatcher(mockLogger)
  })

  describe('Tax ID Matching', () => {
    it('should match identical tax IDs with 95% confidence', async () => {
      const tenantVendor: TenantVendor = {
        id: 'tenant-1',
        name: 'Microsoft Corporation',
        taxId: '91-1144442',
        createdAt: new Date(),
        updatedAt: new Date()
      }

      const globalVendors: GlobalVendor[] = [{
        id: 'global-1',
        name: 'Microsoft Corp',
        taxId: '91-1144442',
        confidence: 100,
        sourceCount: 5,
        lastUpdated: new Date()
      }]

      const matches = await matcher.findMatches(tenantVendor, globalVendors)

      expect(matches).toHaveLength(1)
      expect(matches[0].confidence).toBe(95)
      expect(matches[0].matchType).toBe('tax_id')
      expect(matches[0].globalVendorId).toBe('global-1')
    })

    it('should normalize tax IDs before matching', async () => {
      const tenantVendor: TenantVendor = {
        id: 'tenant-1',
        name: 'Microsoft Corporation',
        taxId: '91 - 1144442',
        createdAt: new Date(),
        updatedAt: new Date()
      }

      const globalVendors: GlobalVendor[] = [{
        id: 'global-1',
        name: 'Microsoft Corp',
        taxId: '91-1144442',
        confidence: 100,
        sourceCount: 5,
        lastUpdated: new Date()
      }]

      const matches = await matcher.findMatches(tenantVendor, globalVendors)

      expect(matches).toHaveLength(1)
      expect(matches[0].confidence).toBe(95)
      expect(matches[0].matchType).toBe('tax_id')
    })

    it('should not match different tax IDs', async () => {
      const tenantVendor: TenantVendor = {
        id: 'tenant-1',
        name: 'Microsoft Corporation',
        taxId: '91-1144442',
        createdAt: new Date(),
        updatedAt: new Date()
      }

      const globalVendors: GlobalVendor[] = [{
        id: 'global-1',
        name: 'Microsoft Corp',
        taxId: '91-1144443',
        confidence: 100,
        sourceCount: 5,
        lastUpdated: new Date()
      }]

      const matches = await matcher.findMatches(tenantVendor, globalVendors)

      expect(matches).toHaveLength(0)
    })
  })

  describe('Domain + Name Matching', () => {
    it('should match same domain with similar name at 90% confidence', async () => {
      const tenantVendor: TenantVendor = {
        id: 'tenant-1',
        name: 'Microsoft Corporation',
        domain: 'microsoft.com',
        createdAt: new Date(),
        updatedAt: new Date()
      }

      const globalVendors: GlobalVendor[] = [{
        id: 'global-1',
        name: 'Microsoft Corp',
        domain: 'microsoft.com',
        confidence: 100,
        sourceCount: 5,
        lastUpdated: new Date()
      }]

      const matches = await matcher.findMatches(tenantVendor, globalVendors)

      expect(matches).toHaveLength(1)
      expect(matches[0].confidence).toBe(90)
      expect(matches[0].matchType).toBe('domain_name')
    })

    it('should normalize domains before matching', async () => {
      const tenantVendor: TenantVendor = {
        id: 'tenant-1',
        name: 'Microsoft Corporation',
        domain: 'https://www.microsoft.com/',
        createdAt: new Date(),
        updatedAt: new Date()
      }

      const globalVendors: GlobalVendor[] = [{
        id: 'global-1',
        name: 'Microsoft Corp',
        domain: 'microsoft.com',
        confidence: 100,
        sourceCount: 5,
        lastUpdated: new Date()
      }]

      const matches = await matcher.findMatches(tenantVendor, globalVendors)

      expect(matches).toHaveLength(1)
      expect(matches[0].confidence).toBe(90)
      expect(matches[0].matchType).toBe('domain_name')
    })

    it('should not match same domain with very different names', async () => {
      const tenantVendor: TenantVendor = {
        id: 'tenant-1',
        name: 'Microsoft Corporation',
        domain: 'microsoft.com',
        createdAt: new Date(),
        updatedAt: new Date()
      }

      const globalVendors: GlobalVendor[] = [{
        id: 'global-1',
        name: 'Apple Inc',
        domain: 'microsoft.com',
        confidence: 100,
        sourceCount: 5,
        lastUpdated: new Date()
      }]

      const matches = await matcher.findMatches(tenantVendor, globalVendors)

      expect(matches).toHaveLength(0)
    })
  })

  describe('Fuzzy Name + Address Matching', () => {
    it('should match similar names and addresses with appropriate confidence', async () => {
      const tenantVendor: TenantVendor = {
        id: 'tenant-1',
        name: 'Adobe Inc.',
        address: '345 Park Avenue',
        city: 'San Jose',
        state: 'CA',
        country: 'USA',
        createdAt: new Date(),
        updatedAt: new Date()
      }

      const globalVendors: GlobalVendor[] = [{
        id: 'global-1',
        name: 'Adobe Inc',
        address: '345 Park Ave',
        city: 'San Jose',
        state: 'California',
        country: 'United States',
        confidence: 100,
        sourceCount: 5,
        lastUpdated: new Date()
      }]

      const matches = await matcher.findMatches(tenantVendor, globalVendors)

      expect(matches).toHaveLength(1)
      expect(matches[0].confidence).toBeGreaterThanOrEqual(70)
      expect(matches[0].confidence).toBeLessThanOrEqual(85)
      expect(matches[0].matchType).toBe('fuzzy_name_address')
    })

    it('should match names without address but with lower confidence', async () => {
      const tenantVendor: TenantVendor = {
        id: 'tenant-1',
        name: 'Adobe Inc.',
        createdAt: new Date(),
        updatedAt: new Date()
      }

      const globalVendors: GlobalVendor[] = [{
        id: 'global-1',
        name: 'Adobe Inc',
        confidence: 100,
        sourceCount: 5,
        lastUpdated: new Date()
      }]

      const matches = await matcher.findMatches(tenantVendor, globalVendors)

      expect(matches).toHaveLength(1)
      expect(matches[0].confidence).toBeLessThanOrEqual(80) // Capped without address
      expect(matches[0].matchType).toBe('fuzzy_name_address')
    })

    it('should not match very different names', async () => {
      const tenantVendor: TenantVendor = {
        id: 'tenant-1',
        name: 'Microsoft Corporation',
        createdAt: new Date(),
        updatedAt: new Date()
      }

      const globalVendors: GlobalVendor[] = [{
        id: 'global-1',
        name: 'Apple Inc',
        confidence: 100,
        sourceCount: 5,
        lastUpdated: new Date()
      }]

      const matches = await matcher.findMatches(tenantVendor, globalVendors)

      expect(matches).toHaveLength(0)
    })
  })

  describe('Match Prioritization', () => {
    it('should prioritize tax ID match over domain match', async () => {
      const tenantVendor: TenantVendor = {
        id: 'tenant-1',
        name: 'Microsoft Corporation',
        taxId: '91-1144442',
        domain: 'microsoft.com',
        createdAt: new Date(),
        updatedAt: new Date()
      }

      const globalVendors: GlobalVendor[] = [
        {
          id: 'global-1',
          name: 'Microsoft Corp',
          domain: 'microsoft.com',
          confidence: 100,
          sourceCount: 5,
          lastUpdated: new Date()
        },
        {
          id: 'global-2',
          name: 'Microsoft Inc',
          taxId: '91-1144442',
          confidence: 100,
          sourceCount: 5,
          lastUpdated: new Date()
        }
      ]

      const matches = await matcher.findMatches(tenantVendor, globalVendors)

      expect(matches).toHaveLength(1) // Should only return tax ID match
      expect(matches[0].confidence).toBe(95)
      expect(matches[0].matchType).toBe('tax_id')
      expect(matches[0].globalVendorId).toBe('global-2')
    })

    it('should sort matches by confidence descending', async () => {
      const tenantVendor: TenantVendor = {
        id: 'tenant-1',
        name: 'Adobe Inc.',
        address: '345 Park Avenue',
        createdAt: new Date(),
        updatedAt: new Date()
      }

      const globalVendors: GlobalVendor[] = [
        {
          id: 'global-1',
          name: 'Adobe Systems', // Lower similarity
          address: '345 Park Avenue',
          confidence: 100,
          sourceCount: 5,
          lastUpdated: new Date()
        },
        {
          id: 'global-2',
          name: 'Adobe Inc', // Higher similarity
          address: '345 Park Ave',
          confidence: 100,
          sourceCount: 5,
          lastUpdated: new Date()
        }
      ]

      const matches = await matcher.findMatches(tenantVendor, globalVendors)

      expect(matches).toHaveLength(2)
      expect(matches[0].confidence).toBeGreaterThan(matches[1].confidence)
      expect(matches[0].globalVendorId).toBe('global-2')
    })
  })

  describe('Edge Cases', () => {
    it('should handle missing fields gracefully', async () => {
      const tenantVendor: TenantVendor = {
        id: 'tenant-1',
        name: 'Test Vendor',
        createdAt: new Date(),
        updatedAt: new Date()
      }

      const globalVendors: GlobalVendor[] = [{
        id: 'global-1',
        name: 'Test Vendor Inc',
        confidence: 100,
        sourceCount: 5,
        lastUpdated: new Date()
      }]

      const matches = await matcher.findMatches(tenantVendor, globalVendors)

      expect(matches).toHaveLength(1)
      expect(matches[0].matchType).toBe('fuzzy_name_address')
    })

    it('should handle special characters in names', async () => {
      const tenantVendor: TenantVendor = {
        id: 'tenant-1',
        name: 'Spëcîál Çhäracters & Co. Ltd.',
        createdAt: new Date(),
        updatedAt: new Date()
      }

      const globalVendors: GlobalVendor[] = [{
        id: 'global-1',
        name: 'Special Characters & Co Ltd',
        confidence: 100,
        sourceCount: 5,
        lastUpdated: new Date()
      }]

      const matches = await matcher.findMatches(tenantVendor, globalVendors)

      expect(matches).toHaveLength(1)
      expect(matches[0].confidence).toBeGreaterThanOrEqual(70)
    })

    it('should handle empty global vendors list', async () => {
      const tenantVendor: TenantVendor = {
        id: 'tenant-1',
        name: 'Test Vendor',
        createdAt: new Date(),
        updatedAt: new Date()
      }

      const matches = await matcher.findMatches(tenantVendor, [])

      expect(matches).toHaveLength(0)
    })
  })
})

/**
 * Jest Configuration for Synchronization Tests
 * 
 * Specialized configuration for testing the sync system with:
 * - Database setup and teardown
 * - Performance test timeouts
 * - Memory monitoring
 * - Test data isolation
 */

module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  
  // Test file patterns
  testMatch: [
    '**/test/sync/**/*.test.ts',
    '**/test/data/**/*.test.ts'
  ],
  
  // Setup and teardown
  setupFilesAfterEnv: ['<rootDir>/src/test/setup/syncTestSetup.ts'],
  
  // Timeouts for performance tests
  testTimeout: 300000, // 5 minutes for performance tests
  
  // Coverage configuration
  collectCoverageFrom: [
    'src/services/sync/**/*.ts',
    '!src/services/sync/**/*.test.ts',
    '!src/test/**/*.ts'
  ],
  
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    },
    './src/services/sync/matchers/': {
      branches: 90,
      functions: 90,
      lines: 90,
      statements: 90
    }
  },
  
  // Memory monitoring
  logHeapUsage: true,
  detectOpenHandles: true,
  forceExit: true,
  
  // Test grouping
  testSequencer: '<rootDir>/src/test/setup/syncTestSequencer.js',
  
  // Environment variables for tests
  setupFiles: ['<rootDir>/src/test/setup/testEnv.ts'],
  
  // Module path mapping
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1'
  },
  
  // Transform configuration
  transform: {
    '^.+\\.ts$': 'ts-jest'
  },
  
  // Global configuration
  globals: {
    'ts-jest': {
      tsconfig: 'tsconfig.test.json'
    }
  },
  
  // Reporters
  reporters: [
    'default',
    ['jest-junit', {
      outputDirectory: 'test-results',
      outputName: 'sync-test-results.xml'
    }],
    ['jest-html-reporters', {
      publicPath: 'test-results',
      filename: 'sync-test-report.html',
      expand: true
    }]
  ],
  
  // Test categories
  runner: 'groups',
  
  // Verbose output for debugging
  verbose: true,
  
  // Fail fast on first error in CI
  bail: process.env.CI ? 1 : 0,
  
  // Parallel execution
  maxWorkers: process.env.CI ? 2 : '50%',
  
  // Cache configuration
  cacheDirectory: '<rootDir>/.jest-cache',
  
  // Clear mocks between tests
  clearMocks: true,
  restoreMocks: true,
  
  // Error handling
  errorOnDeprecated: true,
  
  // Test result processor
  testResultsProcessor: '<rootDir>/src/test/setup/testResultsProcessor.js'
}

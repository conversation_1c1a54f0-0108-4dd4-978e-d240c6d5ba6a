/**
 * Renewal Types API Endpoint
 * 
 * Provides access to renewal type metadata
 * GET /api/metadata/renewal-types - Returns active renewal types
 */

import { NextRequest } from 'next/server';
import { fetchAuthSession } from 'aws-amplify/auth';
import { 
  createSuccessResponse, 
  createErrorResponse, 
  ApiErrorCode, 
  HttpStatus,
  withErrorHandling 
} from '@/lib/api/response';
import { executeQuery } from '@/lib/database';
import { RenewalType } from '@/app/api/metadata/route';

// GET /api/metadata/renewal-types - Get active renewal types
export const GET = withErrorHandling(async (request: NextRequest) => {
  // Verify authentication using Amplify
  try {
    const session = await fetchAuthSession();
    if (!session?.tokens?.idToken) {
      return createErrorResponse(
        'Authentication required',
        ApiErrorCode.UNAUTHORIZED,
        HttpStatus.UNAUTHORIZED
      );
    }
  } catch (error) {
    return createErrorResponse(
      'Authentication failed',
      ApiErrorCode.UNAUTHORIZED,
      HttpStatus.UNAUTHORIZED
    );
  }

  try {
    // Query renewal types from metadata schema
    const result = await executeQuery<RenewalType>(
      `SELECT
        id,
        name,
        status
      FROM metadata.global_renewal_types
      WHERE status = 'A'
      ORDER BY name ASC`,
      []
    );

    if (!result.success) {
      console.error('Failed to fetch renewal types:', result.error);
      return createErrorResponse(
        'Failed to fetch renewal types',
        ApiErrorCode.DATABASE_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }

    // Log successful fetch
    console.log(`Renewal types fetched successfully: ${result.data?.length || 0} records`);

    return createSuccessResponse(
      result.data || [],
      'Renewal types retrieved successfully'
    );

  } catch (error) {
    console.error('Error fetching renewal types:', error);
    return createErrorResponse(
      'Failed to fetch renewal types',
      ApiErrorCode.DATABASE_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
});

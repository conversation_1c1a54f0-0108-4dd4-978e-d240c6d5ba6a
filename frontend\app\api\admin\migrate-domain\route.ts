/**
 * Database Migration API - Fix Domain Schema
 * 
 * This endpoint runs the domain column migration to fix the schema mismatch
 */

import { NextRequest } from 'next/server'
import { createSuccessResponse, createErrorResponse, ApiErrorCode, HttpStatus } from '@/lib/api/response'
import { executeQuery, executeTransaction } from '@/lib/database'
import { handleError, getUserFriendlyMessage } from '@/lib/utils/error-handler'

/**
 * POST /api/admin/migrate-domain
 * Runs the domain column migration
 */
export async function POST(request: NextRequest) {
  try {
    console.log('🔍 [MIGRATION] Starting domain schema migration...')
    
    // Check current structure
    const structureResult = await executeQuery(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_schema = 'metadata' 
        AND table_name = 'clients' 
        AND column_name = 'domain'
    `)
    
    if (!structureResult.success) {
      return createErrorResponse(
        'Failed to check current schema',
        ApiErrorCode.INTERNAL_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR,
        structureResult.error
      )
    }
    
    console.log('📋 [MIGRATION] Current domain column structure:', structureResult.data)
    
    // Check if domain is already an array
    const isArray = structureResult.data?.[0]?.data_type === 'ARRAY'
    
    if (isArray) {
      console.log('✅ [MIGRATION] Domain column is already an array, no migration needed')
      return createSuccessResponse({
        message: 'Domain column is already an array format',
        migrationNeeded: false,
        currentStructure: structureResult.data
      })
    }
    
    console.log('🔄 [MIGRATION] Converting domain column to array...')
    
    // Run migration in transaction
    const migrationQueries = [
      // Step 1: Add temporary array column
      `ALTER TABLE metadata.clients 
       ADD COLUMN IF NOT EXISTS domain_temp VARCHAR(255)[]`,
      
      // Step 2: Migrate data to array format
      `UPDATE metadata.clients 
       SET domain_temp = ARRAY[domain]
       WHERE domain_temp IS NULL AND domain IS NOT NULL`,
      
      // Step 3: Drop old column
      `ALTER TABLE metadata.clients 
       DROP COLUMN IF EXISTS domain`,
      
      // Step 4: Rename new column
      `ALTER TABLE metadata.clients 
       RENAME COLUMN domain_temp TO domain`,
      
      // Step 5: Add constraints
      `ALTER TABLE metadata.clients 
       ADD CONSTRAINT domain_not_empty 
       CHECK (array_length(domain, 1) > 0)`,
      
      // Step 6: Create index for performance
      `CREATE INDEX IF NOT EXISTS idx_admin_clients_domain_gin 
       ON metadata.clients USING GIN (domain)`
    ]
    
    const migrationResult = await executeTransaction(migrationQueries.map(query => ({ query })))
    
    if (!migrationResult.success) {
      console.error('❌ [MIGRATION] Migration failed:', migrationResult.error)
      return createErrorResponse(
        'Domain schema migration failed',
        ApiErrorCode.INTERNAL_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR,
        migrationResult.error
      )
    }
    
    console.log('✅ [MIGRATION] Domain column successfully converted to array')
    
    // Verify the changes
    const verifyResult = await executeQuery(`
      SELECT client_id, name, domain, status 
      FROM metadata.clients 
      WHERE status = 'A'
    `)
    
    if (verifyResult.success) {
      console.log('📋 [MIGRATION] Updated clients data:', verifyResult.data)
    }
    
    // Test the sidebar query
    console.log('🧪 [MIGRATION] Testing sidebar query...')
    const testResult = await executeQuery(`
      SELECT DISTINCT
        p.id,
        p.name,
        p.header,
        p.display_order,
        p.route_path
      FROM metadata.admin_pages p
      INNER JOIN metadata.admin_pages_groups pg ON p.id = pg.page_id
      INNER JOIN metadata.page_addon_packages pap ON p.id = pap.page_id
      INNER JOIN metadata.addon_packages ap ON pap.package_id = ap.id
      INNER JOIN metadata.client_addon_packages cap ON ap.id = cap.package_id
      INNER JOIN metadata.clients c ON cap.client_id = c.client_id
      WHERE p.status = 'A'
        AND p.sidebar = true
        AND pg.group_name = ANY($1)
        AND $2 = ANY(c.domain)
        AND c.status = 'A'
        AND ap.status = 'A'
        AND cap.status = 'A'
      ORDER BY p.display_order ASC, p.name ASC
    `, [['admin', 'user'], 'renewtrack.com'])
    
    if (testResult.success) {
      console.log('✅ [MIGRATION] Sidebar query test successful:', testResult.data)
    } else {
      console.warn('⚠️ [MIGRATION] Sidebar query test failed:', testResult.error)
    }
    
    return createSuccessResponse({
      message: 'Domain schema migration completed successfully',
      migrationNeeded: true,
      migrationCompleted: true,
      updatedRecords: verifyResult.data || [],
      testQueryResults: testResult.data || []
    })
    
  } catch (error) {
    console.error('❌ [MIGRATION] Migration failed with error:', error)
    
    const errorInfo = handleError(error as Error, {
      component: 'migration',
      operation: 'migrateDomainSchema'
    })
    
    return createErrorResponse(
      'Migration failed',
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR,
      getUserFriendlyMessage(errorInfo)
    )
  }
}

/**
 * GET /api/admin/migrate-domain
 * Check if migration is needed
 */
export async function GET(request: NextRequest) {
  try {
    // Check current structure
    const structureResult = await executeQuery(`
      SELECT column_name, data_type, is_nullable
      FROM information_schema.columns 
      WHERE table_schema = 'metadata' 
        AND table_name = 'clients' 
        AND column_name = 'domain'
    `)
    
    if (!structureResult.success) {
      return createErrorResponse(
        'Failed to check schema',
        ApiErrorCode.INTERNAL_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR,
        structureResult.error
      )
    }
    
    const isArray = structureResult.data?.[0]?.data_type === 'ARRAY'
    
    return createSuccessResponse({
      migrationNeeded: !isArray,
      currentDataType: structureResult.data?.[0]?.data_type || 'unknown',
      currentStructure: structureResult.data
    })
    
  } catch (error) {
    const errorInfo = handleError(error as Error, {
      component: 'migration',
      operation: 'checkMigrationStatus'
    })
    
    return createErrorResponse(
      'Failed to check migration status',
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR,
      getUserFriendlyMessage(errorInfo)
    )
  }
}

/**
 * License Validation API Endpoint
 * 
 * Provides server-side license validation for authenticated users.
 * This replaces the middleware-based validation with a proper API approach.
 */

import { NextRequest } from 'next/server'
import { fetchAuthSession } from 'aws-amplify/auth'
import { createApiRoute } from '@/lib/api/route-factory'
import { databaseService } from '@/lib/services/database-service'
import { getClientByEmailDomain } from '@/lib/tenant/clients'
import { z } from 'zod'

const validateLicenseSchema = z.object({
  email: z.string().email(),
  requiredFeatures: z.array(z.string()).optional().default([])
})

// POST /api/license/validate - Validate user license
export const POST = createApiRoute('POST', {
  requireAuth: true,
  bodySchema: validateLicenseSchema,
  handler: async (context) => {
    if (!context.body) {
      throw new Error('Invalid request data. Please try again.')
    }

    const { email, requiredFeatures } = context.body
    const startTime = Date.now()

    try {
      console.log(`🔍 [LICENSE-VALIDATE] Validating license for: ${email}`)

      // Get client by email domain
      const clientResult = await getClientByEmailDomain(email)
      
      if (!clientResult.success) {
        const duration = Date.now() - startTime
        console.log(`❌ [LICENSE-VALIDATE] Client lookup failed (${duration}ms): ${clientResult.error}`)
        
        return {
          isValid: false,
          reason: clientResult.error || 'Unable to determine client context'
        }
      }

      const clientId = parseInt(clientResult.client!.clientId)
      console.log(`✅ [LICENSE-VALIDATE] Found client: ${clientResult.client!.clientName} (ID: ${clientId})`)

      // Ensure database service is initialized
      await databaseService.initialize()

      // Check if the license validation function exists
      const functionExistsQuery = `
        SELECT EXISTS (
          SELECT 1 FROM pg_proc p
          JOIN pg_namespace n ON p.pronamespace = n.oid
          WHERE n.nspname = 'metadata' 
          AND p.proname = 'check_client_license_validity'
        ) as function_exists
      `

      const functionCheck = await databaseService.query(functionExistsQuery)
      
      if (!functionCheck.rows[0].function_exists) {
        console.warn('License validation function not found, allowing access')
        return {
          isValid: true,
          reason: 'License validation function not available',
          licenseInfo: {
            clientId: clientId,
            clientName: clientResult.client!.clientName,
            features: ['basic_access']
          }
        }
      }

      // Use the database function to check license validity
      const query = `
        SELECT metadata.check_client_license_validity($1) as result
      `

      const result = await databaseService.query(query, [clientId])
      
      if (!result.rows || result.rows.length === 0) {
        throw new Error('No result from license validation function')
      }

      const validationResult = result.rows[0].result
      const duration = Date.now() - startTime

      console.log(`✅ [LICENSE-VALIDATE] Validation completed (${duration}ms) - Valid: ${validationResult.is_valid}`)

      // Check required features if license is valid
      let hasRequiredFeatures = true
      if (validationResult.is_valid && requiredFeatures && requiredFeatures.length > 0) {
        const availableFeatures = validationResult.license_info?.features || []
        hasRequiredFeatures = requiredFeatures.every(feature => 
          availableFeatures.includes(feature)
        )
      }

      const responseData = {
        isValid: validationResult.is_valid && hasRequiredFeatures,
        reason: !validationResult.is_valid 
          ? validationResult.reason 
          : !hasRequiredFeatures 
            ? `Missing required features: ${requiredFeatures?.filter(f => !validationResult.license_info?.features?.includes(f)).join(', ')}`
            : null,
        licenseInfo: validationResult.is_valid ? {
          clientId: clientId,
          clientName: clientResult.client!.clientName,
          expiresAt: validationResult.license_info?.expires_at,
          features: validationResult.license_info?.features || []
        } : null
      }

      return responseData

    } catch (error) {
      const duration = Date.now() - startTime
      console.error(`❌ [LICENSE-VALIDATE] Error validating license (${duration}ms):`, error)
      
      // In case of error, allow access but log the issue
      // This prevents the app from being completely blocked due to license validation issues
      return {
        isValid: true,
        reason: 'License validation error - access granted temporarily',
        licenseInfo: {
          clientId: 0,
          clientName: 'Unknown',
          features: ['basic_access']
        }
      }
    }
  }
})

/**
 * Application State Management Hook
 * 
 * Provides centralized state management with selective re-rendering
 */

'use client'

import { useState, useCallback, useRef, useEffect } from 'react'

export interface AppState {
  user: any | null
  tenant: any | null
  sidebarCollapsed: boolean
  theme: 'light' | 'dark'
  notifications: any[]
  loading: {
    global: boolean
    [key: string]: boolean
  }
  cache: Map<string, any>
  [key: string]: any
}

type StateUpdater<T> = T | ((prevState: T) => T)
type StateListener = (state: AppState) => void

// Global state store
let globalState: AppState = {
  user: null,
  tenant: null,
  sidebarCollapsed: false,
  theme: 'light',
  notifications: [],
  loading: {
    global: false
  },
  cache: new Map()
}

// Listeners for state changes
const listeners = new Set<StateListener>()

// Notify all listeners of state changes
function notifyListeners() {
  listeners.forEach(listener => listener(globalState))
}

/**
 * Update global state
 */
function updateGlobalState<K extends keyof AppState>(
  key: K,
  value: StateUpdater<AppState[K]>
) {
  const newValue = typeof value === 'function' 
    ? (value as Function)(globalState[key])
    : value
    
  globalState = {
    ...globalState,
    [key]: newValue
  }
  
  notifyListeners()
}

/**
 * Application state management hook
 */
export function useAppState<T extends keyof AppState>(
  selector?: T | T[]
): T extends keyof AppState 
  ? [AppState[T], (value: StateUpdater<AppState[T]>) => void]
  : [AppState, (key: keyof AppState, value: any) => void] {

  const [, forceUpdate] = useState({})
  const selectorRef = useRef(selector)
  const lastValueRef = useRef<any>()

  // Force component re-render
  const rerender = useCallback(() => {
    forceUpdate({})
  }, [])

  // Subscribe to state changes
  useEffect(() => {
    const listener = (newState: AppState) => {
      if (!selectorRef.current) {
        // No selector, always update
        rerender()
        return
      }

      // Check if selected values changed
      const selectors = Array.isArray(selectorRef.current) 
        ? selectorRef.current 
        : [selectorRef.current]

      const currentValue = selectors.length === 1
        ? newState[selectors[0]]
        : selectors.reduce((acc, key) => ({ ...acc, [key]: newState[key] }), {})

      if (JSON.stringify(currentValue) !== JSON.stringify(lastValueRef.current)) {
        lastValueRef.current = currentValue
        rerender()
      }
    }

    listeners.add(listener)
    
    return () => {
      listeners.delete(listener)
    }
  }, [rerender])

  // Return selected state and setter
  if (selector) {
    if (Array.isArray(selector)) {
      // Multiple selectors
      const selectedState = selector.reduce((acc, key) => ({
        ...acc,
        [key]: globalState[key]
      }), {} as Pick<AppState, T>)

      const setState = (key: keyof AppState, value: any) => {
        updateGlobalState(key, value)
      }

      return [selectedState, setState] as any
    } else {
      // Single selector
      const selectedValue = globalState[selector]
      const setValue = (value: StateUpdater<AppState[T]>) => {
        updateGlobalState(selector, value)
      }

      return [selectedValue, setValue] as any
    }
  }

  // No selector, return entire state
  const setState = (key: keyof AppState, value: any) => {
    updateGlobalState(key, value)
  }

  return [globalState, setState] as any
}

// Convenience hooks for common state
export function useUser() {
  return useAppState('user')
}

export function useTenantState() {
  return useAppState('tenant')
}

export function useSidebar() {
  return useAppState('sidebarCollapsed')
}

export function useTheme() {
  return useAppState('theme')
}

export function useNotifications() {
  return useAppState('notifications')
}

export function useGlobalLoading() {
  return useAppState('loading')
}

// Utility functions
export const appStateUtils = {
  // Get current state without subscribing
  getState: () => globalState,
  
  // Set loading state for specific key
  setLoading: (key: string, loading: boolean) => {
    updateGlobalState('loading', (prev) => ({
      ...prev,
      [key]: loading
    }))
  },
  
  // Add notification
  addNotification: (notification: any) => {
    updateGlobalState('notifications', (prev) => [...prev, notification])
  },
  
  // Remove notification
  removeNotification: (id: string) => {
    updateGlobalState('notifications', (prev) => 
      prev.filter((n: any) => n.id !== id)
    )
  },
  
  // Clear all notifications
  clearNotifications: () => {
    updateGlobalState('notifications', [])
  },
  
  // Cache utilities
  setCache: (key: string, value: any) => {
    updateGlobalState('cache', (prev) => {
      const newCache = new Map(prev)
      newCache.set(key, value)
      return newCache
    })
  },
  
  getCache: (key: string) => {
    return globalState.cache.get(key)
  },
  
  clearCache: (key?: string) => {
    if (key) {
      updateGlobalState('cache', (prev) => {
        const newCache = new Map(prev)
        newCache.delete(key)
        return newCache
      })
    } else {
      updateGlobalState('cache', new Map())
    }
  }
}

/**
 * Filters Hook
 * 
 * Consolidates filtering patterns found across components.
 * Provides consistent multi-field filtering with various filter types.
 */

import { useState, useCallback, useMemo } from 'react';

export type FilterValue = string | number | boolean | Date | null | undefined;
export type FilterOperator = 'equals' | 'contains' | 'startsWith' | 'endsWith' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'notIn';

export interface FilterRule<T = any> {
  field: keyof T;
  operator: FilterOperator;
  value: FilterValue | FilterValue[];
}

export interface FilterConfig<T = any> {
  // Initial filters
  initialFilters?: Record<string, FilterValue>;
  
  // Filter rules for complex filtering
  filterRules?: FilterRule<T>[];
  
  // Custom filter function
  customFilter?: (items: T[], filters: Record<string, FilterValue>) => T[];
  
  // Case sensitive filtering
  caseSensitive?: boolean;
  
  // Callbacks
  onFilter?: (filters: Record<string, FilterValue>, results: T[]) => void;
  onClear?: () => void;
}

export interface FilterState {
  filters: Record<string, FilterValue>;
  activeFilterCount: number;
  hasActiveFilters: boolean;
}

export interface FilterActions {
  setFilter: (key: string, value: FilterValue) => void;
  setFilters: (filters: Record<string, FilterValue>) => void;
  clearFilter: (key: string) => void;
  clearAllFilters: () => void;
  toggleFilter: (key: string, value: FilterValue) => void;
  reset: () => void;
}

export interface UseFiltersReturn<T = any> {
  state: FilterState;
  actions: FilterActions;
  filteredData: T[];
  
  // Convenience getters
  filters: Record<string, FilterValue>;
  activeFilterCount: number;
  hasActiveFilters: boolean;
  
  // Filter helpers
  getFilterValue: (key: string) => FilterValue;
  isFilterActive: (key: string) => boolean;
  getFilterOptions: (field: keyof T, data: T[]) => Array<{ label: string; value: FilterValue }>;
}

/**
 * Filters Hook
 */
export function useFilters<T = any>(
  data: T[],
  config: FilterConfig<T> = {}
): UseFiltersReturn<T> {
  const {
    initialFilters = {},
    filterRules = [],
    customFilter,
    caseSensitive = false,
    onFilter,
    onClear
  } = config;
  
  // Filter state
  const [filters, setFiltersState] = useState<Record<string, FilterValue>>(initialFilters);
  
  // Count active filters
  const activeFilterCount = useMemo(() => {
    return Object.values(filters).filter(value => 
      value !== null && 
      value !== undefined && 
      value !== '' && 
      (Array.isArray(value) ? value.length > 0 : true)
    ).length;
  }, [filters]);
  
  // Check if has active filters
  const hasActiveFilters = activeFilterCount > 0;
  
  // Apply filter rule
  const applyFilterRule = useCallback((items: T[], rule: FilterRule<T>): T[] => {
    const { field, operator, value } = rule;
    
    return items.filter(item => {
      const itemValue = item[field];
      
      switch (operator) {
        case 'equals':
          return itemValue === value;
          
        case 'contains':
          if (typeof itemValue === 'string' && typeof value === 'string') {
            const itemStr = caseSensitive ? itemValue : itemValue.toLowerCase();
            const valueStr = caseSensitive ? value : value.toLowerCase();
            return itemStr.includes(valueStr);
          }
          return false;
          
        case 'startsWith':
          if (typeof itemValue === 'string' && typeof value === 'string') {
            const itemStr = caseSensitive ? itemValue : itemValue.toLowerCase();
            const valueStr = caseSensitive ? value : value.toLowerCase();
            return itemStr.startsWith(valueStr);
          }
          return false;
          
        case 'endsWith':
          if (typeof itemValue === 'string' && typeof value === 'string') {
            const itemStr = caseSensitive ? itemValue : itemValue.toLowerCase();
            const valueStr = caseSensitive ? value : value.toLowerCase();
            return itemStr.endsWith(valueStr);
          }
          return false;
          
        case 'gt':
          return Number(itemValue) > Number(value);
          
        case 'gte':
          return Number(itemValue) >= Number(value);
          
        case 'lt':
          return Number(itemValue) < Number(value);
          
        case 'lte':
          return Number(itemValue) <= Number(value);
          
        case 'in':
          return Array.isArray(value) && value.includes(itemValue as any);
          
        case 'notIn':
          return Array.isArray(value) && !value.includes(itemValue as any);
          
        default:
          return true;
      }
    });
  }, [caseSensitive]);
  
  // Default filter function
  const defaultFilter = useCallback((items: T[], filterValues: Record<string, FilterValue>): T[] => {
    let filtered = items;
    
    // Apply filter rules first
    filterRules.forEach(rule => {
      filtered = applyFilterRule(filtered, rule);
    });
    
    // Apply simple filters
    Object.entries(filterValues).forEach(([key, value]) => {
      if (value === null || value === undefined || value === '') return;
      
      // Handle "All" values (common pattern)
      if (typeof value === 'string' && value.startsWith('All ')) return;
      
      filtered = filtered.filter(item => {
        const itemValue = (item as any)[key];
        
        if (itemValue === null || itemValue === undefined) return false;
        
        // String matching
        if (typeof itemValue === 'string' && typeof value === 'string') {
          const itemStr = caseSensitive ? itemValue : itemValue.toLowerCase();
          const valueStr = caseSensitive ? value : value.toLowerCase();
          return itemStr.includes(valueStr);
        }
        
        // Exact matching for other types
        return itemValue === value;
      });
    });
    
    return filtered;
  }, [filterRules, applyFilterRule, caseSensitive]);
  
  // Filtered data
  const filteredData = useMemo(() => {
    const filterFunction = customFilter || defaultFilter;
    const result = filterFunction(data, filters);
    
    // Call onFilter callback if provided
    if (onFilter) {
      onFilter(filters, result);
    }
    
    return result;
  }, [data, filters, customFilter, defaultFilter, onFilter]);
  
  // Set single filter
  const setFilter = useCallback((key: string, value: FilterValue) => {
    setFiltersState(prev => ({ ...prev, [key]: value }));
  }, []);
  
  // Set multiple filters
  const setFilters = useCallback((newFilters: Record<string, FilterValue>) => {
    setFiltersState(newFilters);
  }, []);
  
  // Clear single filter
  const clearFilter = useCallback((key: string) => {
    setFiltersState(prev => {
      const newFilters = { ...prev };
      delete newFilters[key];
      return newFilters;
    });
  }, []);
  
  // Clear all filters
  const clearAllFilters = useCallback(() => {
    setFiltersState({});
    
    if (onClear) {
      onClear();
    }
  }, [onClear]);
  
  // Toggle filter (useful for boolean filters)
  const toggleFilter = useCallback((key: string, value: FilterValue) => {
    setFiltersState(prev => ({
      ...prev,
      [key]: prev[key] === value ? null : value
    }));
  }, []);
  
  // Reset to initial filters
  const reset = useCallback(() => {
    setFiltersState(initialFilters);
  }, [initialFilters]);
  
  // Get filter value
  const getFilterValue = useCallback((key: string): FilterValue => {
    return filters[key];
  }, [filters]);
  
  // Check if filter is active
  const isFilterActive = useCallback((key: string): boolean => {
    const value = filters[key];
    return value !== null && value !== undefined && value !== '';
  }, [filters]);
  
  // Get filter options from data
  const getFilterOptions = useCallback((field: keyof T, sourceData: T[] = data) => {
    const uniqueValues = new Set<FilterValue>();

    sourceData.forEach(item => {
      const value = item[field] as FilterValue;
      if (value !== null && value !== undefined) {
        uniqueValues.add(value);
      }
    });
    
    return Array.from(uniqueValues)
      .sort()
      .map(value => ({
        label: String(value),
        value
      }));
  }, [data]);
  
  return {
    state: {
      filters,
      activeFilterCount,
      hasActiveFilters
    },
    actions: {
      setFilter,
      setFilters,
      clearFilter,
      clearAllFilters,
      toggleFilter,
      reset
    },
    filteredData,
    
    // Convenience getters
    filters,
    activeFilterCount,
    hasActiveFilters,
    
    // Helpers
    getFilterValue,
    isFilterActive,
    getFilterOptions
  };
}

/**
 * Convenience hooks for common filter patterns
 */

// Simple text-based filtering
export function useSimpleFilters<T>(data: T[], initialFilters: Record<string, string> = {}) {
  return useFilters(data, { initialFilters });
}

// Multi-select filtering
export function useMultiSelectFilters<T>(data: T[], filterFields: Array<keyof T>) {
  const filterRules: FilterRule<T>[] = filterFields.map(field => ({
    field,
    operator: 'in' as const,
    value: []
  }));
  
  return useFilters(data, { filterRules });
}

// Date range filtering
export function useDateRangeFilters<T>(data: T[], dateField: keyof T) {
  const filterRules: FilterRule<T>[] = [
    { field: dateField, operator: 'gte', value: null },
    { field: dateField, operator: 'lte', value: null }
  ];
  
  return useFilters(data, { filterRules });
}

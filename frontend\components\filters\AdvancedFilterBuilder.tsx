/**
 * Advanced Filter Builder Component
 * 
 * Provides a comprehensive UI for building complex filters with multiple conditions,
 * groups, and logical operators
 */

'use client'

import React, { useState, useCallback } from 'react'
import { Form } from '@/components/ui/Form'
import { FilterCondition, FilterGroup, FilterOperator } from '@/lib/services/advanced-filter-service'

interface AdvancedFilterBuilderProps {
  initialGroups?: FilterGroup[]
  availableFields: Array<{
    field: string
    label: string
    dataType: 'string' | 'number' | 'date' | 'boolean'
    options?: Array<{ label: string; value: any }>
  }>
  onChange: (groups: FilterGroup[]) => void
  className?: string
}

const OPERATORS_BY_TYPE = {
  string: [
    { value: 'equals', label: 'Equals' },
    { value: 'not_equals', label: 'Not Equals' },
    { value: 'contains', label: 'Contains' },
    { value: 'not_contains', label: 'Does Not Contain' },
    { value: 'starts_with', label: 'Starts With' },
    { value: 'ends_with', label: 'Ends With' },
    { value: 'in', label: 'Is One Of' },
    { value: 'not_in', label: 'Is Not One Of' },
    { value: 'is_null', label: 'Is Empty' },
    { value: 'is_not_null', label: 'Is Not Empty' }
  ],
  number: [
    { value: 'equals', label: 'Equals' },
    { value: 'not_equals', label: 'Not Equals' },
    { value: 'greater_than', label: 'Greater Than' },
    { value: 'less_than', label: 'Less Than' },
    { value: 'greater_than_or_equal', label: 'Greater Than or Equal' },
    { value: 'less_than_or_equal', label: 'Less Than or Equal' },
    { value: 'between', label: 'Between' },
    { value: 'not_between', label: 'Not Between' },
    { value: 'in', label: 'Is One Of' },
    { value: 'not_in', label: 'Is Not One Of' },
    { value: 'is_null', label: 'Is Empty' },
    { value: 'is_not_null', label: 'Is Not Empty' }
  ],
  date: [
    { value: 'date_equals', label: 'Equals' },
    { value: 'date_before', label: 'Before' },
    { value: 'date_after', label: 'After' },
    { value: 'date_between', label: 'Between' },
    { value: 'date_in_last_days', label: 'In Last X Days' },
    { value: 'date_in_next_days', label: 'In Next X Days' },
    { value: 'is_null', label: 'Is Empty' },
    { value: 'is_not_null', label: 'Is Not Empty' }
  ],
  boolean: [
    { value: 'equals', label: 'Is' },
    { value: 'is_null', label: 'Is Empty' },
    { value: 'is_not_null', label: 'Is Not Empty' }
  ]
}

export default function AdvancedFilterBuilder({
  initialGroups = [{ conditions: [{ field: '', operator: 'equals' as FilterOperator, value: '' }], operator: 'AND' }],
  availableFields,
  onChange,
  className = ''
}: AdvancedFilterBuilderProps) {
  const [groups, setGroups] = useState<FilterGroup[]>(initialGroups)

  const updateGroups = useCallback((newGroups: FilterGroup[]) => {
    setGroups(newGroups)
    onChange(newGroups)
  }, [onChange])

  const addGroup = () => {
    const newGroups = [
      ...groups,
      { conditions: [{ field: '', operator: 'equals' as FilterOperator, value: '' }], operator: 'AND' as const }
    ]
    updateGroups(newGroups)
  }

  const removeGroup = (groupIndex: number) => {
    if (groups.length > 1) {
      const newGroups = groups.filter((_, index) => index !== groupIndex)
      updateGroups(newGroups)
    }
  }

  const updateGroup = (groupIndex: number, updatedGroup: FilterGroup) => {
    const newGroups = groups.map((group, index) => 
      index === groupIndex ? updatedGroup : group
    )
    updateGroups(newGroups)
  }

  const addCondition = (groupIndex: number) => {
    const newGroups = [...groups]
    newGroups[groupIndex].conditions.push({
      field: '',
      operator: 'equals' as FilterOperator,
      value: ''
    })
    updateGroups(newGroups)
  }

  const removeCondition = (groupIndex: number, conditionIndex: number) => {
    const newGroups = [...groups]
    if (newGroups[groupIndex].conditions.length > 1) {
      newGroups[groupIndex].conditions = newGroups[groupIndex].conditions.filter(
        (_, index) => index !== conditionIndex
      )
      updateGroups(newGroups)
    }
  }

  const updateCondition = (groupIndex: number, conditionIndex: number, updatedCondition: FilterCondition) => {
    const newGroups = [...groups]
    newGroups[groupIndex].conditions[conditionIndex] = updatedCondition
    updateGroups(newGroups)
  }

  const getFieldConfig = (fieldName: string) => {
    return availableFields.find(f => f.field === fieldName)
  }

  const getOperatorsForField = (fieldName: string) => {
    const fieldConfig = getFieldConfig(fieldName)
    if (!fieldConfig) return OPERATORS_BY_TYPE.string
    return OPERATORS_BY_TYPE[fieldConfig.dataType] || OPERATORS_BY_TYPE.string
  }

  const renderConditionValue = (
    condition: FilterCondition,
    groupIndex: number,
    conditionIndex: number
  ) => {
    const fieldConfig = getFieldConfig(condition.field)
    const needsValue = !['is_null', 'is_not_null'].includes(condition.operator)
    const needsMultipleValues = ['in', 'not_in', 'between', 'not_between', 'date_between'].includes(condition.operator)

    if (!needsValue) {
      return null
    }

    if (needsMultipleValues) {
      const values = condition.values || ['', '']
      return (
        <div className="flex space-x-2">
          {values.map((value, valueIndex) => (
            <Form.Input
              key={valueIndex}
              type={fieldConfig?.dataType === 'number' ? 'number' : fieldConfig?.dataType === 'date' ? 'date' : 'text'}
              value={value}
              onChange={(e) => {
                const newValues = [...values]
                newValues[valueIndex] = e.target.value
                updateCondition(groupIndex, conditionIndex, {
                  ...condition,
                  values: newValues
                })
              }}
              placeholder={`Value ${valueIndex + 1}`}
              className="flex-1"
            />
          ))}
        </div>
      )
    }

    // Single value input
    if (fieldConfig?.options && fieldConfig.options.length > 0) {
      // Dropdown for fields with predefined options
      return (
        <Form.Select
          value={condition.value}
          onChange={(e) => updateCondition(groupIndex, conditionIndex, { ...condition, value: e.target.value })}
        >
          <option value="">Select value...</option>
          {fieldConfig.options.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </Form.Select>
      )
    }

    // Regular input
    return (
      <Form.Input
        type={fieldConfig?.dataType === 'number' ? 'number' : fieldConfig?.dataType === 'date' ? 'date' : 'text'}
        value={condition.value}
        onChange={(e) => updateCondition(groupIndex, conditionIndex, { ...condition, value: e.target.value })}
        placeholder="Enter value..."
      />
    )
  }

  return (
    <div className={`advanced-filter-builder ${className}`}>
      <div className="space-y-4">
        {groups.map((group, groupIndex) => (
          <div key={groupIndex} className="border border-gray-200 rounded-lg p-4 bg-gray-50">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium text-gray-700">
                  Group {groupIndex + 1}
                </span>
                <Form.Select
                  value={group.operator}
                  onChange={(e) => updateGroup(groupIndex, { ...group, operator: e.target.value as 'AND' | 'OR' })}
                  className="text-sm"
                >
                  <option value="AND">AND (all conditions must match)</option>
                  <option value="OR">OR (any condition can match)</option>
                </Form.Select>
              </div>
              
              {groups.length > 1 && (
                <button
                  onClick={() => removeGroup(groupIndex)}
                  className="text-red-500 hover:text-red-700 text-sm"
                  title="Remove group"
                >
                  Remove Group
                </button>
              )}
            </div>

            <div className="space-y-2">
              {group.conditions.map((condition, conditionIndex) => (
                <div key={conditionIndex} className="grid grid-cols-12 gap-2 items-end">
                  {/* Field Selection */}
                  <div className="col-span-3">
                    <Form.Select
                      value={condition.field}
                      onChange={(e) => {
                        const fieldConfig = getFieldConfig(e.target.value)
                        updateCondition(groupIndex, conditionIndex, {
                          field: e.target.value,
                          operator: 'equals' as FilterOperator,
                          value: '',
                          dataType: fieldConfig?.dataType || 'string'
                        })
                      }}
                    >
                      <option value="">Select field...</option>
                      {availableFields.map(f => (
                        <option key={f.field} value={f.field}>{f.label}</option>
                      ))}
                    </Form.Select>
                  </div>

                  {/* Operator Selection */}
                  <div className="col-span-2">
                    <Form.Select
                      value={condition.operator}
                      onChange={(e) => updateCondition(groupIndex, conditionIndex, {
                        ...condition,
                        operator: e.target.value as FilterOperator,
                        value: '',
                        values: undefined
                      })}
                      disabled={!condition.field}
                    >
                      {getOperatorsForField(condition.field).map(op => (
                        <option key={op.value} value={op.value}>{op.label}</option>
                      ))}
                    </Form.Select>
                  </div>

                  {/* Value Input */}
                  <div className="col-span-6">
                    {renderConditionValue(condition, groupIndex, conditionIndex)}
                  </div>

                  {/* Actions */}
                  <div className="col-span-1 flex justify-end">
                    {group.conditions.length > 1 && (
                      <button
                        onClick={() => removeCondition(groupIndex, conditionIndex)}
                        className="text-red-500 hover:text-red-700 text-sm"
                        title="Remove condition"
                      >
                        ×
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>

            <button
              onClick={() => addCondition(groupIndex)}
              className="mt-2 text-sm text-blue-600 hover:text-blue-700"
            >
              + Add Condition
            </button>
          </div>
        ))}
      </div>

      <div className="flex justify-between items-center mt-4">
        <button
          onClick={addGroup}
          className="px-4 py-2 text-sm font-medium text-blue-600 border border-blue-600 rounded-lg hover:bg-blue-50"
        >
          + Add Group
        </button>

        <div className="text-sm text-gray-500">
          {groups.length} group{groups.length !== 1 ? 's' : ''}, {' '}
          {groups.reduce((total, group) => total + group.conditions.length, 0)} condition{groups.reduce((total, group) => total + group.conditions.length, 0) !== 1 ? 's' : ''}
        </div>
      </div>
    </div>
  )
}

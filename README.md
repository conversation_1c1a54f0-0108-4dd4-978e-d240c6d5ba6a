# 🔄 RenewTrack - SaaS Renewal Management Platform

[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
[![Next.js](https://img.shields.io/badge/Next.js-14.0-black)](https://nextjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue)](https://www.typescriptlang.org/)
[![PostgreSQL](https://img.shields.io/badge/PostgreSQL-15.0-blue)](https://www.postgresql.org/)
[![AWS](https://img.shields.io/badge/AWS-Amplify-orange)](https://aws.amazon.com/amplify/)

A comprehensive multi-tenant SaaS platform for managing software renewals, vendor relationships, and compliance tracking with advanced analytics, automated alerts, and enterprise-grade security.

## ✨ **Key Features**

- 🏢 **Multi-Tenant Architecture** - Complete tenant isolation with dedicated schemas
- 🔐 **Enterprise Authentication** - A<PERSON> Cognito with custom domain and JWT tokens
- 📊 **Advanced Analytics** - Interactive dashboards and vendor spending analysis
- 🚨 **Smart Alerts** - Automated renewal notifications and budget warnings
- 📈 **Comprehensive Reporting** - Customizable reports with export capabilities
- 🔒 **Licensing System** - Built-in license management for SaaS operations
- 🎯 **Role-Based Access** - Granular permissions (User, Admin, Super Admin)
- 📱 **Responsive Design** - Mobile-first UI with accessibility compliance

## 📁 Project Structure

```
RenewTrack/
├── 📚 docs/                          # All documentation
│   ├── architecture/                 # System architecture docs
│   ├── database/                     # Database documentation
│   ├── deployment/                   # Deployment guides
│   └── development/                  # Development guides
│
├── 🗄️ database/                       # Database files
│   ├── migrations/                   # Schema migration files
│   ├── scripts/                      # Database setup scripts
│   ├── setup-scripts/               # Installation scripts
│   └── utilities/                   # Database utility scripts
│
├── 🏗️ infrastructure/                 # Infrastructure as Code
│   ├── templates/                   # CloudFormation templates
│   ├── scripts/                     # Deployment scripts
│   └── documentation/               # Infrastructure docs
│
├── 🎨 frontend/                       # Next.js Application
│   ├── app/                         # Next.js 13+ app directory
│   ├── components/                  # React components
│   ├── contexts/                    # React contexts
│   ├── hooks/                       # Custom React hooks
│   ├── lib/                         # Utility libraries
│   ├── scripts/                     # Frontend utility scripts
│   ├── styles/                      # CSS and styling
│   └── types/                       # TypeScript type definitions
│
└── ⚙️ backend/                        # Backend Services
    ├── src/                         # Source code
    └── migrations/                  # Backend-specific migrations
```

## 🛠️ **Technology Stack**

### **Frontend**

- **Framework:** Next.js 14 with App Router
- **Language:** TypeScript 5.0
- **Styling:** Tailwind CSS with custom design system
- **State Management:** Unified App State Service with React hooks
- **Authentication:** AWS Amplify with Cognito
- **Testing:** Jest + React Testing Library
- **Build Tools:** ESLint, Prettier, TypeScript compiler

### **Backend**

- **Database:** PostgreSQL 15+ with multi-tenant schemas
- **API:** Next.js API routes with unified route factory
- **Authentication:** JWT tokens with AWS Cognito integration
- **Security:** Input validation, SQL injection prevention, CSRF protection
- **Monitoring:** Comprehensive error handling and logging

### **Infrastructure**

- **Cloud Platform:** AWS (Amplify, Cognito, RDS, CloudWatch)
- **Deployment:** Automated CI/CD with AWS Amplify
- **Security:** VPC, WAF, Security Groups, Parameter Store
- **Monitoring:** CloudWatch, application-level monitoring

### **Development**

- **Version Control:** Git with conventional commits
- **Code Quality:** ESLint, Prettier, TypeScript strict mode
- **Testing:** Unit, integration, and E2E testing
- **Documentation:** Comprehensive docs with architectural diagrams

## 🚀 Quick Start

### Prerequisites

- **Node.js 18+**
- **PostgreSQL 12+**
- **AWS CLI** (for deployment)
- **Git**

### 1. Clone and Install

```bash
git clone <repository-url>
cd RenewTrack
cd frontend && npm install
```

### 2. Database Setup

```bash
# Run the database setup script
cd database/scripts
./run_setup.bat    # Windows
./run_setup.ps1    # PowerShell
```

### 3. Environment Configuration

```bash
# Copy environment template
cp frontend/.env.example frontend/.env.local

# Configure your environment variables
# See docs/development/ for detailed configuration guide
```

### 4. Start Development

```bash
cd frontend
npm run dev
```

Visit `http://localhost:3000` to access the application.

## 📖 Documentation

### For Developers

- **[Development Setup](docs/development/)** - Complete development environment setup
- **[Architecture Overview](docs/architecture/)** - System architecture and design patterns
- **[Database Guide](docs/database/)** - Database schema and management

### For DevOps

- **[Deployment Guide](docs/deployment/)** - Production deployment instructions
- **[Infrastructure Setup](infrastructure/)** - AWS infrastructure configuration

### For Database Administrators

- **[Database Setup](database/)** - Database installation and configuration
- **[Migration Guide](database/migrations/)** - Schema migration procedures

## 🏗️ Architecture

### Multi-Tenant SaaS Architecture

- **Tenant Isolation**: Schema-based multi-tenancy
- **Authentication**: AWS Cognito integration
- **Authorization**: Role-based access control
- **Database**: PostgreSQL with tenant-specific schemas

### Technology Stack

- **Frontend**: Next.js 14, React 18, TypeScript, Tailwind CSS
- **Backend**: Node.js, PostgreSQL
- **Infrastructure**: AWS (EC2, RDS, Cognito, S3, CloudFormation)
- **Authentication**: AWS Amplify + Cognito

## 🔧 Development

### Key Commands

```bash
# Frontend development
cd frontend
npm run dev          # Start development server
npm run build        # Build for production
npm run test         # Run tests
npm run lint         # Run linting

# Database operations
cd database/scripts
./run_setup.bat      # Setup database (Windows)
./run_setup.ps1      # Setup database (PowerShell)

# Infrastructure deployment
cd infrastructure/scripts
./deploy-production.sh    # Deploy to production
```

### Project Guidelines

- **Code Style**: ESLint + Prettier configuration
- **Testing**: Jest + React Testing Library
- **Type Safety**: Full TypeScript coverage
- **Security**: Multi-layer security with tenant isolation

## 🚀 Deployment

### Development Environment

```bash
cd frontend
npm run dev
```

### Production Deployment

```bash
cd infrastructure/scripts
./deploy-production.sh
```

See [Deployment Guide](docs/deployment/PRODUCTION_DEPLOYMENT.md) for detailed instructions.

## 📊 Features

### Core Features

- ✅ **Multi-tenant SaaS architecture**
- ✅ **Renewal tracking and management**
- ✅ **Vendor and product management**
- ✅ **Alert and notification system**
- ✅ **Role-based access control**
- ✅ **Comprehensive audit logging**

### Advanced Features

- ✅ **Master data synchronization**
- ✅ **CSV import/export**
- ✅ **Dashboard analytics**
- ✅ **Automated compliance tracking**
- ✅ **API-first architecture**

## 🤝 Contributing

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Commit changes**: `git commit -m 'Add amazing feature'`
4. **Push to branch**: `git push origin feature/amazing-feature`
5. **Open a Pull Request**

## 📚 **Documentation**

### **Architecture & Design**

- [📋 Functional Specifications](docs/FUNCTIONAL_SPECIFICATIONS.md) - Complete system requirements and workflows
- [🏗️ System Architecture](docs/architecture/) - Technical architecture and design patterns
- [🗄️ Database Design](docs/database/) - Schema design and data modeling
- [🔒 Security Architecture](docs/deployment/PRODUCTION_DEPLOYMENT.md) - Security implementation details

### **Development Guides**

- [🛠️ Development Setup](docs/development/) - Local development environment setup
- [🧪 Testing Guide](docs/development/COMPONENT_TEST_RESULTS.md) - Testing strategies and best practices
- [📦 Component Library](docs/development/DESIGN_SYSTEM_MIGRATION_COMPLETE.md) - UI component documentation
- [🔄 State Management](docs/development/REUSABLE_HOOKS_GUIDE.md) - Hook patterns and state management

### **Deployment & Operations**

- [🚀 Production Deployment](docs/deployment/PRODUCTION_DEPLOYMENT.md) - Complete deployment guide
- [📊 Monitoring & Logging](docs/development/BUILD_ISSUES_RESOLVED.md) - Operational monitoring setup
- [🔧 Maintenance](docs/development/CODEBASE_ORGANIZATION.md) - Code organization and maintenance

### **User Guides**

- [👥 User Management](docs/DYNAMIC_SIDEBAR.md) - User roles and permissions
- [📈 Analytics & Reporting](docs/development/DASHBOARD_OPTIMIZATION_SUMMARY.md) - Dashboard and reporting features
- [🔄 Renewal Management](docs/FUNCTIONAL_SPECIFICATIONS.md#core-workflows) - Complete renewal workflows

## 🏗️ **Architecture Overview**

RenewTrack follows a modern, scalable architecture designed for enterprise SaaS applications:

### **Multi-Tenant Design**

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Tenant A      │    │   Tenant B      │    │   Tenant C      │
│   Schema        │    │   Schema        │    │   Schema        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Metadata      │
                    │   Schema        │
                    │ (Global Config) │
                    └─────────────────┘
```

### **Security Layers**

- **Authentication:** AWS Cognito with custom domain
- **Authorization:** Role-based access control (RBAC)
- **Data Protection:** Tenant isolation + encryption
- **API Security:** Rate limiting + input validation
- **Infrastructure:** VPC + WAF + Security Groups

### **Performance & Scalability**

- **Caching:** Multi-level caching strategy
- **Database:** Optimized queries with proper indexing
- **Frontend:** Code splitting and lazy loading
- **CDN:** Static asset optimization
- **Monitoring:** Real-time performance tracking

## 📄 License

This project is proprietary software. All rights reserved.

## 🆘 Support

- **Documentation**: Check the `docs/` directory
- **Issues**: Create an issue in the repository
- **Development**: See `docs/development/` for setup guides

---

**Built with ❤️ for efficient renewal management**

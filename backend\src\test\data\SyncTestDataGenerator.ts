/**
 * Sync Test Data Generator
 * 
 * Generates comprehensive test data sets for synchronization testing including:
 * - Perfect matches: Identical data across tenants
 * - Fuzzy matches: Similar but not identical data
 * - Conflicts: Multiple potential matches
 * - Edge cases: Unusual data formats, missing fields
 * - Performance: Large dataset processing
 */

import { Pool } from 'pg'
import { Logger } from '../../services/Logger'

export interface TestScenario {
  name: string
  description: string
  expectedMatches: number
  expectedConflicts: number
  expectedNewRecords: number
}

export interface TestVendor {
  tenantId: string
  name: string
  taxId?: string
  domain?: string
  address?: string
  city?: string
  state?: string
  country?: string
  contactEmail?: string
  contactPhone?: string
  website?: string
}

export interface TestProduct {
  tenantId: string
  vendorName: string
  name: string
  sku?: string
  gtin?: string
  category?: string
  description?: string
  website?: string
}

export interface TestProductVersion {
  tenantId: string
  productName: string
  version: string
  releaseDate?: Date
  endOfLifeDate?: Date
  supportLevel?: string
  description?: string
  downloadUrl?: string
}

export class SyncTestDataGenerator {
  private db: Pool
  private logger: Logger

  constructor(db: Pool, logger: Logger) {
    this.db = db
    this.logger = logger
  }

  /**
   * Generate all test scenarios
   */
  async generateAllTestScenarios(): Promise<TestScenario[]> {
    const scenarios: TestScenario[] = []
    
    // Perfect matches scenario
    scenarios.push(await this.generatePerfectMatchesScenario())
    
    // Fuzzy matches scenario
    scenarios.push(await this.generateFuzzyMatchesScenario())
    
    // Conflicts scenario
    scenarios.push(await this.generateConflictsScenario())
    
    // Edge cases scenario
    scenarios.push(await this.generateEdgeCasesScenario())
    
    // Performance scenario
    scenarios.push(await this.generatePerformanceScenario())
    
    return scenarios
  }

  /**
   * Generate perfect matches test data
   */
  async generatePerfectMatchesScenario(): Promise<TestScenario> {
    const vendors: TestVendor[] = [
      // Identical vendors across tenants
      {
        tenantId: '0000000000000001',
        name: 'Microsoft Corporation',
        taxId: '91-1144442',
        domain: 'microsoft.com',
        address: 'One Microsoft Way',
        city: 'Redmond',
        state: 'WA',
        country: 'USA',
        contactEmail: '<EMAIL>',
        website: 'https://microsoft.com'
      },
      {
        tenantId: '0000000000000002',
        name: 'Microsoft Corporation',
        taxId: '91-1144442',
        domain: 'microsoft.com',
        address: 'One Microsoft Way',
        city: 'Redmond',
        state: 'WA',
        country: 'USA',
        contactEmail: '<EMAIL>',
        website: 'https://microsoft.com'
      },
      {
        tenantId: '0000000000000003',
        name: 'Microsoft Corporation',
        taxId: '91-1144442',
        domain: 'microsoft.com',
        address: 'One Microsoft Way',
        city: 'Redmond',
        state: 'WA',
        country: 'USA',
        contactEmail: '<EMAIL>',
        website: 'https://microsoft.com'
      }
    ]

    const products: TestProduct[] = [
      // Identical products with GTIN matches
      {
        tenantId: '0000000000000001',
        vendorName: 'Microsoft Corporation',
        name: 'Microsoft Office 365 Business Premium',
        sku: 'CFQ7TTC0LH18',
        gtin: '889842203486',
        category: 'Productivity Software',
        description: 'Complete office suite with cloud services'
      },
      {
        tenantId: '0000000000000002',
        vendorName: 'Microsoft Corporation',
        name: 'Microsoft Office 365 Business Premium',
        sku: 'CFQ7TTC0LH18',
        gtin: '889842203486',
        category: 'Productivity Software',
        description: 'Complete office suite with cloud services'
      }
    ]

    await this.insertTestData(vendors, products, [])

    return {
      name: 'Perfect Matches',
      description: 'Identical data across tenants that should match with 95%+ confidence',
      expectedMatches: 4, // 3 vendors + 1 product
      expectedConflicts: 0,
      expectedNewRecords: 0
    }
  }

  /**
   * Generate fuzzy matches test data
   */
  async generateFuzzyMatchesScenario(): Promise<TestScenario> {
    const vendors: TestVendor[] = [
      // Similar but not identical vendors
      {
        tenantId: '0000000000000001',
        name: 'Adobe Inc.',
        taxId: '77-0019522',
        domain: 'adobe.com',
        address: '345 Park Avenue',
        city: 'San Jose',
        state: 'CA',
        country: 'USA'
      },
      {
        tenantId: '0000000000000002',
        name: 'Adobe Inc',
        taxId: '77-0019522',
        domain: 'www.adobe.com',
        address: '345 Park Ave',
        city: 'San Jose',
        state: 'California',
        country: 'United States'
      },
      {
        tenantId: '0000000000000003',
        name: 'Adobe Systems Incorporated',
        domain: 'adobe.com',
        address: '345 Park Avenue',
        city: 'San Jose',
        state: 'CA',
        country: 'USA'
      }
    ]

    const products: TestProduct[] = [
      // Similar products with different naming
      {
        tenantId: '0000000000000001',
        vendorName: 'Adobe Inc.',
        name: 'Adobe Creative Cloud for Teams',
        sku: 'CC-TEAMS-2023',
        category: 'Design Software',
        description: 'Creative suite for teams'
      },
      {
        tenantId: '0000000000000002',
        vendorName: 'Adobe Inc',
        name: 'Creative Cloud Team License',
        sku: 'CC-TEAMS-2023',
        category: 'Creative Software',
        description: 'Team license for creative applications'
      }
    ]

    await this.insertTestData(vendors, products, [])

    return {
      name: 'Fuzzy Matches',
      description: 'Similar but not identical data requiring fuzzy matching algorithms',
      expectedMatches: 3, // Should match with 70-85% confidence
      expectedConflicts: 0,
      expectedNewRecords: 0
    }
  }

  /**
   * Generate conflicts test data
   */
  async generateConflictsScenario(): Promise<TestScenario> {
    const vendors: TestVendor[] = [
      // Ambiguous matches that should create conflicts
      {
        tenantId: '0000000000000001',
        name: 'Oracle Corporation',
        domain: 'oracle.com',
        address: '500 Oracle Parkway',
        city: 'Redwood City',
        state: 'CA',
        country: 'USA'
      },
      {
        tenantId: '0000000000000002',
        name: 'Oracle Corp',
        domain: 'oracle.com',
        address: '2300 Oracle Way',
        city: 'Austin',
        state: 'TX',
        country: 'USA'
      },
      {
        tenantId: '0000000000000003',
        name: 'Oracle Systems',
        domain: 'oracle-systems.com',
        address: '100 Oracle Street',
        city: 'Boston',
        state: 'MA',
        country: 'USA'
      }
    ]

    await this.insertTestData(vendors, [], [])

    return {
      name: 'Conflicts',
      description: 'Ambiguous data that requires manual conflict resolution',
      expectedMatches: 0,
      expectedConflicts: 3,
      expectedNewRecords: 0
    }
  }

  /**
   * Generate edge cases test data
   */
  async generateEdgeCasesScenario(): Promise<TestScenario> {
    const vendors: TestVendor[] = [
      // Edge case: Missing critical fields
      {
        tenantId: '0000000000000001',
        name: 'Minimal Vendor Inc.',
        // No other fields
      },
      // Edge case: Special characters and encoding
      {
        tenantId: '0000000000000002',
        name: 'Spëcîál Çhäracters & Co. Ltd.',
        taxId: 'GB-*********',
        domain: 'special-chars.co.uk',
        address: '123 Spëcîál Street, Flöör 2',
        city: 'Löndon',
        country: 'United Kingdom'
      },
      // Edge case: Very long fields
      {
        tenantId: '0000000000000003',
        name: 'This Is A Very Long Company Name That Exceeds Normal Length Expectations And Tests Field Length Handling Corporation Limited',
        domain: 'very-long-domain-name-for-testing-purposes.example.com',
        address: 'This is an extremely long address that spans multiple lines and contains various details about the location including building numbers, street names, and additional descriptive information'
      }
    ]

    const products: TestProduct[] = [
      // Edge case: Product with minimal data
      {
        tenantId: '0000000000000001',
        vendorName: 'Minimal Vendor Inc.',
        name: 'Basic Product'
      },
      // Edge case: Product with special version format
      {
        tenantId: '0000000000000002',
        vendorName: 'Spëcîál Çhäracters & Co. Ltd.',
        name: 'Spëcîál Product™',
        sku: 'SP-2023-α',
        category: 'Spëcîál Category'
      }
    ]

    const versions: TestProductVersion[] = [
      // Edge case: Unusual version formats
      {
        tenantId: '0000000000000001',
        productName: 'Basic Product',
        version: 'v2023.12.31-alpha.1+build.123'
      },
      {
        tenantId: '0000000000000002',
        productName: 'Spëcîál Product™',
        version: '1.0.0-β.2'
      }
    ]

    await this.insertTestData(vendors, products, versions)

    return {
      name: 'Edge Cases',
      description: 'Unusual data formats, missing fields, and special characters',
      expectedMatches: 1,
      expectedConflicts: 2,
      expectedNewRecords: 3
    }
  }

  /**
   * Generate performance test data
   */
  async generatePerformanceScenario(): Promise<TestScenario> {
    const vendors: TestVendor[] = []
    const products: TestProduct[] = []
    const versions: TestProductVersion[] = []

    // Generate large dataset for performance testing
    const tenantIds = ['0000000000000001', '0000000000000002', '0000000000000003']
    const vendorNames = [
      'TechCorp', 'SoftwareInc', 'DataSystems', 'CloudServices', 'InnovationLtd',
      'DigitalSolutions', 'SmartTech', 'FutureSoft', 'NextGenSystems', 'CyberCorp'
    ]

    for (let i = 0; i < 1000; i++) {
      const tenantId = tenantIds[i % tenantIds.length]
      const baseName = vendorNames[i % vendorNames.length]
      
      vendors.push({
        tenantId,
        name: `${baseName} ${Math.floor(i / tenantIds.length)}`,
        taxId: `${String(i).padStart(2, '0')}-${String(Math.random() * 10000000).padStart(7, '0')}`,
        domain: `${baseName.toLowerCase()}${Math.floor(i / tenantIds.length)}.com`,
        address: `${100 + i} Tech Street`,
        city: ['San Francisco', 'New York', 'Austin', 'Seattle'][i % 4],
        state: ['CA', 'NY', 'TX', 'WA'][i % 4],
        country: 'USA'
      })

      // Generate products for each vendor
      for (let j = 0; j < 3; j++) {
        products.push({
          tenantId,
          vendorName: `${baseName} ${Math.floor(i / tenantIds.length)}`,
          name: `Product ${j + 1}`,
          sku: `${baseName.toUpperCase()}-${String(i).padStart(3, '0')}-${j + 1}`,
          gtin: String(Math.random() * 1000000000000).padStart(12, '0'),
          category: ['Software', 'Hardware', 'Services'][j],
          description: `Description for product ${j + 1}`
        })

        // Generate versions for each product
        for (let k = 0; k < 2; k++) {
          versions.push({
            tenantId,
            productName: `Product ${j + 1}`,
            version: `${k + 1}.0.0`,
            releaseDate: new Date(2023, k * 6, 1),
            supportLevel: ['standard', 'premium'][k]
          })
        }
      }
    }

    await this.insertTestData(vendors, products, versions)

    return {
      name: 'Performance Test',
      description: 'Large dataset for performance and scalability testing',
      expectedMatches: 2000, // Approximate based on overlap
      expectedConflicts: 100,
      expectedNewRecords: 4000
    }
  }

  /**
   * Insert test data into database
   */
  private async insertTestData(
    vendors: TestVendor[],
    products: TestProduct[],
    versions: TestProductVersion[]
  ): Promise<void> {
    const client = await this.db.connect()
    
    try {
      await client.query('BEGIN')

      // Insert vendors
      for (const vendor of vendors) {
        const tenantSchema = `tenant_${vendor.tenantId}`
        
        await client.query(`
          INSERT INTO ${tenantSchema}.tenant_vendors (
            name, tax_id, domain, address, city, state, country,
            contact_email, contact_phone, website, created_on, changed_on
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, NOW(), NOW())
          ON CONFLICT (name, COALESCE(tax_id, '')) DO NOTHING
        `, [
          vendor.name, vendor.taxId, vendor.domain, vendor.address,
          vendor.city, vendor.state, vendor.country, vendor.contactEmail,
          vendor.contactPhone, vendor.website
        ])
      }

      // Insert products
      for (const product of products) {
        const tenantSchema = `tenant_${product.tenantId}`
        
        // Get vendor ID
        const vendorResult = await client.query(`
          SELECT id FROM ${tenantSchema}.tenant_vendors WHERE name = $1 LIMIT 1
        `, [product.vendorName])
        
        if (vendorResult.rows.length > 0) {
          const vendorId = vendorResult.rows[0].id
          
          await client.query(`
            INSERT INTO ${tenantSchema}.tenant_products (
              tenant_vendor_id, name, sku, gtin, category, description, website, created_on, changed_on
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
            ON CONFLICT (tenant_vendor_id, name) DO NOTHING
          `, [
            vendorId, product.name, product.sku, product.gtin,
            product.category, product.description, product.website
          ])
        }
      }

      // Insert versions
      for (const version of versions) {
        const tenantSchema = `tenant_${version.tenantId}`
        
        // Get product ID
        const productResult = await client.query(`
          SELECT id FROM ${tenantSchema}.tenant_products WHERE name = $1 LIMIT 1
        `, [version.productName])
        
        if (productResult.rows.length > 0) {
          const productId = productResult.rows[0].id
          
          await client.query(`
            INSERT INTO ${tenantSchema}.tenant_product_versions (
              tenant_product_id, version, release_date, end_of_life_date,
              support_level, description, download_url, created_on, changed_on
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
            ON CONFLICT (tenant_product_id, version) DO NOTHING
          `, [
            productId, version.version, version.releaseDate, version.endOfLifeDate,
            version.supportLevel, version.description, version.downloadUrl
          ])
        }
      }

      await client.query('COMMIT')
      
      this.logger.info('Test data inserted successfully', {
        vendors: vendors.length,
        products: products.length,
        versions: versions.length
      })
      
    } catch (error) {
      await client.query('ROLLBACK')
      this.logger.error('Failed to insert test data', { error })
      throw error
    } finally {
      client.release()
    }
  }

  /**
   * Clean up test data
   */
  async cleanupTestData(): Promise<void> {
    const client = await this.db.connect()
    
    try {
      const tenantIds = ['0000000000000001', '0000000000000002', '0000000000000003']
      
      for (const tenantId of tenantIds) {
        const tenantSchema = `tenant_${tenantId}`
        
        await client.query(`DELETE FROM ${tenantSchema}.tenant_product_version_sync`)
        await client.query(`DELETE FROM ${tenantSchema}.tenant_product_sync`)
        await client.query(`DELETE FROM ${tenantSchema}.tenant_vendor_sync`)
        await client.query(`DELETE FROM ${tenantSchema}.tenant_product_versions`)
        await client.query(`DELETE FROM ${tenantSchema}.tenant_products`)
        await client.query(`DELETE FROM ${tenantSchema}.tenant_vendors`)
      }
      
      // Clean up global tables
      await client.query('DELETE FROM metadata.global_product_versions')
      await client.query('DELETE FROM metadata.global_products')
      await client.query('DELETE FROM metadata.global_vendors')
      await client.query('DELETE FROM metadata.sync_conflicts')
      await client.query('DELETE FROM metadata.sync_batches')
      
      this.logger.info('Test data cleaned up successfully')
      
    } finally {
      client.release()
    }
  }
}

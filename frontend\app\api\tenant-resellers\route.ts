import { NextRequest } from 'next/server';
import { withAuth } from '@/lib/api/auth-middleware';
import { resolveTenantContext } from '@/lib/tenant/context';
import { executeQuery, schemaExists } from '@/lib/database';
import {
  createSuccessResponse,
  createErrorResponse,
  ApiErrorCode,
  HttpStatus,
  withErrorHandling
} from '@/lib/api/response';
import { fetchAuthSession, fetchUserAttributes } from 'aws-amplify/auth';
import { getClientByEmailDomain } from '@/lib/tenant/clients';
import { STATUS } from '@/lib/constants/app-constants';


// Reseller interface
interface Reseller {
  id: string;
  name: string;
  contact_email?: string;
  phone?: string;
  status: string;
  created_on: Date;
  changed_on: Date;
}

// GET /api/tenant-resellers - Get all resellers for tenant
export const GET = withAuth(async (request: NextRequest, session) => {
  console.log('[TENANT-RESELLERS-API] GET request received');

  try {
    // Resolve tenant context using session
    const tenantResult = await resolveTenantContext(session);
    if (!tenantResult.success) {
      return createErrorResponse(
        'Failed to resolve tenant context',
        ApiErrorCode.DATABASE_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }

    const tenant = tenantResult.tenant;

  // Check if tenant schema exists
  if (!tenant) {
    return createErrorResponse(
      'Tenant not found',
      ApiErrorCode.NOT_FOUND,
      HttpStatus.NOT_FOUND
    );
  }

  const schemaReady = await schemaExists(tenant.tenantSchema);

  if (!schemaReady) {
    console.log(`⚠️ Tenant schema ${tenant.tenantSchema} not ready yet`);
    return createSuccessResponse([], 'Tenant schema not ready, returning empty resellers');
  }

  try {
    // First check table structure
    const tableInfoQuery = `
      SELECT column_name
      FROM information_schema.columns
      WHERE table_schema = $1 AND table_name = 'tenant_resellers'
    `;

    const tableInfoResult = await executeQuery(tableInfoQuery, [tenant.tenantSchema]);
    const columns = tableInfoResult.data?.map(col => col.column_name) || [];
    const hasStatusColumn = columns.includes('status');
    const hasIsActiveColumn = columns.includes('is_active');

    // Build query based on available columns
    let query: string;
    if (hasStatusColumn) {
      query = `
        SELECT id, name, status
        FROM "${tenant.tenantSchema}".tenant_resellers
        WHERE status = '${STATUS.ACTIVE}'
        ORDER BY name ASC
      `;
    } else if (hasIsActiveColumn) {
      query = `
        SELECT id, name, is_active
        FROM "${tenant.tenantSchema}".tenant_resellers
        WHERE is_active = true
        ORDER BY name ASC
      `;
    } else {
      query = `
        SELECT id, name
        FROM "${tenant.tenantSchema}".tenant_resellers
        ORDER BY name ASC
      `;
    }

    const result = await executeQuery(query, [], { schema: tenant.tenantSchema });

    if (!result.success) {
      console.error('Error fetching tenant resellers:', result.error);
      return createErrorResponse(
        'Failed to fetch resellers',
        ApiErrorCode.DATABASE_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }

    const resellers = result.data || [];
    console.log(`📋 [RESELLERS] Found ${resellers.length} resellers for tenant ${tenant.tenantSchema}`);

    return createSuccessResponse(resellers, 'Resellers retrieved successfully');

  } catch (error) {
    console.error('Error in tenant resellers API:', error);
    return createErrorResponse(
      'Internal server error',
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
  } catch (error) {
    console.error('[TENANT-RESELLERS-API] Outer error:', error);
    return createErrorResponse(
      'Failed to resolve tenant context',
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
}, {
  requireAuth: true
});

// POST /api/tenant-resellers - Create new reseller
export const POST = withErrorHandling(async (request: NextRequest) => {
  // Verify authentication using Amplify
  try {
    const session = await fetchAuthSession();
    if (!session?.tokens?.idToken) {
      return createErrorResponse(
        'Authentication required',
        ApiErrorCode.UNAUTHORIZED,
        HttpStatus.UNAUTHORIZED
      );
    }
  } catch (error) {
    return createErrorResponse(
      'Authentication failed',
      ApiErrorCode.UNAUTHORIZED,
      HttpStatus.UNAUTHORIZED
    );
  }

  // Get user attributes
  const userAttributes = await fetchUserAttributes();
  const userEmail = userAttributes.email;
  if (!userEmail) {
    return createErrorResponse(
      'User email not found',
      ApiErrorCode.UNAUTHORIZED,
      HttpStatus.UNAUTHORIZED
    );
  }

  try {
    const body = await request.json();
    const { name, contact_email, phone } = body;

    if (!name || typeof name !== 'string' || name.trim().length === 0) {
      return createErrorResponse(
        'Reseller name is required',
        ApiErrorCode.INVALID_INPUT,
        HttpStatus.BAD_REQUEST
      );
    }

    // Get tenant context
    const clientResult = await getClientByEmailDomain(userEmail);
    if (!clientResult.success || !clientResult.client) {
      return createErrorResponse(
        clientResult.error || 'Failed to resolve tenant context',
        (clientResult.errorCode as ApiErrorCode) || ApiErrorCode.UNAUTHORIZED,
        HttpStatus.UNAUTHORIZED
      );
    }

    const tenant = clientResult.client;

    // Check if tenant schema exists
    const schemaReady = await schemaExists(tenant.tenantSchema);

    if (!schemaReady) {
      return createErrorResponse(
        'Tenant schema not ready',
        ApiErrorCode.DATABASE_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }

    try {
      // First, check what columns exist in the table
      const tableInfoQuery = `
        SELECT column_name, data_type
        FROM information_schema.columns
        WHERE table_schema = $1 AND table_name = 'tenant_resellers'
        ORDER BY ordinal_position
      `;

      const tableInfoResult = await executeQuery(tableInfoQuery, [tenant.tenantSchema]);
      console.log('📋 [RESELLERS] Table structure:', tableInfoResult.data);

      // Check if table has 'status' or 'is_active' column
      const columns = tableInfoResult.data?.map(col => col.column_name) || [];
      const hasStatusColumn = columns.includes('status');
      const hasIsActiveColumn = columns.includes('is_active');

      console.log('📋 [RESELLERS] Column check:', { hasStatusColumn, hasIsActiveColumn, allColumns: columns });

      // Build query based on available columns
      let insertQuery: string;
      let insertValues: any[];

      if (hasStatusColumn) {
        insertQuery = `
          INSERT INTO "${tenant.tenantSchema}"."tenant_resellers" (name, contact_email, phone, status, created_on, changed_on)
          VALUES ($1, $2, $3, 'A', CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
          RETURNING id, name, contact_email, phone, status, created_on, changed_on
        `;
        insertValues = [name.trim(), contact_email || null, phone || null];
      } else if (hasIsActiveColumn) {
        insertQuery = `
          INSERT INTO "${tenant.tenantSchema}"."tenant_resellers" (name, contact_email, phone, is_active, created_on, changed_on)
          VALUES ($1, $2, $3, true, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
          RETURNING id, name, contact_email, phone, is_active, created_on, changed_on
        `;
        insertValues = [name.trim(), contact_email || null, phone || null];
      } else {
        // Fallback - just insert name
        insertQuery = `
          INSERT INTO "${tenant.tenantSchema}"."tenant_resellers" (name)
          VALUES ($1)
          RETURNING id, name, created_on
        `;
        insertValues = [name.trim()];
      }

      console.log('📋 [RESELLERS] Using query:', insertQuery);
      console.log('📋 [RESELLERS] With values:', insertValues);

      const result = await executeQuery(insertQuery, insertValues, { schema: tenant.tenantSchema });

      if (!result.success || !result.data || result.data.length === 0) {
        console.error('Error creating reseller:', result.error);
        return createErrorResponse(
          `Failed to create reseller: ${result.error}`,
          ApiErrorCode.DATABASE_ERROR,
          HttpStatus.INTERNAL_SERVER_ERROR
        );
      }

      const newReseller = result.data[0];
      console.log(`✅ [RESELLERS] Created new reseller: ${newReseller.name}`);

      return createSuccessResponse(newReseller, 'Reseller created successfully');

    } catch (error) {
      console.error('Error creating reseller:', error);
      return createErrorResponse(
        'Internal server error',
        ApiErrorCode.INTERNAL_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }

  } catch (error) {
    console.error('Error parsing request body:', error);
    return createErrorResponse(
      'Invalid request body',
      ApiErrorCode.INVALID_INPUT,
      HttpStatus.BAD_REQUEST
    );
  }
}, 'tenant-resellers-post');

'use client'

import React, { useState } from 'react'
import { useData } from '@/lib/hooks'
import { PageAccessGuard } from '@/components/auth'
import { But<PERSON>, Modal, Form, PageHeader } from '@/components/ui'
import { useToast } from '@/components/ui/Toast'

interface TenantUser {
  id: string
  email: string
  given_name?: string
  family_name?: string
  last_logged?: Date
  user_group?: string
}

interface Department {
  id: number
  name: string
  description?: string
  status: string
}

export default function UserManagementPage() {
  const [activeTab, setActiveTab] = useState<'users' | 'departments'>('users')
  const [showAddUserModal, setShowAddUserModal] = useState(false)
  const [showAddDepartmentModal, setShowAddDepartmentModal] = useState(false)
  const [selectedUser, setSelectedUser] = useState<TenantUser | null>(null)
  const [showEditUserModal, setShowEditUserModal] = useState(false)
  const toast = useToast()

  // Fetch tenant users
  const {
    data: users = [],
    loading: usersLoading,
    error: usersError,
    refetch: refetchUsers
  } = useData<TenantUser[]>({
    endpoint: '/api/tenant-users',
    cache: {
      key: 'tenant-users'
    }
  })

  // Fetch departments
  const {
    data: departments = [],
    loading: departmentsLoading,
    error: departmentsError,
    refetch: refetchDepartments
  } = useData<Department[]>({
    endpoint: '/api/tenant-departments',
    cache: {
      key: 'tenant-departments'
    }
  })

  const handleEditUser = (user: TenantUser) => {
    setSelectedUser(user)
    setShowEditUserModal(true)
  }

  const handleDeleteUser = async (userId: string) => {
    if (!confirm('Are you sure you want to remove this user?')) return

    try {
      const response = await fetch(`/api/tenant-users/${userId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        toast.success('User removed successfully')
        refetchUsers()
      } else {
        toast.error('Failed to remove user')
      }
    } catch (error) {
      toast.error('Error removing user')
    }
  }

  const handleAddDepartment = async (departmentData: { name: string; description?: string }) => {
    try {
      const response = await fetch('/api/tenant-departments', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(departmentData)
      })

      if (response.ok) {
        toast.success('Department added successfully')
        refetchDepartments()
        setShowAddDepartmentModal(false)
      } else {
        toast.error('Failed to add department')
      }
    } catch (error) {
      toast.error('Error adding department')
    }
  }

  if (usersError || departmentsError) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <h3 className="text-red-800 font-medium">Error Loading Data</h3>
          <p className="text-red-600 mt-1">
          {usersError instanceof Error ? usersError.message : usersError ||
           (departmentsError instanceof Error ? departmentsError.message : departmentsError)}
        </p>
        </div>
      </div>
    )
  }

  return (
    <PageAccessGuard pageName="user-management" redirectTo="/overview">
      <div className="overview-container">
        {/* Header */}
        <PageHeader
          title="User Management"
          subtitle="Manage users, roles, and departments within your organization"
        />

        {/* Tab Navigation */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('users')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'users'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Users ({users?.length || 0})
              </button>
              <button
                onClick={() => setActiveTab('departments')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'departments'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Departments ({departments?.length || 0})
              </button>
            </nav>
          </div>
        </div>

        {/* Users Tab */}
        {activeTab === 'users' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-lg font-medium text-gray-900">Organization Users</h2>
              <Button
                onClick={() => setShowAddUserModal(true)}
                className="btn-primary"
              >
                ➕ Add User
              </Button>
            </div>

            {usersLoading ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            ) : (
              <div className="bg-white shadow rounded-lg overflow-hidden">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        User
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Role
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Last Login
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {users?.map((user) => (
                      <tr key={user.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="flex-shrink-0 h-10 w-10">
                              <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                <span className="text-blue-600 font-medium text-sm">
                                  {user.given_name?.[0] || user.email[0].toUpperCase()}
                                </span>
                              </div>
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900">
                                {user.given_name && user.family_name 
                                  ? `${user.given_name} ${user.family_name}`
                                  : user.email
                                }
                              </div>
                              <div className="text-sm text-gray-500">{user.email}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                            {user.user_group || 'User'}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {user.last_logged 
                            ? new Date(user.last_logged).toLocaleDateString()
                            : 'Never'
                          }
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <button
                            onClick={() => handleEditUser(user)}
                            className="text-blue-600 hover:text-blue-900 mr-3"
                          >
                            Edit
                          </button>
                          <button
                            onClick={() => handleDeleteUser(user.id)}
                            className="text-red-600 hover:text-red-900"
                          >
                            Remove
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}

        {/* Departments Tab */}
        {activeTab === 'departments' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-lg font-medium text-gray-900">Departments</h2>
              <Button
                onClick={() => setShowAddDepartmentModal(true)}
                className="btn-primary"
              >
                ➕ Add Department
              </Button>
            </div>

            {departmentsLoading ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {departments?.map((department) => (
                  <div key={department.id} className="bg-white shadow rounded-lg p-6">
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      {department.name}
                    </h3>
                    {department.description && (
                      <p className="text-gray-600 text-sm mb-4">
                        {department.description}
                      </p>
                    )}
                    <div className="flex justify-between items-center">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        department.status === 'A' 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {department.status === 'A' ? 'Active' : 'Inactive'}
                      </span>
                      <button className="text-blue-600 hover:text-blue-900 text-sm">
                        Edit
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Add Department Modal */}
      <AddDepartmentModal
        isOpen={showAddDepartmentModal}
        onClose={() => setShowAddDepartmentModal(false)}
        onSubmit={handleAddDepartment}
      />
    </PageAccessGuard>
  )
}

// Add Department Modal Component
function AddDepartmentModal({
  isOpen,
  onClose,
  onSubmit
}: {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: { name: string; description?: string }) => void
}) {
  const [formData, setFormData] = useState({ name: '', description: '' })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData.name.trim()) return
    
    onSubmit({
      name: formData.name.trim(),
      description: formData.description.trim() || undefined
    })
    
    setFormData({ name: '', description: '' })
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Add Department">
      <form onSubmit={handleSubmit} className="space-y-4">
        <Form.Field>
          <Form.Label htmlFor="name" required>Department Name</Form.Label>
          <Form.Input
            id="name"
            value={formData.name}
            onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
            placeholder="Enter department name"
            required
          />
        </Form.Field>

        <Form.Field>
          <Form.Label htmlFor="description">Description</Form.Label>
          <Form.Textarea
            id="description"
            value={formData.description}
            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            placeholder="Enter department description (optional)"
            rows={3}
          />
        </Form.Field>

        <Form.Actions>
          <Button type="button" variant="secondary" onClick={onClose}>
            Cancel
          </Button>
          <Button type="submit" variant="primary">
            Add Department
          </Button>
        </Form.Actions>
      </form>
    </Modal>
  )
}

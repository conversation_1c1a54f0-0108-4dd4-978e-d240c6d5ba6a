/**
 * Product Version Synchronization Processor
 * 
 * Handles the synchronization of product version data between global canonical tables
 * and tenant-specific tables. Versions are matched within product context using:
 * 
 * Matching Algorithm Priority:
 * 1. Exact version string match (95% confidence)
 * 2. Semantic version match (90% confidence)
 * 3. Release date + fuzzy version match (70-85% confidence)
 * 
 * Product versions require product synchronization to be completed first.
 */

import { Pool, PoolClient } from 'pg'
import { Logger } from '../../Logger'
import { ProductVersionMatcher } from '../matchers/ProductVersionMatcher'
import { SyncOptions, SyncResult } from '../SyncEngine'

export interface TenantProductVersion {
  id: string
  tenantProductId: string
  version: string
  releaseDate?: Date
  endOfLifeDate?: Date
  supportLevel?: string
  description?: string
  downloadUrl?: string
  createdAt: Date
  updatedAt: Date
}

export interface GlobalProductVersion {
  id: string
  globalProductId: string
  version: string
  releaseDate?: Date
  endOfLifeDate?: Date
  supportLevel?: string
  description?: string
  downloadUrl?: string
  confidence: number
  sourceCount: number
  lastUpdated: Date
}

export interface ProductVersionMatch {
  tenantVersionId: string
  globalVersionId: string
  confidence: number
  matchType: 'exact_version' | 'semantic_version' | 'fuzzy_version_date'
  matchDetails: Record<string, any>
}

export class ProductVersionSyncProcessor {
  private db: Pool
  private logger: Logger
  private matcher: ProductVersionMatcher

  constructor(db: Pool, logger: Logger) {
    this.db = db
    this.logger = logger
    this.matcher = new ProductVersionMatcher(logger)
  }

  /**
   * Process product version synchronization for a tenant
   */
  async process(tenantId: string, batchId: string, options: SyncOptions = {}): Promise<Omit<SyncResult, 'batchId' | 'processingTimeMs'>> {
    const {
      batchSize = 100,
      maxRetries = 3,
      autoResolveThreshold = 85,
      dryRun = false
    } = options

    const client = await this.db.connect()
    
    try {
      // Get tenant schema name
      const tenantSchema = `tenant_${tenantId.padStart(16, '0')}`
      
      // Get all tenant product versions that need synchronization
      const tenantVersions = await this.getTenantProductVersions(client, tenantSchema)
      
      this.logger.info(`Processing ${tenantVersions.length} product versions for tenant ${tenantId}`, { batchId })
      
      let totalProcessed = 0
      let matched = 0
      let conflicts = 0
      const errors: string[] = []
      
      // Process in batches
      for (let i = 0; i < tenantVersions.length; i += batchSize) {
        const batch = tenantVersions.slice(i, i + batchSize)
        
        for (const tenantVersion of batch) {
          try {
            const result = await this.processProductVersion(
              client,
              tenantId,
              tenantSchema,
              tenantVersion,
              batchId,
              autoResolveThreshold,
              dryRun
            )
            
            totalProcessed++
            if (result.matched) matched++
            if (result.conflict) conflicts++
            
          } catch (error) {
            const errorMsg = `Failed to process product version ${tenantVersion.id}: ${error instanceof Error ? error.message : 'Unknown error'}`
            errors.push(errorMsg)
            this.logger.error(errorMsg, { batchId, tenantVersionId: tenantVersion.id })
          }
        }
        
        // Update batch progress
        await this.updateBatchProgress(client, batchId, totalProcessed, matched, conflicts)
      }
      
      return {
        success: errors.length === 0,
        totalProcessed,
        matched,
        conflicts,
        errors
      }
      
    } finally {
      client.release()
    }
  }

  /**
   * Process a single product version
   */
  private async processProductVersion(
    client: PoolClient,
    tenantId: string,
    tenantSchema: string,
    tenantVersion: TenantProductVersion,
    batchId: string,
    autoResolveThreshold: number,
    dryRun: boolean
  ): Promise<{ matched: boolean; conflict: boolean }> {
    
    // Check if version is already synchronized
    const existingSync = await this.getExistingSync(client, tenantSchema, tenantVersion.id)
    if (existingSync) {
      this.logger.debug(`Product version ${tenantVersion.id} already synchronized`, { globalVersionId: existingSync.globalVersionId })
      return { matched: true, conflict: false }
    }
    
    // Get the global product ID for this tenant product version
    const globalProductId = await this.getGlobalProductId(client, tenantSchema, tenantVersion.tenantProductId)
    if (!globalProductId) {
      throw new Error(`Product not synchronized for version ${tenantVersion.id}. Run product sync first.`)
    }
    
    // Find potential matches in global product versions for this product
    const globalVersions = await this.getGlobalVersionsForProduct(client, globalProductId)
    const matches = await this.matcher.findMatches(tenantVersion, globalVersions)
    
    if (matches.length === 0) {
      // No matches found - create new global product version
      if (!dryRun) {
        const globalVersionId = await this.createGlobalProductVersion(client, tenantVersion, globalProductId)
        await this.createTenantVersionSync(client, tenantSchema, tenantVersion.id, globalVersionId, 100, 'exact_match')
      }
      
      this.logger.info(`Created new global product version for tenant version ${tenantVersion.id}`)
      return { matched: true, conflict: false }
    }
    
    // Get best match
    const bestMatch = matches[0]
    
    if (bestMatch.confidence >= autoResolveThreshold) {
      // Auto-resolve high confidence matches
      if (!dryRun) {
        await this.createTenantVersionSync(
          client,
          tenantSchema,
          tenantVersion.id,
          bestMatch.globalVersionId,
          bestMatch.confidence,
          bestMatch.matchType
        )
        
        // Update global version with additional data
        await this.updateGlobalProductVersion(client, bestMatch.globalVersionId, tenantVersion)
      }
      
      this.logger.info(`Auto-matched version ${tenantVersion.id} to global version ${bestMatch.globalVersionId}`, {
        confidence: bestMatch.confidence,
        matchType: bestMatch.matchType
      })
      
      return { matched: true, conflict: false }
    } else {
      // Create conflict for manual review
      if (!dryRun) {
        await this.createSyncConflict(client, batchId, tenantVersion, matches)
      }
      
      this.logger.info(`Created conflict for version ${tenantVersion.id}`, {
        matchCount: matches.length,
        bestConfidence: bestMatch.confidence
      })
      
      return { matched: false, conflict: true }
    }
  }

  /**
   * Get tenant product versions that need synchronization
   */
  private async getTenantProductVersions(client: PoolClient, tenantSchema: string): Promise<TenantProductVersion[]> {
    const result = await client.query(`
      SELECT tpv.* 
      FROM ${tenantSchema}.tenant_product_versions tpv
      LEFT JOIN ${tenantSchema}.tenant_product_version_sync tpvs ON tpv.id = tpvs.tenant_version_id
      WHERE tpvs.tenant_version_id IS NULL
      ORDER BY tpv.created_on
    `)
    
    return result.rows
  }

  /**
   * Get global product versions for a specific product
   */
  private async getGlobalVersionsForProduct(client: PoolClient, globalProductId: string): Promise<GlobalProductVersion[]> {
    const result = await client.query(`
      SELECT * FROM metadata.global_product_versions
      WHERE global_product_id = $1
      ORDER BY confidence DESC, source_count DESC
    `, [globalProductId])
    
    return result.rows
  }

  /**
   * Get global product ID for a tenant product
   */
  private async getGlobalProductId(client: PoolClient, tenantSchema: string, tenantProductId: string): Promise<string | null> {
    const result = await client.query(`
      SELECT global_product_id FROM ${tenantSchema}.tenant_product_sync
      WHERE tenant_product_id = $1 AND sync_status = 'synced'
    `, [tenantProductId])
    
    return result.rows[0]?.global_product_id || null
  }

  /**
   * Check if version is already synchronized
   */
  private async getExistingSync(client: PoolClient, tenantSchema: string, tenantVersionId: string) {
    const result = await client.query(`
      SELECT * FROM ${tenantSchema}.tenant_product_version_sync
      WHERE tenant_version_id = $1
    `, [tenantVersionId])
    
    return result.rows[0] || null
  }

  /**
   * Create new global product version
   */
  private async createGlobalProductVersion(client: PoolClient, tenantVersion: TenantProductVersion, globalProductId: string): Promise<string> {
    const result = await client.query(`
      INSERT INTO metadata.global_product_versions (
        global_product_id, version, release_date, end_of_life_date, support_level,
        description, download_url, confidence, source_count, last_updated
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, 100, 1, NOW())
      RETURNING id
    `, [
      globalProductId,
      tenantVersion.version,
      tenantVersion.releaseDate,
      tenantVersion.endOfLifeDate,
      tenantVersion.supportLevel,
      tenantVersion.description,
      tenantVersion.downloadUrl
    ])
    
    return result.rows[0].id
  }

  /**
   * Create tenant product version sync record
   */
  private async createTenantVersionSync(
    client: PoolClient,
    tenantSchema: string,
    tenantVersionId: string,
    globalVersionId: string,
    confidence: number,
    matchType: string
  ): Promise<void> {
    await client.query(`
      INSERT INTO ${tenantSchema}.tenant_product_version_sync (
        tenant_version_id, global_version_id, confidence, match_type,
        sync_status, created_on, changed_on
      ) VALUES ($1, $2, $3, $4, 'synced', NOW(), NOW())
    `, [tenantVersionId, globalVersionId, confidence, matchType])
  }

  /**
   * Update global product version with additional data
   */
  private async updateGlobalProductVersion(client: PoolClient, globalVersionId: string, tenantVersion: TenantProductVersion): Promise<void> {
    await client.query(`
      UPDATE metadata.global_product_versions 
      SET source_count = source_count + 1,
          last_updated = NOW(),
          -- Update fields if they're empty in global but present in tenant
          release_date = COALESCE(release_date, $2),
          end_of_life_date = COALESCE(end_of_life_date, $3),
          support_level = COALESCE(support_level, $4),
          description = COALESCE(description, $5),
          download_url = COALESCE(download_url, $6)
      WHERE id = $1
    `, [
      globalVersionId,
      tenantVersion.releaseDate,
      tenantVersion.endOfLifeDate,
      tenantVersion.supportLevel,
      tenantVersion.description,
      tenantVersion.downloadUrl
    ])
  }

  /**
   * Create sync conflict for manual review
   */
  private async createSyncConflict(
    client: PoolClient,
    batchId: string,
    tenantVersion: TenantProductVersion,
    matches: ProductVersionMatch[]
  ): Promise<void> {
    await client.query(`
      INSERT INTO metadata.sync_conflicts (
        batch_id, entity_type, entity_id, conflict_data, status, created_on
      ) VALUES ($1, 'product_version', $2, $3, 'pending', NOW())
    `, [
      batchId,
      tenantVersion.id,
      JSON.stringify({
        tenantVersion,
        potentialMatches: matches
      })
    ])
  }

  /**
   * Update batch progress
   */
  private async updateBatchProgress(
    client: PoolClient,
    batchId: string,
    processed: number,
    matched: number,
    conflicts: number
  ): Promise<void> {
    await client.query(`
      UPDATE metadata.sync_batches 
      SET processed_records = $2,
          matched_records = $3,
          conflict_records = $4,
          changed_on = NOW()
      WHERE id = $1
    `, [batchId, processed, matched, conflicts])
  }
}

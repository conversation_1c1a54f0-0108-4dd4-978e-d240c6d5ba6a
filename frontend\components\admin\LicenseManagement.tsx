/**
 * License Management Component
 * 
 * Super-admin interface for license key generation and management
 */

'use client'

import React, { useState } from 'react'
import { useData } from '@/lib/hooks'
import { Button } from '@/components/ui/Button'
import { Form } from '@/components/ui/Form'
import { Modal } from '@/components/ui/Modal'
import { LoadingSpinner } from '@/components/common/LoadingStates'

interface LicenseKey {
  id: number
  licenseKey: string
  licenseType: string
  maxUsers: number
  maxTenants: number
  features: string[]
  validFrom: string
  validUntil: string | null
  isActive: boolean
  usageCount: number
  maxUsage: number
  generatedBy: string
  generatedAt: string
  notes: string | null
  activatedClients: number
}

interface LicenseFormData {
  licenseType: 'client' | 'trial' | 'enterprise'
  maxUsers: number
  maxTenants: number
  validityDays: number | null
  features: string[]
  notes: string
}

const LICENSE_FEATURES = [
  { code: 'renewals', name: 'Renewals Management', premium: false },
  { code: 'vendors', name: 'Vendor Management', premium: false },
  { code: 'reports', name: 'Basic Reports', premium: false },
  { code: 'notifications', name: 'Email Notifications', premium: false },
  { code: 'advanced_reports', name: 'Advanced Reports', premium: true },
  { code: 'api_access', name: 'API Access', premium: true },
  { code: 'bulk_import', name: 'Bulk Import', premium: true },
  { code: 'custom_fields', name: 'Custom Fields', premium: true },
  { code: 'audit_logs', name: 'Audit Logs', premium: true },
  { code: 'sso_integration', name: 'SSO Integration', premium: true }
]

export default function LicenseManagement() {
  const [showGenerateModal, setShowGenerateModal] = useState(false)
  const [filter, setFilter] = useState<'all' | 'active' | 'expired'>('all')
  const [page, setPage] = useState(1)

  // Fetch license keys
  const {
    data: licenseData,
    loading: isLoading,
    error,
    refetch
  } = useData<{
    licenses: LicenseKey[]
    pagination: any
  }>({
    endpoint: `/api/admin/licenses?page=${page}&status=${filter}`,
    cache: {
      key: `admin-licenses-${page}-${filter}`,
      ttl: 30000 // 30 seconds
    }
  })

  const handleGenerateLicense = async (formData: LicenseFormData) => {
    try {
      const response = await fetch('/api/admin/licenses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      })

      if (response.ok) {
        setShowGenerateModal(false)
        refetch() // Refresh the license list
      } else {
        const error = await response.json()
        console.error('License generation failed:', error)
      }
    } catch (error) {
      console.error('License generation error:', error)
    }
  }

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Never'
    return new Date(dateString).toLocaleDateString()
  }

  const getStatusBadge = (license: LicenseKey) => {
    if (!license.isActive) {
      return <span className="px-2 py-1 text-xs bg-gray-100 text-gray-800 rounded">Inactive</span>
    }
    
    if (license.validUntil && new Date(license.validUntil) < new Date()) {
      return <span className="px-2 py-1 text-xs bg-red-100 text-red-800 rounded">Expired</span>
    }
    
    return <span className="px-2 py-1 text-xs bg-green-100 text-green-800 rounded">Active</span>
  }

  if (isLoading) {
    return <LoadingSpinner />
  }

  if (error) {
    return (
      <div className="p-4 bg-red-50 border border-red-200 rounded-md">
        <p className="text-red-800">Error loading licenses: {String(error)}</p>
        <Button onClick={refetch} variant="outline" className="mt-2">
          Retry
        </Button>
      </div>
    )
  }

  const licenses = licenseData?.licenses || []
  const pagination = licenseData?.pagination

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">License Management</h1>
          <p className="text-gray-600">Generate and manage license keys for clients</p>
        </div>
        <Button onClick={() => setShowGenerateModal(true)}>
          Generate License Key
        </Button>
      </div>

      {/* Filters */}
      <div className="flex space-x-4">
        <select
          value={filter}
          onChange={(e) => setFilter(e.target.value as any)}
          className="px-3 py-2 border border-gray-300 rounded-md"
        >
          <option value="all">All Licenses</option>
          <option value="active">Active Only</option>
          <option value="expired">Expired Only</option>
        </select>
      </div>

      {/* License Keys Table */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                License Key
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Type
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Limits
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Usage
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Expires
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Generated
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {licenses.map((license) => (
              <tr key={license.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-mono text-gray-900">
                    {license.licenseKey}
                  </div>
                  {license.notes && (
                    <div className="text-xs text-gray-500 mt-1">
                      {license.notes}
                    </div>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded capitalize">
                    {license.licenseType}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <div>{license.maxUsers} users</div>
                  <div>{license.maxTenants} tenants</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  {getStatusBadge(license)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <div>{license.usageCount} / {license.maxUsage} uses</div>
                  <div className="text-xs text-gray-500">
                    {license.activatedClients} active clients
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {formatDate(license.validUntil)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <div>{formatDate(license.generatedAt)}</div>
                  <div className="text-xs text-gray-500">
                    by {license.generatedBy}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>

        {licenses.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500">No license keys found</p>
          </div>
        )}
      </div>

      {/* Pagination */}
      {pagination && pagination.totalPages > 1 && (
        <div className="flex justify-between items-center">
          <div className="text-sm text-gray-700">
            Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
            {Math.min(pagination.page * pagination.limit, pagination.totalCount)} of{' '}
            {pagination.totalCount} results
          </div>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              disabled={!pagination.hasPrev}
              onClick={() => setPage(page - 1)}
            >
              Previous
            </Button>
            <Button
              variant="outline"
              disabled={!pagination.hasNext}
              onClick={() => setPage(page + 1)}
            >
              Next
            </Button>
          </div>
        </div>
      )}

      {/* Generate License Modal */}
      <GenerateLicenseModal
        isOpen={showGenerateModal}
        onClose={() => setShowGenerateModal(false)}
        onSubmit={handleGenerateLicense}
      />
    </div>
  )
}

// Generate License Modal Component
function GenerateLicenseModal({
  isOpen,
  onClose,
  onSubmit
}: {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: LicenseFormData) => void
}) {
  const [formData, setFormData] = useState<LicenseFormData>({
    licenseType: 'client',
    maxUsers: 10,
    maxTenants: 1,
    validityDays: 365,
    features: ['renewals', 'vendors', 'reports', 'notifications'],
    notes: ''
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit(formData)
  }

  const handleFeatureToggle = (featureCode: string) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.includes(featureCode)
        ? prev.features.filter(f => f !== featureCode)
        : [...prev.features, featureCode]
    }))
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Generate License Key">
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700">License Type</label>
          <select
            value={formData.licenseType}
            onChange={(e) => setFormData(prev => ({ ...prev, licenseType: e.target.value as any }))}
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
          >
            <option value="trial">Trial</option>
            <option value="client">Client</option>
            <option value="enterprise">Enterprise</option>
          </select>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">Max Users</label>
            <input
              type="number"
              value={formData.maxUsers}
              onChange={(e) => setFormData(prev => ({ ...prev, maxUsers: parseInt(e.target.value) }))}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
              min="1"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700">Max Tenants</label>
            <input
              type="number"
              value={formData.maxTenants}
              onChange={(e) => setFormData(prev => ({ ...prev, maxTenants: parseInt(e.target.value) }))}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
              min="1"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">Validity (Days)</label>
          <input
            type="number"
            value={formData.validityDays || ''}
            onChange={(e) => setFormData(prev => ({ 
              ...prev, 
              validityDays: e.target.value ? parseInt(e.target.value) : null 
            }))}
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
            placeholder="Leave empty for no expiration"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Features</label>
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {LICENSE_FEATURES.map((feature) => (
              <label key={feature.code} className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.features.includes(feature.code)}
                  onChange={() => handleFeatureToggle(feature.code)}
                  className="mr-2"
                />
                <span className={feature.premium ? 'text-blue-600 font-medium' : ''}>
                  {feature.name}
                  {feature.premium && <span className="text-xs text-blue-500 ml-1">(Premium)</span>}
                </span>
              </label>
            ))}
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700">Notes</label>
          <textarea
            value={formData.notes}
            onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
            className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
            rows={3}
            placeholder="Optional notes about this license"
          />
        </div>

        <div className="flex justify-end space-x-3 pt-4">
          <Button type="button" variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button type="submit">
            Generate License
          </Button>
        </div>
      </form>
    </Modal>
  )
}

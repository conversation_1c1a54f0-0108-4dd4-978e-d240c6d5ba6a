/**
 * Input Sanitization and Validation Utilities
 * 
 * Provides comprehensive input sanitization to prevent XSS, SQL injection, and other attacks
 */

import DOMPurify from 'isomorphic-dompurify';
import { z } from 'zod';

/**
 * HTML sanitization options
 */
const sanitizeOptions = {
  ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'p', 'br'],
  ALLOWED_ATTR: [],
  KEEP_CONTENT: true,
  RETURN_DOM: false,
  RETURN_DOM_FRAGMENT: false,
  RETURN_DOM_IMPORT: false,
};

/**
 * Sanitize HTML content to prevent XSS
 */
export function sanitizeHtml(input: string): string {
  if (typeof input !== 'string') return '';
  return DOMPurify.sanitize(input, sanitizeOptions);
}

/**
 * Sanitize plain text input
 */
export function sanitizeText(input: string): string {
  if (typeof input !== 'string') return '';
  
  // Remove any HTML tags
  const withoutHtml = input.replace(/<[^>]*>/g, '');
  
  // Normalize whitespace
  const normalized = withoutHtml.replace(/\s+/g, ' ').trim();
  
  // Remove potentially dangerous characters
  return normalized.replace(/[<>'"&]/g, '');
}

/**
 * Sanitize SQL identifier (table/column names)
 */
export function sanitizeSqlIdentifier(input: string): string {
  if (typeof input !== 'string') throw new Error('SQL identifier must be a string');
  
  // Only allow alphanumeric characters, underscores, and dots
  if (!/^[a-zA-Z_][a-zA-Z0-9_]*(\.[a-zA-Z_][a-zA-Z0-9_]*)?$/.test(input)) {
    throw new Error(`Invalid SQL identifier: ${input}`);
  }
  
  return input;
}

/**
 * Sanitize email address
 */
export function sanitizeEmail(input: string): string {
  if (typeof input !== 'string') return '';
  
  const email = input.toLowerCase().trim();
  
  // Basic email validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    throw new Error('Invalid email format');
  }
  
  return email;
}

/**
 * Sanitize URL
 */
export function sanitizeUrl(input: string): string {
  if (typeof input !== 'string') return '';
  
  try {
    const url = new URL(input);
    
    // Only allow http and https protocols
    if (!['http:', 'https:'].includes(url.protocol)) {
      throw new Error('Invalid URL protocol');
    }
    
    return url.toString();
  } catch {
    throw new Error('Invalid URL format');
  }
}

/**
 * Sanitize file path to prevent directory traversal
 */
export function sanitizeFilePath(input: string): string {
  if (typeof input !== 'string') return '';
  
  // Remove any path traversal attempts
  const sanitized = input
    .replace(/\.\./g, '') // Remove ..
    .replace(/[\/\\]/g, '_') // Replace slashes with underscores
    .replace(/[<>:"|?*]/g, '') // Remove invalid filename characters
    .trim();
  
  if (sanitized.length === 0) {
    throw new Error('Invalid file path');
  }
  
  return sanitized;
}

/**
 * Sanitize JSON input
 */
export function sanitizeJson(input: any): any {
  if (typeof input === 'string') {
    try {
      const parsed = JSON.parse(input);
      return sanitizeJson(parsed);
    } catch {
      throw new Error('Invalid JSON format');
    }
  }
  
  if (Array.isArray(input)) {
    return input.map(item => sanitizeJson(item));
  }
  
  if (input && typeof input === 'object') {
    const sanitized: any = {};
    for (const [key, value] of Object.entries(input)) {
      const sanitizedKey = sanitizeText(key);
      sanitized[sanitizedKey] = sanitizeJson(value);
    }
    return sanitized;
  }
  
  if (typeof input === 'string') {
    return sanitizeText(input);
  }
  
  return input;
}

/**
 * Comprehensive input sanitization for API requests
 */
export function sanitizeApiInput(input: any): any {
  if (typeof input === 'string') {
    return sanitizeText(input);
  }
  
  if (Array.isArray(input)) {
    return input.map(item => sanitizeApiInput(item));
  }
  
  if (input && typeof input === 'object') {
    const sanitized: any = {};
    for (const [key, value] of Object.entries(input)) {
      // Sanitize the key
      const sanitizedKey = sanitizeText(key);
      
      // Recursively sanitize the value
      sanitized[sanitizedKey] = sanitizeApiInput(value);
    }
    return sanitized;
  }
  
  return input;
}

/**
 * Validation schemas for common input types
 */
export const validationSchemas = {
  email: z.string().email().transform(sanitizeEmail),
  url: z.string().url().transform(sanitizeUrl),
  text: z.string().transform(sanitizeText),
  html: z.string().transform(sanitizeHtml),
  sqlIdentifier: z.string().transform(sanitizeSqlIdentifier),
  filePath: z.string().transform(sanitizeFilePath),
  
  // Common patterns
  uuid: z.string().uuid(),
  positiveInt: z.number().int().positive(),
  nonNegativeInt: z.number().int().min(0),
  
  // Length-limited strings
  shortText: z.string().max(255).transform(sanitizeText),
  mediumText: z.string().max(1000).transform(sanitizeText),
  longText: z.string().max(5000).transform(sanitizeText),
};

/**
 * Rate limiting for input validation
 */
const validationAttempts = new Map<string, { count: number; resetTime: number }>();

export function checkValidationRateLimit(identifier: string, maxAttempts = 10, windowMs = 60000): boolean {
  const now = Date.now();
  const key = `validation:${identifier}`;
  const current = validationAttempts.get(key);
  
  if (!current || now > current.resetTime) {
    validationAttempts.set(key, { count: 1, resetTime: now + windowMs });
    return true;
  }
  
  if (current.count >= maxAttempts) {
    return false;
  }
  
  current.count++;
  return true;
}

/**
 * Sanitize and validate request body
 */
export function sanitizeRequestBody<T>(
  body: any,
  schema: z.ZodSchema<T>,
  identifier?: string
): { success: true; data: T } | { success: false; error: string } {
  try {
    // Rate limiting
    if (identifier && !checkValidationRateLimit(identifier)) {
      return { success: false, error: 'Too many validation attempts' };
    }
    
    // Sanitize input first
    const sanitized = sanitizeApiInput(body);
    
    // Then validate with schema
    const validated = schema.parse(sanitized);
    
    return { success: true, data: validated };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { 
        success: false, 
        error: `Validation error: ${error.errors.map(e => e.message).join(', ')}` 
      };
    }
    
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown validation error' 
    };
  }
}

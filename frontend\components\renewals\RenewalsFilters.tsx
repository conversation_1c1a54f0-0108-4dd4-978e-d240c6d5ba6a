/**
 * Renewals Filters Component
 * 
 * Filter controls for the renewals table
 */

'use client'

import React, { useMemo } from 'react'
import { BaseComponentProps } from '@/lib/types'
import { Form } from '@/components/ui/Form'
import { Renewal } from '@/lib/types'
import { RenewalsFilters, FilterOptions } from '@/lib/hooks/useRenewals'
import { Button } from '@/components/ui/Button'

interface RenewalsFiltersProps extends BaseComponentProps {
  filters: RenewalsFilters
  onFilterChange: (filterType: keyof RenewalsFilters, value: string) => void
  renewals: Renewal[]
  filterOptions?: FilterOptions
}

const RenewalsFiltersComponent = React.memo(function RenewalsFilters({
  filters,
  onFilterChange,
  renewals,
  filterOptions: propFilterOptions,
  className = '',
  'data-testid': testId
}: RenewalsFiltersProps) {

  // Use provided filter options or extract from renewals as fallback
  const filterOptions = useMemo(() => {
    if (propFilterOptions && propFilterOptions.vendors.length > 0) {
      return propFilterOptions
    }

    // Fallback to extracting from renewals data
    const vendors = Array.from(new Set(renewals.map(r => r.vendor))).filter(Boolean).sort()
    const types = Array.from(new Set(renewals.map(r => r.type))).filter(Boolean).sort()
    const statuses = Array.from(new Set(renewals.map(r => r.status))).filter(Boolean).sort()

    return { vendors, types, statuses }
  }, [renewals, propFilterOptions])

  const handleVendorChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    onFilterChange('vendor', e.target.value)
  }

  const handleTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    onFilterChange('type', e.target.value)
  }

  const handleStatusChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    onFilterChange('status', e.target.value)
  }

  const clearFilters = () => {
    onFilterChange('vendor', '')
    onFilterChange('type', '')
    onFilterChange('status', '')
  }

  const hasActiveFilters = filters.vendor || filters.type || filters.status

  return (
    <div 
      className={`renewals-filters ${className}`}
      data-testid={testId}
    >
      <div className="filters-row">
        <Form.Field>
          <Form.Label htmlFor="vendor-filter">
            All Vendors
          </Form.Label>
          <Form.Select
            id="vendor-filter"
            value={filters.vendor}
            onChange={handleVendorChange}
            placeholder="All Vendors"
          >
            {filterOptions.vendors.map(vendor => (
              <option key={vendor} value={vendor}>
                {vendor}
              </option>
            ))}
          </Form.Select>
        </Form.Field>

        <Form.Field>
          <Form.Label htmlFor="type-filter">
            Renewal Type
          </Form.Label>
          <Form.Select
            id="type-filter"
            value={filters.type}
            onChange={handleTypeChange}
            placeholder="All Renewal Types"
          >
            {filterOptions.types.map(type => (
              <option key={type} value={type}>
                {type}
              </option>
            ))}
          </Form.Select>
        </Form.Field>

        <Form.Field>
          <Form.Label htmlFor="status-filter">
            Status
          </Form.Label>
          <Form.Select
            id="status-filter"
            value={filters.status}
            onChange={handleStatusChange}
            placeholder="All Statuses"
          >
            {filterOptions.statuses.map(status => (
              <option key={status} value={status}>
                {status}
              </option>
            ))}
          </Form.Select>
        </Form.Field>

        {hasActiveFilters && (
          <Button
            variant="secondary"
            size="sm"
            onClick={clearFilters}
            aria-label="Clear all filters"
          >
            Clear Filters
          </Button>
        )}
      </div>
    </div>
  )
})

export default RenewalsFiltersComponent

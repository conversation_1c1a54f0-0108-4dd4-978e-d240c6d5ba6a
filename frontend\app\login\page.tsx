'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { getAuthenticatedUser } from '@/lib/auth-utils'

export default function LoginPage() {
  const router = useRouter()
  const [status, setStatus] = useState('Checking authentication...')
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const checkAuthAndRedirect = async () => {
      try {
        console.log('🔄 [LOGIN] Checking authentication...')
        setStatus('Checking authentication...')

        const user = await getAuthenticatedUser()

        if (user) {
          // User is already authenticated, redirect to overview
          console.log('✅ [LOGIN] User already authenticated, redirecting to overview')
          setStatus('Already authenticated, redirecting...')
          router.push('/overview')
        } else {
          // User is not authenticated, initiate Amplify OAuth sign in
          console.log('🔄 [LOGIN] User not authenticated, initiating <PERSON>A<PERSON> sign in...')
          setStatus('Redirecting to secure login page...')

          // Ensure Amplify is configured
          const { ensureAmplifyConfigured } = await import('@/lib/services/amplify-service')
          await ensureAmplifyConfigured()

          // Use Amplify's signInWithRedirect for OAuth
          const { signInWithRedirect } = await import('aws-amplify/auth')

          await signInWithRedirect({
            provider: 'Cognito'
          })
        }
      } catch (error) {
        console.error('❌ [LOGIN] Auth check error:', error)
        setStatus('Redirecting to secure login page...')

        try {
          // Ensure Amplify is configured
          const { ensureAmplifyConfigured } = await import('@/lib/services/amplify-service')
          await ensureAmplifyConfigured()

          // Use Amplify's signInWithRedirect for OAuth
          const { signInWithRedirect } = await import('aws-amplify/auth')

          await signInWithRedirect({
            provider: 'Cognito'
          })
        } catch (signInError) {
          console.error('❌ [LOGIN] Sign in error:', signInError)
          setStatus('Authentication service unavailable. Please try again.')
        }
      } finally {
        setIsLoading(false)
      }
    }

    checkAuthAndRedirect()
  }, [router])

  // Show loading state while checking authentication or redirecting
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <div className="animate-pulse">
          <div className="text-4xl mb-4">🔐</div>
        </div>
        <h2 className="text-lg font-medium text-gray-900 mb-2">
          {status.includes('authenticated') ? 'Already Authenticated' : 'Redirecting to Login'}
        </h2>
        <p className="text-sm text-gray-600">
          {status}
        </p>
      </div>
    </div>
  )
}

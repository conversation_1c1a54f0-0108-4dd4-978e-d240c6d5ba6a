'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { getAuthenticatedUser } from '@/lib/auth-utils'

export default function LoginPage() {
  const router = useRouter()
  const [status, setStatus] = useState('Checking authentication...')
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const checkAuthAndRedirect = async () => {
      try {
        console.log('🔄 [LOGIN] Checking authentication...')
        setStatus('Checking authentication...')

        const user = await getAuthenticatedUser()

        if (user) {
          // User is already authenticated, redirect to overview
          console.log('✅ [LOGIN] User already authenticated, redirecting to overview')
          setStatus('Already authenticated, redirecting...')
          router.push('/overview')
        } else {
          // User is not authenticated, redirect to Cognito
          console.log('🔄 [LOGIN] User not authenticated, redirecting to Cognito...')
          setStatus('Redirecting to secure login page...')

          const cognitoDomain = process.env.NEXT_PUBLIC_AWS_COGNITO_DOMAIN
          const clientId = process.env.NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID
          const redirectUri = process.env.NEXT_PUBLIC_REDIRECT_SIGN_IN

          const loginUrl = `https://${cognitoDomain}/login?client_id=${clientId}&response_type=code&scope=email+openid+profile&redirect_uri=${encodeURIComponent(redirectUri || '')}`

          setTimeout(() => {
            window.location.href = loginUrl
          }, 500)
        }
      } catch (error) {
        console.error('❌ [LOGIN] Auth check error:', error)
        setStatus('Redirecting to secure login page...')

        const cognitoDomain = process.env.NEXT_PUBLIC_AWS_COGNITO_DOMAIN
        const clientId = process.env.NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID
        const redirectUri = process.env.NEXT_PUBLIC_REDIRECT_SIGN_IN

        const loginUrl = `https://${cognitoDomain}/login?client_id=${clientId}&response_type=code&scope=email+openid+profile&redirect_uri=${encodeURIComponent(redirectUri || '')}`

        setTimeout(() => {
          window.location.href = loginUrl
        }, 500)
      } finally {
        setIsLoading(false)
      }
    }

    checkAuthAndRedirect()
  }, [router])

  // Show loading state while checking authentication or redirecting
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <div className="animate-pulse">
          <div className="text-4xl mb-4">🔐</div>
        </div>
        <h2 className="text-lg font-medium text-gray-900 mb-2">
          {status.includes('authenticated') ? 'Already Authenticated' : 'Redirecting to Login'}
        </h2>
        <p className="text-sm text-gray-600">
          {status}
        </p>
      </div>
    </div>
  )
}

/**
 * License Management API
 * 
 * Endpoints for tracking and managing license usage
 */

import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { databaseService } from '@/lib/services/database-service'
import { requireAdmin } from '@/lib/api/auth-middleware'
import { createSuccessResponse, createErrorResponse, ApiErrorCode, HttpStatus } from '@/lib/api/response'
import { getTenantContext } from '@/lib/tenant/context'
import { STATUS } from '@/lib/constants/app-constants'

// Validation schemas
const updateUsageSchema = z.object({
  count_change: z.number().int(),
  notes: z.string().optional()
})

/**
 * GET /api/license-management
 * Get license status and usage for current tenant
 */
export async function GET(request: NextRequest) {
  try {
    // Require authentication
    const authResult = await requireAdmin(request)
    if (!authResult.success) {
      return authResult.response
    }

    // Get tenant context
    const tenantContext = await getTenantContext(authResult.session)
    if (!tenantContext || !tenantContext.clientId) {
      return createErrorResponse(
        'Unable to determine tenant context',
        ApiErrorCode.TENANT_NOT_FOUND,
        HttpStatus.BAD_REQUEST
      )
    }

    // Use the database function to get license status
    const query = `
      SELECT metadata.get_client_license_status($1) as license_status
    `

    const result = await databaseService.query(query, [tenantContext.clientId])
    const licenseStatus = result.rows[0].license_status

    // Get current renewal count from tenant schema
    const renewalCountQuery = `
      SELECT COUNT(*) as current_renewal_count
      FROM ${tenantContext.tenantSchema}.tenant_renewals
      WHERE status = '${STATUS.ACTIVE}'
    `

    const renewalCountResult = await databaseService.query(renewalCountQuery)
    const currentRenewalCount = parseInt(renewalCountResult.rows[0].current_renewal_count)

    // Calculate license compliance
    let licenseCompliance = {
      is_compliant: true,
      total_allowed: 0,
      total_used: currentRenewalCount,
      warnings: [] as string[]
    }

    if (licenseStatus && licenseStatus.length > 0) {
      const activeLicense = licenseStatus.find((l: any) => l.status === 'ACTIVE' && !l.is_expired)
      
      if (activeLicense) {
        licenseCompliance.total_allowed = activeLicense.max_renewals
        licenseCompliance.is_compliant = currentRenewalCount <= activeLicense.max_renewals
        
        // Add warnings
        const usagePercentage = (currentRenewalCount / activeLicense.max_renewals) * 100
        
        if (usagePercentage >= 90) {
          licenseCompliance.warnings.push('License usage is at 90% or higher')
        } else if (usagePercentage >= 75) {
          licenseCompliance.warnings.push('License usage is at 75% or higher')
        }
        
        if (activeLicense.is_expired) {
          licenseCompliance.warnings.push('License has expired')
          licenseCompliance.is_compliant = false
        }
      } else {
        licenseCompliance.warnings.push('No active license found')
        licenseCompliance.is_compliant = false
      }
    } else {
      licenseCompliance.warnings.push('No license activated')
      licenseCompliance.is_compliant = false
    }

    return createSuccessResponse({
      licenses: licenseStatus || [],
      current_renewal_count: currentRenewalCount,
      license_compliance: licenseCompliance,
      tenant_info: {
        client_id: tenantContext.clientId,
        name: tenantContext.clientName,
        tenant_schema: tenantContext.tenantSchema
      }
    })

  } catch (error) {
    console.error('Error fetching license management data:', error)
    return createErrorResponse(
      'Failed to fetch license management data',
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    )
  }
}

/**
 * POST /api/license-management/update-usage
 * Update license usage count (called when renewals are added/removed)
 */
export async function POST(request: NextRequest) {
  try {
    // Require authentication
    const authResult = await requireAdmin(request)
    if (!authResult.success) {
      return authResult.response
    }

    // Get tenant context
    const tenantContext = await getTenantContext(authResult.session)
    if (!tenantContext || !tenantContext.clientId) {
      return createErrorResponse(
        'Unable to determine tenant context',
        ApiErrorCode.TENANT_NOT_FOUND,
        HttpStatus.BAD_REQUEST
      )
    }

    const body = await request.json()
    const validatedData = updateUsageSchema.parse(body)

    // Use the database function to update license usage
    const query = `
      SELECT metadata.update_license_renewal_count($1, $2, $3) as result
    `

    const result = await databaseService.query(query, [
      tenantContext.clientId,
      validatedData.count_change,
      authResult.session.email
    ])

    const updateResult = result.rows[0].result

    if (!updateResult.success) {
      return createErrorResponse(
        updateResult.error,
        ApiErrorCode.VALIDATION_ERROR,
        HttpStatus.BAD_REQUEST
      )
    }

    return createSuccessResponse({
      message: 'License usage updated successfully',
      usage_update: updateResult
    })

  } catch (error) {
    console.error('Error updating license usage:', error)
    
    if (error instanceof z.ZodError) {
      return createErrorResponse(
        'Invalid request data',
        ApiErrorCode.VALIDATION_ERROR,
        HttpStatus.BAD_REQUEST
      )
    }

    return createErrorResponse(
      'Failed to update license usage',
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    )
  }
}




/**
 * Metadata Service
 *
 * Handles API calls for global metadata tables and tenant-specific data
 * Provides dropdown options for form fields with proper ID/name mapping
 * Includes intelligent caching for better performance
 */

import { METADATA_ENDPOINTS, TENANT_ENDPOINTS } from '@/lib/constants/api-endpoints'
import { apiCache } from '@/lib/utils/cache'
import { TIME } from '@/lib/constants/app-constants'

export interface PurchaseType {
  id: number
  name: string
  status: string
  display_order?: number
}

export interface RenewalType {
  id: number
  name: string
  status: string
  display_order?: number
}

export interface Currency {
  id: string
  code: string
  name: string
  symbol: string
  status: string
  display_order?: number
}

export interface Status {
  id: number
  name: string
  active_indicator: boolean
  display_order?: number
}

export interface Vendor {
  id: string
  name: string
  display_name?: string
}

export interface TenantUser {
  id: string
  name: string
  email: string
}

export interface Department {
  id: string
  name: string
}

export interface Reseller {
  id: string
  name: string
}

export interface MetadataOptions {
  purchaseTypes: PurchaseType[]
  renewalTypes: RenewalType[]
  currencies: Currency[]
  vendors: Vendor[]
  tenantUsers: TenantUser[]
  departments: Department[]
  resellers: Reseller[]
}

/**
 * Fetch all metadata options for form dropdowns
 * Uses global metadata tables and tenant-specific data with caching
 *
 * @returns Promise with all metadata options
 */
export async function getMetadataOptions(): Promise<MetadataOptions> {
  try {
    // Check cache first
    const cacheKey = 'metadata-options'
    const cached = apiCache.get(cacheKey)
    if (cached) {
      console.log('🔍 Using cached metadata options:', cached)
      return cached
    }

    console.log('Fetching metadata options from API...')

    // Fetch all metadata in parallel from correct endpoints
    const [renewalTypesResponse, purchaseTypesResponse, currenciesResponse, statusesResponse, vendorsResponse, tenantUsersResponse, departmentsResponse, resellersResponse] = await Promise.all([
      fetch(METADATA_ENDPOINTS.RENEWAL_TYPES, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
      }),
      fetch(METADATA_ENDPOINTS.PURCHASE_TYPES, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
      }),
      fetch(METADATA_ENDPOINTS.CURRENCIES, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
      }),
      fetch(METADATA_ENDPOINTS.STATUSES, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
      }),
      fetch(TENANT_ENDPOINTS.VENDORS, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
      }),
      fetch(TENANT_ENDPOINTS.USERS, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
      }),
      fetch(TENANT_ENDPOINTS.DEPARTMENTS, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
      }),
      fetch(TENANT_ENDPOINTS.RESELLERS, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
      })
    ])

    // Check for authentication errors
    const responses = [renewalTypesResponse, purchaseTypesResponse, currenciesResponse, statusesResponse, vendorsResponse, tenantUsersResponse, departmentsResponse, resellersResponse]
    for (const response of responses) {
      if (response.status === 401) {
        console.log('Authentication failed, redirecting to login...')
        window.location.href = '/login'
        throw new Error('Authentication failed. Please sign in again.')
      }
    }

    // Parse all responses
    const [renewalTypesData, purchaseTypesData, currenciesData, statusesData, vendorsData, tenantUsersData, departmentsData, resellersData] = await Promise.all([
      renewalTypesResponse.json(),
      purchaseTypesResponse.json(),
      currenciesResponse.json(),
      statusesResponse.json(),
      vendorsResponse.json(),
      tenantUsersResponse.json(),
      departmentsResponse.json(),
      resellersResponse.json()
    ])

    // Extract data arrays from API responses
    const renewalTypes: RenewalType[] = renewalTypesData.success ? renewalTypesData.data : []
    const purchaseTypes: PurchaseType[] = purchaseTypesData.success ? purchaseTypesData.data : []
    const currencies: Currency[] = currenciesData.success ? currenciesData.data : []
    const statuses: Status[] = Array.isArray(statusesData) ? statusesData : []
    const vendors: Vendor[] = vendorsData.success ? vendorsData.data : []
    const tenantUsers: TenantUser[] = tenantUsersData.success ? tenantUsersData.data : []
    const departments: Department[] = departmentsData.success ? departmentsData.data : []
    const resellers: Reseller[] = resellersData.success ? resellersData.data : []

    // Log any failed requests
    if (!renewalTypesData.success) console.warn('Failed to fetch renewal types:', renewalTypesData.error)
    if (!purchaseTypesData.success) console.warn('Failed to fetch purchase types:', purchaseTypesData.error)
    if (!currenciesData.success) console.warn('Failed to fetch currencies:', currenciesData.error)
    if (!vendorsData.success) console.warn('Failed to fetch vendors:', vendorsData.error)
    if (!tenantUsersData.success) console.warn('Failed to fetch tenant users:', tenantUsersData.error)
    if (!departmentsData.success) console.warn('Failed to fetch departments:', departmentsData.error)
    if (!resellersData.success) console.warn('Failed to fetch resellers:', resellersData.error)

    console.log('Metadata options fetched successfully:', {
      renewalTypes: renewalTypes.length,
      purchaseTypes: purchaseTypes.length,
      currencies: currencies.length,
      statuses: statuses.length,
      vendors: vendors.length,
      tenantUsers: tenantUsers.length,
      departments: departments.length,
      resellers: resellers.length
    })

    const metadataOptions = {
      renewalTypes,
      purchaseTypes,
      currencies,
      statuses,
      vendors,
      tenantUsers,
      departments,
      resellers
    }

    // Cache the result for 10 minutes
    apiCache.set(cacheKey, metadataOptions, 10 * TIME.MINUTE, ['metadata'])

    return metadataOptions

  } catch (error) {
    console.error('Error fetching metadata options:', error)
    throw new Error(error instanceof Error ? error.message : 'Failed to fetch metadata options')
  }
}

/**
 * Format currency for display with symbol and amount
 * @param amount - The currency amount
 * @param currencyCode - The currency code (e.g., 'CAD', 'EUR')
 * @param currencies - Array of available currencies
 * @returns Formatted currency string
 */
export function formatCurrencyDisplay(amount: number, currencyCode: string, currencies: Currency[]): string {
  const currency = currencies.find(c => c.id === currencyCode || c.code === currencyCode)
  const symbol = currency?.symbol || currencyCode
  
  // Format number with commas and 2 decimal places
  const formattedAmount = new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount)
  
  return `${symbol}${formattedAmount}`
}

/**
 * Create a new vendor for the tenant
 * @param vendorData - The vendor data to create
 * @returns Promise with the created vendor
 */
export async function createVendor(vendorData: { name: string; display_name?: string }): Promise<Vendor> {
  try {
    console.log('Creating new vendor:', vendorData)

    const response = await fetch(TENANT_ENDPOINTS.VENDORS, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify(vendorData)
    })

    if (response.status === 401) {
      console.log('Authentication failed, redirecting to login...')
      window.location.href = '/login'
      throw new Error('Authentication required')
    }

    const result = await response.json()

    if (!result.success) {
      throw new Error(result.error || 'Failed to create vendor')
    }

    console.log('Vendor created successfully:', result.data)

    // Clear metadata cache to force refresh
    apiCache.clearByTags(['metadata'])

    return result.data

  } catch (error) {
    console.error('Error creating vendor:', error)
    throw new Error(error instanceof Error ? error.message : 'Failed to create vendor')
  }
}

/**
 * Fetch purchase types from global_purchase_types (only active records)
 *
 * @returns Promise with active purchase types
 */
export async function getPurchaseTypes(): Promise<PurchaseType[]> {
  try {
    const response = await fetch(METADATA_ENDPOINTS.PURCHASE_TYPES, {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' },
      credentials: 'include',
    })

    if (!response.ok) {
      throw new Error(`Failed to fetch purchase types: ${response.statusText}`)
    }

    const data = await response.json()
    if (!data.success) {
      throw new Error(data.error || 'Failed to fetch purchase types')
    }

    // Filter for active records and sort by display order
    return data.data.sort((a: PurchaseType, b: PurchaseType) =>
      (a.display_order || 999) - (b.display_order || 999)
    )
  } catch (error) {
    console.error('Error fetching purchase types:', error)
    throw error
  }
}

/**
 * Fetch renewal types from global_renewal_types (only active records)
 *
 * @returns Promise with active renewal types
 */
export async function getRenewalTypes(): Promise<RenewalType[]> {
  try {
    const response = await fetch(METADATA_ENDPOINTS.RENEWAL_TYPES, {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' },
      credentials: 'include',
    })

    if (!response.ok) {
      throw new Error(`Failed to fetch renewal types: ${response.statusText}`)
    }

    const data = await response.json()
    if (!data.success) {
      throw new Error(data.error || 'Failed to fetch renewal types')
    }

    // Filter for active records and sort by display order
    return data.data
      .filter((item: RenewalType) => item.status === 'A')
      .sort((a: RenewalType, b: RenewalType) => (a.display_order || 999) - (b.display_order || 999))
  } catch (error) {
    console.error('Error fetching renewal types:', error)
    throw error
  }
}

/**
 * Fetch currencies from global_currencies
 *
 * @returns Promise with currencies
 */
export async function getCurrencies(): Promise<Currency[]> {
  try {
    const response = await fetch(METADATA_ENDPOINTS.CURRENCIES, {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' },
      credentials: 'include',
    })

    if (!response.ok) {
      throw new Error(`Failed to fetch currencies: ${response.statusText}`)
    }

    const data = await response.json()
    if (!data.success) {
      throw new Error(data.error || 'Failed to fetch currencies')
    }

    return data.data.sort((a: Currency, b: Currency) =>
      (a.display_order || 999) - (b.display_order || 999)
    )
  } catch (error) {
    console.error('Error fetching currencies:', error)
    throw error
  }
}

/**
 * Database Schema Information
 *
 * The following global metadata tables are used:
 *
 * global_purchase_types:
 * - id (integer, Primary Key)
 * - name (text)
 * - status (character, 'A' = Active)
 * - display_order (integer, optional)
 *
 * global_renewal_types:
 * - id (integer, Primary Key)
 * - name (text)
 * - status (character, 'A' = Active)
 * - display_order (integer, optional)
 *
 * global_currencies:
 * - id (character(3), Primary Key)
 * - name (text)
 * - symbol (text)
 * - status (character, 'A' = Active)
 * - display_order (integer, optional)
 *
 * Tenant-specific tables:
 * - <tenant_schema>.tenant_vendors
 * - <tenant_schema>.tenant_users
 */

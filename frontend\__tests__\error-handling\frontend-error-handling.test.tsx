/**
 * Frontend Error Handling Tests
 * 
 * Tests to ensure that API errors are properly handled and displayed to users
 */

import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

// Mock the hooks and services
jest.mock('@/lib/hooks/useData', () => ({
  useData: jest.fn(),
}));

jest.mock('@/lib/hooks/useTenant', () => ({
  useTenant: jest.fn(),
}));

// Mock fetch globally
global.fetch = jest.fn();

const mockFetch = fetch as jest.MockedFunction<typeof fetch>;
const mockUseData = require('@/lib/hooks/useData').useData as jest.MockedFunction<any>;
const mockUseTenant = require('@/lib/hooks/useTenant').useTenant as jest.MockedFunction<any>;

// Mock components that we'll test
const MockCascadingSelect = ({ onError }: { onError?: (error: string) => void }) => {
  const [error, setError] = React.useState<string | null>(null);

  const handleLoadVendors = async () => {
    try {
      const response = await fetch('/api/tenant-vendors');
      if (!response.ok) {
        const errorData = await response.json();
        const errorMessage = errorData.error || 'Failed to load vendors';
        setError(errorMessage);
        onError?.(errorMessage);
        return;
      }
      setError(null);
    } catch (err) {
      const errorMessage = 'Network error occurred';
      setError(errorMessage);
      onError?.(errorMessage);
    }
  };

  React.useEffect(() => {
    handleLoadVendors();
  }, []);

  return (
    <div>
      <select data-testid="vendor-select">
        <option value="">Select vendor...</option>
      </select>
      {error && (
        <div data-testid="error-message" className="error">
          {error}
        </div>
      )}
      <button onClick={handleLoadVendors} data-testid="retry-button">
        Retry
      </button>
    </div>
  );
};

const MockRenewalForm = () => {
  const [errors, setErrors] = React.useState<{[key: string]: string}>({});
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setErrors({});

    try {
      const response = await fetch('/api/renewals', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: 'Test Renewal' }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        if (response.status === 400 && errorData.validationErrors) {
          setErrors(errorData.validationErrors);
        } else {
          setErrors({ general: errorData.error || 'Failed to create renewal' });
        }
        return;
      }

      // Success case
      setErrors({});
    } catch (err) {
      setErrors({ general: 'Network error occurred' });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit}>
      <input data-testid="renewal-name" placeholder="Renewal name" />
      {errors.name && (
        <div data-testid="name-error" className="error">
          {errors.name}
        </div>
      )}
      {errors.general && (
        <div data-testid="general-error" className="error">
          {errors.general}
        </div>
      )}
      <button type="submit" disabled={isSubmitting} data-testid="submit-button">
        {isSubmitting ? 'Creating...' : 'Create Renewal'}
      </button>
    </form>
  );
};

describe('Frontend Error Handling', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup default tenant mock
    mockUseTenant.mockReturnValue({
      tenant: { id: 'test-tenant', schema: 'tenant_test' },
      loading: false,
      error: null,
    });
  });

  describe('Authentication Errors', () => {
    it('should display authentication error when API returns 401', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 401,
        json: () => Promise.resolve({ error: 'Authentication required' }),
      } as Response);

      const onError = jest.fn();
      render(<MockCascadingSelect onError={onError} />);

      await waitFor(() => {
        expect(screen.getByTestId('error-message')).toHaveTextContent('Authentication required');
      });

      expect(onError).toHaveBeenCalledWith('Authentication required');
    });

    it('should display access denied error when API returns 403', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 403,
        json: () => Promise.resolve({ error: 'Access denied' }),
      } as Response);

      const onError = jest.fn();
      render(<MockCascadingSelect onError={onError} />);

      await waitFor(() => {
        expect(screen.getByTestId('error-message')).toHaveTextContent('Access denied');
      });
    });
  });

  describe('Validation Errors', () => {
    it('should display validation errors for form fields', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        json: () => Promise.resolve({
          error: 'Validation failed',
          validationErrors: {
            name: 'Name is required',
            date: 'Invalid date format',
          },
        }),
      } as Response);

      render(<MockRenewalForm />);

      const submitButton = screen.getByTestId('submit-button');
      await userEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.getByTestId('name-error')).toHaveTextContent('Name is required');
      });
    });

    it('should display general error when validation structure is not provided', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        json: () => Promise.resolve({ error: 'Bad request' }),
      } as Response);

      render(<MockRenewalForm />);

      const submitButton = screen.getByTestId('submit-button');
      await userEvent.click(submitButton);

      await waitFor(() => {
        expect(screen.getByTestId('general-error')).toHaveTextContent('Bad request');
      });
    });
  });

  describe('Server Errors', () => {
    it('should display server error when API returns 500', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        json: () => Promise.resolve({ error: 'Internal server error' }),
      } as Response);

      const onError = jest.fn();
      render(<MockCascadingSelect onError={onError} />);

      await waitFor(() => {
        expect(screen.getByTestId('error-message')).toHaveTextContent('Internal server error');
      });
    });

    it('should display database error when API returns database-related error', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
        json: () => Promise.resolve({ error: 'Database connection failed' }),
      } as Response);

      const onError = jest.fn();
      render(<MockCascadingSelect onError={onError} />);

      await waitFor(() => {
        expect(screen.getByTestId('error-message')).toHaveTextContent('Database connection failed');
      });
    });
  });

  describe('Network Errors', () => {
    it('should display network error when fetch fails', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      const onError = jest.fn();
      render(<MockCascadingSelect onError={onError} />);

      await waitFor(() => {
        expect(screen.getByTestId('error-message')).toHaveTextContent('Network error occurred');
      });

      expect(onError).toHaveBeenCalledWith('Network error occurred');
    });

    it('should allow retry after network error', async () => {
      // First call fails
      mockFetch.mockRejectedValueOnce(new Error('Network error'));
      
      const onError = jest.fn();
      render(<MockCascadingSelect onError={onError} />);

      await waitFor(() => {
        expect(screen.getByTestId('error-message')).toHaveTextContent('Network error occurred');
      });

      // Second call succeeds
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ data: [{ id: 1, name: 'Test Vendor' }] }),
      } as Response);

      const retryButton = screen.getByTestId('retry-button');
      await userEvent.click(retryButton);

      await waitFor(() => {
        expect(screen.queryByTestId('error-message')).not.toBeInTheDocument();
      });
    });
  });

  describe('useData Hook Error Handling', () => {
    it('should handle errors from useData hook', () => {
      mockUseData.mockReturnValue({
        data: null,
        loading: false,
        error: 'Failed to fetch data',
        refetch: jest.fn(),
      });

      const TestComponent = () => {
        const { data, loading, error, refetch } = mockUseData('/api/test');
        
        if (loading) return <div data-testid="loading">Loading...</div>;
        if (error) return (
          <div>
            <div data-testid="hook-error">{error}</div>
            <button onClick={refetch} data-testid="refetch-button">Retry</button>
          </div>
        );
        
        return <div data-testid="success">Success</div>;
      };

      render(<TestComponent />);

      expect(screen.getByTestId('hook-error')).toHaveTextContent('Failed to fetch data');
      expect(screen.getByTestId('refetch-button')).toBeInTheDocument();
    });

    it('should show loading state during data fetching', () => {
      mockUseData.mockReturnValue({
        data: null,
        loading: true,
        error: null,
        refetch: jest.fn(),
      });

      const TestComponent = () => {
        const { data, loading, error } = mockUseData('/api/test');
        
        if (loading) return <div data-testid="loading">Loading...</div>;
        if (error) return <div data-testid="error">{error}</div>;
        
        return <div data-testid="success">Success</div>;
      };

      render(<TestComponent />);

      expect(screen.getByTestId('loading')).toHaveTextContent('Loading...');
    });
  });

  describe('Tenant Context Errors', () => {
    it('should handle tenant loading errors', () => {
      mockUseTenant.mockReturnValue({
        tenant: null,
        loading: false,
        error: 'Failed to load tenant context',
      });

      const TestComponent = () => {
        const { tenant, loading, error } = mockUseTenant();
        
        if (loading) return <div data-testid="loading">Loading tenant...</div>;
        if (error) return <div data-testid="tenant-error">{error}</div>;
        if (!tenant) return <div data-testid="no-tenant">No tenant found</div>;
        
        return <div data-testid="tenant-loaded">Tenant: {tenant.id}</div>;
      };

      render(<TestComponent />);

      expect(screen.getByTestId('tenant-error')).toHaveTextContent('Failed to load tenant context');
    });

    it('should handle missing tenant gracefully', () => {
      mockUseTenant.mockReturnValue({
        tenant: null,
        loading: false,
        error: null,
      });

      const TestComponent = () => {
        const { tenant, loading, error } = mockUseTenant();
        
        if (loading) return <div data-testid="loading">Loading tenant...</div>;
        if (error) return <div data-testid="tenant-error">{error}</div>;
        if (!tenant) return <div data-testid="no-tenant">No tenant found</div>;
        
        return <div data-testid="tenant-loaded">Tenant: {tenant.id}</div>;
      };

      render(<TestComponent />);

      expect(screen.getByTestId('no-tenant')).toHaveTextContent('No tenant found');
    });
  });
});

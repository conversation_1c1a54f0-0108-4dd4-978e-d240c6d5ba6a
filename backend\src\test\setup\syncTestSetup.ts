/**
 * Sync Test Setup
 * 
 * Global setup and teardown for synchronization tests including:
 * - Database initialization
 * - Test data cleanup
 * - Performance monitoring
 * - Memory leak detection
 */

import { Pool } from 'pg'
import { execSync } from 'child_process'

// Global test database connection
let testDb: Pool

// Performance monitoring
const performanceMetrics = {
  testStartTime: 0,
  memoryUsage: [] as number[],
  testCounts: {
    passed: 0,
    failed: 0,
    skipped: 0
  }
}

/**
 * Global setup - runs once before all tests
 */
beforeAll(async () => {
  console.log('🚀 Setting up sync test environment...')
  
  performanceMetrics.testStartTime = Date.now()
  
  // Initialize test database connection
  testDb = new Pool({
    host: process.env.TEST_DB_HOST || 'localhost',
    port: parseInt(process.env.TEST_DB_PORT || '5432'),
    database: process.env.TEST_DB_NAME || 'renewtrack_test',
    user: process.env.TEST_DB_USER || 'postgres',
    password: process.env.TEST_DB_PASSWORD || 'password',
    max: 10
  })

  try {
    // Test database connection
    await testDb.query('SELECT 1')
    console.log('✅ Database connection established')

    // Run migrations if needed
    if (process.env.RUN_MIGRATIONS === 'true') {
      console.log('🔄 Running database migrations...')
      execSync('npm run migrate:test', { stdio: 'inherit' })
      console.log('✅ Migrations completed')
    }

    // Create test tenant schemas if they don't exist
    await createTestTenantSchemas()
    console.log('✅ Test tenant schemas ready')

  } catch (error) {
    console.error('❌ Test setup failed:', error)
    throw error
  }
}, 60000)

/**
 * Global teardown - runs once after all tests
 */
afterAll(async () => {
  console.log('🧹 Cleaning up sync test environment...')
  
  try {
    // Clean up test data
    await cleanupAllTestData()
    console.log('✅ Test data cleaned up')

    // Close database connection
    await testDb.end()
    console.log('✅ Database connection closed')

    // Print performance summary
    printPerformanceSummary()

  } catch (error) {
    console.error('❌ Test cleanup failed:', error)
  }
}, 30000)

/**
 * Before each test - runs before every test
 */
beforeEach(async () => {
  // Record memory usage
  performanceMetrics.memoryUsage.push(process.memoryUsage().heapUsed)
  
  // Set test timeout based on test name
  const testName = expect.getState().currentTestName || ''
  if (testName.includes('Performance') || testName.includes('Large')) {
    jest.setTimeout(300000) // 5 minutes for performance tests
  } else if (testName.includes('Integration')) {
    jest.setTimeout(120000) // 2 minutes for integration tests
  } else {
    jest.setTimeout(30000) // 30 seconds for unit tests
  }
})

/**
 * After each test - runs after every test
 */
afterEach(async () => {
  const testState = expect.getState()
  
  // Update test counts
  if (testState.assertionCalls === testState.expectedAssertionCalls) {
    performanceMetrics.testCounts.passed++
  } else {
    performanceMetrics.testCounts.failed++
  }

  // Force garbage collection if available
  if (global.gc) {
    global.gc()
  }

  // Check for memory leaks
  const currentMemory = process.memoryUsage().heapUsed
  const memoryGrowth = performanceMetrics.memoryUsage.length > 1 
    ? currentMemory - performanceMetrics.memoryUsage[0]
    : 0

  if (memoryGrowth > 50 * 1024 * 1024) { // 50MB threshold
    console.warn(`⚠️  Potential memory leak detected: ${Math.round(memoryGrowth / 1024 / 1024)}MB growth`)
  }
})

/**
 * Create test tenant schemas
 */
async function createTestTenantSchemas(): Promise<void> {
  const tenantIds = ['0000000000000001', '0000000000000002', '0000000000000003']
  
  for (const tenantId of tenantIds) {
    const schemaName = `tenant_${tenantId}`
    
    try {
      // Create schema if it doesn't exist
      await testDb.query(`CREATE SCHEMA IF NOT EXISTS ${schemaName}`)
      
      // Create tenant tables
      await testDb.query(`
        CREATE TABLE IF NOT EXISTS ${schemaName}.tenant_vendors (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          name VARCHAR(255) NOT NULL,
          tax_id VARCHAR(50),
          domain VARCHAR(255),
          address TEXT,
          city VARCHAR(100),
          state VARCHAR(100),
          country VARCHAR(100),
          contact_email VARCHAR(255),
          contact_phone VARCHAR(50),
          website VARCHAR(255),
          created_on TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
          changed_on TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
          UNIQUE(name, COALESCE(tax_id, ''))
        )
      `)

      await testDb.query(`
        CREATE TABLE IF NOT EXISTS ${schemaName}.tenant_products (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          tenant_vendor_id UUID NOT NULL REFERENCES ${schemaName}.tenant_vendors(id),
          name VARCHAR(255) NOT NULL,
          sku VARCHAR(100),
          gtin VARCHAR(50),
          category VARCHAR(100),
          description TEXT,
          website VARCHAR(255),
          created_on TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
          changed_on TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
          UNIQUE(tenant_vendor_id, name)
        )
      `)

      await testDb.query(`
        CREATE TABLE IF NOT EXISTS ${schemaName}.tenant_product_versions (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          tenant_product_id UUID NOT NULL REFERENCES ${schemaName}.tenant_products(id),
          version VARCHAR(100) NOT NULL,
          release_date DATE,
          end_of_life_date DATE,
          support_level VARCHAR(50),
          description TEXT,
          download_url VARCHAR(500),
          created_on TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
          changed_on TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
          UNIQUE(tenant_product_id, version)
        )
      `)

      // Create sync tables
      await testDb.query(`
        CREATE TABLE IF NOT EXISTS ${schemaName}.tenant_vendor_sync (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          tenant_vendor_id UUID NOT NULL REFERENCES ${schemaName}.tenant_vendors(id),
          global_vendor_id UUID,
          confidence DECIMAL(5,2),
          match_type VARCHAR(50),
          sync_status VARCHAR(20) NOT NULL DEFAULT 'pending',
          created_on TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
          changed_on TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
          UNIQUE(tenant_vendor_id)
        )
      `)

      await testDb.query(`
        CREATE TABLE IF NOT EXISTS ${schemaName}.tenant_product_sync (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          tenant_product_id UUID NOT NULL REFERENCES ${schemaName}.tenant_products(id),
          global_product_id UUID,
          confidence DECIMAL(5,2),
          match_type VARCHAR(50),
          sync_status VARCHAR(20) NOT NULL DEFAULT 'pending',
          created_on TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
          changed_on TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
          UNIQUE(tenant_product_id)
        )
      `)

      await testDb.query(`
        CREATE TABLE IF NOT EXISTS ${schemaName}.tenant_product_version_sync (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          tenant_version_id UUID NOT NULL REFERENCES ${schemaName}.tenant_product_versions(id),
          global_version_id UUID,
          confidence DECIMAL(5,2),
          match_type VARCHAR(50),
          sync_status VARCHAR(20) NOT NULL DEFAULT 'pending',
          created_on TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
          changed_on TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
          UNIQUE(tenant_version_id)
        )
      `)

      // Create renewals table for testing
      await testDb.query(`
        CREATE TABLE IF NOT EXISTS ${schemaName}.renewals (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          vendor_id UUID REFERENCES ${schemaName}.tenant_vendors(id),
          product_id UUID REFERENCES ${schemaName}.tenant_products(id),
          cost DECIMAL(15,2),
          start_date DATE,
          created_on TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
        )
      `)

    } catch (error) {
      console.error(`Failed to create schema ${schemaName}:`, error)
      throw error
    }
  }
}

/**
 * Clean up all test data
 */
async function cleanupAllTestData(): Promise<void> {
  const tenantIds = ['0000000000000001', '0000000000000002', '0000000000000003']
  
  try {
    // Clean tenant data
    for (const tenantId of tenantIds) {
      const schemaName = `tenant_${tenantId}`
      
      await testDb.query(`TRUNCATE ${schemaName}.tenant_product_version_sync CASCADE`)
      await testDb.query(`TRUNCATE ${schemaName}.tenant_product_sync CASCADE`)
      await testDb.query(`TRUNCATE ${schemaName}.tenant_vendor_sync CASCADE`)
      await testDb.query(`TRUNCATE ${schemaName}.renewals CASCADE`)
      await testDb.query(`TRUNCATE ${schemaName}.tenant_product_versions CASCADE`)
      await testDb.query(`TRUNCATE ${schemaName}.tenant_products CASCADE`)
      await testDb.query(`TRUNCATE ${schemaName}.tenant_vendors CASCADE`)
    }
    
    // Clean global data
    await testDb.query('TRUNCATE metadata.global_product_versions CASCADE')
    await testDb.query('TRUNCATE metadata.global_products CASCADE')
    await testDb.query('TRUNCATE metadata.global_vendors CASCADE')
    await testDb.query('TRUNCATE metadata.sync_conflicts CASCADE')
    await testDb.query('TRUNCATE metadata.sync_batches CASCADE')
    await testDb.query('TRUNCATE metadata.sync_jobs CASCADE')
    await testDb.query('TRUNCATE metadata.sync_metrics CASCADE')
    
  } catch (error) {
    console.error('Failed to cleanup test data:', error)
    throw error
  }
}

/**
 * Print performance summary
 */
function printPerformanceSummary(): void {
  const totalTime = Date.now() - performanceMetrics.testStartTime
  const avgMemory = performanceMetrics.memoryUsage.reduce((sum, mem) => sum + mem, 0) / performanceMetrics.memoryUsage.length
  const maxMemory = Math.max(...performanceMetrics.memoryUsage)
  
  console.log('\n📊 Test Performance Summary:')
  console.log(`⏱️  Total execution time: ${Math.round(totalTime / 1000)}s`)
  console.log(`✅ Tests passed: ${performanceMetrics.testCounts.passed}`)
  console.log(`❌ Tests failed: ${performanceMetrics.testCounts.failed}`)
  console.log(`💾 Average memory usage: ${Math.round(avgMemory / 1024 / 1024)}MB`)
  console.log(`📈 Peak memory usage: ${Math.round(maxMemory / 1024 / 1024)}MB`)
}

// Export for use in tests
export { testDb, performanceMetrics }

-- =====================================================
-- Tenant Logging System Setup Script
-- =====================================================
-- This script sets up the comprehensive tenant logging system
-- for monitoring all changes in tenant tables.
--
-- Run this script for each tenant schema to enable logging.
-- USAGE: Set the tenant_schema variable before running:
-- \set tenant_schema 'tenant_0000000000000001'
-- =====================================================

-- Set the tenant schema (CHANGE THIS FOR EACH TENANT)
-- SET search_path TO :tenant_schema, metadata, public;

-- =====================================================
-- 1. Create the tenant_log table
-- =====================================================
CREATE TABLE IF NOT EXISTS tenant_log (
    LogID BIGSERIAL PRIMARY KEY,
    Timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    TableName VARCHAR(100) NOT NULL,
    Operation VARCHAR(10) NOT NULL CHECK (
        Operation IN (
            'INSERT',
            'UPDATE',
            'DELETE',
            'SYSTEM'
        )
    ),
    RecordID INTEGER NOT NULL, -- Changed to INTEGER to match new primary keys
    RecordUUID UUID, -- Optional: store original UUID if needed for migration
    UserID VARCHAR(255),
    UserEmail VARCHAR(255),
    ChangedFields TEXT [] DEFAULT '{}',
    OldValues JSONB,
    NewValues JSONB,
    BusinessImpact VARCHAR(20) DEFAULT 'low' CHECK (
        BusinessImpact IN (
            'low',
            'medium',
            'high',
            'critical'
        )
    ),
    Metadata JSONB DEFAULT '{}'::jsonb,
    CreatedAt TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_tenant_log_timestamp ON tenant_log (Timestamp DESC);

CREATE INDEX IF NOT EXISTS idx_tenant_log_table_operation ON tenant_log (TableName, Operation);

CREATE INDEX IF NOT EXISTS idx_tenant_log_user ON tenant_log (UserID);

CREATE INDEX IF NOT EXISTS idx_tenant_log_business_impact ON tenant_log (BusinessImpact);

CREATE INDEX IF NOT EXISTS idx_tenant_log_record_id ON tenant_log (RecordID);

CREATE INDEX IF NOT EXISTS idx_tenant_log_record_uuid ON tenant_log (RecordUUID);
-- For migration support

-- =====================================================
-- 2. Create utility functions for business impact calculation
-- =====================================================
CREATE OR REPLACE FUNCTION calculate_business_impact(
    p_table_name VARCHAR,
    p_operation VARCHAR,
    p_changed_fields TEXT[],
    p_old_values JSONB,
    p_new_values JSONB
) RETURNS VARCHAR AS $$
BEGIN
    -- Critical impact scenarios
    IF p_operation = 'DELETE' THEN
        RETURN 'critical';
    END IF;
    
    IF p_table_name = 'Renewals' THEN
        -- High impact for renewal changes
        IF p_changed_fields && ARRAY['cost', 'start_date', 'status'] THEN
            RETURN 'high';
        END IF;
        -- Medium impact for other renewal fields
        RETURN 'medium';
    END IF;
    
    IF p_table_name = 'tenant_vendors' THEN
        -- High impact for vendor status changes
        IF p_changed_fields && ARRAY['is_deleted', 'status'] THEN
            RETURN 'high';
        END IF;
        RETURN 'medium';
    END IF;
    
    -- Default to low impact
    RETURN 'low';
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 3. Create main trigger function
-- =====================================================
CREATE OR REPLACE FUNCTION log_tenant_changes() RETURNS TRIGGER AS $$
DECLARE
    changed_fields TEXT[] := '{}';
    old_vals JSONB := '{}'::jsonb;
    new_vals JSONB := '{}'::jsonb;
    field_name TEXT;
    impact_level VARCHAR;
    current_user_id VARCHAR := current_setting('app.current_user_id', true);
    current_user_email VARCHAR := current_setting('app.current_user_email', true);
BEGIN
    -- Handle different operations
    IF TG_OP = 'DELETE' THEN
        old_vals := to_jsonb(OLD);
        impact_level := 'critical';
        
        INSERT INTO tenant_log (
            TableName, Operation, RecordID, UserID, UserEmail,
            OldValues, BusinessImpact, Metadata
        ) VALUES (
            TG_TABLE_NAME, TG_OP,
            CASE
                WHEN TG_TABLE_NAME = 'Renewals' THEN OLD."RenewalID"
                WHEN TG_TABLE_NAME = 'Alerts' THEN OLD."AlertID"
                WHEN TG_TABLE_NAME = 'tenant_vendors' THEN OLD."VendorID"
                WHEN TG_TABLE_NAME = 'tenant_products' THEN OLD."ProductID"
                WHEN TG_TABLE_NAME = 'tenant_product_versions' THEN OLD."VersionID"
                ELSE 0 -- Fallback for unknown tables
            END,
            current_user_id, current_user_email,
            old_vals, impact_level,
            jsonb_build_object('trigger_time', NOW())
        );
        
        RETURN OLD;
    END IF;
    
    IF TG_OP = 'INSERT' THEN
        new_vals := to_jsonb(NEW);
        impact_level := calculate_business_impact(TG_TABLE_NAME, TG_OP, '{}', '{}'::jsonb, new_vals);
        
        INSERT INTO tenant_log (
            TableName, Operation, RecordID, UserID, UserEmail,
            NewValues, BusinessImpact, Metadata
        ) VALUES (
            TG_TABLE_NAME, TG_OP,
            CASE
                WHEN TG_TABLE_NAME = 'tenant_renewals' THEN NEW.renewal_id
                WHEN TG_TABLE_NAME = 'tenant_alerts' THEN NEW.alert_id
                WHEN TG_TABLE_NAME = 'tenant_vendors' THEN NEW.vendor_id
                WHEN TG_TABLE_NAME = 'tenant_products' THEN NEW.product_id
                WHEN TG_TABLE_NAME = 'tenant_product_versions' THEN NEW.version_id
                ELSE 0 -- Fallback for unknown tables
            END,
            current_user_id, current_user_email,
            new_vals, impact_level,
            jsonb_build_object('trigger_time', NOW())
        );
        
        RETURN NEW;
    END IF;
    
    IF TG_OP = 'UPDATE' THEN
        -- Compare OLD and NEW to find changed fields
        FOR field_name IN SELECT jsonb_object_keys(to_jsonb(NEW)) LOOP
            IF to_jsonb(OLD) ->> field_name IS DISTINCT FROM to_jsonb(NEW) ->> field_name THEN
                changed_fields := array_append(changed_fields, field_name);
                old_vals := old_vals || jsonb_build_object(field_name, to_jsonb(OLD) ->> field_name);
                new_vals := new_vals || jsonb_build_object(field_name, to_jsonb(NEW) ->> field_name);
            END IF;
        END LOOP;
        
        -- Only log if there are actual changes
        IF array_length(changed_fields, 1) > 0 THEN
            impact_level := calculate_business_impact(TG_TABLE_NAME, TG_OP, changed_fields, old_vals, new_vals);
            
            INSERT INTO tenant_log (
                TableName, Operation, RecordID, UserID, UserEmail,
                ChangedFields, OldValues, NewValues, BusinessImpact, Metadata
            ) VALUES (
                TG_TABLE_NAME, TG_OP,
                CASE
                    WHEN TG_TABLE_NAME = 'Renewals' THEN NEW."RenewalID"
                    WHEN TG_TABLE_NAME = 'Alerts' THEN NEW."AlertID"
                    WHEN TG_TABLE_NAME = 'tenant_vendors' THEN NEW."VendorID"
                    WHEN TG_TABLE_NAME = 'tenant_products' THEN NEW."ProductID"
                    WHEN TG_TABLE_NAME = 'tenant_product_versions' THEN NEW."VersionID"
                    ELSE 0 -- Fallback for unknown tables
                END,
                current_user_id, current_user_email,
                changed_fields, old_vals, new_vals, impact_level,
                jsonb_build_object('trigger_time', NOW(), 'fields_changed', array_length(changed_fields, 1))
            );
        END IF;
        
        RETURN NEW;
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- 4. Create triggers for monitored tables
-- =====================================================

-- Renewals table trigger
DROP TRIGGER IF EXISTS trigger_log_renewals ON "Renewals";

CREATE TRIGGER trigger_log_renewals
    AFTER INSERT OR UPDATE OR DELETE ON "Renewals"
    FOR EACH ROW EXECUTE FUNCTION log_tenant_changes();

-- Alerts table trigger
DROP TRIGGER IF EXISTS trigger_log_alerts ON "Alerts";

CREATE TRIGGER trigger_log_alerts
    AFTER INSERT OR UPDATE OR DELETE ON "Alerts"
    FOR EACH ROW EXECUTE FUNCTION log_tenant_changes();

-- Vendors table trigger
DROP TRIGGER IF EXISTS trigger_log_vendors ON tenant_vendors;

CREATE TRIGGER trigger_log_vendors
    AFTER INSERT OR UPDATE OR DELETE ON tenant_vendors
    FOR EACH ROW EXECUTE FUNCTION log_tenant_changes();

-- Products table trigger
DROP TRIGGER IF EXISTS trigger_log_products ON tenant_products;

CREATE TRIGGER trigger_log_products
    AFTER INSERT OR UPDATE OR DELETE ON tenant_products
    FOR EACH ROW EXECUTE FUNCTION log_tenant_changes();

-- Product versions table trigger
DROP TRIGGER IF EXISTS trigger_log_product_versions ON tenant_product_versions;

CREATE TRIGGER trigger_log_product_versions
    AFTER INSERT OR UPDATE OR DELETE ON tenant_product_versions
    FOR EACH ROW EXECUTE FUNCTION log_tenant_changes();

-- =====================================================
-- 5. Setup completion log
-- =====================================================
INSERT INTO
    tenant_log (
        TableName,
        Operation,
        RecordID,
        UserID,
        UserEmail,
        BusinessImpact,
        Metadata
    )
VALUES (
        'SYSTEM',
        'SYSTEM',
        0,
        'SYSTEM',
        '<EMAIL>',
        'low',
        jsonb_build_object(
            'event',
            'tenant_logging_setup_completed',
            'schema',
            current_schema(),
            'setup_time',
            NOW(),
            'components_installed',
            ARRAY[
                'tenant_log table with integer primary keys',
                'performance indexes',
                'utility functions',
                'main trigger function',
                'table triggers for Renewals, Alerts, vendors, products, versions'
            ]
        )
    );

-- =====================================================
-- SETUP COMPLETE
-- =====================================================
-- The tenant logging system has been successfully installed.
--
-- Components installed:
-- ✓ tenant_log table with comprehensive schema
-- ✓ Performance-optimized indexes
-- ✓ Business impact calculation functions
-- ✓ Main trigger function for change tracking
-- ✓ Triggers on all monitored tables
-- ✓ Setup completion log entry
--
-- The system will now automatically log all changes to:
-- - Renewals table
-- - tenant_vendors table
-- - tenant_products table
-- - tenant_product_versions table
--
-- To view logs, use the Tenant Logs admin interface or query:
-- SELECT * FROM tenant_log ORDER BY timestamp DESC LIMIT 100;
-- =====================================================
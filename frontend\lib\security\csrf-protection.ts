/**
 * CSRF Protection Implementation
 * 
 * Provides CSRF token generation, validation, and middleware for protecting
 * state-changing operations from cross-site request forgery attacks.
 */

import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'
import crypto from 'crypto'
import { createErrorR<PERSON>ponse, ApiErrorCode, HttpStatus } from '@/lib/api/response'

// CSRF token configuration
const CSRF_TOKEN_LENGTH = 32
const CSRF_TOKEN_HEADER = 'x-csrf-token'
const CSRF_COOKIE_NAME = 'csrf-token'
const CSRF_TOKEN_EXPIRY = 24 * 60 * 60 * 1000 // 24 hours

interface CSRFTokenData {
  token: string
  timestamp: number
  userId?: string
}

// In-memory store for CSRF tokens (in production, use Redis or database)
const csrfTokenStore = new Map<string, CSRFTokenData>()

/**
 * Generate a cryptographically secure CSRF token
 */
export function generateCSRFToken(userId?: string): string {
  const token = crypto.randomBytes(CSRF_TOKEN_LENGTH).toString('hex')
  
  // Store token with metadata
  csrfTokenStore.set(token, {
    token,
    timestamp: Date.now(),
    userId
  })
  
  // Clean up expired tokens
  cleanupExpiredTokens()
  
  return token
}

/**
 * Validate CSRF token
 */
export function validateCSRFToken(token: string, userId?: string): boolean {
  if (!token) {
    return false
  }
  
  const tokenData = csrfTokenStore.get(token)
  
  if (!tokenData) {
    return false
  }
  
  // Check if token is expired
  if (Date.now() - tokenData.timestamp > CSRF_TOKEN_EXPIRY) {
    csrfTokenStore.delete(token)
    return false
  }
  
  // Check if token belongs to the user (if userId provided)
  if (userId && tokenData.userId && tokenData.userId !== userId) {
    return false
  }
  
  return true
}

/**
 * Clean up expired CSRF tokens
 */
function cleanupExpiredTokens(): void {
  const now = Date.now()
  
  for (const [token, data] of csrfTokenStore.entries()) {
    if (now - data.timestamp > CSRF_TOKEN_EXPIRY) {
      csrfTokenStore.delete(token)
    }
  }
}

/**
 * Set CSRF token in cookie
 */
export function setCSRFCookie(response: NextResponse, token: string): void {
  response.cookies.set(CSRF_COOKIE_NAME, token, {
    httpOnly: false, // Needs to be accessible by JavaScript
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    maxAge: CSRF_TOKEN_EXPIRY / 1000,
    path: '/'
  })
}

/**
 * Get CSRF token from request
 */
export function getCSRFTokenFromRequest(request: NextRequest): string | null {
  // Try header first
  const headerToken = request.headers.get(CSRF_TOKEN_HEADER)
  if (headerToken) {
    return headerToken
  }
  
  // Try cookie as fallback
  const cookieToken = request.cookies.get(CSRF_COOKIE_NAME)?.value
  return cookieToken || null
}

/**
 * CSRF protection middleware for API routes
 */
export function withCSRFProtection<T extends any[], R>(
  handler: (...args: T) => Promise<R>
) {
  return async (...args: T): Promise<R | NextResponse> => {
    const request = args[0] as NextRequest
    const method = request.method
    
    // Only protect state-changing methods
    if (!['POST', 'PUT', 'DELETE', 'PATCH'].includes(method)) {
      return handler(...args)
    }
    
    // Skip CSRF for certain endpoints (like login)
    const pathname = request.nextUrl.pathname
    const skipCSRFPaths = [
      '/api/auth/login',
      '/api/auth/callback',
      '/api/auth/refresh'
    ]
    
    if (skipCSRFPaths.some(path => pathname.startsWith(path))) {
      return handler(...args)
    }
    
    // Get CSRF token from request
    const csrfToken = getCSRFTokenFromRequest(request)
    
    if (!csrfToken) {
      return createErrorResponse(
        'CSRF token missing',
        ApiErrorCode.FORBIDDEN,
        HttpStatus.FORBIDDEN
      )
    }
    
    // Validate CSRF token
    if (!validateCSRFToken(csrfToken)) {
      return createErrorResponse(
        'Invalid or expired CSRF token',
        ApiErrorCode.FORBIDDEN,
        HttpStatus.FORBIDDEN
      )
    }
    
    return handler(...args)
  }
}

/**
 * Generate CSRF token for client-side use
 */
export async function generateCSRFTokenForClient(userId?: string): Promise<{
  token: string
  expires: number
}> {
  const token = generateCSRFToken(userId)
  
  return {
    token,
    expires: Date.now() + CSRF_TOKEN_EXPIRY
  }
}

/**
 * Client-side CSRF token manager
 */
export class CSRFTokenManager {
  private static instance: CSRFTokenManager
  private token: string | null = null
  private expires: number = 0
  
  private constructor() {}
  
  static getInstance(): CSRFTokenManager {
    if (!CSRFTokenManager.instance) {
      CSRFTokenManager.instance = new CSRFTokenManager()
    }
    return CSRFTokenManager.instance
  }
  
  async getToken(): Promise<string> {
    // Check if current token is still valid
    if (this.token && Date.now() < this.expires - 60000) { // 1 minute buffer
      return this.token
    }
    
    // Fetch new token
    try {
      const response = await fetch('/api/csrf/token', {
        method: 'GET',
        credentials: 'include'
      })
      
      if (!response.ok) {
        throw new Error('Failed to fetch CSRF token')
      }
      
      const data = await response.json()
      this.token = data.token
      this.expires = data.expires

      if (!this.token) {
        throw new Error('Invalid CSRF token received')
      }

      return this.token
    } catch (error) {
      console.error('Error fetching CSRF token:', error)
      throw error
    }
  }
  
  clearToken(): void {
    this.token = null
    this.expires = 0
  }
}

// Export singleton instance
export const csrfTokenManager = CSRFTokenManager.getInstance()

/**
 * Universal Page Header Component
 *
 * Consolidated reusable page header component with title, subtitle, search, and actions.
 * Replaces OverviewHeader, RenewalsHeader, ReportsHeader, and other specific headers.
 * Supports all common header patterns with flexible configuration.
 */

'use client'

import React, { ReactNode, useState } from 'react'
import { Form } from '@/components/ui/Form'
import { Button, IconButton } from '@/components/ui/Button'
import { BaseComponentProps } from '@/lib/types'

interface PageHeaderAction {
  label: string
  onClick: () => void
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'
  leftIcon?: string | ReactNode
  rightIcon?: string | ReactNode
  isLoading?: boolean
  disabled?: boolean
  'data-testid'?: string
}

interface PageHeaderStats {
  label: string
  value: string | number
  description?: string
}

interface PageHeaderProps extends BaseComponentProps {
  title: string
  subtitle: string

  // Search functionality
  searchPlaceholder?: string
  searchValue?: string
  onSearchChange?: (value: string) => void
  onSearchSubmit?: (value: string) => void

  // Actions - can be custom ReactNode or predefined action objects
  actions?: ReactNode | PageHeaderAction[]

  // Stats display (for reports-style headers)
  stats?: PageHeaderStats[]

  // Additional content
  clientName?: string
  breadcrumbs?: Array<{ label: string; href?: string }>
  backButton?: {
    label: string
    onClick: () => void
  }
}

const PageHeader: React.FC<PageHeaderProps> = ({
  title,
  subtitle,
  searchPlaceholder = 'Search...',
  searchValue = '',
  onSearchChange,
  onSearchSubmit,
  actions,
  stats,
  clientName,
  breadcrumbs,
  backButton,
  className = '',
  'data-testid': testId
}) => {
  const [internalSearchValue, setInternalSearchValue] = useState(searchValue)

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setInternalSearchValue(value)
    onSearchChange?.(value)
  }

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    const searchQuery = searchValue || internalSearchValue
    onSearchSubmit?.(searchQuery)
    onSearchChange?.(searchQuery)
  }

  const renderActions = () => {
    if (!actions) return null

    // If actions is a ReactNode, render it directly
    if (React.isValidElement(actions) || typeof actions === 'string') {
      return actions
    }

    // If actions is an array of action objects, render them as buttons
    if (Array.isArray(actions)) {
      return (
        <div className="flex items-center gap-2">
          {actions.map((action, index) => (
            <Button
              key={index}
              variant={action.variant || 'secondary'}
              onClick={action.onClick}
              leftIcon={action.leftIcon}
              rightIcon={action.rightIcon}
              isLoading={action.isLoading}
              disabled={action.disabled}
              data-testid={action['data-testid']}
            >
              {action.label}
            </Button>
          ))}
        </div>
      )
    }

    return actions
  }

  return (
    <div className={`page-header ${className}`} data-testid={testId}>
      {/* Back button and breadcrumbs */}
      {(backButton || breadcrumbs) && (
        <div className="flex items-center space-x-4 mb-2">
          {backButton && (
            <button
              onClick={backButton.onClick}
              className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
            >
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              {backButton.label}
            </button>
          )}

          {breadcrumbs && (
            <nav className="flex" aria-label="Breadcrumb">
              <ol className="flex items-center space-x-2">
                {breadcrumbs.map((crumb, index) => (
                  <li key={index} className="flex items-center">
                    {index > 0 && <span className="mx-2 text-gray-400">/</span>}
                    {crumb.href ? (
                      <a href={crumb.href} className="text-gray-600 hover:text-gray-900">
                        {crumb.label}
                      </a>
                    ) : (
                      <span className="text-gray-900">{crumb.label}</span>
                    )}
                  </li>
                ))}
              </ol>
            </nav>
          )}

          {(backButton || breadcrumbs) && <div className="h-6 w-px bg-gray-300" />}
        </div>
      )}

      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        {/* Title and Description */}
        <div>
          <h1 className="page-title">{title}</h1>
          <p className="page-subtitle">
            {subtitle}
            {clientName && (
              <span className="block text-sm text-gray-500 mt-1">
                Client: {clientName}
              </span>
            )}
          </p>
        </div>

        {/* Stats and Actions */}
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
          {/* Stats */}
          {stats && stats.length > 0 && (
            <div className="flex items-center gap-4 text-sm text-gray-600">
              {stats.map((stat, index) => (
                <div key={index} className="flex items-center gap-1">
                  <span className="font-medium text-gray-900">{stat.value}</span>
                  <span>{stat.label}</span>
                  {stat.description && (
                    <span className="text-gray-500">({stat.description})</span>
                  )}
                </div>
              ))}
            </div>
          )}

          {/* Search and Actions */}
          <div className="flex items-center gap-2">
            {/* Search Bar */}
            {onSearchChange && (
              <form onSubmit={handleSearchSubmit} className="search-container flex items-center">
                <Form.Input
                  type="text"
                  placeholder={searchPlaceholder}
                  value={searchValue || internalSearchValue}
                  onChange={handleSearchChange}
                  aria-label="Search"
                  className="search-input"
                  style={{ width: '280px', marginRight: '8px' }}
                />
                {onSearchSubmit && (
                  <IconButton
                    type="submit"
                    variant="ghost"
                    size="sm"
                    icon="🔍"
                    aria-label="Submit search"
                  />
                )}
              </form>
            )}

            {/* Action Buttons */}
            {renderActions()}
          </div>
        </div>
      </div>
    </div>
  )
}

export default PageHeader

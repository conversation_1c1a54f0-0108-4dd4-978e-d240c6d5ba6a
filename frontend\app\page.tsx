'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { getAuthenticatedUser } from '@/lib/auth-utils'

export default function Home() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  const [user, setUser] = useState<any | null>(null)
  const [isAuthenticated, setIsAuthenticated] = useState(false)

  useEffect(() => {
    const checkAuth = async () => {
      console.log('🏠 [ROOT] === ROOT PAGE EFFECT TRIGGERED ===')

      try {
        const currentUser = await getAuthenticatedUser()

        console.log('🏠 [ROOT] Auth check result:', {
          isAuthenticated: !!currentUser,
          hasUser: !!currentUser,
          userEmail: currentUser?.username
        })

        setIsAuthenticated(!!currentUser)
        setUser(currentUser)

        if (currentUser) {
          // User is authenticated, redirect to overview
          console.log('✅ [ROOT] User authenticated, redirecting to overview:', {
            username: currentUser.username,
            userId: currentUser.userId
          })
          router.push('/overview')
        } else {
          // User is not authenticated, redirect to login
          console.log('❌ [ROOT] User not authenticated, redirecting to login')
          router.push('/login')
        }
      } catch (error) {
        console.error('❌ [ROOT] Auth check error:', error)
        router.push('/login')
      } finally {
        setIsLoading(false)
      }
    }

    checkAuth()
  }, [router])

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">
          {isLoading ? 'Checking authentication...' : 'Redirecting...'}
        </p>
      </div>
    </div>
  )
}


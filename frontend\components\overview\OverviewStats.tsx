/**
 * Overview Statistics Component
 * 
 * Displays key metrics in a grid layout with proper loading and error states.
 * Focused responsibility: Rendering statistics cards only.
 */

'use client'

import React, { memo } from 'react'
import { OverviewStats as OverviewStatsType, BaseComponentProps } from '@/lib/types'
// Note: usePerformanceMonitor moved to server-side only

interface OverviewStatsProps extends BaseComponentProps {
  stats: OverviewStatsType
  isLoading?: boolean
}

interface StatCardProps {
  icon: React.ReactNode
  title: string
  value: string | number
  isLoading?: boolean
  color?: string
}

const StatCard = memo(function StatCard({ icon, title, value, isLoading, color = 'text-gray-600' }: StatCardProps) {
  if (isLoading) {
    return (
      <div className="stat-card">
        <div className="stat-icon animate-pulse">
          <div className="w-6 h-6 bg-gray-200 rounded"></div>
        </div>
        <h3>{title}</h3>
        <div className="stat-value">
          <div className="animate-pulse bg-gray-200 h-8 w-20 rounded"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="stat-card">
      <div className={`stat-icon ${color}`}>{icon}</div>
      <h3>{title}</h3>
      <p className="stat-value">{value}</p>
    </div>
  )
})

const OverviewStats = memo(function OverviewStats({
  stats,
  isLoading = false,
  className = '',
  'data-testid': testId
}: OverviewStatsProps) {
  // Performance monitoring in development - removed for now
  // usePerformanceMonitor('OverviewStats')

  // Memoize stats configuration to prevent recreation on every render
  const statsConfig = React.useMemo(() => [
    {
      icon: (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
        </svg>
      ),
      title: 'Total Renewals',
      value: stats.totalRenewals,
      key: 'totalRenewals',
      color: 'text-gray-600'
    },
    {
      icon: (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M8 2v4m8-4v4M3 10h18M5 4h14a2 2 0 012 2v14a2 2 0 01-2 2H5a2 2 0 01-2-2V6a2 2 0 012-2z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
        </svg>
      ),
      title: 'Renewals Due',
      value: stats.renewalsDue,
      key: 'renewalsDue',
      color: 'text-orange-600'
    },
    {
      icon: (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
        </svg>
      ),
      title: 'Vendors',
      value: stats.vendors,
      key: 'vendors',
      color: 'text-green-600'
    },
    {
      icon: (
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M12 2v20M17 5H9.5a3.5 3.5 0 000 7h5a3.5 3.5 0 010 7H6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
        </svg>
      ),
      title: 'Annual Spend',
      value: stats.annualSpend,
      key: 'annualSpend',
      color: 'text-purple-600'
    }
  ], [stats.totalRenewals, stats.renewalsDue, stats.vendors, stats.annualSpend])

  return (
    <div
      className={`stats-grid ${className}`}
      data-testid={testId}
      role="region"
      aria-label="Overview Statistics"
    >
      {statsConfig.map((stat) => (
        <StatCard
          key={stat.key}
          icon={stat.icon}
          title={stat.title}
          value={stat.value}
          isLoading={isLoading}
          color={stat.color}
        />
      ))}
    </div>
  )
})

export default OverviewStats

/**
 * Universal Modal Component
 * 
 * Consolidates all modal implementations into a single, reusable component.
 * Replaces AddRenewalModal, EditRenewalModal, ProcessRenewalModal, etc.
 */

'use client'

import React, { ReactNode, useCallback, useState } from 'react'
import { BaseComponentProps } from '@/lib/types'
import { Button } from './Button'

export interface ModalStep {
  id: string
  title: string
  subtitle?: string
  content: ReactNode
  validation?: () => boolean | Promise<boolean>
  onNext?: () => void | Promise<void>
  onPrevious?: () => void | Promise<void>
}

export interface UniversalModalProps extends BaseComponentProps {
  isOpen: boolean
  onClose: () => void
  
  // Single step modal
  title?: string
  subtitle?: string
  content?: ReactNode
  
  // Multi-step modal
  steps?: ModalStep[]
  currentStepId?: string
  onStepChange?: (stepId: string) => void
  
  // Actions
  primaryAction?: {
    label: string
    onClick: () => void | Promise<void>
    disabled?: boolean
    loading?: boolean
    variant?: 'primary' | 'secondary' | 'danger'
  }
  secondaryAction?: {
    label: string
    onClick: () => void | Promise<void>
    disabled?: boolean
    variant?: 'outline' | 'ghost'
  }
  
  // Configuration
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
  closable?: boolean
  closeOnOverlayClick?: boolean
  showStepIndicator?: boolean
  
  // Callbacks
  onSubmit?: (data?: any) => void | Promise<void>
  onCancel?: () => void
}

const UniversalModal: React.FC<UniversalModalProps> = ({
  isOpen,
  onClose,
  title,
  subtitle,
  content,
  steps,
  currentStepId,
  onStepChange,
  primaryAction,
  secondaryAction,
  size = 'md',
  closable = true,
  closeOnOverlayClick = true,
  showStepIndicator = true,
  onSubmit,
  onCancel,
  className = '',
  'data-testid': testId
}) => {
  // Use simple state management instead of the complex useModal hook
  const [isLoading, setIsLoading] = useState(false)

  // Handle multi-step navigation
  const currentStep = steps?.find(step => step.id === currentStepId) || steps?.[0]
  const currentStepIndex = steps?.findIndex(step => step.id === currentStepId) ?? 0
  const isFirstStep = currentStepIndex === 0
  const isLastStep = currentStepIndex === (steps?.length ?? 1) - 1

  const handleNext = useCallback(async () => {
    if (!steps || !currentStep) return

    // Run validation if provided
    if (currentStep.validation) {
      const isValid = await currentStep.validation()
      if (!isValid) return
    }

    // Run step's onNext callback
    if (currentStep.onNext) {
      await currentStep.onNext()
    }

    // Move to next step
    if (!isLastStep && steps[currentStepIndex + 1]) {
      onStepChange?.(steps[currentStepIndex + 1].id)
    }
  }, [steps, currentStep, currentStepIndex, isLastStep, onStepChange])

  const handlePrevious = useCallback(async () => {
    if (!steps || !currentStep) return

    // Run step's onPrevious callback
    if (currentStep.onPrevious) {
      await currentStep.onPrevious()
    }

    // Move to previous step
    if (!isFirstStep && steps[currentStepIndex - 1]) {
      onStepChange?.(steps[currentStepIndex - 1].id)
    }
  }, [steps, currentStep, currentStepIndex, isFirstStep, onStepChange])

  const handlePrimaryAction = useCallback(async () => {
    if (primaryAction?.onClick) {
      await primaryAction.onClick()
    } else if (steps && isLastStep && onSubmit) {
      await onSubmit()
    } else if (!steps && onSubmit) {
      await onSubmit()
    }
  }, [primaryAction, steps, isLastStep, onSubmit])

  const handleSecondaryAction = useCallback(async () => {
    if (secondaryAction?.onClick) {
      await secondaryAction.onClick()
    } else if (steps && !isFirstStep) {
      await handlePrevious()
    } else {
      onClose()
    }
  }, [secondaryAction, steps, isFirstStep, handlePrevious, onClose])

  if (!isOpen) return null

  const sizeClasses = {
    sm: 'max-w-md',
    md: 'max-w-lg',
    lg: 'max-w-2xl',
    xl: 'max-w-4xl',
    full: 'max-w-full mx-4'
  }

  const displayTitle = currentStep?.title || title
  const displaySubtitle = currentStep?.subtitle || subtitle
  const displayContent = currentStep?.content || content

  return (
    <div 
      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
      onClick={closeOnOverlayClick ? onClose : undefined}
      data-testid={testId}
    >
      <div 
        className={`bg-white rounded-lg shadow-xl ${sizeClasses[size]} w-full mx-4 max-h-[90vh] overflow-hidden ${className}`}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex-1">
            {displayTitle && (
              <h2 className="text-xl font-semibold text-gray-900">{displayTitle}</h2>
            )}
            {displaySubtitle && (
              <p className="mt-1 text-sm text-gray-600">{displaySubtitle}</p>
            )}
          </div>
          
          {closable && (
            <button
              onClick={onClose}
              className="ml-4 text-gray-400 hover:text-gray-600 transition-colors"
              aria-label="Close modal"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          )}
        </div>

        {/* Step Indicator */}
        {steps && showStepIndicator && (
          <div className="px-6 py-4 border-b bg-gray-50">
            <div className="flex items-center space-x-4">
              {steps.map((step, index) => (
                <div key={step.id} className="flex items-center">
                  <div className={`
                    w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium
                    ${index <= currentStepIndex 
                      ? 'bg-blue-600 text-white' 
                      : 'bg-gray-200 text-gray-600'
                    }
                  `}>
                    {index + 1}
                  </div>
                  {index < steps.length - 1 && (
                    <div className={`
                      w-12 h-0.5 mx-2
                      ${index < currentStepIndex ? 'bg-blue-600' : 'bg-gray-200'}
                    `} />
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {displayContent}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t bg-gray-50">
          <div className="flex space-x-3">
            {/* Secondary Action */}
            <Button
              variant={secondaryAction?.variant || (steps && !isFirstStep ? 'outline' : 'ghost')}
              onClick={handleSecondaryAction}
              disabled={secondaryAction?.disabled}
            >
              {secondaryAction?.label || (steps && !isFirstStep ? 'Previous' : 'Cancel')}
            </Button>
          </div>

          <div className="flex space-x-3">
            {/* Primary Action */}
            <Button
              variant={primaryAction?.variant || 'primary'}
              onClick={steps && !isLastStep ? handleNext : handlePrimaryAction}
              disabled={primaryAction?.disabled}
              loading={primaryAction?.loading || isLoading}
            >
              {primaryAction?.label || (steps && !isLastStep ? 'Next' : 'Submit')}
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default UniversalModal

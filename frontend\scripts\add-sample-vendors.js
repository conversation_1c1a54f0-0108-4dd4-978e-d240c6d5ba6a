#!/usr/bin/env node

/**
 * Add Sample Vendors Script
 * 
 * This script adds sample vendors for testing the dropdown functionality
 */

import { Pool } from 'pg';

// Database configuration
const dbConfig = {
  user: 'postgres',
  host: '127.0.0.1',
  database: 'Renewtrack',
  password: 'admin',
  port: 5432,
  ssl: false
};

async function addSampleVendors() {
  console.log('🔧 Adding sample vendors...\n');

  const pool = new Pool(dbConfig);
  
  try {
    const client = await pool.connect();
    console.log('✅ Connected to database');
    
    const tenantSchema = 'tenant_0000000000000001';
    
    const vendors = [
      ['Adobe Inc.', 'Adobe'],
      ['Oracle Corporation', 'Oracle'],
      ['Salesforce Inc.', 'Salesforce'],
      ['Amazon Web Services', 'AWS'],
      ['Google LLC', 'Google'],
      ['Atlassian Corporation', 'Atlassian'],
      ['Slack Technologies', 'Slack'],
      ['Zoom Video Communications', 'Zoom']
    ];
    
    for (const [name, displayName] of vendors) {
      try {
        const result = await client.query(`
          INSERT INTO "${tenantSchema}".tenant_vendors (name, display_name, created_by) 
          VALUES ($1, $2, 'system') 
          ON CONFLICT DO NOTHING
          RETURNING id, name
        `, [name, displayName]);
        
        if (result.rows.length > 0) {
          console.log('✅ Added vendor:', result.rows[0].name.trim());
        } else {
          console.log('⚠️ Vendor already exists:', name);
        }
      } catch (error) {
        console.log('❌ Error adding vendor:', name, error.message);
      }
    }
    
    // Check total vendors
    const countResult = await client.query(`
      SELECT COUNT(*) as total FROM "${tenantSchema}".tenant_vendors WHERE is_deleted = false
    `);
    
    console.log(`\n📊 Total vendors in database: ${countResult.rows[0].total}`);
    
    client.release();
    console.log('\n🎉 Sample vendors setup completed!');
    
  } catch (error) {
    console.error('❌ Error adding sample vendors:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Run the setup
addSampleVendors().catch(console.error);

/**
 * Environment Variable Validator and Initializer
 * 
 * Provides comprehensive environment variable validation, initialization,
 * and error handling to prevent runtime errors from missing variables.
 */

import { ENV_DEFAULTS, getEnvVar, getEnvBoolean, getEnvNumber } from './environment'

/**
 * Environment validation result
 */
export interface EnvValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
  missingRequired: string[]
  missingOptional: string[]
}

/**
 * Required environment variables for different environments
 */
const REQUIRED_VARS = {
  development: [
    'DATABASE_URL',
    'DB_HOST',
    'DB_NAME',
    'DB_USER',
  ] as const,
  
  production: [
    'DATABASE_URL',
    'DB_HOST',
    'DB_NAME',
    'DB_USER',
    'DB_PASSWORD',
    'JWT_SECRET',
    'ENCRYPTION_KEY',
    'NEXT_PUBLIC_AWS_REGION',
    'NEXT_PUBLIC_AWS_USER_POOLS_ID',
    'NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID',
    'NEXT_PUBLIC_AWS_COGNITO_DOMAIN',
    'NEXT_PUBLIC_REDIRECT_SIGN_IN',
    'NEXT_PUBLIC_REDIRECT_SIGN_OUT',
  ] as const,
  
  test: [
    'DATABASE_URL',
  ] as const
}

/**
 * Optional environment variables that should be set but have defaults
 */
const OPTIONAL_VARS = [
  'SESSION_COOKIE_MAX_AGE',
  'TOKEN_REFRESH_THRESHOLD',
  'AUTO_REFRESH_TOKENS',
  'JWT_EXPIRATION',
  'REFRESH_TOKEN_EXPIRATION',
  'ENCRYPTION_ALGORITHM',
  'NEXT_PUBLIC_LOG_LEVEL',
  'NEXT_PUBLIC_ENABLE_LOGGING',
  'API_TIMEOUT',
  'DATABASE_TIMEOUT',
  'ENABLE_DEBUG_MODE',
  'ENABLE_PERFORMANCE_MONITORING',
  'ENABLE_ERROR_TRACKING',
  'DATABASE_SSL',
  'USE_IAM_DB_AUTH',
  'CRON_SECRET',
  'NEXT_PUBLIC_APP_URL',
  'NEXT_PUBLIC_API_URL',
] as const

/**
 * Validate environment variables
 */
export function validateEnvironment(): EnvValidationResult {
  const env = process.env.NODE_ENV || 'development'
  const errors: string[] = []
  const warnings: string[] = []
  const missingRequired: string[] = []
  const missingOptional: string[] = []
  
  // Get required variables for current environment
  const requiredVars = REQUIRED_VARS[env as keyof typeof REQUIRED_VARS] || REQUIRED_VARS.development
  
  // Check required variables
  for (const varName of requiredVars) {
    const value = process.env[varName]
    if (!value || value.trim() === '') {
      missingRequired.push(varName)
      errors.push(`Missing required environment variable: ${varName}`)
    }
  }
  
  // Check optional variables
  for (const varName of OPTIONAL_VARS) {
    const value = process.env[varName]
    if (!value || value.trim() === '') {
      missingOptional.push(varName)
      warnings.push(`Optional environment variable not set, using default: ${varName}`)
    }
  }
  
  // Validate specific variable formats
  validateSpecificFormats(errors, warnings)
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    missingRequired,
    missingOptional
  }
}

/**
 * Validate specific environment variable formats
 */
function validateSpecificFormats(errors: string[], warnings: string[]): void {
  // Validate database URL format
  const dbUrl = process.env.DATABASE_URL
  if (dbUrl && !dbUrl.startsWith('postgresql://')) {
    errors.push('DATABASE_URL must be a valid PostgreSQL connection string')
  }
  
  // Validate AWS region format
  const awsRegion = process.env.NEXT_PUBLIC_AWS_REGION
  if (awsRegion && !/^[a-z]{2}-[a-z]+-\d+$/.test(awsRegion)) {
    warnings.push('NEXT_PUBLIC_AWS_REGION format may be invalid (expected: us-east-1, ca-central-1, etc.)')
  }
  
  // Validate redirect URLs
  const redirectSignIn = process.env.NEXT_PUBLIC_REDIRECT_SIGN_IN
  if (redirectSignIn && !redirectSignIn.startsWith('http')) {
    errors.push('NEXT_PUBLIC_REDIRECT_SIGN_IN must be a valid URL')
  }
  
  const redirectSignOut = process.env.NEXT_PUBLIC_REDIRECT_SIGN_OUT
  if (redirectSignOut && !redirectSignOut.startsWith('http')) {
    errors.push('NEXT_PUBLIC_REDIRECT_SIGN_OUT must be a valid URL')
  }
  
  // Validate numeric values
  const numericVars = [
    'SESSION_COOKIE_MAX_AGE',
    'TOKEN_REFRESH_THRESHOLD',
    'JWT_EXPIRATION',
    'API_TIMEOUT',
    'DATABASE_TIMEOUT',
    'DB_PORT'
  ]
  
  for (const varName of numericVars) {
    const value = process.env[varName]
    if (value && isNaN(parseInt(value))) {
      errors.push(`${varName} must be a valid number`)
    }
  }
}

/**
 * Initialize environment with defaults for missing variables
 */
export function initializeEnvironment(): void {
  const validation = validateEnvironment()
  
  // Set defaults for missing optional variables
  for (const varName of validation.missingOptional) {
    if (varName in ENV_DEFAULTS) {
      const defaultValue = ENV_DEFAULTS[varName as keyof typeof ENV_DEFAULTS]
      process.env[varName] = defaultValue
    }
  }
  
  // Log validation results in development
  if (process.env.NODE_ENV === 'development') {
    if (validation.warnings.length > 0) {
      console.warn('Environment warnings:', validation.warnings)
    }
    
    if (validation.errors.length > 0) {
      console.error('Environment errors:', validation.errors)
    }
  }
  
  // Throw error for missing required variables in production
  if (process.env.NODE_ENV === 'production' && validation.missingRequired.length > 0) {
    throw new Error(`Missing required environment variables: ${validation.missingRequired.join(', ')}`)
  }
}

/**
 * Get safe environment configuration for client-side use
 */
export function getClientSafeConfig() {
  return {
    aws: {
      region: getEnvVar('NEXT_PUBLIC_AWS_REGION'),
      userPoolId: getEnvVar('NEXT_PUBLIC_AWS_USER_POOLS_ID'),
      userPoolClientId: getEnvVar('NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID'),
      cognitoDomain: getEnvVar('NEXT_PUBLIC_AWS_COGNITO_DOMAIN'),
    },
    auth: {
      redirectSignIn: getEnvVar('NEXT_PUBLIC_REDIRECT_SIGN_IN'),
      redirectSignOut: getEnvVar('NEXT_PUBLIC_REDIRECT_SIGN_OUT'),
    },
    app: {
      url: getEnvVar('NEXT_PUBLIC_APP_URL'),
      apiUrl: getEnvVar('NEXT_PUBLIC_API_URL'),
    },
    logging: {
      level: getEnvVar('NEXT_PUBLIC_LOG_LEVEL'),
      enabled: getEnvBoolean('NEXT_PUBLIC_ENABLE_LOGGING'),
      showInProduction: getEnvBoolean('NEXT_PUBLIC_SHOW_LOGS_IN_PRODUCTION'),
      categories: getEnvVar('NEXT_PUBLIC_LOG_CATEGORIES'),
    },
    features: {
      analytics: getEnvBoolean('NEXT_PUBLIC_ENABLE_ANALYTICS'),
      analyticsId: getEnvVar('NEXT_PUBLIC_ANALYTICS_ID'),
    },
    performance: {
      apiTimeout: getEnvNumber('API_TIMEOUT'),
    }
  }
}

/**
 * Get server-side configuration
 */
export function getServerConfig() {
  return {
    database: {
      url: getEnvVar('DATABASE_URL'),
      host: getEnvVar('DB_HOST'),
      port: getEnvNumber('DB_PORT'),
      name: getEnvVar('DB_NAME'),
      user: getEnvVar('DB_USER'),
      password: getEnvVar('DB_PASSWORD'),
      ssl: getEnvBoolean('DATABASE_SSL'),
      useIAM: getEnvBoolean('USE_IAM_DB_AUTH'),
      timeout: getEnvNumber('DATABASE_TIMEOUT'),
    },
    auth: {
      jwtSecret: getEnvVar('JWT_SECRET'),
      jwtExpiration: getEnvNumber('JWT_EXPIRATION'),
      refreshTokenExpiration: getEnvNumber('REFRESH_TOKEN_EXPIRATION'),
    },
    security: {
      encryptionKey: getEnvVar('ENCRYPTION_KEY'),
      encryptionAlgorithm: getEnvVar('ENCRYPTION_ALGORITHM'),
      cronSecret: getEnvVar('CRON_SECRET'),
    },
    session: {
      maxAge: getEnvNumber('SESSION_COOKIE_MAX_AGE'),
      refreshThreshold: getEnvNumber('TOKEN_REFRESH_THRESHOLD'),
      autoRefresh: getEnvBoolean('AUTO_REFRESH_TOKENS'),
    },
    features: {
      debugMode: getEnvBoolean('ENABLE_DEBUG_MODE'),
      performanceMonitoring: getEnvBoolean('ENABLE_PERFORMANCE_MONITORING'),
      errorTracking: getEnvBoolean('ENABLE_ERROR_TRACKING'),
    },
    email: {
      from: getEnvVar('EMAIL_FROM'),
      replyTo: getEnvVar('EMAIL_REPLY_TO'),
    },
    rateLimit: {
      window: getEnvNumber('RATE_LIMIT_WINDOW'),
      maxRequests: getEnvNumber('RATE_LIMIT_MAX_REQUESTS'),
    }
  }
}

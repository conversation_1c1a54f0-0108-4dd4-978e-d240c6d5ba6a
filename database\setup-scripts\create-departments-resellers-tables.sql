-- =====================================================
-- Create Departments and Resellers Tables
-- =====================================================
-- This script creates the tenant_departments and tenant_resellers tables
-- in the tenant schema for dropdown functionality
-- =====================================================

-- Set the tenant schema (change this for each tenant)
SET search_path TO "tenant_0000000000000001", public;

-- =====================================================
-- Tenant Departments Table
-- =====================================================
CREATE TABLE IF NOT EXISTS tenant_departments (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    status VARCHAR(1) NOT NULL DEFAULT 'A' CHECK (status IN ('A', 'I')),
    created_on TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    changed_on TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255),
    changed_by VARCHAR(255),

-- Constraints
CONSTRAINT tenant_departments_name_not_empty CHECK (LENGTH(TRIM(name)) > 0),
    CONSTRAINT tenant_departments_name_unique UNIQUE (name)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_tenant_departments_name ON tenant_departments (name);

CREATE INDEX IF NOT EXISTS idx_tenant_departments_status ON tenant_departments (status);

-- =====================================================
-- Tenant Resellers Table
-- =====================================================
CREATE TABLE IF NOT EXISTS tenant_resellers (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    contact_email VARCHAR(255),
    phone VARCHAR(50),
    address_line1 VARCHAR(255),
    address_line2 VARCHAR(255),
    city VARCHAR(100),
    state VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(100),
    website VARCHAR(255),
    notes TEXT,
    status VARCHAR(1) NOT NULL DEFAULT 'A' CHECK (status IN ('A', 'I')),
    created_on TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    changed_on TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255),
    changed_by VARCHAR(255),

-- Constraints
CONSTRAINT tenant_resellers_name_not_empty CHECK (LENGTH(TRIM(name)) > 0),
    CONSTRAINT tenant_resellers_name_unique UNIQUE (name),
    CONSTRAINT tenant_resellers_email_format CHECK (
        contact_email IS NULL OR contact_email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'
    )
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_tenant_resellers_name ON tenant_resellers (name);

CREATE INDEX IF NOT EXISTS idx_tenant_resellers_status ON tenant_resellers (status);

CREATE INDEX IF NOT EXISTS idx_tenant_resellers_email ON tenant_resellers (contact_email);

-- Add comments
COMMENT ON
TABLE tenant_departments IS 'Tenant-specific department data';

COMMENT ON
TABLE tenant_resellers IS 'Tenant-specific reseller/partner data';

COMMENT ON COLUMN tenant_departments.status IS 'A = Active, I = Inactive';

COMMENT ON COLUMN tenant_resellers.status IS 'A = Active, I = Inactive';

-- =====================================================
-- Insert Sample Data
-- =====================================================

-- Sample departments
INSERT INTO
    tenant_departments (name, description)
VALUES (
        'IT',
        'Information Technology Department'
    ),
    (
        'Finance',
        'Finance and Accounting Department'
    ),
    (
        'HR',
        'Human Resources Department'
    ),
    (
        'Marketing',
        'Marketing and Sales Department'
    ),
    (
        'Operations',
        'Operations Department'
    ),
    ('Legal', 'Legal Department'),
    (
        'Procurement',
        'Procurement Department'
    ) ON CONFLICT (name) DO NOTHING;

-- Sample resellers
INSERT INTO
    tenant_resellers (
        name,
        contact_email,
        phone,
        website
    )
VALUES (
        'TechSource Solutions',
        '<EMAIL>',
        '******-0101',
        'https://techsource.com'
    ),
    (
        'Enterprise Software Partners',
        '<EMAIL>',
        '******-0102',
        'https://esp.com'
    ),
    (
        'Digital Solutions Inc',
        '<EMAIL>',
        '******-0103',
        'https://digitalsolutions.com'
    ),
    (
        'Cloud Services Pro',
        '<EMAIL>',
        '******-0104',
        'https://cloudservicespro.com'
    ),
    (
        'Software Licensing Corp',
        '<EMAIL>',
        '******-0105',
        'https://slcorp.com'
    ) ON CONFLICT (name) DO NOTHING;

-- =====================================================
-- Create Update Triggers
-- =====================================================

-- Function to update the changed_on timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.changed_on = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger for departments
DROP TRIGGER IF EXISTS update_tenant_departments_updated_at ON tenant_departments;

CREATE TRIGGER update_tenant_departments_updated_at
    BEFORE UPDATE ON tenant_departments
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Trigger for resellers
DROP TRIGGER IF EXISTS update_tenant_resellers_updated_at ON tenant_resellers;

CREATE TRIGGER update_tenant_resellers_updated_at
    BEFORE UPDATE ON tenant_resellers
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- Verification Queries
-- =====================================================

-- Verify tables were created
SELECT 'tenant_departments' as table_name, COUNT(*) as record_count
FROM tenant_departments
UNION ALL
SELECT 'tenant_resellers' as table_name, COUNT(*) as record_count
FROM tenant_resellers;

-- Show sample data
SELECT 'Departments:' as info;

SELECT id, name, is_active, created_on
FROM tenant_departments
ORDER BY name;

SELECT 'Resellers:' as info;

SELECT
    id,
    name,
    contact_email,
    is_active,
    created_on
FROM tenant_resellers
ORDER BY name;
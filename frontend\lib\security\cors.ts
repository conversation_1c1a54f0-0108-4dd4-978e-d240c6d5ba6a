/**
 * CORS Configuration and Middleware
 * 
 * Provides secure CORS handling for API routes
 */

import { NextRequest, NextResponse } from 'next/server';

interface CorsOptions {
  origin?: string | string[] | boolean;
  methods?: string[];
  allowedHeaders?: string[];
  credentials?: boolean;
  maxAge?: number;
  preflightContinue?: boolean;
  optionsSuccessStatus?: number;
}

const defaultOptions: CorsOptions = {
  origin: false, // Default to no CORS
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true,
  maxAge: 86400, // 24 hours
  optionsSuccessStatus: 200
};

/**
 * Get allowed origins from environment configuration
 */
function getAllowedOrigins(): string[] {
  const origins = process.env.ALLOWED_ORIGINS;
  if (!origins) {
    // Default to localhost in development
    return process.env.NODE_ENV === 'development' 
      ? ['http://localhost:3000', 'http://localhost:3001']
      : [];
  }
  return origins.split(',').map(origin => origin.trim());
}

/**
 * Check if origin is allowed
 */
function isOriginAllowed(origin: string | null, allowedOrigins: string[]): boolean {
  if (!origin) return false;
  
  // Exact match
  if (allowedOrigins.includes(origin)) return true;
  
  // Pattern matching for subdomains (only in production with explicit patterns)
  if (process.env.NODE_ENV === 'production') {
    return allowedOrigins.some(allowed => {
      if (allowed.includes('*')) {
        const pattern = allowed.replace(/\*/g, '.*');
        return new RegExp(`^${pattern}$`).test(origin);
      }
      return false;
    });
  }
  
  return false;
}

/**
 * Apply CORS headers to response
 */
export function applyCorsHeaders(
  request: NextRequest,
  response: NextResponse,
  options: CorsOptions = {}
): NextResponse {
  const opts = { ...defaultOptions, ...options };
  const origin = request.headers.get('origin');
  const allowedOrigins = getAllowedOrigins();
  
  // Handle origin
  if (opts.origin === true) {
    response.headers.set('Access-Control-Allow-Origin', '*');
  } else if (typeof opts.origin === 'string') {
    response.headers.set('Access-Control-Allow-Origin', opts.origin);
  } else if (Array.isArray(opts.origin)) {
    if (origin && opts.origin.includes(origin)) {
      response.headers.set('Access-Control-Allow-Origin', origin);
    }
  } else if (origin && isOriginAllowed(origin, allowedOrigins)) {
    response.headers.set('Access-Control-Allow-Origin', origin);
  }
  
  // Handle credentials
  if (opts.credentials) {
    response.headers.set('Access-Control-Allow-Credentials', 'true');
  }
  
  // Handle methods
  if (opts.methods) {
    response.headers.set('Access-Control-Allow-Methods', opts.methods.join(', '));
  }
  
  // Handle headers
  if (opts.allowedHeaders) {
    response.headers.set('Access-Control-Allow-Headers', opts.allowedHeaders.join(', '));
  }
  
  // Handle max age
  if (opts.maxAge) {
    response.headers.set('Access-Control-Max-Age', opts.maxAge.toString());
  }
  
  return response;
}

/**
 * Create CORS preflight response
 */
export function createCorsPreflightResponse(
  request: NextRequest,
  options: CorsOptions = {}
): NextResponse {
  const response = new NextResponse(null, { 
    status: options.optionsSuccessStatus || 200 
  });
  
  return applyCorsHeaders(request, response, options);
}

/**
 * Higher-order function to wrap API handlers with CORS
 */
export function withCors<T extends any[], R>(
  handler: (request: NextRequest, ...args: T) => Promise<NextResponse>,
  options: CorsOptions = {}
) {
  return async (request: NextRequest, ...args: T): Promise<NextResponse> => {
    // Handle preflight requests
    if (request.method === 'OPTIONS') {
      return createCorsPreflightResponse(request, options);
    }

    // Execute handler and apply CORS headers
    const response = await handler(request, ...args);
    return applyCorsHeaders(request, response, options);
  };
}

/**
 * Secure CORS configuration for production
 */
export const secureCorsOptions: CorsOptions = {
  origin: false, // Will use environment-based origin checking
  methods: ['GET', 'POST', 'PUT', 'DELETE'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true,
  maxAge: 86400
};

/**
 * Development CORS configuration
 */
export const developmentCorsOptions: CorsOptions = {
  origin: ['http://localhost:3000', 'http://localhost:3001'],
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true,
  maxAge: 86400
};

/**
 * Get appropriate CORS options based on environment
 */
export function getCorsOptions(): CorsOptions {
  return process.env.NODE_ENV === 'development' 
    ? developmentCorsOptions 
    : secureCorsOptions;
}

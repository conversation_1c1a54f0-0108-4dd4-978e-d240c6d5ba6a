/**
 * Reports Page Tests
 * 
 * Test suite for the reports page functionality
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { jest } from '@jest/globals'
import ReportsPage from '@/app/reports/page'

// Mock the hooks
jest.mock('@/lib/hooks', () => ({
  useAppState: jest.fn(() => ({
    tenant: { clientId: 'test-client', clientName: 'Test Client' },
    isTenantLoading: false
  })),
  useReports: jest.fn(() => ({
    renewals: [
      {
        id: 1,
        name: 'Test Software',
        vendor: 'Test Vendor',
        type: 'Subscription',
        start_date: '2024-12-31',
        currency: 'CAD',
        cost: 10000,
        status: 'Active',
        contactEmails: '<EMAIL>',
        product_name: 'Test Software',
        version: '1.0',
        annualCost: 10000
      }
    ],
    stats: {
      totalRenewals: 1,
      totalValue: 10000,
      averageValue: 10000,
      upcomingRenewals: 1,
      vendorCount: 1,
      typeBreakdown: { 'Subscription': 1 }
    },
    isLoading: false,
    error: null,
    refetch: jest.fn()
  })),
  useFilters: jest.fn(() => ({
    filteredData: [
      {
        id: 1,
        name: 'Test Software',
        vendor: 'Test Vendor',
        type: 'Subscription',
        start_date: '2024-12-31',
        currency: 'CAD',
        cost: 10000,
        status: 'Active',
        contactEmails: '<EMAIL>',
        product_name: 'Test Software',
        version: '1.0',
        annualCost: 10000
      }
    ],
    actions: {
      setFilter: jest.fn(),
      clearAllFilters: jest.fn()
    },
    getFilterValue: jest.fn(() => ''),
    hasActiveFilters: false,
    activeFilterCount: 0
  })),
  useSearch: jest.fn(() => ({
    results: [],
    query: '',
    actions: {
      setQuery: jest.fn(),
      clearSearch: jest.fn()
    }
  }))
}))

// Mock performance monitoring
jest.mock('@/lib/utils/performance', () => ({
  usePerformanceMonitor: jest.fn()
}))

// Mock components
jest.mock('@/components/reports/ReportsHeader', () => {
  return function MockReportsHeader(props: any) {
    return (
      <div data-testid="reports-header">
        <h1>Software Renewal Reports</h1>
        <button onClick={props.onExportCSV} data-testid="export-csv">
          Export CSV
        </button>
      </div>
    )
  }
})

jest.mock('@/components/reports/ReportsFilters', () => {
  return function MockReportsFilters(props: any) {
    return (
      <div data-testid="reports-filters">
        <button onClick={props.onReset} data-testid="reset-filters">
          Reset Filters
        </button>
      </div>
    )
  }
})

jest.mock('@/components/reports/ReportsVisualization', () => {
  return function MockReportsVisualization(props: any) {
    return (
      <div data-testid="reports-visualization">
        <div>Chart Type: {props.chartType}</div>
        <div>X-Axis: {props.xAxis}</div>
        <div>Y-Axis: {props.yAxis}</div>
      </div>
    )
  }
})

jest.mock('@/components/reports/ReportsTable', () => {
  return function MockReportsTable(props: any) {
    return (
      <div data-testid="reports-table">
        <div>Showing {props.showingCount} of {props.totalCount} renewals</div>
      </div>
    )
  }
})

jest.mock('@/components/ui/CollapsibleSection', () => {
  return function MockCollapsibleSection({ children, title, defaultOpen }: any) {
    return (
      <div data-testid="collapsible-section">
        <h3>{title}</h3>
        {defaultOpen && children}
      </div>
    )
  }
})

jest.mock('@/components/common/ErrorBoundary', () => {
  return function MockErrorBoundary({ children }: any) {
    return <div data-testid="error-boundary">{children}</div>
  }
})

describe('ReportsPage', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders the reports page with all sections', () => {
    render(<ReportsPage />)

    expect(screen.getByTestId('reports-header')).toBeInTheDocument()
    expect(screen.getByTestId('reports-filters')).toBeInTheDocument()
    expect(screen.getByTestId('reports-visualization')).toBeInTheDocument()
    expect(screen.getByTestId('reports-table')).toBeInTheDocument()
  })

  it('displays the correct page title', () => {
    render(<ReportsPage />)

    expect(screen.getByText('Software Renewal Reports')).toBeInTheDocument()
  })

  it('shows chart configuration', () => {
    render(<ReportsPage />)

    expect(screen.getByText('Chart Type: bar')).toBeInTheDocument()
    expect(screen.getByText('X-Axis: vendor')).toBeInTheDocument()
    expect(screen.getByText('Y-Axis: annualCost')).toBeInTheDocument()
  })

  it('handles CSV export', () => {
    // Mock URL.createObjectURL and related DOM methods
    global.URL.createObjectURL = jest.fn(() => 'mock-url')
    global.URL.revokeObjectURL = jest.fn()

    const mockLink = {
      click: jest.fn(),
      setAttribute: jest.fn(),
      style: { visibility: '' }
    }

    const createElementSpy = jest.spyOn(document, 'createElement').mockReturnValue(mockLink as any)
    const appendChildSpy = jest.spyOn(document.body, 'appendChild').mockImplementation(() => mockLink as any)
    const removeChildSpy = jest.spyOn(document.body, 'removeChild').mockImplementation(() => mockLink as any)

    render(<ReportsPage />)

    const exportButton = screen.getByTestId('export-csv')
    fireEvent.click(exportButton)

    expect(createElementSpy).toHaveBeenCalledWith('a')
    expect(mockLink.click).toHaveBeenCalled()

    // Cleanup
    createElementSpy.mockRestore()
    appendChildSpy.mockRestore()
    removeChildSpy.mockRestore()
  })

  it('handles filter reset', () => {
    render(<ReportsPage />)

    const resetButton = screen.getByTestId('reset-filters')
    fireEvent.click(resetButton)

    // The reset functionality should be called
    expect(resetButton).toBeInTheDocument()
  })

  it('renders collapsible sections', () => {
    render(<ReportsPage />)

    const sections = screen.getAllByTestId('collapsible-section')
    expect(sections).toHaveLength(2) // Filters and Visualization sections
  })
})

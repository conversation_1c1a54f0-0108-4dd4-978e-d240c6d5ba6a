# Reusable Hooks Implementation Guide

## Overview

This document outlines the new reusable hooks that have been extracted from common patterns found throughout the RenewTrack codebase. These hooks consolidate repetitive logic into reusable, well-tested utilities that improve code consistency and maintainability.

## 🎯 **New Reusable Hooks**

### 1. **useModal** - Modal State Management

**Purpose**: Consolidates modal open/close state management patterns found across components.

**Common Pattern Replaced**:

```typescript
// Before: Repeated in multiple components
const [isModalOpen, setIsModalOpen] = useState(false);
const [isLoading, setIsLoading] = useState(false);
const [error, setError] = useState<string | null>(null);

const handleOpen = () => setIsModalOpen(true);
const handleClose = () => setIsModalOpen(false);
const handleSubmit = async (data) => {
  setIsLoading(true);
  try {
    await submitData(data);
    setIsModalOpen(false);
  } catch (err) {
    setError(err.message);
  } finally {
    setIsLoading(false);
  }
};
```

**After: Using useModal**:

```typescript
const modal = useModal({
  onSubmit: async (data) => {
    await submitData(data);
  },
});

// Usage
<Modal {...modal.getModalProps()}>
  <Button onClick={modal.actions.submit} loading={modal.isLoading}>
    Submit
  </Button>
</Modal>;
```

### 2. **useSearch** - Search and Filtering

**Purpose**: Consolidates search functionality with debouncing and field-specific searching.

**Common Pattern Replaced**:

```typescript
// Before: Repeated search logic
const [searchQuery, setSearchQuery] = useState("");
const debouncedQuery = useDebounce(searchQuery, 300);

const filteredItems = useMemo(() => {
  return items.filter(
    (item) =>
      item.name.toLowerCase().includes(debouncedQuery.toLowerCase()) ||
      item.description.toLowerCase().includes(debouncedQuery.toLowerCase())
  );
}, [items, debouncedQuery]);
```

**After: Using useSearch**:

```typescript
const search = useSearch(items, {
  searchFields: ['name', 'description'],
  debounceDelay: 300
});

// Usage
<input {...search.getSearchInputProps()} />
<div>Results: {search.results.length}</div>
```

### 3. **useTableSort** - Table Sorting Logic

**Purpose**: Consolidates table sorting patterns with support for different data types.

**Common Pattern Replaced**:

```typescript
// Before: Repeated sorting logic
const [sortField, setSortField] = useState("name");
const [sortDirection, setSortDirection] = useState("asc");

const sortedData = useMemo(() => {
  return [...data].sort((a, b) => {
    const aValue = a[sortField];
    const bValue = b[sortField];
    // Complex sorting logic...
  });
}, [data, sortField, sortDirection]);
```

**After: Using useTableSort**:

```typescript
const sort = useTableSort(data, {
  initialSortField: "name",
  dateFields: ["createdAt", "updatedAt"],
  numberFields: ["amount", "quantity"],
});

// Usage
<th {...sort.getHeaderProps("name")}>Name {sort.getSortIcon("name")}</th>;
```

### 4. **useFileUpload** - File Upload with Drag & Drop

**Purpose**: Consolidates file upload patterns including drag and drop functionality.

**Common Pattern Replaced**:

```typescript
// Before: Complex drag and drop logic
const [isDragOver, setIsDragOver] = useState(false);
const [selectedFile, setSelectedFile] = useState<File | null>(null);
const [isUploading, setIsUploading] = useState(false);

const handleDrop = (e: React.DragEvent) => {
  e.preventDefault();
  setIsDragOver(false);
  const files = e.dataTransfer.files;
  // File validation and handling...
};
```

**After: Using useFileUpload**:

```typescript
const upload = useFileUpload({
  acceptedTypes: [".csv"],
  maxFileSize: 10 * 1024 * 1024,
  uploadFunction: async (files) => {
    return await uploadFiles(files);
  },
});

// Usage
<div {...upload.getDragHandlers()}>
  <input {...upload.getFileInputProps()} />
  {upload.hasFiles && <Button onClick={upload.actions.upload}>Upload</Button>}
</div>;
```

### 5. **useFilters** - Multi-Field Filtering

**Purpose**: Consolidates filtering patterns with support for multiple filter types.

**Common Pattern Replaced**:

```typescript
// Before: Complex filtering logic
const [filters, setFilters] = useState({
  vendor: "",
  type: "",
  status: "",
});

const filteredData = useMemo(() => {
  return data.filter((item) => {
    const matchesVendor = !filters.vendor || item.vendor === filters.vendor;
    const matchesType = !filters.type || item.type === filters.type;
    // More filter logic...
  });
}, [data, filters]);
```

**After: Using useFilters**:

```typescript
const filters = useFilters(data, {
  initialFilters: { vendor: '', type: '', status: '' }
});

// Usage
<select onChange={(e) => filters.actions.setFilter('vendor', e.target.value)}>
  {/* Options */}
</select>
<div>Filtered Results: {filters.filteredData.length}</div>
```

## 🔄 **Migration Strategy**

### Phase 1: Identify Components to Migrate

**High Priority Components** (Heavy pattern usage):

- `frontend/app/overview/page.tsx` - Modal and search patterns
- `frontend/app/renewals/page.tsx` - Filtering and search patterns
- `frontend/components/renewals/RenewalsTable.tsx` - Table sorting patterns
- `frontend/components/renewals/ImportCSVModal.tsx` - File upload patterns

### Phase 2: Component-by-Component Migration

1. **Replace modal patterns** in overview and renewal pages
2. **Replace search patterns** in list/table components
3. **Replace sorting patterns** in table components
4. **Replace file upload patterns** in import modals
5. **Replace filtering patterns** in data display components

### Phase 3: Remove Redundant Code

After migration, remove the old pattern implementations and consolidate imports.

## 📊 **Expected Benefits**

### Code Reduction

- **Estimated 40% reduction** in component-level state management code
- **Consistent behavior** across all modal, search, and filter implementations
- **Improved maintainability** with centralized pattern logic

### Performance Improvements

- **Optimized re-renders** with proper memoization in hooks
- **Consistent debouncing** for search and filter operations
- **Reduced bundle size** through code deduplication

### Developer Experience

- **Faster development** with pre-built patterns
- **Consistent APIs** across all similar functionality
- **Better testing** with centralized hook logic

## 🧪 **Testing Strategy**

Each hook includes comprehensive test coverage:

- **Unit tests** for all hook functionality
- **Integration tests** with React components
- **Performance tests** for debouncing and memoization
- **Accessibility tests** for UI patterns

## 📝 **Usage Examples**

### Complete Modal Example

```typescript
function AddRenewalModal() {
  const modal = useModal({
    onSubmit: async (renewalData) => {
      await createRenewal(renewalData);
      toast.success("Renewal created successfully");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  return (
    <>
      <Button onClick={modal.actions.open}>Add Renewal</Button>
      <Modal {...modal.getModalProps()}>
        <RenewalForm
          onSubmit={modal.actions.submit}
          loading={modal.isLoading}
        />
      </Modal>
    </>
  );
}
```

### Complete Search and Filter Example

```typescript
function RenewalsList({ renewals }: { renewals: Renewal[] }) {
  const search = useSearch(renewals, {
    searchFields: ["name", "vendor", "product_name"],
  });

  const filters = useFilters(search.results, {
    initialFilters: { vendor: "", type: "", status: "" },
  });

  const sort = useTableSort(filters.filteredData, {
    dateFields: ["start_date"],
    numberFields: ["amount"],
  });

  return (
    <div>
      <input {...search.getSearchInputProps()} />
      <FilterBar filters={filters} />
      <RenewalsTable data={sort.sortedData} sortProps={sort} />
    </div>
  );
}
```

## 🎯 **Next Steps**

1. **Begin Migration**: Start with high-impact components
2. **Update Tests**: Ensure all migrated components maintain test coverage
3. **Update Documentation**: Update component documentation to reflect new patterns
4. **Performance Monitoring**: Monitor performance improvements after migration
5. **Team Training**: Ensure team understands new hook patterns and usage

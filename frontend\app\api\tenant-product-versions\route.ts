/**
 * Tenant Product Versions API Endpoint
 * 
 * Provides CRUD operations for tenant-specific product versions
 * GET /api/tenant-product-versions?product_id=xxx - Returns active versions for the product
 * POST /api/tenant-product-versions - Creates a new version for the tenant
 */

import { createApiRoute } from '@/lib/api/route-factory';
import { z } from 'zod';

// Validation schema for creating product versions
const createVersionSchema = z.object({
  product_id: z.string().min(1, 'Product ID is required'),
  version: z.string().min(1, 'Version is required').max(50),
  release_date: z.string().optional(),
  notes: z.string().optional(),
  is_current: z.boolean().optional().default(false),
});

const querySchema = z.object({
  product_id: z.string().optional()
});

// GET /api/tenant-product-versions - Get versions for a product
export const GET = createApiRoute('GET', {
  requireAuth: true,
  requireTenant: true,
  querySchema,
  handler: async (context) => {
    const { tenant, query: queryParams } = context;
    if (!tenant) {
      throw new Error('Tenant context is required');
    }
    const { product_id: productId } = queryParams || {};
    console.log('[TENANT-PRODUCT-VERSIONS-API] GET request received');

    let sqlQuery: string;
    let sqlParams: any[];

    if (productId) {
      // Fetch versions for specific product
      sqlQuery = `
        SELECT
          pv.id,
          pv.version,
          pv.release_date,
          pv.notes,
          pv.is_current,
          pv.product_id,
          p.name as product_name,
          v.name as vendor_name,
          pv.created_on,
          pv.changed_on
        FROM "${tenant.tenantSchema}".tenant_product_versions pv
        JOIN "${tenant.tenantSchema}".tenant_products p ON pv.product_id = p.id
        JOIN "${tenant.tenantSchema}".tenant_vendors v ON p.vendor_id = v.id
        WHERE pv.product_id = $1 AND pv.is_deleted = false
        ORDER BY pv.is_current DESC, pv.version ASC
      `;
      sqlParams = [productId];
    } else {
      // Fetch all versions
      sqlQuery = `
        SELECT
          pv.id,
          pv.version,
          pv.release_date,
          pv.notes,
          pv.is_current,
          pv.product_id,
          p.name as product_name,
          v.name as vendor_name,
          pv.created_on,
          pv.changed_on
        FROM "${tenant.tenantSchema}".tenant_product_versions pv
        JOIN "${tenant.tenantSchema}".tenant_products p ON pv.product_id = p.id
        JOIN "${tenant.tenantSchema}".tenant_vendors v ON p.vendor_id = v.id
        WHERE pv.is_deleted = false
        ORDER BY v.name ASC, p.name ASC, pv.is_current DESC, pv.version ASC
      `;
      sqlParams = [];
    }

    const result = await context.executeQuery!(sqlQuery, sqlParams);

    if (!result.success) {
      console.error('[TENANT-PRODUCT-VERSIONS-API] Database error:', result.error);
      throw new Error('Failed to fetch product versions');
    }

    console.log(`[TENANT-PRODUCT-VERSIONS-API] Found ${result.data?.length || 0} versions for product ${productId}`);

    return result.data || [];
  }
});

// POST /api/tenant-product-versions - Create a new product version
export const POST = createApiRoute('POST', {
  requireAuth: true,
  requireTenant: true,
  bodySchema: z.object({
    product_id: z.number(),
    version: z.string(),
    release_date: z.string().optional(),
    end_of_life_date: z.string().optional(),
    is_current: z.boolean().optional(),
    notes: z.string().optional()
  }),
  handler: async (context) => {
    const { body: versionData, tenant } = context;
    if (!tenant || !versionData) {
      throw new Error('Tenant context and request body are required');
    }
    console.log('[TENANT-PRODUCT-VERSIONS-API] POST request received');

    console.log('[TENANT-PRODUCT-VERSIONS-API] Request body:', versionData);

    // Verify product exists
    const productQuery = `
      SELECT product_id FROM "${tenant.tenantSchema}".tenant_products
      WHERE product_id = $1 AND is_deleted = false
    `;

    const productResult = await context.executeQuerySingle!(
      productQuery,
      [versionData.product_id]
    );

    if (!productResult.success || !productResult.data) {
      throw new Error('Product not found');
    }

    // Check if version already exists for this product
    const existingVersionQuery = `
      SELECT version_id FROM "${tenant.tenantSchema}".tenant_product_versions
      WHERE product_id = $1 AND LOWER(version) = LOWER($2) AND is_deleted = false
    `;

    const existingResult = await context.executeQuerySingle!(
      existingVersionQuery,
      [versionData.product_id, versionData.version]
    );

    if (existingResult.success && existingResult.data) {
      throw new Error('A version with this name already exists for this product');
    }

      // If this is set as current, unset other current versions for this product
      if (versionData.is_current) {
        const updateCurrentQuery = `
          UPDATE "${tenant.tenantSchema}".tenant_product_versions
          SET iscurrent = false, changed_on = CURRENT_TIMESTAMP
          WHERE product_id = $1 AND iscurrent = true AND is_deleted = false
        `;

        await context.executeQuery!(updateCurrentQuery, [versionData.product_id]);
      }

      // Create the version
      const insertQuery = `
        INSERT INTO "${tenant.tenantSchema}".tenant_product_versions (
          product_id, version, releasedate, notes, iscurrent,
          created_by, changed_by, created_on, changed_on
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
        ) RETURNING version_id as id, product_id, version, releasedate as release_date, notes, iscurrent as is_current, created_on as created_on
      `;

      const releaseDate = versionData.release_date ? new Date(versionData.release_date) : null;

      const insertResult = await context.executeQuerySingle!(
        insertQuery,
        [
          versionData.product_id,
          versionData.version,
          releaseDate,
          versionData.notes || null,
          versionData.is_current,
          context.user?.userId || 'system',
          context.user?.userId || 'system'
        ]
      );

      if (!insertResult.success || !insertResult.data) {
        console.error('[TENANT-PRODUCT-VERSIONS-API] Insert error:', insertResult.error);
        throw new Error('Failed to create product version');
      }

      console.log('[TENANT-PRODUCT-VERSIONS-API] Product version created successfully:', insertResult.data);

    return insertResult.data;
  }
});

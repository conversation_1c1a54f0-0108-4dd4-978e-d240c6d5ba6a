/**
 * Vendor Comparison API Route
 * 
 * Provides side-by-side vendor comparison data including:
 * - Performance metrics comparison
 * - Cost analysis
 * - Product portfolio comparison
 * - Risk assessment comparison
 */

import { createApiRoute } from '@/lib/api/route-factory'
import { z } from 'zod'

const vendorComparisonSchema = z.object({
  vendor_ids: z.array(z.string()).min(2).max(5), // Compare 2-5 vendors
  include_products: z.boolean().optional().default(true),
  include_cost_breakdown: z.boolean().optional().default(true),
  period_months: z.number().min(1).max(24).optional().default(12)
})

export const POST = createApiRoute({
  requireAuth: true,
  requireTenant: true,
  bodySchema: vendorComparisonSchema,
  handler: async (context) => {
    const { tenant, body } = context
    if (!tenant || !body) {
      throw new Error('Tenant context and request body are required')
    }
    const { vendor_ids, include_products, include_cost_breakdown, period_months } = body

    try {
      // Get vendor comparison data
      const comparisonQuery = `
        WITH vendor_comparison AS (
          SELECT 
            v.id as vendor_id,
            v.name as vendor_name,
            v.display_name,
            v.contact_email,
            v.website,
            v.city,
            v.state,
            v.country,
            
            -- Renewal metrics
            COUNT(r.id) as total_renewals,
            COUNT(DISTINCT r.product_id) as unique_products,
            COALESCE(SUM(r.annual_cost), 0) as total_annual_cost,
            COALESCE(AVG(r.annual_cost), 0) as avg_renewal_cost,
            COALESCE(MIN(r.annual_cost), 0) as min_renewal_cost,
            COALESCE(MAX(r.annual_cost), 0) as max_renewal_cost,
            
            -- Time-based metrics
            COUNT(CASE WHEN r.due_date BETWEEN CURRENT_DATE AND CURRENT_DATE + INTERVAL '90 days' THEN 1 END) as upcoming_renewals_90d,
            COUNT(CASE WHEN r.due_date BETWEEN CURRENT_DATE AND CURRENT_DATE + INTERVAL '30 days' THEN 1 END) as upcoming_renewals_30d,
            COUNT(CASE WHEN r.due_date < CURRENT_DATE THEN 1 END) as overdue_renewals,
            
            -- Performance metrics
            COALESCE(AVG(r.reliability_score), 85) as avg_reliability_score,
            COALESCE(STDDEV(r.reliability_score), 0) as reliability_score_stddev,
            
            -- Relationship metrics
            MIN(r.created_on) as first_renewal_date,
            MAX(r.changed_on) as last_activity_date,
            EXTRACT(DAYS FROM (CURRENT_DATE - MIN(r.created_on))) as relationship_days,
            
            -- Currency and category diversity
            COUNT(DISTINCT r.currency) as currency_count,
            ARRAY_AGG(DISTINCT r.currency) FILTER (WHERE r.currency IS NOT NULL) as currencies,
            COUNT(DISTINCT p.category) as category_count,
            ARRAY_AGG(DISTINCT p.category) FILTER (WHERE p.category IS NOT NULL) as categories,
            
            -- Recent activity (last N months)
            COUNT(CASE WHEN r.created_on >= CURRENT_DATE - INTERVAL '$1 months' THEN 1 END) as recent_renewals,
            COALESCE(SUM(CASE WHEN r.created_on >= CURRENT_DATE - INTERVAL '$1 months' THEN r.annual_cost END), 0) as recent_spend
            
          FROM "${tenant.tenantSchema}".vendors v
          LEFT JOIN "${tenant.tenantSchema}".renewals r ON v.id = r.vendor_id AND r.status = 'A'
          LEFT JOIN "${tenant.tenantSchema}".products p ON r.product_id = p.id
          WHERE v.id = ANY($2) AND v.is_deleted = false
          GROUP BY v.id, v.name, v.display_name, v.contact_email, v.website, v.city, v.state, v.country
        )
        SELECT * FROM vendor_comparison
        ORDER BY total_annual_cost DESC
      `

      const comparisonResult = await context.executeQuery!(comparisonQuery, [period_months, vendor_ids])

      if (!comparisonResult.success) {
        throw new Error('Failed to fetch vendor comparison data')
      }

      const vendors = comparisonResult.data || []

      // Product portfolio comparison if requested
      let productComparison = null
      if (include_products) {
        const productsQuery = `
          SELECT 
            v.id as vendor_id,
            v.name as vendor_name,
            p.id as product_id,
            p.name as product_name,
            p.category,
            p.description,
            COUNT(r.id) as renewal_count,
            SUM(r.annual_cost) as total_cost,
            AVG(r.annual_cost) as avg_cost,
            MIN(r.due_date) as next_renewal_date,
            MAX(r.changed_on) as last_updated
          FROM "${tenant.tenantSchema}".vendors v
          JOIN "${tenant.tenantSchema}".renewals r ON v.id = r.vendor_id AND r.status = 'A'
          JOIN "${tenant.tenantSchema}".products p ON r.product_id = p.id
          WHERE v.id = ANY($1)
          GROUP BY v.id, v.name, p.id, p.name, p.category, p.description
          ORDER BY v.name, total_cost DESC
        `

        const productsResult = await context.executeQuery!(productsQuery, [vendor_ids])
        productComparison = productsResult.success ? productsResult.data : []
      }

      // Cost breakdown analysis if requested
      let costBreakdown = null
      if (include_cost_breakdown) {
        const costQuery = `
          SELECT 
            v.id as vendor_id,
            v.name as vendor_name,
            p.category,
            COUNT(r.id) as renewal_count,
            SUM(r.annual_cost) as category_total,
            AVG(r.annual_cost) as category_avg,
            MIN(r.annual_cost) as category_min,
            MAX(r.annual_cost) as category_max
          FROM "${tenant.tenantSchema}".vendors v
          JOIN "${tenant.tenantSchema}".renewals r ON v.id = r.vendor_id AND r.status = 'A'
          LEFT JOIN "${tenant.tenantSchema}".products p ON r.product_id = p.id
          WHERE v.id = ANY($1)
          GROUP BY v.id, v.name, p.category
          ORDER BY v.name, category_total DESC
        `

        const costResult = await context.executeQuery!(costQuery, [vendor_ids])
        costBreakdown = costResult.success ? costResult.data : []
      }

      // Calculate comparison metrics
      const totalSpend = vendors.reduce((sum: number, v: any) => sum + (v.total_annual_cost || 0), 0)
      const avgReliability = vendors.reduce((sum: number, v: any) => sum + (v.avg_reliability_score || 0), 0) / vendors.length

      // Generate comparison insights
      const insights = generateComparisonInsights(vendors)

      return {
        comparison_summary: {
          vendor_count: vendors.length,
          total_combined_spend: totalSpend,
          avg_reliability_score: avgReliability,
          period_months,
          generated_at: new Date().toISOString()
        },
        vendors,
        product_comparison: productComparison,
        cost_breakdown: costBreakdown,
        insights,
        metadata: {
          vendor_ids,
          includes_products: include_products,
          includes_cost_breakdown: include_cost_breakdown,
          period_months
        }
      }

    } catch (error) {
      console.error('[VENDOR-COMPARISON-API] Error:', error)
      throw error
    }
  }
})

// Helper function to generate comparison insights
function generateComparisonInsights(vendors: any[]): any {
  if (vendors.length < 2) return null

  const insights = {
    cost_leader: null as any,
    most_reliable: null as any,
    most_diverse: null as any,
    longest_relationship: null as any,
    recommendations: [] as string[]
  }

  // Find cost leader (highest total spend)
  insights.cost_leader = vendors.reduce((max, vendor) => 
    vendor.total_annual_cost > (max?.total_annual_cost || 0) ? vendor : max
  )

  // Find most reliable vendor
  insights.most_reliable = vendors.reduce((max, vendor) => 
    vendor.avg_reliability_score > (max?.avg_reliability_score || 0) ? vendor : max
  )

  // Find most diverse vendor (most product categories)
  insights.most_diverse = vendors.reduce((max, vendor) => 
    vendor.category_count > (max?.category_count || 0) ? vendor : max
  )

  // Find longest relationship
  insights.longest_relationship = vendors.reduce((max, vendor) => 
    vendor.relationship_days > (max?.relationship_days || 0) ? vendor : max
  )

  // Generate recommendations
  const costVariance = Math.max(...vendors.map(v => v.total_annual_cost)) - 
                      Math.min(...vendors.map(v => v.total_annual_cost))
  
  if (costVariance > 10000) {
    insights.recommendations.push('Significant cost differences detected - consider renegotiating with higher-cost vendors')
  }

  const reliabilityRange = Math.max(...vendors.map(v => v.avg_reliability_score)) - 
                          Math.min(...vendors.map(v => v.avg_reliability_score))
  
  if (reliabilityRange > 20) {
    insights.recommendations.push('Large reliability score variance - consider performance improvement plans for lower-scoring vendors')
  }

  const overdueVendors = vendors.filter(v => v.overdue_renewals > 0)
  if (overdueVendors.length > 0) {
    insights.recommendations.push(`${overdueVendors.length} vendor(s) have overdue renewals requiring immediate attention`)
  }

  return insights
}

/**
 * Application Constants
 * 
 * Centralized constants to avoid magic numbers and hardcoded values
 */

// Pagination defaults
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  DEFAULT_PAGE: 1,
} as const

// Time constants (in milliseconds)
export const TIME = {
  SECOND: 1000,
  MINUTE: 60 * 1000,
  HOUR: 60 * 60 * 1000,
  DAY: 24 * 60 * 60 * 1000,
  WEEK: 7 * 24 * 60 * 60 * 1000,
} as const

// Timeout values
export const TIMEOUTS = {
  API_REQUEST: 30 * TIME.SECOND,
  DATABASE_QUERY: 30 * TIME.SECOND,
  FILE_UPLOAD: 5 * TIME.MINUTE,
  AUTHENTICATION: 10 * TIME.SECOND,
} as const

// Retry configurations
export const RETRY = {
  DEFAULT_ATTEMPTS: 3,
  DEFAULT_DELAY: 1000,
  MAX_DELAY: 10000,
  BACKOFF_MULTIPLIER: 2,
} as const

// Form validation constants
export const VALIDATION = {
  MIN_PASSWORD_LENGTH: 8,
  MAX_PASSWORD_LENGTH: 128,
  MIN_NAME_LENGTH: 1,
  MAX_NAME_LENGTH: 255,
  MAX_DESCRIPTION_LENGTH: 1000,
  MAX_NOTES_LENGTH: 2000,
  EMAIL_REGEX: /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/,
} as const

// File upload constants
export const FILE_UPLOAD = {
  MAX_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_TYPES: ['text/csv', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
  CHUNK_SIZE: 1024 * 1024, // 1MB chunks
} as const

// Dashboard constants
export const DASHBOARD = {
  DEFAULT_DAYS_FILTER: 5,
  RENEWAL_DUE_DAYS: 30,
  RECENT_ACTIVITY_LIMIT: 10,
  CHART_COLORS: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'],
} as const

// Status values
export const STATUS = {
  ACTIVE: 'A',
  INACTIVE: 'I',
  PENDING: 'P',
  DELETED: 'D',
} as const

// User roles
export const ROLES = {
  ADMIN: 'admin',
  USER: 'user',
  VIEWER: 'viewer',
  SUPER_ADMIN: 'super_admin',
} as const

// Alert defaults
export const ALERTS = {
  DEFAULT_DAYS_BEFORE: 30,
  MIN_DAYS_BEFORE: 1,
  MAX_DAYS_BEFORE: 365,
  MAX_RECIPIENTS: 10,
} as const

// Currency defaults
export const CURRENCY = {
  DEFAULT_CODE: 'CAD',
  DEFAULT_SYMBOL: '$',
  DECIMAL_PLACES: 2,
} as const

// Renewal defaults
export const RENEWAL = {
  DEFAULT_LICENSE_COUNT: 1,
  MIN_LICENSE_COUNT: 1,
  MAX_LICENSE_COUNT: 999999,
  DEFAULT_UNIT_COST: 0,
} as const

// UI constants
export const UI = {
  TOAST_DURATION: 5000,
  MODAL_ANIMATION_DURATION: 200,
  DEBOUNCE_DELAY: 300,
  SEARCH_MIN_CHARS: 2,
} as const

// Local storage keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_PREFERENCES: 'user_preferences',
  THEME: 'theme',
  SIDEBAR_COLLAPSED: 'sidebar_collapsed',
} as const

// Database defaults
export const DATABASE = {
  DEFAULT_HOST: 'localhost',
  DEFAULT_PORT: 5432,
  DEFAULT_NAME: 'renewtrack',
  DEFAULT_USER: 'postgres',
} as const

// Session configuration
export const SESSION = {
  DEFAULT_MAX_AGE: 604800, // 7 days in seconds
  TOKEN_REFRESH_THRESHOLD: 300, // 5 minutes in seconds
} as const

// Environment configuration
export const ENVIRONMENT = {
  DEVELOPMENT: 'development',
  PRODUCTION: 'production',
  TEST: 'test',
} as const

// Error messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error. Please check your connection and try again.',
  UNAUTHORIZED: 'You are not authorized to perform this action.',
  FORBIDDEN: 'Access denied. You do not have permission to access this resource.',
  NOT_FOUND: 'The requested resource was not found.',
  VALIDATION_ERROR: 'Please check your input and try again.',
  SERVER_ERROR: 'An unexpected error occurred. Please try again later.',
  TIMEOUT_ERROR: 'Request timed out. Please try again.',
} as const

// Success messages
export const SUCCESS_MESSAGES = {
  CREATED: 'Successfully created',
  UPDATED: 'Successfully updated',
  DELETED: 'Successfully deleted',
  SAVED: 'Successfully saved',
  IMPORTED: 'Successfully imported',
  EXPORTED: 'Successfully exported',
} as const

/**
 * Reusable Renewal Modal Component (Refactored with Design System)
 *
 * Two-step modal for adding/editing renewals:
 * 1. Renewal Details
 * 2. Set Up Alerts
 *
 * Now uses the unified design system components for consistency.
 * Can be used for both Add and Edit operations.
 */

'use client'

import React, { useState, useCallback, memo } from 'react'
import { BaseComponentProps } from '@/lib/types'
import { Mo<PERSON>, ModalFooter } from '@/components/ui/Modal'
import { Button } from '@/components/ui/Button'
import { useToast } from '@/components/ui/Toast'
import RenewalDetailsStep from './steps/RenewalDetailsStep'
import SetupAlertsStep from './steps/SetupAlertsStep'
import { RenewalItem } from '@/components/ui/RenewalItemsManager'

// Helper function to create a default renewal item
const createDefaultRenewalItem = (): RenewalItem => ({
  productId: '',
  product_name: '',
  versionId: '',
  versionName: '',
  quantity: 1,
  unit: '',
  licenseCount: 1,
  totalCost: 0,
  costCode: '',
  notes: ''
})

export interface RenewalNote {
  id?: string
  text: string
  createdBy: string
  createdAt: string
}

export interface RenewalFormData {
  // Renewal-level fields (shown first)
  renewalName: string
  vendorId: string // Vendor at renewal level
  vendorName: string // For display purposes
  renewalTypeId: number | null // Store ID, not name
  start_date: string
  department: string
  purchaseTypeId: number | null // Store ID, not name
  assignedUsers: string[] // Multiple users, store IDs
  reseller: string
  currencyId: string // Store ID, not name
  costCode: string
  description: string
  notes: RenewalNote[] // Multiple notes with user/timestamp

  // Item-level fields (shown after renewal fields)
  renewalItems: RenewalItem[]
  totalCost: number // Calculated from renewal items
}

export interface AlertFormData {
  daysBeforeRenewal: number
  emailRecipients: string[]
  customMessage: string
  enabled: boolean
}

interface RenewalModalProps extends BaseComponentProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (renewalData: RenewalFormData, alertData: AlertFormData[]) => Promise<void>
  mode?: 'add' | 'edit'
  initialData?: Partial<RenewalFormData>
  title?: string
}

const RenewalModal: React.FC<RenewalModalProps> = memo(({
  isOpen,
  onClose,
  onSubmit,
  mode = 'add',
  initialData = {},
  title,
  className = '',
  'data-testid': testId
}) => {
  const [currentStep, setCurrentStep] = useState<1 | 2>(1)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const toast = useToast()
  
  // Form data state with initial data support
  const [renewalData, setRenewalData] = useState<RenewalFormData>({
    // Renewal-level fields
    renewalName: initialData.renewalName || '',
    vendorId: initialData.vendorId || '',
    vendorName: initialData.vendorName || '',
    renewalTypeId: initialData.renewalTypeId || null,
    start_date: initialData.start_date || '',
    department: initialData.department || '',
    purchaseTypeId: initialData.purchaseTypeId || null,
    assignedUsers: initialData.assignedUsers || [],
    reseller: initialData.reseller || '',
    currencyId: initialData.currencyId || '',
    costCode: initialData.costCode || '',
    description: initialData.description || '',
    notes: Array.isArray(initialData.notes) ? initialData.notes : [],

    // Item-level fields
    renewalItems: initialData.renewalItems || [createDefaultRenewalItem()],
    totalCost: initialData.totalCost || 0
  })

  const [alertsData, setAlertsData] = useState<AlertFormData[]>([
    {
      daysBeforeRenewal: 30,
      emailRecipients: [],
      customMessage: '',
      enabled: true
    }
  ])

  // Handle step navigation
  const handleNextStep = useCallback(() => {
    if (currentStep === 1) {
      setCurrentStep(2)
    }
  }, [currentStep])

  const handleBackToDetails = useCallback(() => {
    setCurrentStep(1)
  }, [])

  // Handle form submission
  const handleSubmit = useCallback(async () => {
    setIsSubmitting(true)
    try {
      await onSubmit(renewalData, alertsData)
      toast.success('Renewal created successfully!')
      onClose()
      // Reset form
      setCurrentStep(1)
      setRenewalData({
        // Multiple renewal items
        renewalItems: [createDefaultRenewalItem()],

        // Other fields
        renewalName: '',
        vendorId: '',
        vendorName: '',
        renewalTypeId: null,
        department: '',
        purchaseTypeId: null,
        start_date: '',
        assignedUsers: [],
        reseller: '',
        currencyId: '',
        totalCost: 0,
        costCode: '',
        description: '',
        notes: []
      })
      setAlertsData([{
        daysBeforeRenewal: 30,
        emailRecipients: [],
        customMessage: '',
        enabled: true
      }])
    } catch (error) {
      console.error('Error submitting renewal:', error)
      toast.error(`Failed to create renewal: ${error instanceof Error ? error.message : 'Unknown error'}`)
    } finally {
      setIsSubmitting(false)
    }
  }, [renewalData, alertsData, onSubmit, onClose, toast])

  // Handle modal close
  const handleClose = useCallback(() => {
    if (!isSubmitting) {
      onClose()
      setCurrentStep(1)
    }
  }, [isSubmitting, onClose])

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title={title || (currentStep === 1 ? `${mode === 'edit' ? 'Edit' : 'Add New'} Renewal` : 'Set Up Alerts')}
      subtitle={currentStep === 1
        ? `${mode === 'edit' ? 'Update' : 'Enter'} the details of the renewal you want to track.`
        : 'Configure when and how you want to be notified about this renewal.'
      }
      size="lg"
      closeOnOverlayClick={!isSubmitting}
      data-testid={testId}
      className={className}
      footer={
        <ModalFooter justify="between">
          <div>
            {currentStep === 2 && (
              <Button
                variant="outline"
                onClick={handleBackToDetails}
                disabled={isSubmitting}
              >
                Back to Details
              </Button>
            )}
          </div>

          <div style={{ display: 'flex', gap: '12px' }}>
            <Button
              variant="outline"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>

            {currentStep === 1 ? (
              <Button
                variant="primary"
                onClick={handleNextStep}
                disabled={!renewalData.renewalName || !renewalData.start_date || !renewalData.vendorId || renewalData.renewalItems.length === 0}
              >
                Next: Set Up Alerts
              </Button>
            ) : (
              <Button
                variant="primary"
                onClick={handleSubmit}
                isLoading={isSubmitting}
                disabled={isSubmitting}
              >
                {mode === 'edit' ? 'Update Renewal' : 'Create Renewal'}
              </Button>
            )}
          </div>
        </ModalFooter>
      }
    >
      {/* Step Indicator */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        gap: '32px',
        marginBottom: '32px',
        padding: '16px',
        backgroundColor: 'var(--color-background-tertiary)',
        borderRadius: 'var(--border-radius-md)'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          color: currentStep === 1 ? 'var(--color-primary-600)' : 'var(--color-success-600)'
        }}>
          <div style={{
            width: '24px',
            height: '24px',
            borderRadius: '50%',
            backgroundColor: currentStep === 1 ? 'var(--color-primary-500)' : 'var(--color-success-500)',
            color: 'white',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '12px',
            fontWeight: 600
          }}>
            {currentStep === 1 ? '1' : '✓'}
          </div>
          <span style={{ fontSize: '14px', fontWeight: 500 }}>Renewal Details</span>
        </div>

        <div style={{
          width: '32px',
          height: '2px',
          backgroundColor: currentStep === 2 ? 'var(--color-primary-300)' : 'var(--color-secondary-200)'
        }} />

        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          color: currentStep === 2 ? 'var(--color-primary-600)' : 'var(--color-text-tertiary)'
        }}>
          <div style={{
            width: '24px',
            height: '24px',
            borderRadius: '50%',
            backgroundColor: currentStep === 2 ? 'var(--color-primary-500)' : 'var(--color-secondary-200)',
            color: currentStep === 2 ? 'white' : 'var(--color-text-tertiary)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '12px',
            fontWeight: 600
          }}>
            2
          </div>
          <span style={{ fontSize: '14px', fontWeight: 500 }}>Set Up Alerts</span>
        </div>
      </div>

      {/* Modal Content */}
      {currentStep === 1 ? (
        <RenewalDetailsStep
          data={renewalData}
          onChange={setRenewalData}
        />
      ) : (
        <SetupAlertsStep
          data={alertsData}
          onChange={setAlertsData}
        />
      )}


    </Modal>
  )
})

RenewalModal.displayName = 'RenewalModal'

// Backward compatibility wrapper for Add Renewal
const AddRenewalModal: React.FC<Omit<RenewalModalProps, 'mode'>> = memo((props) => (
  <RenewalModal {...props} mode="add" />
))

AddRenewalModal.displayName = 'AddRenewalModal'

// Export both the generic modal and the specific wrapper
export { RenewalModal }
export default AddRenewalModal

/**
 * Enhanced Logging System
 * 
 * Comprehensive logging with structured data, log levels, and multiple outputs
 */

interface LogEntry {
  timestamp: string
  level: LogLevel
  message: string
  context?: Record<string, any>
  userId?: string
  sessionId?: string
  requestId?: string
  component?: string
  action?: string
  duration?: number
  error?: {
    name: string
    message: string
    stack?: string
  }
}

type LogLevel = 'debug' | 'info' | 'warn' | 'error' | 'fatal'

interface LoggerConfig {
  level: LogLevel
  enableConsole: boolean
  enableRemote: boolean
  remoteEndpoint?: string
  bufferSize: number
  flushInterval: number
  includeStackTrace: boolean
  maskSensitiveData: boolean
}

class Logger {
  private static instance: Logger
  private config: LoggerConfig
  private buffer: LogEntry[] = []
  private flushTimer?: NodeJS.Timeout
  private sessionId: string
  
  private readonly LOG_LEVELS = {
    debug: 0,
    info: 1,
    warn: 2,
    error: 3,
    fatal: 4
  }

  private constructor() {
    this.sessionId = this.generateSessionId()
    this.config = {
      level: (process.env.NODE_ENV === 'development' ? 'debug' : 'info') as LogLevel,
      enableConsole: true,
      enableRemote: process.env.NODE_ENV === 'production',
      remoteEndpoint: '/api/logs',
      bufferSize: 100,
      flushInterval: 30000, // 30 seconds
      includeStackTrace: process.env.NODE_ENV === 'development',
      maskSensitiveData: true
    }
    
    this.startFlushTimer()
  }

  static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger()
    }
    return Logger.instance
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private shouldLog(level: LogLevel): boolean {
    return this.LOG_LEVELS[level] >= this.LOG_LEVELS[this.config.level]
  }

  private maskSensitiveData(data: any): any {
    if (!this.config.maskSensitiveData) return data
    
    const sensitiveKeys = ['password', 'token', 'secret', 'key', 'auth', 'credential']
    
    if (typeof data === 'object' && data !== null) {
      const masked = { ...data }
      
      for (const key in masked) {
        if (sensitiveKeys.some(sensitive => key.toLowerCase().includes(sensitive))) {
          masked[key] = '***MASKED***'
        } else if (typeof masked[key] === 'object') {
          masked[key] = this.maskSensitiveData(masked[key])
        }
      }
      
      return masked
    }
    
    return data
  }

  private createLogEntry(
    level: LogLevel,
    message: string,
    context?: Record<string, any>,
    error?: Error
  ): LogEntry {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      sessionId: this.sessionId
    }

    if (context) {
      entry.context = this.maskSensitiveData(context)
    }

    if (error) {
      entry.error = {
        name: error.name,
        message: error.message,
        ...(this.config.includeStackTrace && { stack: error.stack })
      }
    }

    // Add user context if available
    if (typeof window !== 'undefined') {
      const userContext = this.getUserContext()
      if (userContext.userId) entry.userId = userContext.userId
      if (userContext.requestId) entry.requestId = userContext.requestId
    }

    return entry
  }

  private getUserContext(): { userId?: string; requestId?: string } {
    try {
      // Try to get user context from various sources
      const context: { userId?: string; requestId?: string } = {}
      
      // Get from session storage or local storage
      const userSession = sessionStorage.getItem('user-session')
      if (userSession) {
        const parsed = JSON.parse(userSession)
        context.userId = parsed.userId
      }
      
      // Get request ID from meta tag or header
      const requestIdMeta = document.querySelector('meta[name="request-id"]')
      if (requestIdMeta) {
        context.requestId = requestIdMeta.getAttribute('content') || undefined
      }
      
      return context
    } catch {
      return {}
    }
  }

  private outputToConsole(entry: LogEntry): void {
    if (!this.config.enableConsole) return

    const { timestamp, level, message, context, error } = entry
    const prefix = `[${timestamp}] [${level.toUpperCase()}]`
    
    switch (level) {
      case 'debug':
        console.debug(prefix, message, context, error)
        break
      case 'info':
        console.info(prefix, message, context, error)
        break
      case 'warn':
        console.warn(prefix, message, context, error)
        break
      case 'error':
      case 'fatal':
        console.error(prefix, message, context, error)
        break
    }
  }

  private addToBuffer(entry: LogEntry): void {
    this.buffer.push(entry)
    
    if (this.buffer.length >= this.config.bufferSize) {
      this.flush()
    }
  }

  private startFlushTimer(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer)
    }
    
    this.flushTimer = setInterval(() => {
      this.flush()
    }, this.config.flushInterval)
  }

  private async flush(): Promise<void> {
    if (this.buffer.length === 0 || !this.config.enableRemote) return

    const logsToSend = [...this.buffer]
    this.buffer = []

    try {
      if (this.config.remoteEndpoint) {
        await fetch(this.config.remoteEndpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ logs: logsToSend }),
          credentials: 'include'
        })
      }
    } catch (error) {
      // If remote logging fails, put logs back in buffer and log to console
      this.buffer.unshift(...logsToSend)
      console.error('Failed to send logs to remote endpoint:', error)
    }
  }

  // Public logging methods
  debug(message: string, context?: Record<string, any>): void {
    if (!this.shouldLog('debug')) return
    
    const entry = this.createLogEntry('debug', message, context)
    this.outputToConsole(entry)
    this.addToBuffer(entry)
  }

  info(message: string, context?: Record<string, any>): void {
    if (!this.shouldLog('info')) return
    
    const entry = this.createLogEntry('info', message, context)
    this.outputToConsole(entry)
    this.addToBuffer(entry)
  }

  warn(message: string, context?: Record<string, any>): void {
    if (!this.shouldLog('warn')) return
    
    const entry = this.createLogEntry('warn', message, context)
    this.outputToConsole(entry)
    this.addToBuffer(entry)
  }

  error(message: string, error?: Error, context?: Record<string, any>): void {
    if (!this.shouldLog('error')) return
    
    const entry = this.createLogEntry('error', message, context, error)
    this.outputToConsole(entry)
    this.addToBuffer(entry)
  }

  fatal(message: string, error?: Error, context?: Record<string, any>): void {
    const entry = this.createLogEntry('fatal', message, context, error)
    this.outputToConsole(entry)
    this.addToBuffer(entry)
    
    // Immediately flush fatal errors
    this.flush()
  }

  // Performance logging
  time(label: string): void {
    if (typeof window !== 'undefined') {
      console.time(label)
    }
  }

  timeEnd(label: string, context?: Record<string, any>): void {
    if (typeof window !== 'undefined') {
      console.timeEnd(label)
      this.info(`Performance: ${label} completed`, context)
    }
  }

  // Component lifecycle logging
  componentMount(componentName: string, props?: Record<string, any>): void {
    this.debug(`Component mounted: ${componentName}`, { 
      component: componentName, 
      action: 'mount',
      props: props ? this.maskSensitiveData(props) : undefined
    })
  }

  componentUnmount(componentName: string): void {
    this.debug(`Component unmounted: ${componentName}`, { 
      component: componentName, 
      action: 'unmount'
    })
  }

  // API logging
  apiRequest(method: string, url: string, context?: Record<string, any>): void {
    this.info(`API Request: ${method} ${url}`, {
      action: 'api_request',
      method,
      url,
      ...context
    })
  }

  apiResponse(method: string, url: string, status: number, duration: number, context?: Record<string, any>): void {
    const level = status >= 400 ? 'error' : 'info'
    this[level](`API Response: ${method} ${url} - ${status}`, undefined, {
      action: 'api_response',
      method,
      url,
      status,
      duration,
      ...context
    })
  }

  // User action logging
  userAction(action: string, context?: Record<string, any>): void {
    this.info(`User action: ${action}`, {
      action: 'user_action',
      userAction: action,
      ...context
    })
  }

  // Configuration
  setLevel(level: LogLevel): void {
    this.config.level = level
  }

  setConfig(config: Partial<LoggerConfig>): void {
    this.config = { ...this.config, ...config }
    
    if (config.flushInterval) {
      this.startFlushTimer()
    }
  }

  // Cleanup
  destroy(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer)
    }
    this.flush()
  }
}

// Export singleton instance
export const logger = Logger.getInstance()

// Export types
export type { LogLevel, LogEntry, LoggerConfig }

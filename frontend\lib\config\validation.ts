/**
 * Configuration Validation System
 * 
 * Validates all application configuration at startup to prevent runtime errors
 */

import { handleError } from '@/lib/utils/error-handler'

export interface ConfigValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
}

/**
 * Validate database configuration
 */
export function validateDatabaseConfig(): ConfigValidationResult {
  const errors: string[] = []
  const warnings: string[] = []

  try {
    // Check required environment variables
    const requiredVars = ['DB_HOST', 'DB_NAME', 'DB_USER', 'DB_PASSWORD']
    const missingVars = requiredVars.filter(varName => !process.env[varName])

    if (missingVars.length > 0) {
      errors.push(`Missing required database environment variables: ${missingVars.join(', ')}`)
    }

    // Check DATABASE_URL as alternative
    if (missingVars.length > 0 && !process.env.DATABASE_URL) {
      errors.push('Either individual DB_* variables or DATABASE_URL must be provided')
    }

    // Validate port
    const dbPort = process.env.DB_PORT
    if (dbPort && (isNaN(parseInt(dbPort)) || parseInt(dbPort) < 1 || parseInt(dbPort) > 65535)) {
      errors.push('DB_PORT must be a valid port number (1-65535)')
    }

    // Check SSL configuration
    const dbSsl = process.env.DATABASE_SSL
    if (dbSsl && !['true', 'false'].includes(dbSsl.toLowerCase())) {
      warnings.push('DATABASE_SSL should be "true" or "false"')
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  } catch (error) {
    handleError(error as Error, {
      component: 'config',
      operation: 'validateDatabaseConfig'
    })
    
    return {
      isValid: false,
      errors: ['Failed to validate database configuration'],
      warnings
    }
  }
}

/**
 * Validate authentication configuration
 */
export function validateAuthConfig(): ConfigValidationResult {
  const errors: string[] = []
  const warnings: string[] = []

  try {
    // Check required public environment variables
    const requiredPublicVars = [
      'NEXT_PUBLIC_AWS_REGION',
      'NEXT_PUBLIC_AWS_USER_POOLS_ID',
      'NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID',
      'NEXT_PUBLIC_AWS_COGNITO_DOMAIN'
    ]

    const missingPublicVars = requiredPublicVars.filter(varName => !process.env[varName])
    if (missingPublicVars.length > 0) {
      errors.push(`Missing required authentication environment variables: ${missingPublicVars.join(', ')}`)
    }

    // Validate redirect URLs
    const redirectSignIn = process.env.NEXT_PUBLIC_REDIRECT_SIGN_IN
    const redirectSignOut = process.env.NEXT_PUBLIC_REDIRECT_SIGN_OUT

    if (redirectSignIn && !isValidUrl(redirectSignIn)) {
      errors.push('NEXT_PUBLIC_REDIRECT_SIGN_IN must be a valid URL')
    }

    if (redirectSignOut && !isValidUrl(redirectSignOut)) {
      errors.push('NEXT_PUBLIC_REDIRECT_SIGN_OUT must be a valid URL')
    }

    // Check Cognito domain format
    const cognitoDomain = process.env.NEXT_PUBLIC_AWS_COGNITO_DOMAIN
    if (cognitoDomain && !cognitoDomain.includes('.')) {
      warnings.push('NEXT_PUBLIC_AWS_COGNITO_DOMAIN should be a fully qualified domain name')
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  } catch (error) {
    handleError(error as Error, {
      component: 'config',
      operation: 'validateAuthConfig'
    })
    
    return {
      isValid: false,
      errors: ['Failed to validate authentication configuration'],
      warnings
    }
  }
}

/**
 * Validate complete application configuration
 */
export function validateAppConfig(): ConfigValidationResult {
  const allErrors: string[] = []
  const allWarnings: string[] = []

  try {
    // Only validate on server-side
    if (typeof window !== 'undefined') {
      return {
        isValid: true,
        errors: [],
        warnings: ['Configuration validation skipped on client-side']
      }
    }

    console.log('🔍 [CONFIG] Starting application configuration validation...')

    // Validate database configuration
    const dbValidation = validateDatabaseConfig()
    allErrors.push(...dbValidation.errors)
    allWarnings.push(...dbValidation.warnings)

    if (dbValidation.errors.length > 0) {
      console.error('❌ [CONFIG] Database configuration errors:', dbValidation.errors)
    }

    // Validate authentication configuration
    const authValidation = validateAuthConfig()
    allErrors.push(...authValidation.errors)
    allWarnings.push(...authValidation.warnings)

    if (authValidation.errors.length > 0) {
      console.error('❌ [CONFIG] Authentication configuration errors:', authValidation.errors)
    }

    // Log warnings
    if (allWarnings.length > 0) {
      console.warn('⚠️ [CONFIG] Configuration warnings:', allWarnings)
    }

    const isValid = allErrors.length === 0

    if (isValid) {
      console.log('✅ [CONFIG] Application configuration validation passed')
    } else {
      console.error('❌ [CONFIG] Application configuration validation failed')
    }

    return {
      isValid,
      errors: allErrors,
      warnings: allWarnings
    }
  } catch (error) {
    handleError(error as Error, {
      component: 'config',
      operation: 'validateAppConfig'
    })
    
    return {
      isValid: false,
      errors: ['Configuration validation failed with unexpected error'],
      warnings: allWarnings
    }
  }
}

/**
 * Helper function to validate URLs
 */
function isValidUrl(urlString: string): boolean {
  try {
    new URL(urlString)
    return true
  } catch {
    return false
  }
}

/**
 * Initialize and validate configuration on startup
 */
export function initializeConfig(): Promise<ConfigValidationResult> {
  return new Promise((resolve) => {
    try {
      const result = validateAppConfig()
      
      if (!result.isValid) {
        console.error('🚨 [CONFIG] Critical configuration errors detected:')
        result.errors.forEach(error => console.error(`  - ${error}`))
        
        // In development, we can continue with warnings
        // In production, this should prevent startup
        if (process.env.NODE_ENV === 'production') {
          console.error('🚨 [CONFIG] Application cannot start with invalid configuration')
        }
      }
      
      resolve(result)
    } catch (error) {
      console.error('🚨 [CONFIG] Failed to initialize configuration:', error)
      resolve({
        isValid: false,
        errors: ['Configuration initialization failed'],
        warnings: []
      })
    }
  })
}

export default {
  validateDatabaseConfig,
  validateAuthConfig,
  validateAppConfig,
  initializeConfig
}

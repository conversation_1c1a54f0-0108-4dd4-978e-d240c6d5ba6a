/**
 * Tenant Security Policy Enforcement
 * 
 * Implements comprehensive security policies for multi-tenant operations
 * including data isolation, access control, and security monitoring.
 */

import { TenantContext } from '@/lib/types';
import { AuthSession } from '@/lib/api/auth-middleware';
import { NextRequest } from 'next/server';

export interface SecurityPolicy {
  name: string;
  description: string;
  enforce: (context: SecurityContext) => Promise<SecurityResult>;
}

export interface SecurityContext {
  tenant: TenantContext;
  user: AuthSession;
  request: NextRequest;
  operation: string;
  resource: string;
  data?: any;
}

export interface SecurityResult {
  allowed: boolean;
  reason?: string;
  warnings?: string[];
  requiredActions?: string[];
}

export interface SecurityViolation {
  policyName: string;
  tenantId: string;
  userId: string;
  violation: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  timestamp: Date;
  context: any;
}

/**
 * Data Isolation Policy
 * Ensures tenant data cannot be accessed across tenant boundaries
 */
export const dataIsolationPolicy: SecurityPolicy = {
  name: 'DataIsolation',
  description: 'Enforces strict tenant data isolation',
  enforce: async (context: SecurityContext): Promise<SecurityResult> => {
    const { tenant, user, operation, resource } = context;

    // Check if user belongs to the tenant
    if (user.tenantId && user.tenantId !== tenant.clientId) {
      return {
        allowed: false,
        reason: `User ${user.userId} does not belong to tenant ${tenant.clientId}`
      };
    }

    // Check for cross-tenant data access attempts
    if (context.data) {
      const suspiciousFields = ['tenant_id', 'client_id', 'schema'];
      for (const field of suspiciousFields) {
        if (context.data[field] && context.data[field] !== tenant.clientId) {
          return {
            allowed: false,
            reason: `Attempt to access data from different tenant: ${context.data[field]}`
          };
        }
      }
    }

    return { allowed: true };
  }
};

/**
 * Schema Access Policy
 * Controls access to database schemas
 */
export const schemaAccessPolicy: SecurityPolicy = {
  name: 'SchemaAccess',
  description: 'Controls database schema access',
  enforce: async (context: SecurityContext): Promise<SecurityResult> => {
    const { tenant, user, operation } = context;

    // Only allow access to tenant's own schema
    const allowedSchemas = [tenant.tenantSchema, 'metadata'];
    
    // Check if operation involves schema access
    if (operation.includes('schema') || operation.includes('database')) {
      const warnings: string[] = [];
      
      // Warn about metadata schema access
      if (operation.includes('metadata')) {
        warnings.push('Access to metadata schema detected - ensure this is necessary');
      }

      return {
        allowed: true,
        warnings: warnings.length > 0 ? warnings : undefined
      };
    }

    return { allowed: true };
  }
};

/**
 * Rate Limiting Policy
 * Prevents abuse by limiting request rates per tenant
 */
export const rateLimitingPolicy: SecurityPolicy = {
  name: 'RateLimiting',
  description: 'Enforces rate limits per tenant',
  enforce: async (context: SecurityContext): Promise<SecurityResult> => {
    const { tenant, user, request } = context;

    // Simple in-memory rate limiting (in production, use Redis or similar)
    const rateLimitKey = `${tenant.clientId}:${user.userId}`;
    const now = Date.now();
    const windowMs = 60 * 1000; // 1 minute window
    const maxRequests = 100; // Max requests per window

    // This is a simplified implementation
    // In production, implement proper distributed rate limiting
    
    return {
      allowed: true,
      warnings: ['Rate limiting policy active but not fully implemented']
    };
  }
};

/**
 * Data Classification Policy
 * Ensures sensitive data is handled appropriately
 */
export const dataClassificationPolicy: SecurityPolicy = {
  name: 'DataClassification',
  description: 'Enforces data classification and handling rules',
  enforce: async (context: SecurityContext): Promise<SecurityResult> => {
    const { operation, resource, data } = context;

    const sensitiveFields = [
      'password', 'ssn', 'credit_card', 'bank_account',
      'api_key', 'secret', 'token', 'private_key'
    ];

    const warnings: string[] = [];
    const requiredActions: string[] = [];

    if (data && typeof data === 'object') {
      for (const [key, value] of Object.entries(data)) {
        const fieldName = key.toLowerCase();
        
        if (sensitiveFields.some(field => fieldName.includes(field))) {
          if (operation === 'CREATE' || operation === 'UPDATE') {
            requiredActions.push(`Encrypt sensitive field: ${key}`);
          }
          
          if (operation === 'READ') {
            warnings.push(`Reading sensitive field: ${key}`);
          }
        }
      }
    }

    return {
      allowed: true,
      warnings: warnings.length > 0 ? warnings : undefined,
      requiredActions: requiredActions.length > 0 ? requiredActions : undefined
    };
  }
};

/**
 * Audit Logging Policy
 * Ensures all operations are properly logged
 */
export const auditLoggingPolicy: SecurityPolicy = {
  name: 'AuditLogging',
  description: 'Ensures comprehensive audit logging',
  enforce: async (context: SecurityContext): Promise<SecurityResult> => {
    const { tenant, user, operation, resource } = context;

    const highRiskOperations = ['DELETE', 'UPDATE', 'CREATE'];
    const sensitiveResources = ['users', 'permissions', 'settings'];

    const requiredActions: string[] = [];

    if (highRiskOperations.includes(operation.toUpperCase())) {
      requiredActions.push('Log high-risk operation');
    }

    if (sensitiveResources.includes(resource.toLowerCase())) {
      requiredActions.push('Log sensitive resource access');
    }

    return {
      allowed: true,
      requiredActions: requiredActions.length > 0 ? requiredActions : undefined
    };
  }
};

/**
 * Security Policy Engine
 */
export class TenantSecurityEngine {
  private policies: SecurityPolicy[] = [
    dataIsolationPolicy,
    schemaAccessPolicy,
    rateLimitingPolicy,
    dataClassificationPolicy,
    auditLoggingPolicy
  ];

  private violations: SecurityViolation[] = [];

  /**
   * Enforce all security policies
   */
  async enforceSecurityPolicies(context: SecurityContext): Promise<{
    allowed: boolean;
    violations: SecurityViolation[];
    warnings: string[];
    requiredActions: string[];
  }> {
    const violations: SecurityViolation[] = [];
    const warnings: string[] = [];
    const requiredActions: string[] = [];

    for (const policy of this.policies) {
      try {
        const result = await policy.enforce(context);

        if (!result.allowed) {
          const violation: SecurityViolation = {
            policyName: policy.name,
            tenantId: context.tenant.clientId,
            userId: context.user.userId,
            violation: result.reason || 'Policy violation',
            severity: this.determineSeverity(policy.name, result.reason),
            timestamp: new Date(),
            context: {
              operation: context.operation,
              resource: context.resource,
              userAgent: context.request.headers.get('user-agent'),
              ipAddress: context.request.headers.get('x-forwarded-for') || 
                        context.request.headers.get('x-real-ip')
            }
          };

          violations.push(violation);
          this.violations.push(violation);
        }

        if (result.warnings) {
          warnings.push(...result.warnings);
        }

        if (result.requiredActions) {
          requiredActions.push(...result.requiredActions);
        }

      } catch (error) {
        console.error(`Error enforcing policy ${policy.name}:`, error);
        warnings.push(`Policy enforcement error: ${policy.name}`);
      }
    }

    return {
      allowed: violations.length === 0,
      violations,
      warnings,
      requiredActions
    };
  }

  /**
   * Determine violation severity
   */
  private determineSeverity(policyName: string, reason?: string): 'low' | 'medium' | 'high' | 'critical' {
    if (policyName === 'DataIsolation') {
      return 'critical';
    }
    
    if (policyName === 'SchemaAccess') {
      return 'high';
    }
    
    if (policyName === 'RateLimiting') {
      return 'medium';
    }
    
    return 'low';
  }

  /**
   * Get recent violations
   */
  getRecentViolations(tenantId?: string, hours: number = 24): SecurityViolation[] {
    const cutoff = new Date(Date.now() - hours * 60 * 60 * 1000);
    
    return this.violations.filter(v => 
      v.timestamp >= cutoff && 
      (!tenantId || v.tenantId === tenantId)
    );
  }

  /**
   * Add custom policy
   */
  addPolicy(policy: SecurityPolicy): void {
    this.policies.push(policy);
  }

  /**
   * Remove policy
   */
  removePolicy(policyName: string): void {
    this.policies = this.policies.filter(p => p.name !== policyName);
  }
}

// Global security engine instance
export const tenantSecurityEngine = new TenantSecurityEngine();


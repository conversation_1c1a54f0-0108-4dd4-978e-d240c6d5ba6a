/**
 * Dropdown Validation Integration Tests
 *
 * Tests to ensure that dropdown validation works correctly with the new API endpoints
 */

import { z } from 'zod';

// Import validation schemas from API endpoints
const createVersionSchema = z.object({
  product_id: z.string().min(1, 'Product ID is required'),
  version: z.string().min(1, 'Version is required').max(50),
  release_date: z.string().optional(),
  notes: z.string().optional(),
  is_current: z.boolean().optional().default(false),
});

const createProductSchema = z.object({
  vendor_id: z.string().uuid('Invalid vendor ID'),
  name: z.string().min(1, 'Product name is required').max(255),
  description: z.string().optional(),
  category: z.string().max(100).optional(),
  sku: z.string().max(100).optional(),
  barcode: z.string().max(50).optional(),
  unit_of_measure: z.string().max(50).optional(),
});

const renewalItemSchema = z.object({
  product_id: z.string().min(1, 'Product ID is required'),
  version_id: z.string().min(1, 'Version ID is required'),
  license_count: z.number().int().positive('License count must be positive').default(1),
  unit_cost: z.number().min(0, 'Unit cost must be non-negative').default(0),
  total_cost: z.number().min(0, 'Total cost must be non-negative').default(0),
  cost_code: z.string().max(100).optional(),
  notes: z.string().optional(),
});

describe('Dropdown Validation Integration', () => {
  describe('API Validation Schemas', () => {
    describe('Product Version Validation', () => {
      it('should validate required product_id field', () => {
        const invalidData = {
          version: '2024',
          release_date: '2024-01-01',
          notes: 'Test version'
        };

        const result = createVersionSchema.safeParse(invalidData);
        expect(result.success).toBe(false);
        if (!result.success) {
          expect(result.error.errors[0].message).toBe('Required');
        }
      });

      it('should validate required version field', () => {
        const invalidData = {
          product_id: '123',
          release_date: '2024-01-01',
          notes: 'Test version'
        };

        const result = createVersionSchema.safeParse(invalidData);
        expect(result.success).toBe(false);
        if (!result.success) {
          expect(result.error.errors[0].message).toBe('Required');
        }
      });

      it('should validate version length limit', () => {
        const invalidData = {
          product_id: '123',
          version: 'a'.repeat(51), // Too long
          release_date: '2024-01-01'
        };

        const result = createVersionSchema.safeParse(invalidData);
        expect(result.success).toBe(false);
        if (!result.success) {
          expect(result.error.errors[0].message).toContain('50');
        }
      });

      it('should accept valid product version data', () => {
        const validData = {
          product_id: '123',
          version: '2024',
          release_date: '2024-01-01',
          notes: 'Test version',
          is_current: true
        };

        const result = createVersionSchema.safeParse(validData);
        expect(result.success).toBe(true);
      });
    });

    describe('Product Validation', () => {
      it('should validate required vendor_id field', () => {
        const invalidData = {
          name: 'Test Product',
          description: 'Test description'
        };

        const result = createProductSchema.safeParse(invalidData);
        expect(result.success).toBe(false);
        if (!result.success) {
          expect(result.error.errors[0].message).toBe('Required');
        }
      });

      it('should validate vendor_id UUID format', () => {
        const invalidData = {
          vendor_id: 'invalid-uuid',
          name: 'Test Product'
        };

        const result = createProductSchema.safeParse(invalidData);
        expect(result.success).toBe(false);
        if (!result.success) {
          expect(result.error.errors[0].message).toBe('Invalid vendor ID');
        }
      });

      it('should validate required name field', () => {
        const invalidData = {
          vendor_id: '123e4567-e89b-12d3-a456-************',
          description: 'Test description'
        };

        const result = createProductSchema.safeParse(invalidData);
        expect(result.success).toBe(false);
        if (!result.success) {
          expect(result.error.errors[0].message).toBe('Required');
        }
      });

      it('should accept valid product data', () => {
        const validData = {
          vendor_id: '123e4567-e89b-12d3-a456-************',
          name: 'Test Product',
          description: 'Test description',
          category: 'Software',
          sku: 'TEST-001'
        };

        const result = createProductSchema.safeParse(validData);
        expect(result.success).toBe(true);
      });
    });

    describe('Renewal Item Validation', () => {
      it('should validate required product_id field', () => {
        const invalidData = {
          version_id: '123',
          license_count: 1,
          unit_cost: 100
        };

        const result = renewalItemSchema.safeParse(invalidData);
        expect(result.success).toBe(false);
        if (!result.success) {
          expect(result.error.errors[0].message).toBe('Required');
        }
      });

      it('should validate required version_id field', () => {
        const invalidData = {
          product_id: '123',
          license_count: 1,
          unit_cost: 100
        };

        const result = renewalItemSchema.safeParse(invalidData);
        expect(result.success).toBe(false);
        if (!result.success) {
          expect(result.error.errors[0].message).toBe('Required');
        }
      });

      it('should validate positive license_count', () => {
        const invalidData = {
          product_id: '123',
          version_id: '456',
          license_count: -1,
          unit_cost: 100
        };

        const result = renewalItemSchema.safeParse(invalidData);
        expect(result.success).toBe(false);
        if (!result.success) {
          expect(result.error.errors[0].message).toBe('License count must be positive');
        }
      });

      it('should validate non-negative unit_cost', () => {
        const invalidData = {
          product_id: '123',
          version_id: '456',
          license_count: 1,
          unit_cost: -100
        };

        const result = renewalItemSchema.safeParse(invalidData);
        expect(result.success).toBe(false);
        if (!result.success) {
          expect(result.error.errors[0].message).toBe('Unit cost must be non-negative');
        }
      });

      it('should accept valid renewal item data', () => {
        const validData = {
          product_id: '123',
          version_id: '456',
          license_count: 10,
          unit_cost: 99.99,
          total_cost: 999.90,
          cost_code: 'CC-001',
          notes: 'Test renewal item'
        };

        const result = renewalItemSchema.safeParse(validData);
        expect(result.success).toBe(true);
      });

      it('should use default values for optional fields', () => {
        const minimalData = {
          product_id: '123',
          version_id: '456'
        };

        const result = renewalItemSchema.safeParse(minimalData);
        expect(result.success).toBe(true);
        if (result.success) {
          expect(result.data.license_count).toBe(1);
          expect(result.data.unit_cost).toBe(0);
          expect(result.data.total_cost).toBe(0);
        }
      });
    });

  });

  describe('Form Validation Logic', () => {
    it('should validate cascading dropdown selection requirements', () => {
      // Test the validation logic that would be used in forms
      const formData = {
        vendorId: '',
        productId: '',
        versionId: '',
        quantity: 0
      };

      const errors: {[key: string]: string} = {};

      // Simulate the validation logic from RenewalItemsManager
      if (!formData.productId) {
        errors.product = 'Product is required';
      }

      if (!formData.versionId) {
        errors.version = 'Version is required';
      }

      if (!formData.quantity || formData.quantity <= 0) {
        errors.quantity = 'Quantity must be greater than 0';
      }

      expect(Object.keys(errors)).toHaveLength(3);
      expect(errors.product).toBe('Product is required');
      expect(errors.version).toBe('Version is required');
      expect(errors.quantity).toBe('Quantity must be greater than 0');
    });

    it('should clear validation errors when fields are filled', () => {
      const formData = {
        vendorId: '123',
        productId: '456',
        versionId: '789',
        quantity: 5
      };

      const errors: {[key: string]: string} = {};

      // Simulate the validation logic
      if (!formData.productId) {
        errors.product = 'Product is required';
      }

      if (!formData.versionId) {
        errors.version = 'Version is required';
      }

      if (!formData.quantity || formData.quantity <= 0) {
        errors.quantity = 'Quantity must be greater than 0';
      }

      expect(Object.keys(errors)).toHaveLength(0);
    });

    it('should validate hierarchical dropdown dependencies', () => {
      // Test that product selection requires vendor selection
      const formData = {
        vendorId: '',
        productId: '456', // Product selected but no vendor
        versionId: '',
        quantity: 1
      };

      // In a real cascading dropdown, product options would be empty if no vendor is selected
      // This simulates that validation logic
      const hasVendor = !!formData.vendorId;
      const canSelectProduct = hasVendor;
      const canSelectVersion = hasVendor && !!formData.productId;

      expect(canSelectProduct).toBe(false);
      expect(canSelectVersion).toBe(false);
    });
  });
});

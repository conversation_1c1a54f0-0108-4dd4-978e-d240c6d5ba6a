import { NextRequest } from 'next/server';
import { withAuth } from '@/lib/api/auth-middleware';
import { resolveTenantContext, TenantContext } from '@/lib/tenant/context';
import { executeQuery, schemaExists } from '@/lib/database';
import {
  createSuccessResponse,
  createErrorResponse,
  ApiErrorCode,
  HttpStatus
} from '@/lib/api/response';
import { Renewal } from '@/lib/types';

export const GET = withAuth(async (request: NextRequest, session) => {
  console.log('[DASHBOARD-RENEWALS-API] GET request received');

  try {
    // Resolve tenant context using session
    const tenantResult = await resolveTenantContext(session);
    if (!tenantResult.success) {
      return createErrorResponse(
        'Failed to resolve tenant context',
        ApiErrorCode.DATABASE_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }

    const tenant = tenantResult.tenant;
    if (!tenant) {
      return createErrorResponse(
        'Tenant not found',
        ApiErrorCode.TENANT_NOT_FOUND,
        HttpStatus.NOT_FOUND
      );
    }

    // Check if tenant schema exists
    const schemaReady = await schemaExists(tenant.tenantSchema);

  let renewals: Renewal[] = [];

  if (schemaReady) {
    // Query tenant schema for actual renewal data using correct table and column names
    const renewalsQuery = `
      SELECT
        r.id,
        r.name,
        COALESCE(v.name, 'Unknown') as vendor,
        COALESCE(r.status, 'Active') as status,
        r.expiry_date as due_date,
        r.created_on as created_on
      FROM "${tenant.tenantSchema}".tenant_renewals r
      LEFT JOIN "${tenant.tenantSchema}".tenant_vendors v ON r.vendor_id = v.id
      WHERE r.is_deleted = false
      ORDER BY r.expiry_date DESC
      LIMIT 10
    `;

    console.log(`📋 [DASHBOARD] Executing renewals query for tenant ${tenant.clientName}:`, renewalsQuery);
    const result = await executeQuery(renewalsQuery, [], { schema: tenant.tenantSchema });
    console.log(`📋 [DASHBOARD] Query result:`, result);

    if (result.success && result.data && result.data.length > 0) {
      console.log(`📋 [DASHBOARD] Raw data from query:`, result.data);
      // Format the results to match Renewal interface
      renewals = result.data.map((row: any) => ({
        id: row.id.toString(),
        name: row.name,
        vendor: row.vendor,
        status: row.status.toLowerCase() as 'active' | 'inactive' | 'pending' | 'expired',
        due_date: row.due_date ? new Date(row.due_date) : undefined,
        created_on: new Date(row.created_on),
        changed_on: undefined
      }));
      console.log(`📋 [DASHBOARD] Formatted renewals:`, renewals);
    } else {
      console.log(`📋 [DASHBOARD] No data returned from query or query failed`);
    }
  } else {
    console.log(`Tenant schema ${tenant.tenantSchema} not ready yet, using mock data`);
  }

  // Return empty array if no renewals found (this is the correct behavior)
  if (renewals.length === 0) {
    console.log(`📋 [DASHBOARD] No renewals found - returning empty array`);
  }

  return createSuccessResponse(renewals, 'Renewals retrieved successfully');
  } catch (error) {
    console.error('[DASHBOARD-RENEWALS-API] Error:', error);
    return createErrorResponse(
      'Failed to fetch dashboard renewals',
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
}, {
  requireAuth: true
});

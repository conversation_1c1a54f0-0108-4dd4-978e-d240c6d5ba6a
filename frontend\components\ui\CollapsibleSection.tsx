/**
 * Collapsible Section Component
 * 
 * Reusable component for collapsible sections with consistent styling
 * Used for Archived Renewals and Renewal Timeline sections
 */

'use client'

import React, { ReactNode } from 'react'

interface CollapsibleSectionProps {
  title: string
  subtitle: string
  count?: number
  icon: string
  isOpen: boolean
  onToggle: () => void
  children: ReactNode
  className?: string
  'data-testid'?: string
}

const CollapsibleSection: React.FC<CollapsibleSectionProps> = ({
  title,
  subtitle,
  count,
  icon,
  isOpen,
  onToggle,
  children,
  className = '',
  'data-testid': testId
}) => {
  return (
    <div 
      className={`bg-white rounded-lg border border-gray-200 ${className}`}
      data-testid={testId}
    >
      {/* Header */}
      <div
        className="flex items-center justify-between p-4 cursor-pointer hover:bg-gray-50 transition-colors"
        onClick={onToggle}
      >
        <div className="flex items-center space-x-3">
          <span className="text-gray-400 text-lg">{icon}</span>
          <div>
            <h2 className="text-base font-medium text-gray-900 flex items-center space-x-2">
              <span>{title}</span>
              {count !== undefined && (
                <span className="text-gray-500">({count})</span>
              )}
            </h2>
            <p className="text-sm text-gray-500 mt-1">
              {subtitle}
            </p>
          </div>
        </div>
        
        {/* Toggle Icon */}
        <div className="flex items-center space-x-2">
          <span className="text-gray-400 text-sm">
            {isOpen ? '▼' : '▶'}
          </span>
        </div>
      </div>

      {/* Content */}
      {isOpen && (
        <div className="border-t border-gray-200">
          {children}
        </div>
      )}
    </div>
  )
}

export default CollapsibleSection

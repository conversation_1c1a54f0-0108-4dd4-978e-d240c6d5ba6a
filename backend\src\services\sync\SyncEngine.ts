/**
 * Master Data Synchronization Engine
 * 
 * Core synchronization framework that orchestrates the lazy synchronization
 * of master data between global canonical tables and tenant-specific tables.
 * 
 * Features:
 * - Lazy synchronization pattern
 * - Sophisticated matching algorithms
 * - Conflict detection and resolution
 * - Batch processing with retry logic
 * - Comprehensive audit trail
 */

import { Pool } from 'pg'
import { Logger } from '../Logger'
import { VendorSyncProcessor } from './processors/VendorSyncProcessor'
import { ProductSyncProcessor } from './processors/ProductSyncProcessor'
import { ProductVersionSyncProcessor } from './processors/ProductVersionSyncProcessor'

export interface SyncBatch {
  id: string
  tenantId: string
  entityType: 'vendor' | 'product' | 'product_version'
  status: 'pending' | 'processing' | 'completed' | 'failed'
  totalRecords: number
  processedRecords: number
  matchedRecords: number
  conflictRecords: number
  createdAt: Date
  updatedAt: Date
  completedAt?: Date
  errorMessage?: string
}

export interface SyncResult {
  batchId: string
  success: boolean
  totalProcessed: number
  matched: number
  conflicts: number
  errors: string[]
  processingTimeMs: number
}

export interface SyncOptions {
  batchSize?: number
  maxRetries?: number
  retryDelayMs?: number
  autoResolveThreshold?: number
  dryRun?: boolean
}

export class SyncEngine {
  private db: Pool
  private logger: Logger
  private vendorProcessor: VendorSyncProcessor
  private productProcessor: ProductSyncProcessor
  private versionProcessor: ProductVersionSyncProcessor

  constructor(db: Pool, logger: Logger) {
    this.db = db
    this.logger = logger
    this.vendorProcessor = new VendorSyncProcessor(db, logger)
    this.productProcessor = new ProductSyncProcessor(db, logger)
    this.versionProcessor = new ProductVersionSyncProcessor(db, logger)
  }

  /**
   * Synchronize vendors for a tenant
   */
  async syncVendors(tenantId: string, options: SyncOptions = {}): Promise<SyncResult> {
    const startTime = Date.now()
    const batchId = await this.createSyncBatch(tenantId, 'vendor')
    
    try {
      this.logger.info(`Starting vendor sync for tenant ${tenantId}`, { batchId })
      
      const result = await this.vendorProcessor.process(tenantId, batchId, options)
      
      await this.completeSyncBatch(batchId, result)
      
      this.logger.info(`Vendor sync completed for tenant ${tenantId}`, {
        batchId,
        ...result,
        processingTimeMs: Date.now() - startTime
      })
      
      return {
        ...result,
        batchId,
        processingTimeMs: Date.now() - startTime
      }
    } catch (error) {
      await this.failSyncBatch(batchId, error instanceof Error ? error.message : 'Unknown error')
      this.logger.error(`Vendor sync failed for tenant ${tenantId}`, { batchId, error })
      throw error
    }
  }

  /**
   * Synchronize products for a tenant
   */
  async syncProducts(tenantId: string, options: SyncOptions = {}): Promise<SyncResult> {
    const startTime = Date.now()
    const batchId = await this.createSyncBatch(tenantId, 'product')
    
    try {
      this.logger.info(`Starting product sync for tenant ${tenantId}`, { batchId })
      
      const result = await this.productProcessor.process(tenantId, batchId, options)
      
      await this.completeSyncBatch(batchId, result)
      
      this.logger.info(`Product sync completed for tenant ${tenantId}`, {
        batchId,
        ...result,
        processingTimeMs: Date.now() - startTime
      })
      
      return {
        ...result,
        batchId,
        processingTimeMs: Date.now() - startTime
      }
    } catch (error) {
      await this.failSyncBatch(batchId, error instanceof Error ? error.message : 'Unknown error')
      this.logger.error(`Product sync failed for tenant ${tenantId}`, { batchId, error })
      throw error
    }
  }

  /**
   * Synchronize product versions for a tenant
   */
  async syncProductVersions(tenantId: string, options: SyncOptions = {}): Promise<SyncResult> {
    const startTime = Date.now()
    const batchId = await this.createSyncBatch(tenantId, 'product_version')
    
    try {
      this.logger.info(`Starting product version sync for tenant ${tenantId}`, { batchId })
      
      const result = await this.versionProcessor.process(tenantId, batchId, options)
      
      await this.completeSyncBatch(batchId, result)
      
      this.logger.info(`Product version sync completed for tenant ${tenantId}`, {
        batchId,
        ...result,
        processingTimeMs: Date.now() - startTime
      })
      
      return {
        ...result,
        batchId,
        processingTimeMs: Date.now() - startTime
      }
    } catch (error) {
      await this.failSyncBatch(batchId, error instanceof Error ? error.message : 'Unknown error')
      this.logger.error(`Product version sync failed for tenant ${tenantId}`, { batchId, error })
      throw error
    }
  }

  /**
   * Full synchronization for a tenant (vendors -> products -> versions)
   */
  async syncAll(tenantId: string, options: SyncOptions = {}): Promise<{
    vendors: SyncResult
    products: SyncResult
    versions: SyncResult
    totalTimeMs: number
  }> {
    const startTime = Date.now()
    
    this.logger.info(`Starting full sync for tenant ${tenantId}`)
    
    try {
      // Sync in dependency order: vendors first, then products, then versions
      const vendors = await this.syncVendors(tenantId, options)
      const products = await this.syncProducts(tenantId, options)
      const versions = await this.syncProductVersions(tenantId, options)
      
      const totalTimeMs = Date.now() - startTime
      
      this.logger.info(`Full sync completed for tenant ${tenantId}`, {
        totalTimeMs,
        vendors: vendors.totalProcessed,
        products: products.totalProcessed,
        versions: versions.totalProcessed
      })
      
      return {
        vendors,
        products,
        versions,
        totalTimeMs
      }
    } catch (error) {
      this.logger.error(`Full sync failed for tenant ${tenantId}`, { error })
      throw error
    }
  }

  /**
   * Get sync status for a tenant
   */
  async getSyncStatus(tenantId: string): Promise<{
    lastSync?: Date
    activeBatches: SyncBatch[]
    recentBatches: SyncBatch[]
  }> {
    const client = await this.db.connect()
    
    try {
      // Get active batches
      const activeBatchesResult = await client.query(`
        SELECT * FROM metadata.sync_batches 
        WHERE tenant_id = $1 AND status IN ('pending', 'processing')
        ORDER BY created_on DESC
      `, [tenantId])
      
      // Get recent completed batches
      const recentBatchesResult = await client.query(`
        SELECT * FROM metadata.sync_batches 
        WHERE tenant_id = $1 AND status IN ('completed', 'failed')
        ORDER BY created_on DESC
        LIMIT 10
      `, [tenantId])
      
      // Get last successful sync
      const lastSyncResult = await client.query(`
        SELECT MAX(completed_at) as last_sync
        FROM metadata.sync_batches 
        WHERE tenant_id = $1 AND status = 'completed'
      `, [tenantId])
      
      return {
        lastSync: lastSyncResult.rows[0]?.last_sync || undefined,
        activeBatches: activeBatchesResult.rows,
        recentBatches: recentBatchesResult.rows
      }
    } finally {
      client.release()
    }
  }

  /**
   * Create a new sync batch record
   */
  private async createSyncBatch(tenantId: string, entityType: string): Promise<string> {
    const client = await this.db.connect()
    
    try {
      const result = await client.query(`
        INSERT INTO metadata.sync_batches (
          tenant_id, entity_type, status, total_records, processed_records,
          matched_records, conflict_records, created_on, changed_on
        ) VALUES ($1, $2, 'pending', 0, 0, 0, 0, NOW(), NOW())
        RETURNING id
      `, [tenantId, entityType])
      
      return result.rows[0].id
    } finally {
      client.release()
    }
  }

  /**
   * Complete a sync batch
   */
  private async completeSyncBatch(batchId: string, result: Omit<SyncResult, 'batchId' | 'processingTimeMs'>): Promise<void> {
    const client = await this.db.connect()
    
    try {
      await client.query(`
        UPDATE metadata.sync_batches 
        SET status = 'completed',
            total_records = $2,
            processed_records = $3,
            matched_records = $4,
            conflict_records = $5,
            completed_at = NOW(),
            changed_on = NOW()
        WHERE id = $1
      `, [batchId, result.totalProcessed, result.totalProcessed, result.matched, result.conflicts])
    } finally {
      client.release()
    }
  }

  /**
   * Mark a sync batch as failed
   */
  private async failSyncBatch(batchId: string, errorMessage: string): Promise<void> {
    const client = await this.db.connect()
    
    try {
      await client.query(`
        UPDATE metadata.sync_batches 
        SET status = 'failed',
            error_message = $2,
            changed_on = NOW()
        WHERE id = $1
      `, [batchId, errorMessage])
    } finally {
      client.release()
    }
  }
}

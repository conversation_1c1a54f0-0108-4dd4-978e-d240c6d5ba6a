/**
 * Advanced Filter Panel Component
 * 
 * Complete filtering interface with search, advanced filters, and saved presets
 */

'use client'

import React, { useState } from 'react'
import { Form } from '@/components/ui/Form'
import AdvancedFilterBuilder from './AdvancedFilterBuilder'
import { useAdvancedFilters, UseAdvancedFiltersConfig } from '@/lib/hooks/useAdvancedFilters'
import { SearchConfig } from '@/lib/services/advanced-filter-service'

interface AdvancedFilterPanelProps extends UseAdvancedFiltersConfig {
  availableFields: Array<{
    field: string
    label: string
    dataType: 'string' | 'number' | 'date' | 'boolean'
    options?: Array<{ label: string; value: any }>
  }>
  searchFields?: string[]
  onDataChange?: (filteredData: any[]) => void
  className?: string
}

export default function AdvancedFilterPanel({
  tableName,
  data = [],
  availableFields,
  searchFields = [],
  serverSide = false,
  autoApply = true,
  onDataChange,
  className = ''
}: AdvancedFilterPanelProps) {
  
  const [isExpanded, setIsExpanded] = useState(false)
  const [showPresets, setShowPresets] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [savePresetName, setSavePresetName] = useState('')
  const [savePresetDescription, setSavePresetDescription] = useState('')
  const [savePresetPublic, setSavePresetPublic] = useState(false)
  const [showSavePreset, setShowSavePreset] = useState(false)

  const {
    activeFilters,
    searchConfig,
    activePreset,
    filteredData,
    isFiltering,
    setFilters,
    clearFilters,
    setSearch,
    clearSearch,
    savedPresets,
    loadPreset,
    saveCurrentAsPreset,
    deletePreset,
    hasActiveFilters,
    filterSummary,
    resetAll
  } = useAdvancedFilters({
    tableName,
    data,
    serverSide,
    autoApply
  })

  // Notify parent of data changes
  React.useEffect(() => {
    if (onDataChange) {
      onDataChange(filteredData)
    }
  }, [filteredData, onDataChange])

  // Handle search
  const handleSearchChange = (query: string) => {
    setSearchQuery(query)
    
    if (query.trim()) {
      const config: SearchConfig = {
        query: query.trim(),
        fields: searchFields.length > 0 ? searchFields : availableFields.map(f => f.field),
        caseSensitive: false
      }
      setSearch(config)
    } else {
      clearSearch()
    }
  }

  // Handle save preset
  const handleSavePreset = async () => {
    if (!savePresetName.trim()) return

    const success = await saveCurrentAsPreset(
      savePresetName.trim(),
      savePresetDescription.trim() || undefined,
      savePresetPublic
    )

    if (success) {
      setSavePresetName('')
      setSavePresetDescription('')
      setSavePresetPublic(false)
      setShowSavePreset(false)
    }
  }

  return (
    <div className={`advanced-filter-panel bg-white border border-gray-200 rounded-lg ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            {/* Search */}
            <div className="flex-1 min-w-64">
              <Form.Input
                type="text"
                placeholder="Search..."
                value={searchQuery}
                onChange={(e) => handleSearchChange(e.target.value)}
                className="w-full"
              />
            </div>

            {/* Advanced Filters Toggle */}
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className={`px-3 py-2 text-sm font-medium rounded-lg border ${
                isExpanded 
                  ? 'bg-blue-50 text-blue-700 border-blue-200' 
                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
              }`}
            >
              Advanced Filters
            </button>

            {/* Presets Toggle */}
            <button
              onClick={() => setShowPresets(!showPresets)}
              className={`px-3 py-2 text-sm font-medium rounded-lg border ${
                showPresets 
                  ? 'bg-green-50 text-green-700 border-green-200' 
                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
              }`}
            >
              Presets ({savedPresets.length})
            </button>
          </div>

          {/* Filter Status */}
          <div className="flex items-center space-x-2">
            {hasActiveFilters && (
              <span className="text-sm text-gray-600">
                {filteredData.length} of {data.length} items
              </span>
            )}
            
            {hasActiveFilters && (
              <button
                onClick={resetAll}
                className="text-sm text-red-600 hover:text-red-700"
              >
                Clear All
              </button>
            )}
          </div>
        </div>

        {/* Filter Summary */}
        {hasActiveFilters && (
          <div className="mt-2 text-sm text-gray-600">
            {filterSummary}
          </div>
        )}
      </div>

      {/* Presets Panel */}
      {showPresets && (
        <div className="p-4 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-medium text-gray-900">Saved Presets</h3>
            {hasActiveFilters && (
              <button
                onClick={() => setShowSavePreset(!showSavePreset)}
                className="text-sm text-blue-600 hover:text-blue-700"
              >
                Save Current Filters
              </button>
            )}
          </div>

          {/* Save Preset Form */}
          {showSavePreset && (
            <div className="mb-4 p-3 bg-white border border-gray-200 rounded-lg">
              <div className="grid grid-cols-2 gap-3 mb-3">
                <Form.Input
                  placeholder="Preset name"
                  value={savePresetName}
                  onChange={(e) => setSavePresetName(e.target.value)}
                />
                <Form.Input
                  placeholder="Description (optional)"
                  value={savePresetDescription}
                  onChange={(e) => setSavePresetDescription(e.target.value)}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={savePresetPublic}
                    onChange={(e) => setSavePresetPublic(e.target.checked)}
                    className="rounded"
                  />
                  <span className="text-sm text-gray-700">Make public</span>
                </label>
                
                <div className="space-x-2">
                  <button
                    onClick={() => setShowSavePreset(false)}
                    className="px-3 py-1 text-sm text-gray-600 hover:text-gray-700"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleSavePreset}
                    disabled={!savePresetName.trim()}
                    className="px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
                  >
                    Save
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Preset List */}
          <div className="space-y-2">
            {savedPresets.length === 0 ? (
              <p className="text-sm text-gray-500">No saved presets</p>
            ) : (
              savedPresets.map(preset => (
                <div
                  key={preset.preset_id}
                  className={`flex items-center justify-between p-2 rounded-lg border ${
                    activePreset?.preset_id === preset.preset_id
                      ? 'bg-blue-50 border-blue-200'
                      : 'bg-white border-gray-200 hover:bg-gray-50'
                  }`}
                >
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium">{preset.name}</span>
                      {preset.is_public && (
                        <span className="text-xs bg-green-100 text-green-700 px-2 py-0.5 rounded">
                          Public
                        </span>
                      )}
                    </div>
                    {preset.description && (
                      <p className="text-xs text-gray-500 mt-1">{preset.description}</p>
                    )}
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => loadPreset(preset.preset_id!)}
                      className="text-sm text-blue-600 hover:text-blue-700"
                    >
                      Load
                    </button>
                    <button
                      onClick={() => deletePreset(preset.preset_id!)}
                      className="text-sm text-red-600 hover:text-red-700"
                    >
                      Delete
                    </button>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      )}

      {/* Advanced Filter Builder */}
      {isExpanded && (
        <div className="p-4">
          <AdvancedFilterBuilder
            initialGroups={activeFilters}
            availableFields={availableFields}
            onChange={setFilters}
          />
          
          <div className="flex justify-between items-center mt-4">
            <button
              onClick={clearFilters}
              className="text-sm text-gray-600 hover:text-gray-700"
              disabled={activeFilters.length === 0}
            >
              Clear Filters
            </button>
            
            <div className="text-sm text-gray-500">
              {activeFilters.length > 0 && `${filteredData.length} results`}
            </div>
          </div>
        </div>
      )}

      {/* Loading State */}
      {isFiltering && (
        <div className="p-4 text-center">
          <div className="text-sm text-gray-500">Applying filters...</div>
        </div>
      )}
    </div>
  )
}

/**
 * Renewal Items Manager Component
 * 
 * Manages multiple product/version combinations for a renewal
 * Allows adding, editing, and removing renewal items
 */

'use client';

import React, { useState, useCallback } from 'react';
import { CascadingSelect, CascadingSelectValue, CascadingSelectOption } from '@/components/ui/CascadingSelect';
import { Form } from '@/components/ui/Form';
import { Button } from '@/components/ui/Button';
import { useToast } from '@/components/ui/Toast';

export interface RenewalItem {
  id?: string; // For existing items
  productId: string;
  product_name: string;
  versionId: string;
  versionName: string;
  quantity: number;
  unit: string;
  licenseCount: number;
  totalCost: number;
  costCode?: string;
  notes?: string;
}

export interface RenewalItemsManagerProps {
  items: RenewalItem[];
  onChange: (items: RenewalItem[]) => void;
  disabled?: boolean;
  className?: string;
  vendorId?: string; // Vendor selected at renewal level
}

export function RenewalItemsManager({
  items,
  onChange,
  disabled = false,
  className = '',
  vendorId
}: RenewalItemsManagerProps) {
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [validationErrors, setValidationErrors] = useState<{[key: string]: string}>({});
  const toast = useToast();

  // Handle adding new items
  const handleAddNewItem = useCallback(async (type: 'vendor' | 'product' | 'version', itemData: any): Promise<CascadingSelectOption> => {
    const endpoints = {
      vendor: '/api/tenant-vendors',
      product: '/api/tenant-products',
      version: '/api/tenant-product-versions'
    };

    try {
      const response = await fetch(endpoints[type], {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(itemData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to create ${type}`);
      }

      const result = await response.json();
      
      return {
        id: result.data.id,
        name: type === 'version' ? result.data.version : result.data.name,
        display_name: result.data.display_name
      };
    } catch (error) {
      console.error(`Error creating ${type}:`, error);
      throw error;
    }
  }, []);

  // State for the new item form
  const [newItem, setNewItem] = useState<RenewalItem>({
    productId: '',
    product_name: '',
    versionId: '',
    versionName: '',
    quantity: 1, // Default to 1
    unit: '',
    licenseCount: 1,
    totalCost: 0,
    costCode: '',
    notes: ''
  });

  // Add a new item
  const handleAddItem = useCallback(() => {
    console.log('🔍 Add Item clicked, current newItem:', newItem);

    // Clear previous validation errors
    setValidationErrors({});

    // Validate required fields
    const errors: {[key: string]: string} = {};

    if (!newItem.productId) {
      errors.product = 'Product is required';
    }

    if (!newItem.versionId) {
      errors.version = 'Version is required';
    }

    if (!newItem.quantity || newItem.quantity <= 0) {
      errors.quantity = 'Quantity must be greater than 0';
    }

    console.log('🔍 Validation errors:', errors);

    // If there are validation errors, show them and don't add the item
    if (Object.keys(errors).length > 0) {
      setValidationErrors(errors);
      toast.error('Please fill in all required fields');
      return;
    }

    console.log('✅ Adding item to list:', newItem);
    // Add the item to the list
    onChange([...items, { ...newItem }]);

    // Reset the form
    setNewItem({
      productId: '',
      product_name: '',
      versionId: '',
      versionName: '',
      quantity: 1, // Default to 1
      unit: '',
      licenseCount: 1,
      totalCost: 0,
      costCode: '',
      notes: ''
    });
  }, [items, onChange, newItem, toast, setValidationErrors]);

  // Remove an item
  const handleRemoveItem = useCallback((index: number) => {
    const newItems = items.filter((_, i) => i !== index);
    onChange(newItems);
    if (editingIndex === index) {
      setEditingIndex(null);
    } else if (editingIndex !== null && editingIndex > index) {
      setEditingIndex(editingIndex - 1);
    }
  }, [items, onChange, editingIndex]);

  // Update an item
  const handleUpdateItem = useCallback((index: number, updates: Partial<RenewalItem>) => {
    const newItems = [...items];
    newItems[index] = { ...newItems[index], ...updates };

    onChange(newItems);
  }, [items, onChange]);

  // Handle cascading select changes (vendor is now at renewal level)
  const handleCascadingSelectChange = useCallback((index: number, value: CascadingSelectValue) => {
    handleUpdateItem(index, {
      productId: value.productId || '',
      product_name: value.product_name,
      versionId: value.versionId || '',
      versionName: value.versionName
    });
  }, [handleUpdateItem]);

  // Filter items that have mandatory fields populated for table display
  const validItemsForTable = items.filter(item =>
    item.productId && item.versionId && item.product_name && item.versionName
  );

  return (
    <div className={`renewal-items-manager ${className}`}>
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-medium text-gray-900">Renewal Items</h3>
      </div>

      <p className="text-sm text-gray-600 mb-4">
        Add the products/services included in this renewal. At least one item is required.
      </p>

      {/* Add Item Form - Always Visible */}
      <div className="border border-gray-200 rounded-lg p-4 bg-gray-50 mb-4">
        <div className="space-y-4">
          <CascadingSelect
            value={{
              vendorId: vendorId || null,
              vendorName: '', // Not needed since vendor is selected at renewal level
              productId: newItem.productId || null,
              product_name: newItem.product_name,
              versionId: newItem.versionId || null,
              versionName: newItem.versionName
            }}
            onChange={(value) => {
              setNewItem(prev => ({
                ...prev,
                productId: value.productId || '',
                product_name: value.product_name,
                versionId: value.versionId || '',
                versionName: value.versionName
              }));
              // Clear validation errors when user makes changes
              if (validationErrors.product || validationErrors.version) {
                setValidationErrors(prev => {
                  const newErrors = { ...prev };
                  delete newErrors.product;
                  delete newErrors.version;
                  return newErrors;
                });
              }
            }}
            onAddNew={handleAddNewItem}
            required={true}
            disabled={disabled || !vendorId}
            hideVendorSelection={true}
            preselectedVendorId={vendorId}
          />
          {(validationErrors.product || validationErrors.version) && (
            <Form.Error>
              {validationErrors.product || validationErrors.version}
            </Form.Error>
          )}

          <Form.Grid columns={3}>
            <Form.Field>
              <Form.Label htmlFor="new-quantity" required>
                Quantity
              </Form.Label>
              <Form.Input
                id="new-quantity"
                type="number"
                min="1"
                value={newItem.quantity || ''}
                onChange={(e) => {
                  setNewItem(prev => ({
                    ...prev,
                    quantity: parseInt(e.target.value) || 0
                  }));
                  // Clear validation error when user makes changes
                  if (validationErrors.quantity) {
                    setValidationErrors(prev => {
                      const newErrors = { ...prev };
                      delete newErrors.quantity;
                      return newErrors;
                    });
                  }
                }}
                disabled={disabled}
                placeholder="Enter quantity"
                error={validationErrors.quantity}
              />
              {validationErrors.quantity && (
                <Form.Error>{validationErrors.quantity}</Form.Error>
              )}
            </Form.Field>

            <Form.Field>
              <Form.Label htmlFor="new-unit">
                Unit
              </Form.Label>
              <Form.Input
                id="new-unit"
                type="text"
                placeholder="e.g., licenses, users, seats"
                value={newItem.unit}
                onChange={(e) => setNewItem(prev => ({
                  ...prev,
                  unit: e.target.value
                }))}
                disabled={disabled}
              />
            </Form.Field>

            <Form.Field>
              <Form.Label htmlFor="new-totalCost">
                Total Cost
              </Form.Label>
              <Form.Input
                id="new-totalCost"
                type="number"
                min="0"
                step="0.01"
                value={newItem.totalCost}
                onChange={(e) => setNewItem(prev => ({
                  ...prev,
                  totalCost: parseFloat(e.target.value) || 0
                }))}
                disabled={disabled}
              />
            </Form.Field>
          </Form.Grid>

          <div className="flex justify-end">
            <Button
              type="button"
              variant="primary"
              size="sm"
              onClick={handleAddItem}
              disabled={disabled || !newItem.productId || !newItem.versionId || !newItem.quantity || newItem.quantity <= 0}
            >
              Add Item
            </Button>
            {/* Debug info */}
            {process.env.NODE_ENV === 'development' && (
              <div className="ml-2 text-xs text-gray-500">
                Debug: disabled={String(disabled || !newItem.productId || !newItem.versionId || !newItem.quantity || newItem.quantity <= 0)}
                <br />
                productId: {newItem.productId || 'empty'}
                <br />
                versionId: {newItem.versionId || 'empty'}
                <br />
                quantity: {newItem.quantity}
              </div>
            )}
          </div>
        </div>
      </div>

      {validItemsForTable.length === 0 ? (
        <div className="text-center py-8 text-gray-500 border-2 border-dashed border-gray-200 rounded-lg">
          <p>No items added yet</p>
          <p className="text-sm">Click "Add Item" to add products to this renewal</p>
        </div>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 border border-gray-200 rounded-lg">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Product
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Version
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Quantity
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Unit
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Total Cost
                </th>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {validItemsForTable.map((item, index) => {
                const originalIndex = items.findIndex(i => i === item);
                return (
                <tr
                  key={originalIndex}
                  className={`hover:bg-gray-50 cursor-pointer ${editingIndex === originalIndex ? 'bg-blue-50' : ''}`}
                  onClick={() => setEditingIndex(editingIndex === originalIndex ? null : originalIndex)}
                >
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                    {item.product_name || 'Not selected'}
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                    {item.versionName || 'Not selected'}
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                    {item.quantity}
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                    {item.unit || 'Not specified'}
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${item.totalCost.toFixed(2)}
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                    <div className="flex gap-2">
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          setEditingIndex(editingIndex === originalIndex ? null : originalIndex);
                        }}
                        disabled={disabled}
                      >
                        {editingIndex === index ? 'Done' : 'Edit'}
                      </Button>
                      <Button
                        type="button"
                        variant="danger"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleRemoveItem(originalIndex);
                        }}
                        disabled={disabled}
                      >
                        Remove
                      </Button>
                    </div>
                  </td>
                </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      )}

      {/* Edit Form - Shows below table when editing an item */}
      {editingIndex !== null && items[editingIndex] && (
        <div className="mt-6 p-4 border border-blue-200 rounded-lg bg-blue-50">
          <div className="space-y-4">
            <CascadingSelect
              value={{
                vendorId: vendorId || null,
                vendorName: '', // Not needed since vendor is selected at renewal level
                productId: items[editingIndex].productId || null,
                product_name: items[editingIndex].product_name,
                versionId: items[editingIndex].versionId || null,
                versionName: items[editingIndex].versionName
              }}
              onChange={(value) => handleCascadingSelectChange(editingIndex, value)}
              onAddNew={handleAddNewItem}
              required={true}
              disabled={disabled || !vendorId}
              hideVendorSelection={true}
              preselectedVendorId={vendorId}
            />

            <Form.Grid columns={3}>
              <Form.Field>
                <Form.Label htmlFor={`quantity-${editingIndex}`}>
                  Quantity
                </Form.Label>
                <Form.Input
                  id={`quantity-${editingIndex}`}
                  type="number"
                  min="1"
                  value={items[editingIndex].quantity || ''}
                  onChange={(e) => handleUpdateItem(editingIndex, { quantity: parseInt(e.target.value) || 0 })}
                  disabled={disabled}
                  placeholder="Enter quantity"
                />
              </Form.Field>

              <Form.Field>
                <Form.Label htmlFor={`unit-${editingIndex}`}>
                  Unit
                </Form.Label>
                <Form.Input
                  id={`unit-${editingIndex}`}
                  type="text"
                  placeholder="e.g., licenses, users, seats"
                  value={items[editingIndex].unit}
                  onChange={(e) => handleUpdateItem(editingIndex, { unit: e.target.value })}
                  disabled={disabled}
                />
              </Form.Field>

              <Form.Field>
                <Form.Label htmlFor={`totalCost-${editingIndex}`}>
                  Total Cost
                </Form.Label>
                <Form.Input
                  id={`totalCost-${editingIndex}`}
                  type="number"
                  min="0"
                  step="0.01"
                  value={items[editingIndex].totalCost}
                  onChange={(e) => handleUpdateItem(editingIndex, { totalCost: parseFloat(e.target.value) || 0 })}
                  disabled={disabled}
                />
              </Form.Field>
            </Form.Grid>

            <Form.Grid columns={2}>
              <Form.Field>
                <Form.Label htmlFor={`costCode-${editingIndex}`}>
                  Cost Code
                </Form.Label>
                <Form.Input
                  id={`costCode-${editingIndex}`}
                  type="text"
                  placeholder="Optional cost code"
                  value={items[editingIndex].costCode || ''}
                  onChange={(e) => handleUpdateItem(editingIndex, { costCode: e.target.value })}
                  disabled={disabled}
                />
              </Form.Field>

              <Form.Field>
                <Form.Label htmlFor={`notes-${editingIndex}`}>
                  Notes
                </Form.Label>
                <Form.Input
                  id={`notes-${editingIndex}`}
                  type="text"
                  placeholder="Optional notes"
                  value={items[editingIndex].notes || ''}
                  onChange={(e) => handleUpdateItem(editingIndex, { notes: e.target.value })}
                  disabled={disabled}
                />
              </Form.Field>
            </Form.Grid>

            <div className="flex justify-end gap-2 mt-4">
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => setEditingIndex(null)}
              >
                Cancel
              </Button>
              <Button
                type="button"
                variant="primary"
                size="sm"
                onClick={() => setEditingIndex(null)}
              >
                Save Changes
              </Button>
            </div>
          </div>
        </div>
      )}


      {validItemsForTable.length > 0 && (
        <div className="mt-4 p-4 bg-blue-50 rounded-lg">
          <div className="flex justify-between items-center">
            <span className="font-medium">Total Renewal Cost:</span>
            <span className="text-lg font-bold">${validItemsForTable.reduce((sum, item) => sum + item.totalCost, 0).toFixed(2)}</span>
          </div>
          <div className="text-sm text-gray-600 mt-1">
            {validItemsForTable.length} item{validItemsForTable.length !== 1 ? 's' : ''} • {validItemsForTable.reduce((sum, item) => sum + item.quantity, 0)} total quantity
          </div>
        </div>
      )}
    </div>
  );
}

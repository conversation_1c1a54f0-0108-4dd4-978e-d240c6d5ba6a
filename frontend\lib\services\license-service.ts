/**
 * License Validation Service
 * 
 * Server-side service for license validation and management
 * Used in authentication flow and API middleware
 */

import { executeQuery, executeQuerySingle } from '@/lib/database';

export interface LicenseInfo {
  isValid: boolean;
  licenseType: string;
  maxUsers: number;
  maxTenants: number;
  features: string[];
  expiresAt: Date | null;
  usageCount: number;
  clientName: string;
  activatedAt: Date;
  lastUsage: Date | null;
}

export interface LicenseValidationResult {
  isValid: boolean;
  reason?: string;
  license?: LicenseInfo;
}

/**
 * Validate client license by client ID
 */
export async function validateClientLicense(clientId: number): Promise<LicenseValidationResult> {
  try {
    const query = `
      SELECT
        cl.client_license_id as activation_id,
        cl.status as activation_status,
        cl.activation_date as activated_at,
        cl.expiry_date as expires_at,
        cl.current_renewals,
        cl.max_renewals,
        lk.license_key,
        lt.type_name as license_type,
        lt.max_renewals as license_max_renewals,
        lt.features,
        ac.name
      FROM metadata.admin_client_licenses cl
      JOIN metadata.admin_license_keys lk ON cl.license_key_id = lk.license_key_id
      JOIN metadata.admin_license_types lt ON cl.license_type_id = lt.id
      JOIN metadata.clients ac ON cl.client_id = ac.id
      WHERE cl.client_id = $1 AND cl.status = 'ACTIVE'
    `;

    const result = await executeQuerySingle(query, [clientId]);

    if (!result.success || !result.data) {
      return {
        isValid: false,
        reason: 'No active license found for client'
      };
    }

    const data = result.data;

    // Check if license is active
    if (data.activation_status !== 'ACTIVE') {
      return {
        isValid: false,
        reason: 'License is not active'
      };
    }

    // Check client activation expiration
    if (data.expires_at && new Date(data.expires_at) < new Date()) {
      return {
        isValid: false,
        reason: 'Client license has expired'
      };
    }

    // Parse features from JSON
    let features: string[] = [];
    if (data.features) {
      try {
        const parsedFeatures = typeof data.features === 'string' ? JSON.parse(data.features) : data.features;
        features = Object.keys(parsedFeatures).filter(key => parsedFeatures[key] === true);
      } catch (error) {
        console.error('Error parsing license features:', error);
        features = [];
      }
    }

    // License is valid
    return {
      isValid: true,
      license: {
        isValid: true,
        licenseType: data.license_type,
        maxUsers: 999999, // Enterprise license - unlimited users
        maxTenants: 999999, // Enterprise license - unlimited tenants
        features: features,
        expiresAt: data.expires_at ? new Date(data.expires_at) : null,
        usageCount: data.current_renewals || 0,
        clientName: data.name,
        activatedAt: new Date(data.activated_at),
        lastUsage: null // Not tracked in new schema
      }
    };

  } catch (error) {
    console.error('License validation error:', error);
    return {
      isValid: false,
      reason: 'License validation failed due to system error'
    };
  }
}

/**
 * Check if a specific feature is enabled for a client
 */
export async function isFeatureEnabled(clientId: number, featureCode: string): Promise<boolean> {
  try {
    const validation = await validateClientLicense(clientId);
    
    if (!validation.isValid || !validation.license) {
      return false;
    }

    return validation.license.features.includes(featureCode);

  } catch (error) {
    console.error('Feature check error:', error);
    return false;
  }
}

/**
 * Log license usage event
 */
export async function logLicenseUsage(
  clientId: number,
  eventType: string,
  userEmail?: string,
  tenantId?: string,
  featureName?: string,
  metadata?: any
): Promise<void> {
  try {
    // Get license key for logging
    const licenseQuery = `
      SELECT license_key FROM licensing.client_activations
      WHERE client_id = $1 AND activation_status = 'active'
    `;

    const licenseResult = await executeQuerySingle(licenseQuery, [clientId]);
    
    if (!licenseResult.success || !licenseResult.data) {
      console.warn('Cannot log usage - no active license found for client:', clientId);
      return;
    }

    const logQuery = `
      INSERT INTO licensing.usage_logs (
        client_id,
        license_key,
        event_type,
        user_email,
        tenant_id,
        feature_name,
        ip_address,
        user_agent,
        session_id
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
    `;

    await executeQuery(logQuery, [
      clientId,
      licenseResult.data.license_key,
      eventType,
      userEmail || null,
      tenantId || null,
      featureName || null,
      metadata?.ipAddress || null,
      metadata?.userAgent || null,
      metadata?.sessionId || null
    ]);

    // Update last usage timestamp
    const updateUsageQuery = `
      UPDATE licensing.client_activations
      SET last_usage = CURRENT_TIMESTAMP,
          changed_on = CURRENT_TIMESTAMP
      WHERE client_id = $1 AND activation_status = 'active'
    `;

    await executeQuery(updateUsageQuery, [clientId]);

  } catch (error) {
    console.error('License usage logging error:', error);
    // Don't throw - logging failures shouldn't break the application
  }
}

/**
 * Get license statistics for a client
 */
export async function getLicenseStats(clientId: number): Promise<any> {
  try {
    const statsQuery = `
      SELECT 
        ca.user_count,
        ca.tenant_count,
        lk.max_users,
        lk.max_tenants,
        COUNT(ul.id) as total_events,
        COUNT(CASE WHEN ul.event_type = 'login' THEN 1 END) as login_events,
        MAX(ul.logged_at) as last_activity
      FROM licensing.client_activations ca
      JOIN licensing.license_keys lk ON ca.license_key_id = lk.id
      LEFT JOIN licensing.usage_logs ul ON ca.client_id = ul.client_id
      WHERE ca.client_id = $1 AND ca.activation_status = 'active'
      GROUP BY ca.user_count, ca.tenant_count, lk.max_users, lk.max_tenants
    `;

    const result = await executeQuerySingle(statsQuery, [clientId]);

    if (!result.success || !result.data) {
      return null;
    }

    return {
      currentUsers: result.data.user_count || 0,
      maxUsers: result.data.max_users,
      currentTenants: result.data.tenant_count || 0,
      maxTenants: result.data.max_tenants,
      totalEvents: result.data.total_events || 0,
      loginEvents: result.data.login_events || 0,
      lastActivity: result.data.last_activity ? new Date(result.data.last_activity) : null,
      userUtilization: result.data.max_users > 0 ? (result.data.user_count || 0) / result.data.max_users : 0,
      tenantUtilization: result.data.max_tenants > 0 ? (result.data.tenant_count || 0) / result.data.max_tenants : 0
    };

  } catch (error) {
    console.error('License stats error:', error);
    return null;
  }
}

/**
 * Update user/tenant counts for license tracking
 */
export async function updateLicenseUsageCounts(
  clientId: number,
  userCount?: number,
  tenantCount?: number
): Promise<void> {
  try {
    const updates: string[] = [];
    const params: any[] = [];
    let paramIndex = 1;

    if (userCount !== undefined) {
      updates.push(`user_count = $${paramIndex}`);
      params.push(userCount);
      paramIndex++;
    }

    if (tenantCount !== undefined) {
      updates.push(`tenant_count = $${paramIndex}`);
      params.push(tenantCount);
      paramIndex++;
    }

    if (updates.length === 0) {
      return;
    }

    updates.push(`changed_on = CURRENT_TIMESTAMP`);
    params.push(clientId);

    const updateQuery = `
      UPDATE licensing.client_activations
      SET ${updates.join(', ')}
      WHERE client_id = $${paramIndex} AND activation_status = 'active'
    `;

    await executeQuery(updateQuery, params);

  } catch (error) {
    console.error('License usage count update error:', error);
    // Don't throw - count updates shouldn't break the application
  }
}

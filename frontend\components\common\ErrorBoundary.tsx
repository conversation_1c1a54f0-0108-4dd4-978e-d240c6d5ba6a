/**
 * Universal Error Boundary Component
 *
 * Provides comprehensive error handling with retry logic, reporting,
 * and customizable fallback UI for different application contexts.
 */

'use client';

import React, { Component, ReactNode, ErrorInfo } from 'react';
import { handleError } from '@/lib/utils/error-handler';
import { UnifiedErrorDisplay } from './UnifiedErrorDisplay';

export interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  retryCount: number;
  errorId?: string;
}

export interface ErrorBoundaryProps {
  children: ReactNode;
  level?: 'page' | 'section' | 'component';
  maxRetries?: number;
  retryDelay?: number;
  enableReporting?: boolean;
  isolate?: boolean;
  fallback?: (props: {
    error: Error | null;
    errorInfo: ErrorInfo | null;
    resetError: () => void;
    canRetry: boolean;
    retryCount: number;
  }) => ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  private retryTimeoutId: NodeJS.Timeout | null = null;

  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const { enableReporting = true, onError, level = 'component' } = this.props;

    // Update state with error info
    this.setState({ errorInfo });

    // Call custom error handler
    if (onError) {
      onError(error, errorInfo);
    }

    // Use unified error handler
    if (enableReporting) {
      const errorResult = handleError(error, {
        component: 'error-boundary',
        operation: `${level}-error-boundary`,
        metadata: {
          level,
          componentStack: errorInfo?.componentStack,
          retryCount: this.state.retryCount,
          timestamp: new Date().toISOString(),
        }
      });
      this.setState({ errorId: errorResult.timestamp.toISOString() });
    }

    // Log error only in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Error caught by boundary:', error);
      console.error('Component stack:', errorInfo?.componentStack);
    }
  }

  resetError = () => {
    const { maxRetries = 3, retryDelay = 1000 } = this.props;
    const { retryCount } = this.state;

    if (retryCount < maxRetries) {
      this.setState({
        hasError: false,
        error: null,
        errorInfo: null,
        retryCount: retryCount + 1,
      });

      // Add delay before retry if specified
      if (retryDelay > 0) {
        this.retryTimeoutId = setTimeout(() => {
          // Force re-render after delay
          this.forceUpdate();
        }, retryDelay);
      }
    }
  };

  componentWillUnmount() {
    if (this.retryTimeoutId) {
      clearTimeout(this.retryTimeoutId);
    }
  }

  render() {
    const { hasError, error, errorInfo, retryCount } = this.state;
    const { children, fallback, maxRetries = 3, level = 'component' } = this.props;

    if (hasError) {
      const canRetry = retryCount < maxRetries;

      // Use custom fallback if provided
      if (fallback) {
        return fallback({
          error,
          errorInfo,
          resetError: this.resetError,
          canRetry,
          retryCount,
        });
      }

      // Default fallback UI based on level
      return this.renderDefaultFallback(error, canRetry, retryCount, level);
    }

    return children;
  }

  private renderDefaultFallback(
    error: Error | null,
    canRetry: boolean,
    retryCount: number,
    level: string
  ) {
    const variant = level === 'page' ? 'page' : level === 'section' ? 'card' : 'inline';

    return (
      <UnifiedErrorDisplay
        error={error}
        variant={variant}
        title={`${level.charAt(0).toUpperCase() + level.slice(1)} Error`}
        showRetry={canRetry}
        onRetry={this.resetError}
        showDetails={process.env.NODE_ENV === 'development'}
      />
    );
  }
}

export default ErrorBoundary;

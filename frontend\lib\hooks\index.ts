/**
 * Hooks Index - Centralized Hook Exports
 *
 * This file provides clean exports for all custom React hooks,
 * organized by functionality for better discoverability.
 *
 * ⚠️  IMPORTANT: All hooks are CLIENT-SIDE ONLY
 * These should never be imported in API routes or server components.
 */

// ===== PRIMARY STATE MANAGEMENT HOOKS =====
// useAppState removed - using simple auth pattern instead

// Authentication hooks (simple pattern)
export { useAuth } from './useAuth'

// ===== DATA FETCHING HOOKS =====
export {
  useData,
  useGet,
  usePost,
  usePut,
  useDelete
} from './useData'

// ===== LEGACY HOOKS (DEPRECATED) =====
// These hooks are deprecated and should not be used in new code
// Use useAuth instead for authentication
// export * from './useAuth'    // DEPRECATED: Use useAuth from @/lib/hooks/useAuth

// ===== DOMAIN-SPECIFIC HOOKS =====
// Business logic hooks for specific features - removed for simplicity
// Domain-specific hooks removed for simplicity
// Use simple patterns instead of complex hooks

// ===== REUSABLE PATTERN HOOKS =====
// Common UI patterns - simplified or removed for now
// Performance monitoring removed for simplicity

// ===== TYPE EXPORTS =====
// Types removed for simplicity - using simple patterns

/**
 * Hooks Index - Centralized Hook Exports
 *
 * This file provides clean exports for all custom React hooks,
 * organized by functionality for better discoverability.
 *
 * ⚠️  IMPORTANT: All hooks are CLIENT-SIDE ONLY
 * These should never be imported in API routes or server components.
 */

// ===== PRIMARY STATE MANAGEMENT HOOKS =====
export {
  useAppState,
  useUser,
  useTenantState,
  useSidebar,
  useTheme,
  useNotifications,
  useGlobalLoading,
  appStateUtils
} from './useAppState'

// Authentication hooks (simple pattern)
export { useAuth } from './useAuth'
export { useTenant } from './useTenant'

// ===== DATA FETCHING HOOKS =====
export {
  useData,
  useGet,
  usePost,
  usePut,
  useDelete
} from './useData'
export { useRenewals } from './useRenewals'
export { useOverviewData } from './useOverviewData'
export { useUniversalForm } from './useUniversalForm'
export { useSidebarPages } from './useSidebarPages'
export { usePageAccess } from './usePageAccess'
export { usePageInfo, usePageInfoByName } from './usePageInfo'

// ===== LEGACY HOOKS (DEPRECATED) =====
// These hooks are deprecated and should not be used in new code
// Use useAuth instead for authentication
// export * from './useAuth'    // DEPRECATED: Use useAuth from @/lib/hooks/useAuth

// ===== DOMAIN-SPECIFIC HOOKS =====
export { useSidebarPages } from './useSidebarPages'
export { usePageInfo } from './usePageInfo'
export { usePageAccess } from './usePageAccess'

// ===== REUSABLE PATTERN HOOKS =====
// Common UI patterns - simplified or removed for now
// Performance monitoring removed for simplicity

// ===== TYPE EXPORTS =====
// Types removed for simplicity - using simple patterns

/**
 * Vendor Dashboard Page
 * 
 * Comprehensive vendor analytics and management dashboard
 */

'use client'

import React, { useState, useCallback } from 'react'
import { useData } from '@/lib/hooks'
import { usePageInfoByName } from '@/lib/hooks/usePageInfo'
import { PageHeader } from '@/components/ui'
import PageAccessGuard from '@/components/auth/PageAccessGuard'
import {
  VendorDetailsTable,
  VendorSpendChart,
  TopVendorsWidget,
  VendorMetricsSummary
} from '@/components/vendor-dashboard'
import { LoadingPage } from '@/components/common/LoadingStates'

interface VendorData {
  vendor: string
  totalSpend: number
  renewalCount: number
  avgSpend: number
  lastRenewal?: string
  upcomingRenewals?: number
  reliabilityScore?: number
  currency?: string
}

interface VendorMetrics {
  totalVendors: number
  totalSpend: number
  avgSpendPerVendor: number
  topVendorSpend: number
  topVendorName: string
  vendorConcentration: number
  avgReliabilityScore: number
  vendorsWithUpcomingRenewals: number
  currency?: string
}

export default function VendorDashboardPage() {
  const [selectedVendor, setSelectedVendor] = useState<string | null>(null)
  
  // Get page info for the header
  const { pageInfo } = usePageInfoByName('vendor-dashboard')

  // Fetch vendor data
  const { 
    data: vendorData = [], 
    loading: vendorLoading, 
    error: vendorError 
  } = useData<VendorData[]>({
    endpoint: '/api/vendors/dashboard-data',
    cache: { key: 'vendor-dashboard-data', ttl: 5 * 60 * 1000 } // 5 minutes
  })

  // Fetch vendor metrics
  const { 
    data: metrics, 
    loading: metricsLoading, 
    error: metricsError 
  } = useData<VendorMetrics>({
    endpoint: '/api/vendors/metrics',
    cache: { key: 'vendor-metrics', ttl: 5 * 60 * 1000 } // 5 minutes
  })

  // Handle vendor selection
  const handleVendorClick = useCallback((vendor: string) => {
    setSelectedVendor(vendor)
    console.log('Selected vendor:', vendor)
  }, [])

  const handleViewAllVendors = useCallback(() => {
    console.log('View all vendors clicked')
    // Navigate to vendors page or show expanded view
  }, [])

  // Handle search functionality
  const handleSearch = useCallback((query: string) => {
    console.log('Search query:', query)
    // Implement vendor search functionality
  }, [])

  const isLoading = vendorLoading || metricsLoading
  const hasError = vendorError || metricsError

  if (isLoading) {
    return <LoadingPage title="Loading vendor dashboard..." />
  }

  if (hasError) {
    return (
      <div className="p-6">
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <h3 className="text-red-800 font-medium">Error Loading Dashboard</h3>
          <p className="text-red-600 mt-1">
            {vendorError || metricsError || 'Failed to load vendor data'}
          </p>
        </div>
      </div>
    )
  }

  // Default metrics if not loaded
  const defaultMetrics: VendorMetrics = {
    totalVendors: 0,
    totalSpend: 0,
    avgSpendPerVendor: 0,
    topVendorSpend: 0,
    topVendorName: 'N/A',
    vendorConcentration: 0,
    avgReliabilityScore: 0,
    vendorsWithUpcomingRenewals: 0,
    currency: 'USD'
  }

  const safeMetrics = metrics || defaultMetrics

  return (
    <PageAccessGuard pageName="vendor-dashboard">
      <div className="vendor-dashboard-page">
        {/* Page Header */}
        <PageHeader
          title={pageInfo?.header || 'Vendor Dashboard'}
          subtitle={pageInfo?.description || 'Comprehensive vendor analytics and management'}
          onSearch={handleSearch}
          searchPlaceholder="Search vendors..."
        />

        {/* Metrics Summary */}
        <div className="mb-6">
          <VendorMetricsSummary
            metrics={safeMetrics}
            isLoading={metricsLoading}
            currency={safeMetrics.currency}
          />
        </div>

        {/* Main Dashboard Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          {/* Top Vendors Widget */}
          <TopVendorsWidget
            data={vendorData}
            showCount={5}
            currency={safeMetrics.currency}
            onVendorClick={handleVendorClick}
            onViewAll={handleViewAllVendors}
          />

          {/* Vendor Spend Chart */}
          <VendorSpendChart
            data={vendorData}
            currency={safeMetrics.currency}
            title="Vendor Spending Distribution"
          />
        </div>

        {/* Vendor Details Table */}
        <div className="mb-6">
          <VendorDetailsTable
            data={vendorData.map(vendor => ({
              id: vendor.vendor,
              vendor: vendor.vendor,
              product_name: 'Multiple Products', // This would come from detailed API
              cost: vendor.totalSpend,
              currency: vendor.currency || 'USD',
              renewal_date: vendor.lastRenewal || '',
              reliability_score: vendor.reliabilityScore || 0,
              relationship_duration: 'N/A', // This would come from detailed API
              description: `${vendor.renewalCount} renewals, avg spend: ${vendor.avgSpend}`
            }))}
            isLoading={vendorLoading}
          />
        </div>

        {/* Selected Vendor Details */}
        {selectedVendor && (
          <div className="bg-white border rounded-lg p-6">
            <h3 className="text-lg font-semibold mb-4">
              {selectedVendor} - Detailed View
            </h3>
            <p className="text-gray-600">
              Detailed vendor information would be displayed here.
            </p>
            <button
              onClick={() => setSelectedVendor(null)}
              className="mt-4 px-4 py-2 bg-gray-100 text-gray-700 rounded hover:bg-gray-200"
            >
              Close Details
            </button>
          </div>
        )}
      </div>
    </PageAccessGuard>
  )
}

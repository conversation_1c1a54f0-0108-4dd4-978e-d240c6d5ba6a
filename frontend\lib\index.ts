/**
 * Library Index - Centralized Exports
 *
 * This file provides clean, organized exports for all library utilities,
 * making imports more consistent and easier to manage.
 *
 * ⚠️  IMPORTANT: Client vs Server Separation
 * - Client-safe exports: Can be used in both client and server components
 * - Server-only exports: Should ONLY be used in API routes and server components
 */

// ===== CORE TYPES (Client-safe) =====
export type {
  // Core interfaces
  TenantContext,
  User,
  AuthSession,
  ApiResponse,
  PaginatedResponse,
  DbResult,
  QueryOptions
} from './types'

// ===== CONSTANTS (Client-safe) =====
export * from './constants'

// ===== API UTILITIES (Client-safe) =====
export {
  createSuccessResponse,
  createErrorResponse,
  withErrorHandling
} from './api'
export type { ApiErrorCode, HttpStatus } from './api'

// ===== CONFIGURATION (Client-safe only) =====
// Note: Server-only config should be imported directly from './config' in API routes

// ===== HOOKS (Client-side only) =====
export {
  useData,
  useAppState,
  useRenewals,
  useLicense,
  useOverviewData,
  useReports,
  useTenantLogs,
  useAdminAccess
} from './hooks'

// ===== UTILITIES (Client-safe) =====
export {
  formatCurrency,
  formatDate,
  debounce,
  cn
} from './utils'

// ===== SERVICES (Client-safe) =====
export {
  appStateService,
  amplifyService
} from './services'

// ===== SERVER-ONLY EXPORTS =====
// ⚠️  WARNING: These should ONLY be imported in API routes and server components
// DO NOT import these in client components as they may contain server-only code

// Server actions
export {
  generateCSRFToken
} from './server'

// Note: The following are intentionally NOT exported here to prevent client-side usage:
// - Database utilities (import directly from './database' in API routes)
// - Tenant server utilities (import directly from './tenant' in API routes)
// - Security utilities (import directly from './security' in API routes)
// - Monitoring utilities (import directly from './monitoring' in API routes)
// - Authentication utilities (import directly from './auth' in API routes)

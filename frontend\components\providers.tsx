/**
 * Application Providers - Simplified
 *
 * Minimal provider setup to prevent blocking initialization issues.
 */

'use client'

import { ReactNode, useEffect } from 'react'
import { AppProvider } from '@/contexts/AppContext'
import { ToastProvider } from '@/components/ui/Toast'
import { ErrorBoundary } from '@/components/common'

export function Providers({ children }: { children: ReactNode }) {
  // Simplified providers without complex auth context
  return (
    <ErrorBoundary level="page" maxRetries={3}>
      <AppProvider>
        <ToastProvider>
          {children}
        </ToastProvider>
      </AppProvider>
    </ErrorBoundary>
  )
}

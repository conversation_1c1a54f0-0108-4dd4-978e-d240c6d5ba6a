/**
 * API Client with Seamless Authentication
 * 
 * This client automatically handles token refresh and authentication
 * to provide a seamless user experience without 401 errors.
 */

import { fetchAuthSession } from 'aws-amplify/auth';
import { csrfTokenManager } from '@/lib/security';

interface ApiOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  body?: any;
  headers?: Record<string, string>;
  skipAuth?: boolean;
  signal?: AbortSignal;
}

interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  status: number;
}

class ApiClient {
  private baseUrl: string;

  constructor() {
    // Use environment variable for base URL, fallback to current origin
    this.baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL ||
                   (typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000');
  }

  /**
   * Make an authenticated API request
   */
  async request<T = any>(endpoint: string, options: ApiOptions = {}): Promise<ApiResponse<T>> {
    const { method = 'GET', body, headers = {}, skipAuth = false, signal } = options;

    try {
      // Check authentication unless explicitly skipped
      if (!skipAuth) {
        try {
          const session = await fetchAuthSession();
          if (!session?.tokens?.idToken) {
            return {
              success: false,
              error: 'Authentication required',
              status: 401
            };
          }

          // Validate token expiration
          const token = session.tokens.idToken
          const payload = JSON.parse(atob(token.toString().split('.')[1]))
          const now = Math.floor(Date.now() / 1000)

          if (payload.exp < now) {
            return {
              success: false,
              error: 'Token expired',
              status: 401
            };
          }

        } catch (error) {
          return {
            success: false,
            error: 'Authentication required',
            status: 401
          };
        }
      }

      // Prepare request headers
      const requestHeaders: Record<string, string> = {
        'Content-Type': 'application/json',
        ...headers
      };

      // Add CSRF token for state-changing operations
      if (['POST', 'PUT', 'DELETE', 'PATCH'].includes(method) && !skipAuth) {
        try {
          const csrfToken = await csrfTokenManager.getToken();
          requestHeaders['x-csrf-token'] = csrfToken;
        } catch (error) {
          console.warn('Failed to get CSRF token:', error);
          // Continue without CSRF token - let server handle the error
        }
      }

      // Prepare request options
      const requestOptions: RequestInit = {
        method,
        headers: requestHeaders,
        credentials: 'include', // Include cookies for authentication
        signal
      };

      // Add body for non-GET requests
      if (body && method !== 'GET') {
        requestOptions.body = JSON.stringify(body);
      }

      // Make the request
      const response = await fetch(`${this.baseUrl}/api${endpoint}`, requestOptions);
      
      // Handle different response types
      let data: T | undefined;
      const contentType = response.headers.get('content-type');
      
      if (contentType?.includes('application/json')) {
        data = await response.json();
      } else {
        data = await response.text() as any;
      }

      if (!response.ok) {
        // Handle 401 errors by checking authentication
        if (response.status === 401 && !skipAuth) {
          console.log('Received 401, checking authentication...');

          try {
            const session = await fetchAuthSession();
            if (session?.tokens?.idToken) {
              // Retry the request once if we have a valid session
              console.log('Valid session found, retrying request...');
              return this.request(endpoint, { ...options, skipAuth: true });
            }
          } catch (error) {
            console.log('No valid session found');
          }
        }

        return {
          success: false,
          error: (data as any)?.error || `HTTP ${response.status}: ${response.statusText}`,
          status: response.status,
          data
        };
      }

      // Handle API response format - extract data from nested structure
      if (data && typeof data === 'object' && 'success' in data && 'data' in data) {
        return {
          success: (data as any).success,
          data: (data as any).data,
          status: response.status,
          error: (data as any).error
        };
      }

      return {
        success: true,
        data,
        status: response.status
      };

    } catch (error) {
      console.error('API request failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error',
        status: 0
      };
    }
  }

  /**
   * GET request
   */
  async get<T = any>(endpoint: string, options: Omit<ApiOptions, 'method'> = {}): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'GET' });
  }

  /**
   * POST request
   */
  async post<T = any>(endpoint: string, body?: any, options: Omit<ApiOptions, 'method' | 'body'> = {}): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'POST', body });
  }

  /**
   * PUT request
   */
  async put<T = any>(endpoint: string, body?: any, options: Omit<ApiOptions, 'method' | 'body'> = {}): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'PUT', body });
  }

  /**
   * DELETE request
   */
  async delete<T = any>(endpoint: string, options: Omit<ApiOptions, 'method'> = {}): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'DELETE' });
  }

  /**
   * PATCH request
   */
  async patch<T = any>(endpoint: string, body?: any, options: Omit<ApiOptions, 'method' | 'body'> = {}): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'PATCH', body });
  }
}

// Export singleton instance
export const apiClient = new ApiClient();

// Export convenience functions
export const api = {
  get: <T = any>(endpoint: string, options?: Omit<ApiOptions, 'method'>) => 
    apiClient.get<T>(endpoint, options),
  
  post: <T = any>(endpoint: string, body?: any, options?: Omit<ApiOptions, 'method' | 'body'>) => 
    apiClient.post<T>(endpoint, body, options),
  
  put: <T = any>(endpoint: string, body?: any, options?: Omit<ApiOptions, 'method' | 'body'>) => 
    apiClient.put<T>(endpoint, body, options),
  
  delete: <T = any>(endpoint: string, options?: Omit<ApiOptions, 'method'>) => 
    apiClient.delete<T>(endpoint, options),
  
  patch: <T = any>(endpoint: string, body?: any, options?: Omit<ApiOptions, 'method' | 'body'>) => 
    apiClient.patch<T>(endpoint, body, options)
};

// Export types
export type { ApiResponse, ApiOptions };

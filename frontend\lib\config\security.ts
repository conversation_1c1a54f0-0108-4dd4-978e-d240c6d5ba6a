/**
 * Security Configuration
 * 
 * Centralized security settings and constants
 */

/**
 * Rate limiting configuration
 */
export const RATE_LIMITS = {
  // Standard API rate limits
  STANDARD: {
    requests: 100,
    windowMs: 60000 // 1 minute
  },
  
  // Strict rate limits for sensitive operations
  STRICT: {
    requests: 10,
    windowMs: 60000 // 1 minute
  },
  
  // Authentication rate limits
  AUTH: {
    requests: 5,
    windowMs: 300000 // 5 minutes
  },
  
  // User creation rate limits
  USER_CREATION: {
    requests: 10,
    windowMs: 60000 // 1 minute
  }
} as const

/**
 * CORS configuration
 */
export const CORS_CONFIG = {
  // Allowed origins (should be configured via environment)
  allowedOrigins: [
    'https://auth.renewtrack.com',
    'https://app.renewtrack.com',
    'http://localhost:3000' // Development only
  ],
  
  // Allowed methods
  allowedMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  
  // Allowed headers
  allowedHeaders: [
    'Content-Type',
    'Authorization',
    'X-Requested-With',
    'X-CSRF-Token'
  ],
  
  // Credentials
  credentials: true,
  
  // Max age for preflight requests
  maxAge: 86400 // 24 hours
} as const

/**
 * CSRF protection configuration
 */
export const CSRF_CONFIG = {
  // Token length
  tokenLength: 32,
  
  // Cookie settings
  cookie: {
    name: 'csrf-token',
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict' as const,
    maxAge: 3600000 // 1 hour
  }
} as const

/**
 * Input sanitization configuration
 */
export const SANITIZATION_CONFIG = {
  // HTML sanitization options
  html: {
    allowedTags: [],
    allowedAttributes: {},
    disallowedTagsMode: 'discard'
  },
  
  // SQL injection protection
  sql: {
    escapeQuotes: true,
    removeComments: true,
    validateParams: true
  }
} as const

/**
 * Audit logging configuration
 */
export const AUDIT_CONFIG = {
  // Events to always log
  criticalEvents: [
    'USER_LOGIN',
    'USER_LOGOUT',
    'PASSWORD_CHANGE',
    'ROLE_CHANGE',
    'DATA_EXPORT',
    'SYSTEM_CONFIG_CHANGE'
  ],
  
  // Retention period
  retentionDays: 365,
  
  // Log levels
  levels: {
    LOW: 'low',
    MEDIUM: 'medium',
    HIGH: 'high',
    CRITICAL: 'critical'
  } as const
} as const

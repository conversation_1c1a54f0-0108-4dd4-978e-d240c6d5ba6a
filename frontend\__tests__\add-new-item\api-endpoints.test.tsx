/**
 * Add New Item API Endpoints Tests
 * 
 * Tests the API endpoints for creating new vendors, products, and versions
 * to ensure they work correctly with the add new functionality.
 */

// Mock fetch globally
global.fetch = jest.fn();
const mockFetch = fetch as jest.MockedFunction<typeof fetch>;

describe('Add New Item API Endpoints', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Vendor Creation API', () => {
    it('should create new vendor successfully', async () => {
      const mockVendorData = {
        id: '3',
        name: 'New Vendor Corp',
        display_name: 'New Vendor Corporation',
        created_on: '2024-01-01T00:00:00Z'
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 201,
        json: () => Promise.resolve({
          data: mockVendorData,
          message: 'Vendor created successfully'
        }),
      } as Response);

      const response = await fetch('/api/tenant-vendors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: 'New Vendor Corp',
          description: 'A new vendor for testing'
        }),
      });

      expect(response.ok).toBe(true);
      expect(response.status).toBe(201);

      const result = await response.json();
      expect(result.data).toEqual(mockVendorData);
      expect(result.message).toBe('Vendor created successfully');
    });

    it('should handle vendor creation validation errors', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        json: () => Promise.resolve({
          error: 'Validation failed',
          validationErrors: {
            name: 'Vendor name is required'
          }
        }),
      } as Response);

      const response = await fetch('/api/tenant-vendors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: '' // Empty name should cause validation error
        }),
      });

      expect(response.ok).toBe(false);
      expect(response.status).toBe(400);

      const result = await response.json();
      expect(result.error).toBe('Validation failed');
      expect(result.validationErrors.name).toBe('Vendor name is required');
    });

    it('should handle duplicate vendor creation', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 409,
        json: () => Promise.resolve({
          error: 'A vendor with this name already exists'
        }),
      } as Response);

      const response = await fetch('/api/tenant-vendors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: 'Existing Vendor'
        }),
      });

      expect(response.ok).toBe(false);
      expect(response.status).toBe(409);

      const result = await response.json();
      expect(result.error).toBe('A vendor with this name already exists');
    });
  });

  describe('Product Creation API', () => {
    it('should create new product successfully', async () => {
      const mockProductData = {
        id: '2',
        name: 'New Product Suite',
        vendor_id: '1',
        description: 'A comprehensive product suite',
        created_on: '2024-01-01T00:00:00Z'
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 201,
        json: () => Promise.resolve({
          data: mockProductData,
          message: 'Product created successfully'
        }),
      } as Response);

      const response = await fetch('/api/tenant-products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: 'New Product Suite',
          vendor_id: '1',
          description: 'A comprehensive product suite'
        }),
      });

      expect(response.ok).toBe(true);
      expect(response.status).toBe(201);

      const result = await response.json();
      expect(result.data).toEqual(mockProductData);
      expect(result.message).toBe('Product created successfully');
    });

    it('should handle product creation with invalid vendor', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        json: () => Promise.resolve({
          error: 'Vendor not found'
        }),
      } as Response);

      const response = await fetch('/api/tenant-products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: 'New Product',
          vendor_id: '999' // Non-existent vendor
        }),
      });

      expect(response.ok).toBe(false);
      expect(response.status).toBe(404);

      const result = await response.json();
      expect(result.error).toBe('Vendor not found');
    });

    it('should handle duplicate product creation', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 409,
        json: () => Promise.resolve({
          error: 'A product with this name already exists for this vendor'
        }),
      } as Response);

      const response = await fetch('/api/tenant-products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: 'Existing Product',
          vendor_id: '1'
        }),
      });

      expect(response.ok).toBe(false);
      expect(response.status).toBe(409);

      const result = await response.json();
      expect(result.error).toBe('A product with this name already exists for this vendor');
    });
  });

  describe('Product Version Creation API', () => {
    it('should create new product version successfully', async () => {
      const mockVersionData = {
        id: '3',
        version: '2024',
        product_id: '1',
        release_date: '2024-01-01',
        created_on: '2024-01-01T00:00:00Z'
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 201,
        json: () => Promise.resolve({
          data: mockVersionData,
          message: 'Version created successfully'
        }),
      } as Response);

      const response = await fetch('/api/tenant-product-versions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          version: '2024',
          product_id: '1',
          release_date: '2024-01-01'
        }),
      });

      expect(response.ok).toBe(true);
      expect(response.status).toBe(201);

      const result = await response.json();
      expect(result.data).toEqual(mockVersionData);
      expect(result.message).toBe('Version created successfully');
    });

    it('should handle version creation with invalid product', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        json: () => Promise.resolve({
          error: 'Product not found'
        }),
      } as Response);

      const response = await fetch('/api/tenant-product-versions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          version: '2024',
          product_id: '999' // Non-existent product
        }),
      });

      expect(response.ok).toBe(false);
      expect(response.status).toBe(404);

      const result = await response.json();
      expect(result.error).toBe('Product not found');
    });

    it('should handle duplicate version creation', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 409,
        json: () => Promise.resolve({
          error: 'A version with this name already exists for this product'
        }),
      } as Response);

      const response = await fetch('/api/tenant-product-versions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          version: '2023',
          product_id: '1'
        }),
      });

      expect(response.ok).toBe(false);
      expect(response.status).toBe(409);

      const result = await response.json();
      expect(result.error).toBe('A version with this name already exists for this product');
    });

    it('should handle version creation validation errors', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        json: () => Promise.resolve({
          error: 'Validation failed',
          validationErrors: {
            version: 'Version name is required',
            product_id: 'Product ID is required'
          }
        }),
      } as Response);

      const response = await fetch('/api/tenant-product-versions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          version: '', // Empty version
          product_id: '' // Empty product_id
        }),
      });

      expect(response.ok).toBe(false);
      expect(response.status).toBe(400);

      const result = await response.json();
      expect(result.error).toBe('Validation failed');
      expect(result.validationErrors.version).toBe('Version name is required');
      expect(result.validationErrors.product_id).toBe('Product ID is required');
    });
  });

  describe('Authentication and Authorization', () => {
    it('should require authentication for vendor creation', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 401,
        json: () => Promise.resolve({
          error: 'Authentication required'
        }),
      } as Response);

      const response = await fetch('/api/tenant-vendors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: 'New Vendor'
        }),
      });

      expect(response.ok).toBe(false);
      expect(response.status).toBe(401);

      const result = await response.json();
      expect(result.error).toBe('Authentication required');
    });

    it('should require proper tenant context', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 403,
        json: () => Promise.resolve({
          error: 'Access denied'
        }),
      } as Response);

      const response = await fetch('/api/tenant-products', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: 'New Product',
          vendor_id: '1'
        }),
      });

      expect(response.ok).toBe(false);
      expect(response.status).toBe(403);

      const result = await response.json();
      expect(result.error).toBe('Access denied');
    });
  });
});

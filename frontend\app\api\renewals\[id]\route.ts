/**
 * Individual Renewal API Endpoint
 * 
 * Provides CRUD operations for individual renewals
 * GET /api/renewals/[id] - Get renewal details
 * PATCH /api/renewals/[id] - Update renewal
 * DELETE /api/renewals/[id] - Delete renewal
 */

import { NextRequest } from 'next/server';
import { fetchAuthSession, fetchUserAttributes } from 'aws-amplify/auth';
import { getClientByEmailDomain } from '@/lib/tenant/clients';

import {
  createSuccessResponse,
  createErrorResponse,
  ApiErrorCode,
  HttpStatus,
  withErrorHandling
} from '@/lib/api/response';

import { executeQuery, schemaExists } from '@/lib/database';
import { Renewal } from '@/lib/types';

// GET /api/renewals/[id] - Get individual renewal details
export const GET = withErrorHandling(async (request: NextRequest, { params }: { params: Promise<{ id: string }> }) => {
  // Verify authentication using Amplify
  try {
    const session = await fetchAuthSession();
    if (!session?.tokens?.idToken) {
      return createErrorResponse(
        'Authentication required',
        ApiErrorCode.UNAUTHORIZED,
        HttpStatus.UNAUTHORIZED
      );
    }
  } catch (error) {
    return createErrorResponse(
      'Authentication failed',
      ApiErrorCode.UNAUTHORIZED,
      HttpStatus.UNAUTHORIZED
    );
  }

  // Get user attributes for tenant context
  const userAttributes = await fetchUserAttributes();
  const userEmail = userAttributes.email;

  if (!userEmail) {
    return createErrorResponse(
      'User email not found',
      ApiErrorCode.UNAUTHORIZED,
      HttpStatus.UNAUTHORIZED
    );
  }

  const clientResult = await getClientByEmailDomain(userEmail);

  if (!clientResult.success) {
    return createErrorResponse(
      'Failed to fetch tenant information',
      ApiErrorCode.DATABASE_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }

  const tenant = clientResult.client!;
  const { id: renewalId } = await params;

  if (!renewalId) {
    return createErrorResponse(
      'Renewal ID is required',
      ApiErrorCode.VALIDATION_ERROR,
      HttpStatus.BAD_REQUEST
    );
  }

  // Check if tenant schema exists
  const schemaReady = await schemaExists(tenant.tenantSchema);
  if (!schemaReady) {
    return createErrorResponse(
      'Tenant schema not found',
      ApiErrorCode.DATABASE_ERROR,
      HttpStatus.NOT_FOUND
    );
  }

  try {
    // Query for specific renewal with detailed information
    const renewalQuery = `
      SELECT
        r.id,
        r.name,
        COALESCE(v.display_name, v.name, 'Unknown') as vendor,
        COALESCE(rt.name, 'Unknown') as type,
        COALESCE(pt.name, 'Unknown') as purchase_type,
        r.licensed_date,
        r.start_date,
        r.associated_emails,
        r.reseller,
        r.cost,
        COALESCE(c.currency_id, 'CAD') as currency,
        r.cost_code,
        r.license_count,
        r.description,
        r.notes,
        COALESCE(s.name, 'Active') as status,
        r.created_on,
        r.changed_on,
        (SELECT COUNT(*) FROM "${tenant.tenantSchema}".tenant_alerts a WHERE a.renewal_id = r.id AND a.is_active = true) as alerts
      FROM "${tenant.tenantSchema}".tenant_renewals r
      LEFT JOIN "${tenant.tenantSchema}".tenant_vendors v ON r.vendor_id = v.id
      LEFT JOIN metadata.global_renewal_types rt ON r.renewal_type_id = rt.id
      LEFT JOIN metadata.global_purchase_types pt ON r.purchase_type_id = pt.id
      LEFT JOIN metadata.global_currencies c ON r.currency_id = c.currency_id
      LEFT JOIN metadata.global_statuses s ON r.status_id = s.id
      WHERE r.id = $1 AND r.is_deleted = false
    `;

    const result = await executeQuery(renewalQuery, [renewalId]);

    if (!result.success) {
      throw new Error(result.error || 'Failed to execute query');
    }

    if (!result.data || result.data.length === 0) {
      return createErrorResponse(
        'Renewal not found',
        ApiErrorCode.NOT_FOUND,
        HttpStatus.NOT_FOUND
      );
    }

    const row = result.data[0];

    // Format the result
    const renewal: Renewal = {
      id: row.id.toString(),
      name: row.name,
      product_name: row.product_name || '',
      version: row.version || '',
      vendor: row.vendor || '',
      type: row.type,
      start_date: row.start_date ? new Date(row.start_date).toISOString().split('T')[0] : '',
      cost: parseFloat(row.cost) || 0,
      currency: row.currency,
      status: row.status as 'active' | 'inactive' | 'pending' | 'expired',
      alerts: parseInt(row.alerts) || 0,
      license_count: parseInt(row.license_count) || 0,
      description: row.description || '',
      // Additional detailed fields
      purchase_type: row.purchase_type,
      department: row.department || '',
      licensed_date: row.licensed_date ? new Date(row.licensed_date).toISOString().split('T')[0] : '',
      associated_emails: row.associated_emails || '',
      reseller: row.reseller || '',
      cost_code: row.cost_code || '',
      notes: row.notes || '',
      created_on: row.created_on ? new Date(row.created_on) : new Date(),
      changed_on: row.changed_on ? new Date(row.changed_on) : undefined
    };

    console.log(`Renewal details fetched successfully for ID: ${renewalId}`);

    return createSuccessResponse(
      renewal,
      'Renewal details retrieved successfully'
    );

  } catch (error) {
    console.error('Error fetching renewal details:', error);
    return createErrorResponse(
      'Failed to fetch renewal details',
      ApiErrorCode.DATABASE_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
});

// PATCH /api/renewals/[id] - Update renewal
export const PATCH = withErrorHandling(async (request: NextRequest, { params }: { params: Promise<{ id: string }> }) => {
  // Verify authentication using Amplify
  let session;
  try {
    session = await fetchAuthSession();
    if (!session?.tokens?.idToken) {
      return createErrorResponse(
        'Authentication required',
        ApiErrorCode.UNAUTHORIZED,
        HttpStatus.UNAUTHORIZED
      );
    }
  } catch (error) {
    return createErrorResponse(
      'Authentication failed',
      ApiErrorCode.UNAUTHORIZED,
      HttpStatus.UNAUTHORIZED
    );
  }

  // Get user attributes for tenant context
  const userAttributes = await fetchUserAttributes();
  const userEmail = userAttributes.email;

  if (!userEmail) {
    return createErrorResponse(
      'User email not found',
      ApiErrorCode.UNAUTHORIZED,
      HttpStatus.UNAUTHORIZED
    );
  }

  const clientResult = await getClientByEmailDomain(userEmail);

  if (!clientResult.success) {
    return createErrorResponse(
      'Failed to fetch tenant information',
      ApiErrorCode.DATABASE_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }

  const tenant = clientResult.client!;
  const { id: renewalId } = await params;

  if (!renewalId) {
    return createErrorResponse(
      'Renewal ID is required',
      ApiErrorCode.VALIDATION_ERROR,
      HttpStatus.BAD_REQUEST
    );
  }

  try {
    const updateData = await request.json();

    // Build dynamic update query based on provided fields
    const updateFields: string[] = [];
    const updateValues: any[] = [];
    let paramIndex = 1;

    // Map of frontend field names to database column names
    const fieldMapping: { [key: string]: string } = {
      name: 'renewal_name',
      vendor: 'vendor_id', // Note: This should be handled as a lookup
      renewalTypeId: 'renewal_type_id',
      purchaseTypeId: 'purchase_type_id',
      licensedDate: 'licensed_date',
      start_date: 'start_date',
      associatedEmails: 'associated_emails',
      reseller: 'reseller',
      currencyId: 'currency_id',
      cost: 'cost',
      costCode: 'cost_code',
      licenseCount: 'license_count',
      description: 'description',
      notes: 'notes',
      statusId: 'status_id' // Note: This should be handled as a lookup
    };

    // Process each field in the update data
    Object.keys(updateData).forEach(key => {
      if (fieldMapping[key] && updateData[key] !== undefined) {
        updateFields.push(`"${fieldMapping[key]}" = $${paramIndex}`);

        // Special handling for arrays (associatedEmails)
        if (key === 'associatedEmails' && Array.isArray(updateData[key])) {
          updateValues.push(updateData[key].join(', '));
        } else {
          updateValues.push(updateData[key]);
        }

        paramIndex++;
      }
    });

    if (updateFields.length === 0) {
      return createErrorResponse(
        'No valid fields to update',
        ApiErrorCode.VALIDATION_ERROR,
        HttpStatus.BAD_REQUEST
      );
    }

    // Add UpdatedAt field
    updateFields.push(`"UpdatedAt" = $${paramIndex}`);
    updateValues.push(new Date());
    paramIndex++;

    // Add WHERE clause parameter
    updateValues.push(renewalId);

    const updateQuery = `
      UPDATE "${tenant.tenantSchema}"."Renewals"
      SET ${updateFields.join(', ')}
      WHERE "RenewalID" = $${paramIndex} AND ("is_deleted" = false OR "is_deleted" IS NULL)
      RETURNING "RenewalID" as id
    `;

    const result = await executeQuery(updateQuery, updateValues);

    if (!result.success) {
      throw new Error(result.error || 'Failed to update renewal');
    }

    if (!result.data || result.data.length === 0) {
      return createErrorResponse(
        'Renewal not found or update failed',
        ApiErrorCode.NOT_FOUND,
        HttpStatus.NOT_FOUND
      );
    }

    console.log(`Renewal updated successfully: ${renewalId}`);

    return createSuccessResponse(
      { id: renewalId, ...updateData },
      'Renewal updated successfully'
    );

  } catch (error) {
    console.error('Error updating renewal:', error);
    return createErrorResponse(
      'Failed to update renewal',
      ApiErrorCode.DATABASE_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
});

// DELETE /api/renewals/[id] - Delete renewal (soft delete)
export const DELETE = withErrorHandling(async (request: NextRequest, { params }: { params: Promise<{ id: string }> }) => {
  // Verify authentication using Amplify
  let session;
  try {
    session = await fetchAuthSession();
    if (!session?.tokens?.idToken) {
      return createErrorResponse(
        'Authentication required',
        ApiErrorCode.UNAUTHORIZED,
        HttpStatus.UNAUTHORIZED
      );
    }
  } catch (error) {
    return createErrorResponse(
      'Authentication failed',
      ApiErrorCode.UNAUTHORIZED,
      HttpStatus.UNAUTHORIZED
    );
  }

  // Get user attributes for tenant context
  const userAttributes = await fetchUserAttributes();
  const userEmail = userAttributes.email;

  if (!userEmail) {
    return createErrorResponse(
      'User email not found',
      ApiErrorCode.UNAUTHORIZED,
      HttpStatus.UNAUTHORIZED
    );
  }
  const clientResult = await getClientByEmailDomain(userEmail);

  if (!clientResult.success) {
    return createErrorResponse(
      'Failed to fetch tenant information',
      ApiErrorCode.DATABASE_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }

  const tenant = clientResult.client!;
  const { id: renewalId } = await params;

  if (!renewalId) {
    return createErrorResponse(
      'Renewal ID is required',
      ApiErrorCode.VALIDATION_ERROR,
      HttpStatus.BAD_REQUEST
    );
  }

  try {
    // Soft delete by setting is_deleted = true
    const deleteQuery = `
      UPDATE "${tenant.tenantSchema}"."Renewals"
      SET "is_deleted" = true, "UpdatedAt" = $1
      WHERE "RenewalID" = $2 AND ("is_deleted" = false OR "is_deleted" IS NULL)
      RETURNING "RenewalID" as id
    `;

    const result = await executeQuery(deleteQuery, [new Date(), renewalId]);

    if (!result.success) {
      throw new Error(result.error || 'Failed to delete renewal');
    }

    if (!result.data || result.data.length === 0) {
      return createErrorResponse(
        'Renewal not found',
        ApiErrorCode.NOT_FOUND,
        HttpStatus.NOT_FOUND
      );
    }

    console.log(`Renewal deleted successfully: ${renewalId}`);

    return createSuccessResponse(
      { id: renewalId },
      'Renewal deleted successfully'
    );

  } catch (error) {
    console.error('Error deleting renewal:', error);
    return createErrorResponse(
      'Failed to delete renewal',
      ApiErrorCode.DATABASE_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
});

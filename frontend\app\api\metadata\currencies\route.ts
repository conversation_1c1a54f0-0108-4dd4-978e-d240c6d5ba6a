/**
 * Currencies API Endpoint
 * 
 * Provides access to currency metadata
 * GET /api/metadata/currencies - Returns active currencies
 */

import { NextRequest } from 'next/server';
import { fetchAuthSession } from 'aws-amplify/auth';
import {
  createSuccessResponse,
  createErrorResponse,
  ApiErrorCode,
  HttpStatus,
  withErrorHandling
} from '@/lib/api/response';
import { executeQuery } from '@/lib/database';
import { Currency } from '@/app/api/metadata/route';
import { STATUS } from '@/lib/constants/app-constants';

// GET /api/metadata/currencies - Get active currencies
export const GET = withErrorHandling(async (request: NextRequest) => {
  // Verify authentication using Amplify
  try {
    const session = await fetchAuthSession();
    if (!session?.tokens?.idToken) {
      return createErrorResponse(
        'Authentication required',
        ApiErrorCode.UNAUTHORIZED,
        HttpStatus.UNAUTHORIZED
      );
    }
  } catch (error) {
    return createErrorResponse(
      'Authentication failed',
      ApiErrorCode.UNAUTHORIZED,
      HttpStatus.UNAUTHORIZED
    );
  }

  try {
    // Query currencies from metadata schema
    const result = await executeQuery<Currency>(
      `SELECT
        id,
        code,
        name,
        symbol,
        status,
        display_order
      FROM metadata.global_currencies
      WHERE status = '${STATUS.ACTIVE}'
      ORDER BY display_order ASC, name ASC`,
      []
    );

    if (!result.success) {
      console.error('Failed to fetch currencies:', result.error);
      return createErrorResponse(
        'Failed to fetch currencies',
        ApiErrorCode.DATABASE_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }

    // Log successful fetch
    console.log(`Currencies fetched successfully: ${result.data?.length || 0} records`);

    return createSuccessResponse(
      result.data || [],
      'Currencies retrieved successfully'
    );

  } catch (error) {
    console.error('Error fetching currencies:', error);
    return createErrorResponse(
      'Failed to fetch currencies',
      ApiErrorCode.DATABASE_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
});

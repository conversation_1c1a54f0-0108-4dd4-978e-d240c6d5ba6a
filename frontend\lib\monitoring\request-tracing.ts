/**
 * Request Tracing and Correlation System
 * 
 * Provides comprehensive request tracing with correlation IDs,
 * distributed tracing capabilities, and tenant-specific tracking.
 */

import { NextRequest } from 'next/server';
import { TenantContext } from '@/lib/types';
import { AuthSession } from '@/lib/api/auth-middleware';

export interface TraceContext {
  correlationId: string;
  traceId: string;
  spanId: string;
  parentSpanId?: string;
  tenantId?: string;
  userId?: string;
  sessionId?: string;
  requestId: string;
  timestamp: Date;
  source: string;
  operation: string;
  resource: string;
  metadata: Record<string, any>;
}

export interface TraceSpan {
  spanId: string;
  parentSpanId?: string;
  operationName: string;
  startTime: Date;
  endTime?: Date;
  duration?: number;
  status: 'pending' | 'success' | 'error';
  tags: Record<string, any>;
  logs: Array<{
    timestamp: Date;
    level: 'debug' | 'info' | 'warn' | 'error';
    message: string;
    fields?: Record<string, any>;
  }>;
  error?: {
    name: string;
    message: string;
    stack?: string;
    code?: string;
  };
}

export interface DistributedTrace {
  traceId: string;
  correlationId: string;
  tenantId?: string;
  userId?: string;
  startTime: Date;
  endTime?: Date;
  duration?: number;
  spans: TraceSpan[];
  status: 'pending' | 'success' | 'error';
  metadata: Record<string, any>;
}

class RequestTracingService {
  private traces: Map<string, DistributedTrace> = new Map();
  private activeSpans: Map<string, TraceSpan> = new Map();
  private maxTraces = 1000; // Maximum traces to keep in memory
  private traceRetentionMs = 24 * 60 * 60 * 1000; // 24 hours

  /**
   * Generate a new correlation ID
   */
  generateCorrelationId(): string {
    return `corr_${Date.now()}_${Math.random().toString(36).substr(2, 12)}`;
  }

  /**
   * Generate a new trace ID
   */
  generateTraceId(): string {
    return `trace_${Date.now()}_${Math.random().toString(36).substr(2, 16)}`;
  }

  /**
   * Generate a new span ID
   */
  generateSpanId(): string {
    return `span_${Date.now()}_${Math.random().toString(36).substr(2, 8)}`;
  }

  /**
   * Extract trace context from request headers
   */
  extractTraceContext(request: NextRequest): Partial<TraceContext> {
    const correlationId = request.headers.get('x-correlation-id') || this.generateCorrelationId();
    const traceId = request.headers.get('x-trace-id') || this.generateTraceId();
    const parentSpanId = request.headers.get('x-parent-span-id') || undefined;
    const tenantId = request.headers.get('x-tenant-id') || undefined;
    const userId = request.headers.get('x-user-id') || undefined;

    return {
      correlationId,
      traceId,
      parentSpanId,
      tenantId,
      userId
    };
  }

  /**
   * Create a new trace context
   */
  createTraceContext(
    request: NextRequest,
    operation: string,
    resource: string,
    tenant?: TenantContext,
    user?: AuthSession
  ): TraceContext {
    const extracted = this.extractTraceContext(request);
    const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 8)}`;

    return {
      correlationId: extracted.correlationId!,
      traceId: extracted.traceId!,
      spanId: this.generateSpanId(),
      parentSpanId: extracted.parentSpanId,
      tenantId: tenant?.clientId || extracted.tenantId,
      userId: user?.userId || extracted.userId,
      sessionId: user?.sessionId,
      requestId,
      timestamp: new Date(),
      source: 'api',
      operation,
      resource,
      metadata: {
        method: request.method,
        url: request.url,
        userAgent: request.headers.get('user-agent'),
        ipAddress: request.headers.get('x-forwarded-for') || 
                   request.headers.get('x-real-ip') || 
                   'unknown',
        referer: request.headers.get('referer')
      }
    };
  }

  /**
   * Start a new distributed trace
   */
  startTrace(context: TraceContext): DistributedTrace {
    const trace: DistributedTrace = {
      traceId: context.traceId,
      correlationId: context.correlationId,
      tenantId: context.tenantId,
      userId: context.userId,
      startTime: new Date(),
      spans: [],
      status: 'pending',
      metadata: {
        source: context.source,
        operation: context.operation,
        resource: context.resource,
        ...context.metadata
      }
    };

    this.traces.set(context.traceId, trace);
    this.cleanupOldTraces();

    console.log(`🔍 [${context.correlationId}] Started trace: ${context.traceId}`);
    return trace;
  }

  /**
   * Start a new span
   */
  startSpan(
    traceId: string,
    operationName: string,
    parentSpanId?: string,
    tags: Record<string, any> = {}
  ): TraceSpan {
    const span: TraceSpan = {
      spanId: this.generateSpanId(),
      parentSpanId,
      operationName,
      startTime: new Date(),
      status: 'pending',
      tags: {
        ...tags,
        traceId
      },
      logs: []
    };

    this.activeSpans.set(span.spanId, span);

    // Add span to trace
    const trace = this.traces.get(traceId);
    if (trace) {
      trace.spans.push(span);
    }

    console.log(`📊 [${traceId}] Started span: ${operationName} (${span.spanId})`);
    return span;
  }

  /**
   * Finish a span
   */
  finishSpan(
    spanId: string,
    status: 'success' | 'error' = 'success',
    error?: Error
  ): void {
    const span = this.activeSpans.get(spanId);
    if (!span) {
      console.warn(`⚠️ Span not found: ${spanId}`);
      return;
    }

    span.endTime = new Date();
    span.duration = span.endTime.getTime() - span.startTime.getTime();
    span.status = status;

    if (error) {
      span.error = {
        name: error.name,
        message: error.message,
        stack: error.stack,
        code: (error as any).code
      };
    }

    this.activeSpans.delete(spanId);

    console.log(`✅ [${span.tags.traceId}] Finished span: ${span.operationName} (${span.duration}ms)`);
  }

  /**
   * Add log to span
   */
  addSpanLog(
    spanId: string,
    level: 'debug' | 'info' | 'warn' | 'error',
    message: string,
    fields?: Record<string, any>
  ): void {
    const span = this.activeSpans.get(spanId);
    if (!span) {
      console.warn(`⚠️ Span not found for log: ${spanId}`);
      return;
    }

    span.logs.push({
      timestamp: new Date(),
      level,
      message,
      fields
    });
  }

  /**
   * Add tags to span
   */
  addSpanTags(spanId: string, tags: Record<string, any>): void {
    const span = this.activeSpans.get(spanId);
    if (!span) {
      console.warn(`⚠️ Span not found for tags: ${spanId}`);
      return;
    }

    Object.assign(span.tags, tags);
  }

  /**
   * Finish a trace
   */
  finishTrace(traceId: string, status: 'success' | 'error' = 'success'): void {
    const trace = this.traces.get(traceId);
    if (!trace) {
      console.warn(`⚠️ Trace not found: ${traceId}`);
      return;
    }

    trace.endTime = new Date();
    trace.duration = trace.endTime.getTime() - trace.startTime.getTime();
    trace.status = status;

    console.log(`🏁 [${trace.correlationId}] Finished trace: ${traceId} (${trace.duration}ms)`);
  }

  /**
   * Get trace by ID
   */
  getTrace(traceId: string): DistributedTrace | undefined {
    return this.traces.get(traceId);
  }

  /**
   * Get traces by tenant
   */
  getTracesByTenant(tenantId: string, limit: number = 50): DistributedTrace[] {
    return Array.from(this.traces.values())
      .filter(trace => trace.tenantId === tenantId)
      .sort((a, b) => b.startTime.getTime() - a.startTime.getTime())
      .slice(0, limit);
  }

  /**
   * Get traces by user
   */
  getTracesByUser(userId: string, limit: number = 50): DistributedTrace[] {
    return Array.from(this.traces.values())
      .filter(trace => trace.userId === userId)
      .sort((a, b) => b.startTime.getTime() - a.startTime.getTime())
      .slice(0, limit);
  }

  /**
   * Get error traces
   */
  getErrorTraces(limit: number = 50): DistributedTrace[] {
    return Array.from(this.traces.values())
      .filter(trace => trace.status === 'error')
      .sort((a, b) => b.startTime.getTime() - a.startTime.getTime())
      .slice(0, limit);
  }

  /**
   * Get slow traces
   */
  getSlowTraces(thresholdMs: number = 5000, limit: number = 50): DistributedTrace[] {
    return Array.from(this.traces.values())
      .filter(trace => trace.duration && trace.duration > thresholdMs)
      .sort((a, b) => (b.duration || 0) - (a.duration || 0))
      .slice(0, limit);
  }

  /**
   * Clean up old traces
   */
  private cleanupOldTraces(): void {
    const now = Date.now();
    const cutoff = now - this.traceRetentionMs;
    let cleaned = 0;

    for (const [traceId, trace] of this.traces.entries()) {
      if (trace.startTime.getTime() < cutoff) {
        this.traces.delete(traceId);
        cleaned++;
      }
    }

    // Also enforce max traces limit
    if (this.traces.size > this.maxTraces) {
      const sortedTraces = Array.from(this.traces.entries())
        .sort(([, a], [, b]) => b.startTime.getTime() - a.startTime.getTime());

      // Keep only the most recent traces
      const toKeep = sortedTraces.slice(0, this.maxTraces);
      this.traces.clear();
      
      for (const [traceId, trace] of toKeep) {
        this.traces.set(traceId, trace);
      }

      cleaned += sortedTraces.length - this.maxTraces;
    }

    if (cleaned > 0) {
      console.log(`🧹 Cleaned up ${cleaned} old traces`);
    }
  }

  /**
   * Get tracing statistics
   */
  getStats(): {
    totalTraces: number;
    activeSpans: number;
    errorRate: number;
    averageDuration: number;
    slowTraces: number;
  } {
    const traces = Array.from(this.traces.values());
    const completedTraces = traces.filter(t => t.duration !== undefined);
    const errorTraces = traces.filter(t => t.status === 'error');
    const slowTraces = traces.filter(t => t.duration && t.duration > 5000);

    const totalDuration = completedTraces.reduce((sum, t) => sum + (t.duration || 0), 0);
    const averageDuration = completedTraces.length > 0 ? totalDuration / completedTraces.length : 0;

    return {
      totalTraces: traces.length,
      activeSpans: this.activeSpans.size,
      errorRate: traces.length > 0 ? errorTraces.length / traces.length : 0,
      averageDuration,
      slowTraces: slowTraces.length
    };
  }

  /**
   * Export traces for external systems
   * Currently supports JSON format only
   */
  exportTraces(format: 'json' = 'json'): any {
    const traces = Array.from(this.traces.values());

    if (format === 'json') {
      return traces;
    }

    throw new Error(`Export format '${format}' is not supported. Only 'json' format is available.`);
  }
}

// Global tracing service instance
export const requestTracing = new RequestTracingService();

/**
 * Create trace headers for downstream requests
 */
export function createTraceHeaders(context: TraceContext): Record<string, string> {
  return {
    'x-correlation-id': context.correlationId,
    'x-trace-id': context.traceId,
    'x-parent-span-id': context.spanId,
    'x-tenant-id': context.tenantId || '',
    'x-user-id': context.userId || '',
    'x-request-id': context.requestId
  };
}

/**
 * Middleware wrapper for automatic request tracing
 */
export function withRequestTracing<T extends any[], R>(
  operationName: string,
  handler: (context: TraceContext, ...args: T) => Promise<R>
) {
  return async (...args: T): Promise<R> => {
    const request = args[0] as NextRequest;
    const traceContext = requestTracing.createTraceContext(
      request,
      operationName,
      request.nextUrl.pathname
    );

    const trace = requestTracing.startTrace(traceContext);
    const span = requestTracing.startSpan(
      trace.traceId,
      operationName,
      traceContext.parentSpanId,
      {
        'http.method': request.method,
        'http.url': request.url,
        'tenant.id': traceContext.tenantId,
        'user.id': traceContext.userId
      }
    );

    try {
      const result = await handler(traceContext, ...args);
      
      requestTracing.finishSpan(span.spanId, 'success');
      requestTracing.finishTrace(trace.traceId, 'success');
      
      return result;
    } catch (error) {
      requestTracing.addSpanLog(
        span.spanId,
        'error',
        error instanceof Error ? error.message : 'Unknown error',
        { error: error instanceof Error ? error.stack : error }
      );
      
      requestTracing.finishSpan(span.spanId, 'error', error instanceof Error ? error : new Error(String(error)));
      requestTracing.finishTrace(trace.traceId, 'error');
      
      throw error;
    }
  };
}

/**
 * Product Synchronization Processor
 * 
 * Handles the synchronization of product data between global canonical tables
 * and tenant-specific tables. Products are matched within vendor context using:
 * 
 * Matching Algorithm Priority:
 * 1. GTIN (Global Trade Item Number) match (95% confidence)
 * 2. Vendor + SKU match (90% confidence)
 * 3. Vendor + Fuzzy Name match (70-85% confidence)
 * 
 * Products require vendor synchronization to be completed first.
 */

import { Pool, PoolClient } from 'pg'
import { Logger } from '../../Logger'
import { ProductMatcher } from '../matchers/ProductMatcher'
import { SyncOptions, SyncResult } from '../SyncEngine'

export interface TenantProduct {
  id: string
  tenantVendorId: string
  name: string
  sku?: string
  gtin?: string
  category?: string
  description?: string
  website?: string
  createdAt: Date
  updatedAt: Date
}

export interface GlobalProduct {
  id: string
  globalVendorId: string
  name: string
  sku?: string
  gtin?: string
  category?: string
  description?: string
  website?: string
  confidence: number
  sourceCount: number
  lastUpdated: Date
}

export interface ProductMatch {
  tenantProductId: string
  globalProductId: string
  confidence: number
  matchType: 'gtin' | 'vendor_sku' | 'fuzzy_name'
  matchDetails: Record<string, any>
}

export class ProductSyncProcessor {
  private db: Pool
  private logger: Logger
  private matcher: ProductMatcher

  constructor(db: Pool, logger: Logger) {
    this.db = db
    this.logger = logger
    this.matcher = new ProductMatcher(logger)
  }

  /**
   * Process product synchronization for a tenant
   */
  async process(tenantId: string, batchId: string, options: SyncOptions = {}): Promise<Omit<SyncResult, 'batchId' | 'processingTimeMs'>> {
    const {
      batchSize = 100,
      maxRetries = 3,
      autoResolveThreshold = 85,
      dryRun = false
    } = options

    const client = await this.db.connect()
    
    try {
      // Get tenant schema name
      const tenantSchema = `tenant_${tenantId.padStart(16, '0')}`
      
      // Get all tenant products that need synchronization
      const tenantProducts = await this.getTenantProducts(client, tenantSchema)
      
      this.logger.info(`Processing ${tenantProducts.length} products for tenant ${tenantId}`, { batchId })
      
      let totalProcessed = 0
      let matched = 0
      let conflicts = 0
      const errors: string[] = []
      
      // Process in batches
      for (let i = 0; i < tenantProducts.length; i += batchSize) {
        const batch = tenantProducts.slice(i, i + batchSize)
        
        for (const tenantProduct of batch) {
          try {
            const result = await this.processProduct(
              client,
              tenantId,
              tenantSchema,
              tenantProduct,
              batchId,
              autoResolveThreshold,
              dryRun
            )
            
            totalProcessed++
            if (result.matched) matched++
            if (result.conflict) conflicts++
            
          } catch (error) {
            const errorMsg = `Failed to process product ${tenantProduct.id}: ${error instanceof Error ? error.message : 'Unknown error'}`
            errors.push(errorMsg)
            this.logger.error(errorMsg, { batchId, tenantProductId: tenantProduct.id })
          }
        }
        
        // Update batch progress
        await this.updateBatchProgress(client, batchId, totalProcessed, matched, conflicts)
      }
      
      return {
        success: errors.length === 0,
        totalProcessed,
        matched,
        conflicts,
        errors
      }
      
    } finally {
      client.release()
    }
  }

  /**
   * Process a single product
   */
  private async processProduct(
    client: PoolClient,
    tenantId: string,
    tenantSchema: string,
    tenantProduct: TenantProduct,
    batchId: string,
    autoResolveThreshold: number,
    dryRun: boolean
  ): Promise<{ matched: boolean; conflict: boolean }> {
    
    // Check if product is already synchronized
    const existingSync = await this.getExistingSync(client, tenantSchema, tenantProduct.id)
    if (existingSync) {
      this.logger.debug(`Product ${tenantProduct.id} already synchronized`, { globalProductId: existingSync.globalProductId })
      return { matched: true, conflict: false }
    }
    
    // Get the global vendor ID for this tenant product
    const globalVendorId = await this.getGlobalVendorId(client, tenantSchema, tenantProduct.tenantVendorId)
    if (!globalVendorId) {
      throw new Error(`Vendor not synchronized for product ${tenantProduct.id}. Run vendor sync first.`)
    }
    
    // Find potential matches in global products for this vendor
    const globalProducts = await this.getGlobalProductsForVendor(client, globalVendorId)
    const matches = await this.matcher.findMatches(tenantProduct, globalProducts)
    
    if (matches.length === 0) {
      // No matches found - create new global product
      if (!dryRun) {
        const globalProductId = await this.createGlobalProduct(client, tenantProduct, globalVendorId)
        await this.createTenantProductSync(client, tenantSchema, tenantProduct.id, globalProductId, 100, 'exact_match')
      }
      
      this.logger.info(`Created new global product for tenant product ${tenantProduct.id}`)
      return { matched: true, conflict: false }
    }
    
    // Get best match
    const bestMatch = matches[0]
    
    if (bestMatch.confidence >= autoResolveThreshold) {
      // Auto-resolve high confidence matches
      if (!dryRun) {
        await this.createTenantProductSync(
          client,
          tenantSchema,
          tenantProduct.id,
          bestMatch.globalProductId,
          bestMatch.confidence,
          bestMatch.matchType
        )
        
        // Update global product with additional data
        await this.updateGlobalProduct(client, bestMatch.globalProductId, tenantProduct)
      }
      
      this.logger.info(`Auto-matched product ${tenantProduct.id} to global product ${bestMatch.globalProductId}`, {
        confidence: bestMatch.confidence,
        matchType: bestMatch.matchType
      })
      
      return { matched: true, conflict: false }
    } else {
      // Create conflict for manual review
      if (!dryRun) {
        await this.createSyncConflict(client, batchId, tenantProduct, matches)
      }
      
      this.logger.info(`Created conflict for product ${tenantProduct.id}`, {
        matchCount: matches.length,
        bestConfidence: bestMatch.confidence
      })
      
      return { matched: false, conflict: true }
    }
  }

  /**
   * Get tenant products that need synchronization
   */
  private async getTenantProducts(client: PoolClient, tenantSchema: string): Promise<TenantProduct[]> {
    const result = await client.query(`
      SELECT tp.* 
      FROM ${tenantSchema}.tenant_products tp
      LEFT JOIN ${tenantSchema}.tenant_product_sync tps ON tp.id = tps.tenant_product_id
      WHERE tps.tenant_product_id IS NULL
      ORDER BY tp.created_on
    `)
    
    return result.rows
  }

  /**
   * Get global products for a specific vendor
   */
  private async getGlobalProductsForVendor(client: PoolClient, globalVendorId: string): Promise<GlobalProduct[]> {
    const result = await client.query(`
      SELECT * FROM metadata.global_products
      WHERE global_vendor_id = $1
      ORDER BY confidence DESC, source_count DESC
    `, [globalVendorId])
    
    return result.rows
  }

  /**
   * Get global vendor ID for a tenant vendor
   */
  private async getGlobalVendorId(client: PoolClient, tenantSchema: string, tenantVendorId: string): Promise<string | null> {
    const result = await client.query(`
      SELECT global_vendor_id FROM ${tenantSchema}.tenant_vendor_sync
      WHERE tenant_vendor_id = $1 AND sync_status = 'synced'
    `, [tenantVendorId])
    
    return result.rows[0]?.global_vendor_id || null
  }

  /**
   * Check if product is already synchronized
   */
  private async getExistingSync(client: PoolClient, tenantSchema: string, tenantProductId: string) {
    const result = await client.query(`
      SELECT * FROM ${tenantSchema}.tenant_product_sync
      WHERE tenant_product_id = $1
    `, [tenantProductId])
    
    return result.rows[0] || null
  }

  /**
   * Create new global product
   */
  private async createGlobalProduct(client: PoolClient, tenantProduct: TenantProduct, globalVendorId: string): Promise<string> {
    const result = await client.query(`
      INSERT INTO metadata.global_products (
        global_vendor_id, name, sku, gtin, category, description, website,
        confidence, source_count, last_updated
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, 100, 1, NOW())
      RETURNING id
    `, [
      globalVendorId,
      tenantProduct.name,
      tenantProduct.sku,
      tenantProduct.gtin,
      tenantProduct.category,
      tenantProduct.description,
      tenantProduct.website
    ])
    
    return result.rows[0].id
  }

  /**
   * Create tenant product sync record
   */
  private async createTenantProductSync(
    client: PoolClient,
    tenantSchema: string,
    tenantProductId: string,
    globalProductId: string,
    confidence: number,
    matchType: string
  ): Promise<void> {
    await client.query(`
      INSERT INTO ${tenantSchema}.tenant_product_sync (
        tenant_product_id, global_product_id, confidence, match_type,
        sync_status, created_on, changed_on
      ) VALUES ($1, $2, $3, $4, 'synced', NOW(), NOW())
    `, [tenantProductId, globalProductId, confidence, matchType])
  }

  /**
   * Update global product with additional data
   */
  private async updateGlobalProduct(client: PoolClient, globalProductId: string, tenantProduct: TenantProduct): Promise<void> {
    await client.query(`
      UPDATE metadata.global_products 
      SET source_count = source_count + 1,
          last_updated = NOW(),
          -- Update fields if they're empty in global but present in tenant
          sku = COALESCE(sku, $2),
          gtin = COALESCE(gtin, $3),
          category = COALESCE(category, $4),
          description = COALESCE(description, $5),
          website = COALESCE(website, $6)
      WHERE id = $1
    `, [
      globalProductId,
      tenantProduct.sku,
      tenantProduct.gtin,
      tenantProduct.category,
      tenantProduct.description,
      tenantProduct.website
    ])
  }

  /**
   * Create sync conflict for manual review
   */
  private async createSyncConflict(
    client: PoolClient,
    batchId: string,
    tenantProduct: TenantProduct,
    matches: ProductMatch[]
  ): Promise<void> {
    await client.query(`
      INSERT INTO metadata.sync_conflicts (
        batch_id, entity_type, entity_id, conflict_data, status, created_on
      ) VALUES ($1, 'product', $2, $3, 'pending', NOW())
    `, [
      batchId,
      tenantProduct.id,
      JSON.stringify({
        tenantProduct,
        potentialMatches: matches
      })
    ])
  }

  /**
   * Update batch progress
   */
  private async updateBatchProgress(
    client: PoolClient,
    batchId: string,
    processed: number,
    matched: number,
    conflicts: number
  ): Promise<void> {
    await client.query(`
      UPDATE metadata.sync_batches 
      SET processed_records = $2,
          matched_records = $3,
          conflict_records = $4,
          changed_on = NOW()
      WHERE id = $1
    `, [batchId, processed, matched, conflicts])
  }
}

/**
 * Add New Item Functionality Tests
 * 
 * Tests the ability to create new vendors, products, and versions
 * directly from dropdown interfaces in the renewal forms.
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { CascadingSelect } from '@/components/ui/CascadingSelect';
import { RenewalItemsManager } from '@/components/ui/RenewalItemsManager';

// Mock the toast hook
jest.mock('@/components/ui/Toast', () => ({
  useToast: () => ({
    success: jest.fn(),
    error: jest.fn(),
  }),
}));

// Mock fetch globally
global.fetch = jest.fn();
const mockFetch = fetch as jest.MockedFunction<typeof fetch>;

describe('Add New Item Functionality', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockFetch.mockClear();
    mockFetch.mockReset();
  });

  describe('CascadingSelect Add New Functionality', () => {
    const mockOnChange = jest.fn();
    const mockOnAddNew = jest.fn();

    const defaultProps = {
      value: {
        vendorId: null,
        vendorName: '',
        productId: null,
        product_name: '',
        versionId: null,
        versionName: '',
      },
      onChange: mockOnChange,
      onAddNew: mockOnAddNew,
    };

    beforeEach(() => {
      // Clear all mocks for this test suite
      jest.clearAllMocks();
      mockFetch.mockClear();
      mockFetch.mockReset();

      // Mock successful vendor fetch
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          data: [
            { id: '1', name: 'Microsoft', display_name: 'Microsoft Corporation' },
            { id: '3', name: 'Adobe', display_name: 'Adobe Inc.' }, // Changed from '2' to '3' to avoid conflicts
          ]
        }),
      } as Response);
    });

    it('should show add vendor button and form', async () => {
      render(<CascadingSelect {...defaultProps} />);

      // Wait for vendors to load
      await waitFor(() => {
        expect(screen.getByText('Select vendor...')).toBeInTheDocument();
      });

      // Click add vendor button
      const addVendorButton = screen.getByTitle('Add new vendor');
      fireEvent.click(addVendorButton);

      // Check if add vendor form appears
      expect(screen.getByPlaceholderText('Enter vendor name')).toBeInTheDocument();
      expect(screen.getByText('Add')).toBeInTheDocument();
      expect(screen.getByText('Cancel')).toBeInTheDocument();
    });

    it('should create new vendor successfully', async () => {
      const user = userEvent.setup();
      mockOnAddNew.mockResolvedValue({
        id: '4',
        name: 'New Vendor',
        display_name: 'New Vendor Corp',
      });

      render(<CascadingSelect {...defaultProps} />);

      // Wait for vendors to load
      await waitFor(() => {
        expect(screen.getByText('Select vendor...')).toBeInTheDocument();
      });

      // Click add vendor button
      const addVendorButton = screen.getByTitle('Add new vendor');
      await user.click(addVendorButton);

      // Enter vendor name
      const vendorInput = screen.getByPlaceholderText('Enter vendor name');
      await user.type(vendorInput, 'New Vendor');

      // Click add button
      const addButton = screen.getByText('Add');
      await user.click(addButton);

      // Verify onAddNew was called with correct data
      expect(mockOnAddNew).toHaveBeenCalledWith('vendor', { name: 'New Vendor' });
    });

    it('should show add product button when vendor is selected', async () => {
      const propsWithVendor = {
        ...defaultProps,
        value: {
          ...defaultProps.value,
          vendorId: '1',
          vendorName: 'Microsoft',
        },
      };

      // Mock products fetch
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          data: [
            { id: '1', name: 'Office 365', display_name: 'Microsoft Office 365' },
          ]
        }),
      } as Response);

      render(<CascadingSelect {...propsWithVendor} />);

      // Wait for products to load
      await waitFor(() => {
        expect(screen.getByText('Select product...')).toBeInTheDocument();
      });

      // Check if add product button is visible
      expect(screen.getByTitle('Add new product')).toBeInTheDocument();
    });

    it('should create new product successfully', async () => {
      const user = userEvent.setup();
      const propsWithVendor = {
        ...defaultProps,
        value: {
          ...defaultProps.value,
          vendorId: '1',
          vendorName: 'Microsoft',
        },
      };

      mockOnAddNew.mockResolvedValue({
        id: '2',
        name: 'New Product',
        display_name: 'New Product Suite',
      });

      // Mock products fetch
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ data: [] }),
      } as Response);

      render(<CascadingSelect {...propsWithVendor} />);

      // Wait for products to load
      await waitFor(() => {
        expect(screen.getByText('Select product...')).toBeInTheDocument();
      });

      // Click add product button
      const addProductButton = screen.getByTitle('Add new product');
      await user.click(addProductButton);

      // Enter product name
      const productInput = screen.getByPlaceholderText('Enter product name');
      await user.type(productInput, 'New Product');

      // Click add button
      const addButton = screen.getByText('Add');
      await user.click(addButton);

      // Verify onAddNew was called with correct data
      expect(mockOnAddNew).toHaveBeenCalledWith('product', { 
        name: 'New Product',
        vendor_id: '1'
      });
    });

    it('should show add version button when product is selected', async () => {
      const propsWithProduct = {
        ...defaultProps,
        value: {
          ...defaultProps.value,
          vendorId: '1',
          vendorName: 'Microsoft',
          productId: '1',
          product_name: 'Office 365',
        },
      };

      // Mock versions fetch
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          data: [
            { id: '1', version: '2023', name: '2023' },
          ]
        }),
      } as Response);

      render(<CascadingSelect {...propsWithProduct} />);

      // Wait for versions to load
      await waitFor(() => {
        expect(screen.getByText('Select version...')).toBeInTheDocument();
      });

      // Check if add version button is visible
      expect(screen.getByTitle('Add new version')).toBeInTheDocument();
    });

    it('should create new version successfully', async () => {
      const user = userEvent.setup();
      const propsWithProduct = {
        ...defaultProps,
        value: {
          ...defaultProps.value,
          vendorId: '1',
          vendorName: 'Microsoft',
          productId: '1',
          product_name: 'Office 365',
        },
      };

      mockOnAddNew.mockResolvedValue({
        id: '2',
        version: '2024',
        name: '2024',
      });

      // Mock versions fetch
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ data: [] }),
      } as Response);

      render(<CascadingSelect {...propsWithProduct} />);

      // Wait for versions to load
      await waitFor(() => {
        expect(screen.getByText('Select version...')).toBeInTheDocument();
      });

      // Click add version button
      const addVersionButton = screen.getByTitle('Add new version');
      await user.click(addVersionButton);

      // Enter version name
      const versionInput = screen.getByPlaceholderText('Enter version name');
      await user.type(versionInput, '2024');

      // Click add button
      const addButton = screen.getByText('Add');
      await user.click(addButton);

      // Verify onAddNew was called with correct data
      expect(mockOnAddNew).toHaveBeenCalledWith('version', { 
        version: '2024',
        product_id: '1'
      });
    });

    it('should handle add new item errors gracefully', async () => {
      const user = userEvent.setup();
      mockOnAddNew.mockRejectedValue(new Error('Failed to create vendor'));

      render(<CascadingSelect {...defaultProps} />);

      // Wait for vendors to load
      await waitFor(() => {
        expect(screen.getByText('Select vendor...')).toBeInTheDocument();
      });

      // Click add vendor button
      const addVendorButton = screen.getByTitle('Add new vendor');
      await user.click(addVendorButton);

      // Enter vendor name
      const vendorInput = screen.getByPlaceholderText('Enter vendor name');
      await user.type(vendorInput, 'New Vendor');

      // Click add button
      const addButton = screen.getByText('Add');
      await user.click(addButton);

      // Verify error handling (form should remain visible)
      await waitFor(() => {
        expect(screen.getByPlaceholderText('Enter vendor name')).toBeInTheDocument();
      });
    });

    it('should cancel add new item form', async () => {
      const user = userEvent.setup();

      render(<CascadingSelect {...defaultProps} />);

      // Wait for vendors to load
      await waitFor(() => {
        expect(screen.getByText('Select vendor...')).toBeInTheDocument();
      });

      // Click add vendor button
      const addVendorButton = screen.getByTitle('Add new vendor');
      await user.click(addVendorButton);

      // Enter some text
      const vendorInput = screen.getByPlaceholderText('Enter vendor name');
      await user.type(vendorInput, 'Test Vendor');

      // Click cancel button
      const cancelButton = screen.getByText('Cancel');
      await user.click(cancelButton);

      // Verify form is hidden and input is cleared
      expect(screen.queryByPlaceholderText('Enter vendor name')).not.toBeInTheDocument();
    });

    it('should work with hideVendorSelection and preselectedVendorId', async () => {
      const user = userEvent.setup();
      const mockOnChange = jest.fn();
      const mockOnAddNew = jest.fn();

      // Clear all mocks
      jest.clearAllMocks();
      mockFetch.mockClear();
      mockFetch.mockReset();

      // Mock products fetch for vendor '1'
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          data: [
            { id: '10', name: 'Office 365', display_name: 'Microsoft Office 365' },
          ]
        }),
      } as Response);

      const props = {
        value: {
          vendorId: '1', // This should match preselectedVendorId
          vendorName: '',
          productId: null,
          product_name: '',
          versionId: null,
          versionName: ''
        },
        onChange: mockOnChange,
        onAddNew: mockOnAddNew,
        required: true,
        disabled: false,
        hideVendorSelection: true,
        preselectedVendorId: '1'
      };

      render(<CascadingSelect {...props} />);

      // Wait for products to load and add product button to appear
      await waitFor(() => {
        expect(screen.getByText('Select product...')).toBeInTheDocument();
        expect(screen.getByTitle('Add new product')).toBeInTheDocument();
      });

      // Click add product button
      const addProductButton = screen.getByTitle('Add new product');
      await user.click(addProductButton);

      // Wait for form to appear
      await waitFor(() => {
        expect(screen.getByPlaceholderText('Enter product name')).toBeInTheDocument();
      });

      expect(screen.getByPlaceholderText('Enter product name')).toBeInTheDocument();
    });
  });

  describe('RenewalItemsManager Add New Integration', () => {
    const mockOnChange = jest.fn();

    const defaultProps = {
      items: [],
      onChange: mockOnChange,
      vendorId: '1',
      disabled: false,
    };

    beforeEach(() => {
      // Clear all previous mocks and reset
      jest.clearAllMocks();
      mockFetch.mockClear();
      mockFetch.mockReset();

      // Mock successful API responses for RenewalItemsManager tests
      // First call: products for vendor '1' (called on mount due to preselectedVendorId)
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            data: [
              { id: '10', name: 'Office 365', display_name: 'Microsoft Office 365' },
            ]
          }),
        } as Response)
        // Second call: versions for product (if needed)
        .mockResolvedValueOnce({
          ok: true,
          json: () => Promise.resolve({
            data: [
              { id: '100', version: '2023', name: '2023' },
            ]
          }),
        } as Response);
    });

    it('should integrate add new functionality with renewal items', async () => {
      const user = userEvent.setup();

      // Mock successful product creation
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          data: { id: '20', name: 'New Product', display_name: 'New Product Suite' }
        }),
      } as Response);

      render(<RenewalItemsManager {...defaultProps} />);

      // Wait for initial data to load
      await waitFor(() => {
        expect(screen.getByText('Select product...')).toBeInTheDocument();
      });

      // Click add product button
      const addProductButton = screen.getByTitle('Add new product');
      await user.click(addProductButton);

      // Enter product name
      const productInput = screen.getByPlaceholderText('Enter product name');
      await user.type(productInput, 'New Product');

      // Click add button
      const addButton = screen.getByText('Add');
      await user.click(addButton);

      // Verify API was called correctly
      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith('/api/tenant-products', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: 'New Product',
            vendor_id: '1'
          }),
        });
      });
    });

    it('should show add product form when button is clicked', async () => {
      const user = userEvent.setup();

      // Clear previous mocks and set up fresh ones
      mockFetch.mockClear();

      // Mock initial products load for vendor '1'
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          data: [
            { id: '10', name: 'Office 365', display_name: 'Microsoft Office 365' },
          ]
        }),
      } as Response);

      render(<RenewalItemsManager {...defaultProps} />);

      // Wait for initial data to load and add product button to appear
      await waitFor(() => {
        expect(screen.getByText('Select product...')).toBeInTheDocument();
        expect(screen.getByTitle('Add new product')).toBeInTheDocument();
      });

      // Verify the form is not visible initially
      expect(screen.queryByPlaceholderText('Enter product name')).not.toBeInTheDocument();

      // Get the add product button and click it
      const addProductButton = screen.getByTitle('Add new product');
      await user.click(addProductButton);

      // Check if the form appears
      await waitFor(() => {
        expect(screen.getByPlaceholderText('Enter product name')).toBeInTheDocument();
      }, { timeout: 3000 });

      // Verify the form elements are present
      expect(screen.getByText('Add')).toBeInTheDocument();
      expect(screen.getByText('Cancel')).toBeInTheDocument();
    });

    it.skip('should handle API errors when creating new items', async () => {
      const user = userEvent.setup();

      // Clear previous mocks and set up fresh ones
      mockFetch.mockClear();

      // Mock initial products load for vendor '1'
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          data: [
            { id: '10', name: 'Office 365', display_name: 'Microsoft Office 365' },
          ]
        }),
      } as Response);

      render(<RenewalItemsManager {...defaultProps} />);

      // Wait for initial data to load and add product button to appear
      await waitFor(() => {
        expect(screen.getByText('Select product...')).toBeInTheDocument();
        expect(screen.getByTitle('Add new product')).toBeInTheDocument();
      });

      // Get the add product button and verify it's enabled
      const addProductButton = screen.getByTitle('Add new product');
      expect(addProductButton).not.toBeDisabled();

      // Click add product button
      await user.click(addProductButton);

      // Wait for form to appear
      await waitFor(() => {
        expect(screen.getByPlaceholderText('Enter product name')).toBeInTheDocument();
      }, { timeout: 3000 });

      // Mock API error for product creation
      mockFetch.mockResolvedValueOnce({
        ok: false,
        json: () => Promise.resolve({
          message: 'Product already exists'
        }),
      } as Response);

      // Enter product name
      const productInput = screen.getByPlaceholderText('Enter product name');
      await user.type(productInput, 'Existing Product');

      // Click add button
      const addButton = screen.getByText('Add');
      await user.click(addButton);

      // Form should remain visible after error
      await waitFor(() => {
        expect(screen.getByPlaceholderText('Enter product name')).toBeInTheDocument();
      });
    });

    it.skip('should create new version and update renewal item', async () => {
      const user = userEvent.setup();

      // Clear previous mocks and set up fresh ones
      mockFetch.mockClear();

      // Mock initial products load for vendor '1'
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          data: [
            { id: '1', name: 'Office 365', display_name: 'Microsoft Office 365' },
          ]
        }),
      } as Response);

      // Mock initial versions load for product '1'
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          data: [
            { id: '100', version: '2023', name: '2023' },
          ]
        }),
      } as Response);

      // Mock successful version creation
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({
          data: { id: '200', version: '2024', name: '2024' }
        }),
      } as Response);

      const propsWithProduct = {
        ...defaultProps,
        items: [{
          productId: '1',
          product_name: 'Office 365',
          versionId: '',
          versionName: '',
          quantity: 1,
          unit: '',
          licenseCount: 1,
          totalCost: 0,
          costCode: '',
          notes: ''
        }]
      };

      render(<RenewalItemsManager {...propsWithProduct} />);

      // Wait for versions to load and add version button to appear
      await waitFor(() => {
        expect(screen.getByText('Select version...')).toBeInTheDocument();
        expect(screen.getByTitle('Add new version')).toBeInTheDocument();
      });

      // Click add version button
      const addVersionButton = screen.getByTitle('Add new version');
      await user.click(addVersionButton);

      // Wait for form to appear and enter version name
      await waitFor(() => {
        expect(screen.getByPlaceholderText('Enter version name')).toBeInTheDocument();
      }, { timeout: 3000 });

      const versionInput = screen.getByPlaceholderText('Enter version name');
      await user.type(versionInput, '2024');

      // Click add button
      const addButton = screen.getByText('Add');
      await user.click(addButton);

      // Verify API was called correctly
      await waitFor(() => {
        expect(mockFetch).toHaveBeenCalledWith('/api/tenant-product-versions', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            version: '2024',
            product_id: '1'
          }),
        });
      });
    });
  });
});

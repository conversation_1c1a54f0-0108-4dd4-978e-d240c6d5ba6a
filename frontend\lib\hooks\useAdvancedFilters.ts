/**
 * Advanced Filters Hook
 * 
 * Provides comprehensive filtering functionality with saved presets,
 * server-side and client-side filtering, and search capabilities
 */

'use client'

import { useState, useEffect, useCallback, useMemo } from 'react'
import { 
  AdvancedFilter, 
  FilterGroup, 
  SearchConfig, 
  SavedFilterPreset,
  AdvancedFilterService 
} from '@/lib/services/advanced-filter-service'

export interface UseAdvancedFiltersConfig {
  tableName: string
  data?: any[]
  serverSide?: boolean
  autoApply?: boolean
  debounceMs?: number
}

export interface UseAdvancedFiltersReturn {
  // Filter state
  activeFilters: FilterGroup[]
  searchConfig: SearchConfig | null
  activePreset: SavedFilterPreset | null
  
  // Filtered data
  filteredData: any[]
  isFiltering: boolean
  
  // Filter actions
  setFilters: (groups: FilterGroup[]) => void
  clearFilters: () => void
  setSearch: (config: SearchConfig | null) => void
  clearSearch: () => void
  
  // Preset management
  savedPresets: SavedFilterPreset[]
  loadPreset: (presetId: number) => void
  saveCurrentAsPreset: (name: string, description?: string, isPublic?: boolean) => Promise<boolean>
  deletePreset: (presetId: number) => Promise<boolean>
  refreshPresets: () => void
  
  // Filter options
  getFilterOptions: (fieldName: string) => Promise<any[]>
  
  // Utility
  hasActiveFilters: boolean
  filterSummary: string
  resetAll: () => void
}

export function useAdvancedFilters({
  tableName,
  data = [],
  serverSide = false,
  autoApply = true,
  debounceMs = 300
}: UseAdvancedFiltersConfig): UseAdvancedFiltersReturn {
  
  // State
  const [activeFilters, setActiveFilters] = useState<FilterGroup[]>([])
  const [searchConfig, setSearchConfig] = useState<SearchConfig | null>(null)
  const [activePreset, setActivePreset] = useState<SavedFilterPreset | null>(null)
  const [savedPresets, setSavedPresets] = useState<SavedFilterPreset[]>([])
  const [isFiltering, setIsFiltering] = useState(false)
  const [filterOptions, setFilterOptions] = useState<Record<string, any[]>>({})

  // Load saved presets on mount
  useEffect(() => {
    refreshPresets()
  }, [tableName])

  // Refresh presets from server
  const refreshPresets = useCallback(async () => {
    try {
      const response = await fetch(`/api/filter-presets?table=${tableName}`)
      if (response.ok) {
        const result = await response.json()
        setSavedPresets(result.data?.presets || [])
      }
    } catch (error) {
      console.error('Error loading filter presets:', error)
    }
  }, [tableName])

  // Apply filters to data
  const filteredData = useMemo(() => {
    if (serverSide) {
      // Server-side filtering - return data as-is, filtering happens on server
      return data
    }

    let result = [...data]

    // Apply advanced filters
    if (activeFilters.length > 0) {
      result = AdvancedFilterService.applyFiltersToData(result, activeFilters)
    }

    // Apply search
    if (searchConfig && searchConfig.query.trim()) {
      const query = searchConfig.query.toLowerCase()
      result = result.filter(item => {
        return searchConfig.fields.some(field => {
          const fieldValue = String(item[field] || '').toLowerCase()
          return searchConfig.caseSensitive 
            ? String(item[field] || '').includes(searchConfig.query)
            : fieldValue.includes(query)
        })
      })
    }

    return result
  }, [data, activeFilters, searchConfig, serverSide])

  // Set filters
  const setFilters = useCallback((groups: FilterGroup[]) => {
    setActiveFilters(groups)
    setActivePreset(null) // Clear active preset when manually changing filters
    
    if (autoApply && serverSide) {
      // Trigger server-side filtering
      setIsFiltering(true)
      // Note: Server-side filtering would be handled by parent component
      // This hook just manages the filter state
    }
  }, [autoApply, serverSide])

  // Clear filters
  const clearFilters = useCallback(() => {
    setActiveFilters([])
    setActivePreset(null)
  }, [])

  // Set search
  const setSearch = useCallback((config: SearchConfig | null) => {
    setSearchConfig(config)
    
    if (autoApply && serverSide) {
      setIsFiltering(true)
    }
  }, [autoApply, serverSide])

  // Clear search
  const clearSearch = useCallback(() => {
    setSearchConfig(null)
  }, [])

  // Load preset
  const loadPreset = useCallback(async (presetId: number) => {
    const preset = savedPresets.find(p => p.preset_id === presetId)
    if (preset) {
      setActiveFilters(preset.filter_config.groups)
      setActivePreset(preset)
      setSearchConfig(null) // Clear search when loading preset
    }
  }, [savedPresets])

  // Save current filters as preset
  const saveCurrentAsPreset = useCallback(async (
    name: string, 
    description?: string, 
    isPublic: boolean = false
  ): Promise<boolean> => {
    try {
      const filterConfig: AdvancedFilter = {
        name,
        description,
        table: tableName,
        groups: activeFilters
      }

      const response = await fetch('/api/advanced-filters', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name,
          description,
          table_name: tableName,
          filter_config: filterConfig,
          is_public: isPublic
        })
      })

      if (response.ok) {
        await refreshPresets()
        return true
      }
      return false
    } catch (error) {
      console.error('Error saving filter preset:', error)
      return false
    }
  }, [tableName, activeFilters, refreshPresets])

  // Delete preset
  const deletePreset = useCallback(async (presetId: number): Promise<boolean> => {
    try {
      const response = await fetch(`/api/filter-presets/${presetId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        await refreshPresets()
        if (activePreset?.preset_id === presetId) {
          setActivePreset(null)
        }
        return true
      }
      return false
    } catch (error) {
      console.error('Error deleting filter preset:', error)
      return false
    }
  }, [activePreset, refreshPresets])

  // Get filter options for a field
  const getFilterOptions = useCallback(async (fieldName: string): Promise<any[]> => {
    if (filterOptions[fieldName]) {
      return filterOptions[fieldName]
    }

    try {
      // This would typically call an API endpoint to get field options
      // For now, return empty array - implement based on your needs
      const options: any[] = []
      
      setFilterOptions(prev => ({
        ...prev,
        [fieldName]: options
      }))
      
      return options
    } catch (error) {
      console.error(`Error getting filter options for ${fieldName}:`, error)
      return []
    }
  }, [filterOptions])

  // Reset all filters and search
  const resetAll = useCallback(() => {
    setActiveFilters([])
    setSearchConfig(null)
    setActivePreset(null)
  }, [])

  // Computed properties
  const hasActiveFilters = useMemo(() => {
    return activeFilters.length > 0 || (searchConfig && searchConfig.query.trim().length > 0)
  }, [activeFilters, searchConfig])

  const filterSummary = useMemo(() => {
    const parts: string[] = []
    
    if (activeFilters.length > 0) {
      const conditionCount = activeFilters.reduce((total, group) => total + group.conditions.length, 0)
      parts.push(`${conditionCount} filter condition${conditionCount !== 1 ? 's' : ''}`)
    }
    
    if (searchConfig && searchConfig.query.trim()) {
      parts.push(`searching "${searchConfig.query}"`)
    }
    
    if (activePreset) {
      parts.push(`using preset "${activePreset.name}"`)
    }
    
    return parts.length > 0 ? parts.join(', ') : 'No filters applied'
  }, [activeFilters, searchConfig, activePreset])

  return {
    // Filter state
    activeFilters,
    searchConfig,
    activePreset,
    
    // Filtered data
    filteredData,
    isFiltering,
    
    // Filter actions
    setFilters,
    clearFilters,
    setSearch,
    clearSearch,
    
    // Preset management
    savedPresets,
    loadPreset,
    saveCurrentAsPreset,
    deletePreset,
    refreshPresets,
    
    // Filter options
    getFilterOptions,
    
    // Utility
    hasActiveFilters: hasActiveFilters ?? false,
    filterSummary,
    resetAll
  }
}

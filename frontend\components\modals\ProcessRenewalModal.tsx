/**
 * Process Renewal Modal Component (Refactored with Design System)
 *
 * Modal for processing/renewing a renewal with confirmation details.
 * Now uses the unified Button component for consistency.
 */

'use client'

import React, { useState } from 'react'
import { Renewal, useRenewals } from '@/lib/hooks'
import { BaseComponentProps } from '@/lib/types'
import { Button } from '@/components/ui/Button'

interface ProcessRenewalModalProps extends BaseComponentProps {
  isOpen: boolean
  renewal: Renewal | null
  onClose: () => void
  onProcess?: (renewal: Renewal) => void
}

export default function ProcessRenewalModal({
  isOpen,
  renewal,
  onClose,
  onProcess,
  className = '',
  'data-testid': testId
}: ProcessRenewalModalProps) {
  const { processRenewal } = useRenewals()
  const [isProcessing, setIsProcessing] = useState(false)

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency || 'CAD'
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    if (!dateString) return 'Not set'
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const getNewRenewalDate = () => {
    if (!renewal?.start_date) return 'Not calculated'

    const currentDate = new Date(renewal.start_date)
    const newDate = new Date(currentDate)
    newDate.setFullYear(newDate.getFullYear() + 1)
    
    return formatDate(newDate.toISOString())
  }

  const getDaysUntilRenewal = () => {
    if (!renewal?.start_date) return null

    const start_date = new Date(renewal.start_date)
    const today = new Date()
    const diffTime = start_date.getTime() - today.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    return diffDays
  }

  const handleProcess = async () => {
    if (!renewal) return

    try {
      setIsProcessing(true)
      
      const success = await processRenewal(renewal.id)
      
      if (success) {
        if (onProcess) {
          onProcess(renewal)
        }
        onClose()
      } else {
        alert('Failed to process renewal. Please try again.')
      }
    } catch (error) {
      console.error('Error processing renewal:', error)
      alert('An error occurred while processing the renewal.')
    } finally {
      setIsProcessing(false)
    }
  }

  const handleCancel = () => {
    onClose()
  }

  if (!isOpen || !renewal) return null

  const daysUntil = getDaysUntilRenewal()

  return (
    <div className={`modal-overlay ${className}`} data-testid={testId}>
      <div className="modal-container max-w-lg">
        {/* Modal Header */}
        <div className="modal-header">
          <h2 className="modal-title">Renewal Actions</h2>
          <p className="modal-subtitle">
            Process or cancel the renewal for {renewal.name}
          </p>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            aria-label="Close modal"
            style={{
              position: 'absolute',
              top: '20px',
              right: '20px',
              padding: '4px',
              minWidth: 'auto',
              height: 'auto'
            }}
          >
            ×
          </Button>
        </div>

        {/* Modal Body */}
        <div className="modal-body">
          <div className="space-y-6">
            {/* Renewal Summary */}
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-2">Software</h3>
                  <p className="text-sm text-gray-900">{renewal.name}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-2">Vendor</h3>
                  <p className="text-sm text-gray-900">{renewal.vendor}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-2">Department</h3>
                  <p className="text-sm text-gray-900">{renewal.department || 'Unspecified Department'}</p>
                </div>
                <div>
                  <h3 className="text-sm font-medium text-gray-700 mb-2">Annual Cost</h3>
                  <p className="text-sm text-gray-900 font-semibold">
                    {formatCurrency(renewal.cost || 0, renewal.currency || 'CAD')}
                  </p>
                </div>
              </div>
            </div>

            {/* Current vs New Start Date */}
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                <div>
                  <h3 className="text-sm font-medium text-gray-700">Current Start Date</h3>
                  <p className="text-sm text-gray-900">
                    📅 {formatDate(renewal.start_date || '')}
                  </p>
                  <p className="text-xs text-gray-600">
                    {daysUntil !== null && daysUntil > 0 ? 
                      `Due in ${daysUntil} days` : 
                      daysUntil === 0 ? 'Due today' : 
                      `Expired ${Math.abs(daysUntil!)} days ago`
                    }
                  </p>
                </div>
              </div>

              <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                <div>
                  <h3 className="text-sm font-medium text-gray-700">New Start Date</h3>
                  <p className="text-sm text-gray-900">
                    📅 {getNewRenewalDate()}
                  </p>
                  <p className="text-xs text-gray-600">If renewed</p>
                </div>
              </div>
            </div>

            {/* Warning/Info Message */}
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-yellow-800">
                    Confirm Renewal Processing
                  </h3>
                  <div className="mt-2 text-sm text-yellow-700">
                    <p>
                      Processing this renewal will:
                    </p>
                    <ul className="list-disc list-inside mt-1 space-y-1">
                      <li>Update the Start Date to {getNewRenewalDate()}</li>
                      <li>Mark the renewal as active</li>
                      <li>Reset any expiration alerts</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Modal Footer */}
        <div style={{
          display: 'flex',
          justifyContent: 'flex-end',
          gap: '12px',
          marginTop: '32px',
          paddingTop: '20px',
          borderTop: '1px solid var(--color-border-primary)'
        }}>
          <Button
            variant="secondary"
            onClick={handleCancel}
            disabled={isProcessing}
            leftIcon={
              <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            }
          >
            Cancel Renewal
          </Button>
          <Button
            variant="success"
            onClick={handleProcess}
            isLoading={isProcessing}
            disabled={isProcessing}
            leftIcon={
              <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            }
          >
            Process Renewal
          </Button>
        </div>
      </div>
    </div>
  )
}

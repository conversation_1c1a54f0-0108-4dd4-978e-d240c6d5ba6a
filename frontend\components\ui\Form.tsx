/**
 * Universal Form Components
 * 
 * Provides consistent form layouts, inputs, and validation across the application.
 * Replaces scattered form implementations with a unified design system.
 */

'use client';

import React, { forwardRef } from 'react';

// Design tokens for consistent styling
const designTokens = {
  components: {
    input: {
      height: {
        sm: '32px',
        md: '40px',
        lg: '48px'
      },
      padding: {
        sm: '6px 12px',
        md: '8px 16px',
        lg: '12px 20px'
      }
    }
  },
  colors: {
    border: '#d1d5db',
    borderFocus: '#3b82f6',
    borderError: '#ef4444',
    background: '#ffffff',
    backgroundDisabled: '#f9fafb',
    text: '#111827',
    textMuted: '#6b7280',
    error: '#ef4444'
  },
  spacing: {
    xs: '4px',
    sm: '8px',
    md: '16px',
    lg: '24px'
  }
};

// Form Root Container
export const FormRoot = forwardRef<HTMLFormElement, React.FormHTMLAttributes<HTMLFormElement>>(
  ({ className = '', ...props }, ref) => {
    return (
      <form
        ref={ref}
        className={`space-y-4 ${className}`}
        {...props}
      />
    );
  }
);
FormRoot.displayName = 'FormRoot';

// Form Field Container
export const FormField = forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className = '', ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={`space-y-2 ${className}`}
        {...props}
      />
    );
  }
);
FormField.displayName = 'FormField';

// Form Label
export const FormLabel = forwardRef<HTMLLabelElement, React.LabelHTMLAttributes<HTMLLabelElement> & {
  required?: boolean;
}>(
  ({ className = '', required, children, ...props }, ref) => {
    return (
      <label
        ref={ref}
        className={`block text-sm font-medium text-gray-700 ${className}`}
        {...props}
      >
        {children}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>
    );
  }
);
FormLabel.displayName = 'FormLabel';

// Form Input
export const FormInput = forwardRef<HTMLInputElement, React.InputHTMLAttributes<HTMLInputElement> & {
  error?: string;
  size?: 'sm' | 'md' | 'lg';
}>(
  ({ error, size = 'md', className = '', ...props }, ref) => {
    const hasError = !!error;
    const styles = {
      width: '100%',
      height: designTokens.components.input.height[size as keyof typeof designTokens.components.input.height],
      padding: designTokens.components.input.padding[size as keyof typeof designTokens.components.input.padding],
      border: `1px solid ${hasError ? designTokens.colors.borderError : designTokens.colors.border}`,
      borderRadius: '6px',
      backgroundColor: props.disabled ? designTokens.colors.backgroundDisabled : designTokens.colors.background,
      color: designTokens.colors.text,
      fontSize: '14px',
      outline: 'none',
      transition: 'border-color 0.2s ease-in-out'
    };

    return (
      <input
        ref={ref}
        style={styles}
        className={`focus:border-blue-500 focus:ring-1 focus:ring-blue-500 ${className}`}
        {...props}
      />
    );
  }
);
FormInput.displayName = 'FormInput';

// Form Textarea
export const FormTextarea = forwardRef<HTMLTextAreaElement, React.TextareaHTMLAttributes<HTMLTextAreaElement> & {
  error?: string;
}>(
  ({ error, className = '', ...props }, ref) => {
    const hasError = !!error;
    const styles = {
      width: '100%',
      padding: designTokens.components.input.padding.md,
      border: `1px solid ${hasError ? designTokens.colors.borderError : designTokens.colors.border}`,
      borderRadius: '6px',
      backgroundColor: props.disabled ? designTokens.colors.backgroundDisabled : designTokens.colors.background,
      color: designTokens.colors.text,
      fontSize: '14px',
      outline: 'none',
      transition: 'border-color 0.2s ease-in-out',
      resize: 'vertical' as const,
      minHeight: '80px'
    };

    return (
      <textarea
        ref={ref}
        style={styles}
        className={`focus:border-blue-500 focus:ring-1 focus:ring-blue-500 ${className}`}
        {...props}
      />
    );
  }
);
FormTextarea.displayName = 'FormTextarea';

// Form Select
export const FormSelect = forwardRef<HTMLSelectElement, React.SelectHTMLAttributes<HTMLSelectElement> & {
  error?: string;
  placeholder?: string;
}>(
  ({ error, placeholder, className = '', children, ...props }, ref) => {
    const hasError = !!error;
    const styles = {
      width: '100%',
      height: designTokens.components.input.height.md,
      padding: designTokens.components.input.padding.md,
      border: `1px solid ${hasError ? designTokens.colors.borderError : designTokens.colors.border}`,
      borderRadius: '6px',
      backgroundColor: props.disabled ? designTokens.colors.backgroundDisabled : designTokens.colors.background,
      color: designTokens.colors.text,
      fontSize: '14px',
      outline: 'none',
      transition: 'border-color 0.2s ease-in-out'
    };

    return (
      <select
        ref={ref}
        style={styles}
        className={`focus:border-blue-500 focus:ring-1 focus:ring-blue-500 ${className}`}
        {...props}
      >
        {placeholder && <option value="">{placeholder}</option>}
        {children}
      </select>
    );
  }
);
FormSelect.displayName = 'FormSelect';

// Form Checkbox
export const FormCheckbox = forwardRef<HTMLInputElement, React.InputHTMLAttributes<HTMLInputElement> & {
  label?: string;
}>(
  ({ label, className = '', ...props }, ref) => {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <input
          ref={ref}
          type="checkbox"
          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          {...props}
        />
        {label && (
          <label className="text-sm text-gray-700">
            {label}
          </label>
        )}
      </div>
    );
  }
);
FormCheckbox.displayName = 'FormCheckbox';

// Form Error Message
export const FormError = forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className = '', ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={`text-sm text-red-600 ${className}`}
        {...props}
      />
    );
  }
);
FormError.displayName = 'FormError';

// Form Help Text
export const FormHelp = forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement>>(
  ({ className = '', ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={`text-sm text-gray-500 ${className}`}
        {...props}
      />
    );
  }
);
FormHelp.displayName = 'FormHelp';

// Form Grid Layout
export const FormGrid = forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement> & {
  columns?: number;
}>(
  ({ columns = 2, className = '', ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={`grid grid-cols-1 md:grid-cols-${columns} gap-4 ${className}`}
        {...props}
      />
    );
  }
);
FormGrid.displayName = 'FormGrid';

// Form Actions Container
export const FormActions = forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement> & {
  justify?: 'start' | 'center' | 'end' | 'between';
}>(
  ({ justify = 'end', className = '', ...props }, ref) => {
    const justifyClass = {
      start: 'justify-start',
      center: 'justify-center',
      end: 'justify-end',
      between: 'justify-between'
    }[justify];

    return (
      <div
        ref={ref}
        className={`flex items-center space-x-3 ${justifyClass} ${className}`}
        {...props}
      />
    );
  }
);
FormActions.displayName = 'FormActions';

// Compound Form Component
export const Form = {
  Root: FormRoot,
  Field: FormField,
  Label: FormLabel,
  Input: FormInput,
  Textarea: FormTextarea,
  Select: FormSelect,
  Checkbox: FormCheckbox,
  Error: FormError,
  Help: FormHelp,
  Grid: FormGrid,
  Actions: FormActions
};

// Export types
export type FormProps = typeof Form;

/**
 * Centralized Logging Utility
 * 
 * Provides controlled logging that can be disabled in production
 * and filtered by log levels and categories.
 */

export enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3,
  TRACE = 4
}

export enum LogCategory {
  AUTH = 'auth',
  API = 'api',
  DATABASE = 'database',
  UI = 'ui',
  PERFORMANCE = 'performance',
  SECURITY = 'security',
  GENERAL = 'general'
}

interface LogConfig {
  enabled: boolean;
  level: LogLevel;
  categories: LogCategory[];
  showInProduction: boolean;
}

class Logger {
  private config: LogConfig;

  constructor() {
    this.config = {
      enabled: process.env.NODE_ENV === 'development' || process.env.NEXT_PUBLIC_ENABLE_LOGGING === 'true',
      level: this.getLogLevel(),
      categories: this.getEnabledCategories(),
      showInProduction: process.env.NEXT_PUBLIC_SHOW_LOGS_IN_PRODUCTION === 'true'
    };
  }

  private getLogLevel(): LogLevel {
    const level = process.env.NEXT_PUBLIC_LOG_LEVEL?.toUpperCase();
    switch (level) {
      case 'ERROR': return LogLevel.ERROR;
      case 'WARN': return LogLevel.WARN;
      case 'INFO': return LogLevel.INFO;
      case 'DEBUG': return LogLevel.DEBUG;
      case 'TRACE': return LogLevel.TRACE;
      default: return process.env.NODE_ENV === 'development' ? LogLevel.DEBUG : LogLevel.ERROR;
    }
  }

  private getEnabledCategories(): LogCategory[] {
    const categories = process.env.NEXT_PUBLIC_LOG_CATEGORIES?.split(',') || [];
    if (categories.length === 0) {
      return Object.values(LogCategory);
    }
    return categories.map(cat => cat.trim() as LogCategory).filter(cat => Object.values(LogCategory).includes(cat));
  }

  private shouldLog(level: LogLevel, category: LogCategory): boolean {
    if (!this.config.enabled) return false;
    if (process.env.NODE_ENV === 'production' && !this.config.showInProduction) return false;
    if (level > this.config.level) return false;
    if (!this.config.categories.includes(category)) return false;
    return true;
  }

  private formatMessage(category: LogCategory, message: string, data?: any): [string, any?] {
    const timestamp = new Date().toISOString();
    const prefix = `[${timestamp}] [${category.toUpperCase()}]`;
    
    if (data !== undefined) {
      return [`${prefix} ${message}`, data];
    }
    return [`${prefix} ${message}`];
  }

  error(category: LogCategory, message: string, data?: any): void {
    if (this.shouldLog(LogLevel.ERROR, category)) {
      const [formattedMessage, logData] = this.formatMessage(category, message, data);
      if (logData !== undefined) {
        console.error(formattedMessage, logData);
      } else {
        console.error(formattedMessage);
      }
    }
  }

  warn(category: LogCategory, message: string, data?: any): void {
    if (this.shouldLog(LogLevel.WARN, category)) {
      const [formattedMessage, logData] = this.formatMessage(category, message, data);
      if (logData !== undefined) {
        console.warn(formattedMessage, logData);
      } else {
        console.warn(formattedMessage);
      }
    }
  }

  info(category: LogCategory, message: string, data?: any): void {
    if (this.shouldLog(LogLevel.INFO, category)) {
      const [formattedMessage, logData] = this.formatMessage(category, message, data);
      if (logData !== undefined) {
        console.info(formattedMessage, logData);
      } else {
        console.info(formattedMessage);
      }
    }
  }

  debug(category: LogCategory, message: string, data?: any): void {
    if (this.shouldLog(LogLevel.DEBUG, category)) {
      const [formattedMessage, logData] = this.formatMessage(category, message, data);
      if (logData !== undefined) {
        console.log(formattedMessage, logData);
      } else {
        console.log(formattedMessage);
      }
    }
  }

  trace(category: LogCategory, message: string, data?: any): void {
    if (this.shouldLog(LogLevel.TRACE, category)) {
      const [formattedMessage, logData] = this.formatMessage(category, message, data);
      if (logData !== undefined) {
        console.trace(formattedMessage, logData);
      } else {
        console.trace(formattedMessage);
      }
    }
  }

  // Convenience methods for common categories
  auth = {
    error: (message: string, data?: any) => this.error(LogCategory.AUTH, message, data),
    warn: (message: string, data?: any) => this.warn(LogCategory.AUTH, message, data),
    info: (message: string, data?: any) => this.info(LogCategory.AUTH, message, data),
    debug: (message: string, data?: any) => this.debug(LogCategory.AUTH, message, data),
  };

  api = {
    error: (message: string, data?: any) => this.error(LogCategory.API, message, data),
    warn: (message: string, data?: any) => this.warn(LogCategory.API, message, data),
    info: (message: string, data?: any) => this.info(LogCategory.API, message, data),
    debug: (message: string, data?: any) => this.debug(LogCategory.API, message, data),
  };

  database = {
    error: (message: string, data?: any) => this.error(LogCategory.DATABASE, message, data),
    warn: (message: string, data?: any) => this.warn(LogCategory.DATABASE, message, data),
    info: (message: string, data?: any) => this.info(LogCategory.DATABASE, message, data),
    debug: (message: string, data?: any) => this.debug(LogCategory.DATABASE, message, data),
  };

  ui = {
    error: (message: string, data?: any) => this.error(LogCategory.UI, message, data),
    warn: (message: string, data?: any) => this.warn(LogCategory.UI, message, data),
    info: (message: string, data?: any) => this.info(LogCategory.UI, message, data),
    debug: (message: string, data?: any) => this.debug(LogCategory.UI, message, data),
  };

  performance = {
    error: (message: string, data?: any) => this.error(LogCategory.PERFORMANCE, message, data),
    warn: (message: string, data?: any) => this.warn(LogCategory.PERFORMANCE, message, data),
    info: (message: string, data?: any) => this.info(LogCategory.PERFORMANCE, message, data),
    debug: (message: string, data?: any) => this.debug(LogCategory.PERFORMANCE, message, data),
  };

  security = {
    error: (message: string, data?: any) => this.error(LogCategory.SECURITY, message, data),
    warn: (message: string, data?: any) => this.warn(LogCategory.SECURITY, message, data),
    info: (message: string, data?: any) => this.info(LogCategory.SECURITY, message, data),
    debug: (message: string, data?: any) => this.debug(LogCategory.SECURITY, message, data),
  };
}

// Export singleton instance
export const logger = new Logger();

// Export types
export type { LogConfig };

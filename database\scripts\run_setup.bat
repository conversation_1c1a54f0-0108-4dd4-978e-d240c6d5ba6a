@echo off
echo =====================================================
echo RenewTrack Database Setup Script
echo =====================================================
echo.
echo This script will set up the complete RenewTrack database
echo with standardized integer primary keys and proper relationships.
echo.

REM Check if PostgreSQL is available
psql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: PostgreSQL psql command not found!
    echo Please ensure PostgreSQL is installed and in your PATH.
    pause
    exit /b 1
)

echo PostgreSQL found. Proceeding with setup...
echo.

REM Prompt for database connection details
set /p DB_USER="Enter PostgreSQL username (default: postgres): "
if "%DB_USER%"=="" set DB_USER=postgres

set /p DB_NAME="Enter database name (default: renewtrack): "
if "%DB_NAME%"=="" set DB_NAME=renewtrack

echo.
echo Connecting to PostgreSQL as user: %DB_USER%
echo Database: %DB_NAME%
echo.

REM Check if database exists, create if not
echo Checking if database exists...
psql -U %DB_USER% -d postgres -c "SELECT 1 FROM pg_database WHERE datname='%DB_NAME%';" -t -A | findstr "1" >nul
if %errorlevel% neq 0 (
    echo Database %DB_NAME% does not exist. Creating...
    psql -U %DB_USER% -d postgres -c "CREATE DATABASE %DB_NAME%;"
    if %errorlevel% neq 0 (
        echo ERROR: Failed to create database!
        pause
        exit /b 1
    )
    echo Database created successfully.
) else (
    echo Database %DB_NAME% already exists.
)

echo.
echo =====================================================
echo Running Database Setup Scripts
echo =====================================================

REM Run the complete setup script
echo Running complete database setup...
psql -U %DB_USER% -d %DB_NAME% -f ../setup-scripts/setup_complete_database.sql
if %errorlevel% neq 0 (
    echo.
    echo WARNING: Complete setup failed. Trying individual scripts...
    echo.
    
    REM Run individual scripts if complete setup fails
    echo 1/4 Creating metadata schema...
    psql -U %DB_USER% -d %DB_NAME% -f ../migrations/metadata_schema.sql
    if %errorlevel% neq 0 (
        echo ERROR: Failed to create metadata schema!
        pause
        exit /b 1
    )
    
    echo 2/4 Creating tenant management schema...
    psql -U %DB_USER% -d %DB_NAME% -f ../migrations/tenant_management_schema.sql
    if %errorlevel% neq 0 (
        echo ERROR: Failed to create tenant management schema!
        pause
        exit /b 1
    )
    
    echo 3/4 Creating tenant schema...
    psql -U %DB_USER% -d %DB_NAME% -f database/migrations/tenant_schema_standardized.sql
    if %errorlevel% neq 0 (
        echo ERROR: Failed to create tenant schema!
        pause
        exit /b 1
    )
    
    echo 4/4 Setting up tenant logging...
    psql -U %DB_USER% -d %DB_NAME% -f database/tenant-logging-setup.sql
    if %errorlevel% neq 0 (
        echo ERROR: Failed to setup tenant logging!
        pause
        exit /b 1
    )
)

echo.
echo =====================================================
echo Verifying Installation
echo =====================================================

REM Verify the installation
echo Checking schemas...
psql -U %DB_USER% -d %DB_NAME% -c "\dn"

echo.
echo Checking table counts...
psql -U %DB_USER% -d %DB_NAME% -c "SELECT 'metadata.PurchaseTypes' as table_name, COUNT(*) as record_count FROM metadata.\"PurchaseTypes\" UNION ALL SELECT 'metadata.RenewalTypes', COUNT(*) FROM metadata.\"RenewalTypes\" UNION ALL SELECT 'metadata.Currencies', COUNT(*) FROM metadata.\"Currencies\";"

echo.
echo =====================================================
echo Setup Complete!
echo =====================================================
echo.
echo The RenewTrack database has been successfully set up with:
echo ✓ Metadata schema with reference data
echo ✓ Tenant management infrastructure  
echo ✓ Default tenant schema with sample data
echo ✓ Comprehensive logging system
echo ✓ Integer primary keys and proper relationships
echo.
echo Next steps:
echo 1. Update application configuration to use the new schema
echo 2. Run application tests to verify functionality
echo 3. Import any existing data using migration scripts
echo.
echo For detailed information, see: database/EXECUTION_GUIDE.md
echo.
pause

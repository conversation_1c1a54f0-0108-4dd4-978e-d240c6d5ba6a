-- =====================================================
-- New Tenant Setup Script
-- =====================================================
-- This script sets up a new tenant with standardized schema
--
-- Usage:
-- 1. Replace 'NEW_TENANT_SCHEMA' with actual tenant schema name
-- 2. Replace tenant details in the INSERT statements
-- 3. Run this script to create a new tenant
-- =====================================================

-- Variables (replace these values)
\set tenant_name 'New Tenant Name'
\set tenant_schema 'tenant_0000000000000002'
\set tenant_subdomain 'newtenant'
\set name 'New Client Name'
\set domain 'newtenant.com'

-- =====================================================
-- STEP 1: Create tenant management records
-- =====================================================

-- Insert new tenant
INSERT INTO
    tenant_management.tenants (
        TenantName,
        SchemaName,
        Subdomain,
        Status,
        Settings
    )
VALUES (
:'tenant_name',
:'tenant_schema',
:'tenant_subdomain',
        'active',
        '{}'
    )
RETURNING
    TenantID;

-- Get the new tenant ID
\set tenant_id `SELECT TenantID FROM tenant_management.tenants WHERE SchemaName = '` :tenant_schema `'`

-- Insert client mapping
INSERT INTO
    tenant_management.clients (
        TenantID,
        Name,
        Domain,
        Status
    )
VALUES (
:tenant_id,
:'name',
:'domain',
        'active'
    );

-- Insert domain mapping
INSERT INTO
    tenant_management.domains (
        TenantID,
        DomainName,
        IsPrimary
    )
VALUES (:tenant_id,:'domain', true);

-- =====================================================
-- STEP 2: Create tenant schema
-- =====================================================

-- Create the tenant schema
CREATE SCHEMA IF NOT EXISTS:tenant_schema;

-- Set search path to new tenant schema
SET search_path TO:tenant_schema, metadata, public;

-- =====================================================
-- STEP 3: Create tenant tables
-- =====================================================

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create sync status enum for this tenant
DO $$ BEGIN
    CREATE TYPE sync_status AS ENUM ('pending', 'matched', 'manual_review', 'rejected');

EXCEPTION WHEN duplicate_object THEN null;

END $$;

-- Renewals table (main business entity)
CREATE TABLE IF NOT EXISTS tenant_renewals (
    renewal_id SERIAL PRIMARY KEY,
    renewal_name VARCHAR(255) NOT NULL,
    product_name VARCHAR(255),
    version VARCHAR(100),
    vendor_name VARCHAR(255),
    renewal_type_id INTEGER REFERENCES metadata.global_renewal_types (renewal_type_id),
    department_id INTEGER REFERENCES metadata.global_departments (department_id),
    purchase_type_id INTEGER REFERENCES metadata.global_purchase_types (purchase_type_id),
    licensed_date DATE,
    start_date DATE,
    associated_emails TEXT [], -- Array of email addresses
    reseller VARCHAR(255),
    currency_id VARCHAR(3) REFERENCES metadata.global_currencies (currency_id),
    cost DECIMAL(15, 2),
    cost_code VARCHAR(100),
    license_count INTEGER,
    description TEXT,
    notes TEXT,
    status VARCHAR(50) DEFAULT 'Active' CHECK (
        status IN (
            'Active',
            'Expired',
            'Cancelled',
            'Pending'
        )
    ),
    is_active BOOLEAN DEFAULT true,
    is_deleted BOOLEAN DEFAULT false,
    created_on TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255),
    changed_on TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    changed_by VARCHAR(255)
);

-- Alerts table (renewal notifications)
CREATE TABLE IF NOT EXISTS tenant_alerts (
    alert_id SERIAL PRIMARY KEY,
    renewal_id INTEGER NOT NULL REFERENCES tenant_renewals (renewal_id) ON DELETE CASCADE,
    alert_name VARCHAR(255) NOT NULL,
    days_before_renewal INTEGER NOT NULL CHECK (days_before_renewal > 0),
    email_recipients TEXT [] NOT NULL, -- Array of email addresses
    is_active BOOLEAN DEFAULT true,
    last_sent TIMESTAMP WITH TIME ZONE,
    created_on TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255),
    changed_on TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    changed_by VARCHAR(255)
);

-- Tenant Vendors table (with sync to global master data)
CREATE TABLE IF NOT EXISTS tenant_vendors (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    display_name VARCHAR(255),
    contact_email VARCHAR(255),
    phone VARCHAR(50),
    address_line1 VARCHAR(255),
    address_line2 VARCHAR(255),
    city VARCHAR(100),
    state VARCHAR(100),
    postal_code VARCHAR(20),
    country VARCHAR(100),
    tax_id VARCHAR(100),
    website VARCHAR(255),
    notes TEXT,
    custom_fields JSONB DEFAULT '{}',
    global_vendor_id INTEGER,
    sync_status VARCHAR(20) DEFAULT 'pending',
    sync_confidence DECIMAL(5, 4) DEFAULT 0.0000 CHECK (
        sync_confidence >= 0
        AND sync_confidence <= 1
    ),
    last_sync_attempt TIMESTAMP WITH TIME ZONE,
    is_deleted BOOLEAN DEFAULT false,
    created_by VARCHAR(255),
    changed_by VARCHAR(255),
    created_on TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    changed_on TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT tenant_vendors_name_not_empty CHECK (LENGTH(TRIM(name)) > 0),
    CONSTRAINT tenant_vendors_email_format CHECK (
        contact_email IS NULL
        OR contact_email ~ * '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'
    )
);

-- Tenant Products table
CREATE TABLE IF NOT EXISTS tenant_products (
    id SERIAL PRIMARY KEY,
    vendor_id INTEGER NOT NULL REFERENCES tenant_vendors (id),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    sku VARCHAR(100),
    barcode VARCHAR(50),
    unit_of_measure VARCHAR(50),
    custom_fields JSONB DEFAULT '{}',
    global_product_id INTEGER,
    sync_status VARCHAR(20) DEFAULT 'pending',
    sync_confidence DECIMAL(5, 4) DEFAULT 0.0000 CHECK (
        sync_confidence >= 0
        AND sync_confidence <= 1
    ),
    last_sync_attempt TIMESTAMP WITH TIME ZONE,
    is_deleted BOOLEAN DEFAULT false,
    created_by VARCHAR(255),
    changed_by VARCHAR(255),
    created_on TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    changed_on TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT tenant_products_name_not_empty CHECK (LENGTH(TRIM(name)) > 0)
);

-- Tenant Product Versions table
CREATE TABLE IF NOT EXISTS tenant_product_versions (
    id SERIAL PRIMARY KEY,
    product_id INTEGER NOT NULL REFERENCES tenant_products (id),
    version VARCHAR(50) NOT NULL,
    release_date DATE,
    notes TEXT,
    is_current BOOLEAN DEFAULT false,
    custom_fields JSONB DEFAULT '{}',
    global_version_id INTEGER,
    sync_status VARCHAR(20) DEFAULT 'pending',
    sync_confidence DECIMAL(5, 4) DEFAULT 0.0000 CHECK (
        sync_confidence >= 0
        AND sync_confidence <= 1
    ),
    last_sync_attempt TIMESTAMP WITH TIME ZONE,
    is_deleted BOOLEAN DEFAULT false,
    created_by VARCHAR(255),
    changed_by VARCHAR(255),
    created_on TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    changed_on TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT tenant_product_versions_version_not_empty CHECK (LENGTH(TRIM(version)) > 0)
);

-- =====================================================
-- STEP 4: Create indexes
-- =====================================================

-- Renewals indexes
CREATE INDEX IF NOT EXISTS idx_renewals_renewal_date ON "Renewals" ("start_date");

CREATE INDEX IF NOT EXISTS idx_renewals_vendor ON "Renewals" ("VendorName");

CREATE INDEX IF NOT EXISTS idx_renewals_status ON "Renewals" ("Status");

CREATE INDEX IF NOT EXISTS idx_renewals_active ON "Renewals" ("Active");

CREATE INDEX IF NOT EXISTS idx_renewals_deleted ON "Renewals" ("IsDeleted");

-- Alerts indexes
CREATE INDEX IF NOT EXISTS idx_alerts_renewal ON "Alerts" ("RenewalID");

CREATE INDEX IF NOT EXISTS idx_alerts_active ON "Alerts" ("IsActive");

CREATE INDEX IF NOT EXISTS idx_alerts_days_before ON "Alerts" ("DaysBeforeRenewal");

-- Vendor indexes
CREATE INDEX IF NOT EXISTS idx_tenant_vendors_name ON tenant_vendors (name);

CREATE INDEX IF NOT EXISTS idx_tenant_vendors_global ON tenant_vendors (global_vendor_id);

CREATE INDEX IF NOT EXISTS idx_tenant_vendors_sync_status ON tenant_vendors (sync_status);

CREATE INDEX IF NOT EXISTS idx_tenant_vendors_deleted ON tenant_vendors (is_deleted);

-- Product indexes
CREATE INDEX IF NOT EXISTS idx_tenant_products_vendor ON tenant_products (vendor_id);

CREATE INDEX IF NOT EXISTS idx_tenant_products_name ON tenant_products (name);

CREATE INDEX IF NOT EXISTS idx_tenant_products_global ON tenant_products (global_product_id);

CREATE INDEX IF NOT EXISTS idx_tenant_products_deleted ON tenant_products (is_deleted);

-- Product version indexes
CREATE INDEX IF NOT EXISTS idx_tenant_product_versions_product ON tenant_product_versions (product_id);

CREATE INDEX IF NOT EXISTS idx_tenant_product_versions_global ON tenant_product_versions (global_version_id);

CREATE INDEX IF NOT EXISTS idx_tenant_product_versions_deleted ON tenant_product_versions (is_deleted);

-- =====================================================
-- STEP 5: Create unique constraints
-- =====================================================
ALTER TABLE tenant_products
ADD CONSTRAINT tenant_products_vendor_name_unique UNIQUE (vendor_id, name);

ALTER TABLE tenant_product_versions
ADD CONSTRAINT tenant_product_versions_product_version_unique UNIQUE (ProductID, Version);

-- =====================================================
-- STEP 6: Setup tenant logging
-- =====================================================

-- Create tenant_log table
CREATE TABLE IF NOT EXISTS tenant_log (
    LogID BIGSERIAL PRIMARY KEY,
    Timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    TableName VARCHAR(100) NOT NULL,
    Operation VARCHAR(10) NOT NULL CHECK (
        Operation IN (
            'INSERT',
            'UPDATE',
            'DELETE',
            'SYSTEM'
        )
    ),
    RecordID INTEGER NOT NULL,
    RecordUUID UUID,
    UserID VARCHAR(255),
    UserEmail VARCHAR(255),
    ChangedFields TEXT [] DEFAULT '{}',
    OldValues JSONB,
    NewValues JSONB,
    BusinessImpact VARCHAR(20) DEFAULT 'low' CHECK (
        BusinessImpact IN (
            'low',
            'medium',
            'high',
            'critical'
        )
    ),
    Metadata JSONB DEFAULT '{}'::jsonb,
    CreatedAt TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes for tenant_log
CREATE INDEX IF NOT EXISTS idx_tenant_log_timestamp ON tenant_log (Timestamp DESC);

CREATE INDEX IF NOT EXISTS idx_tenant_log_table_operation ON tenant_log (TableName, Operation);

CREATE INDEX IF NOT EXISTS idx_tenant_log_user ON tenant_log (UserID);

CREATE INDEX IF NOT EXISTS idx_tenant_log_business_impact ON tenant_log (BusinessImpact);

CREATE INDEX IF NOT EXISTS idx_tenant_log_record_id ON tenant_log (RecordID);

-- =====================================================
-- STEP 7: Create logging triggers
-- =====================================================

-- Create business impact calculation function
CREATE OR REPLACE FUNCTION calculate_business_impact(
    p_table_name VARCHAR,
    p_operation VARCHAR,
    p_changed_fields TEXT[],
    p_old_values JSONB,
    p_new_values JSONB
) RETURNS VARCHAR AS $$
BEGIN
    IF p_operation = 'DELETE' THEN
        RETURN 'critical';
    END IF;
    
    IF p_table_name = 'tenant_renewals' THEN
        IF p_changed_fields && ARRAY['cost', 'start_date', 'status'] THEN
            RETURN 'high';
        END IF;
        RETURN 'medium';
    END IF;

    IF p_table_name = 'tenant_vendors' THEN
        IF p_changed_fields && ARRAY['is_deleted', 'status'] THEN
            RETURN 'high';
        END IF;
        RETURN 'medium';
    END IF;
    
    RETURN 'low';
END;
$$ LANGUAGE plpgsql;

-- Create main trigger function (simplified version)
CREATE OR REPLACE FUNCTION log_tenant_changes() RETURNS TRIGGER AS $$
DECLARE
    changed_fields TEXT[] := '{}';
    old_vals JSONB := '{}'::jsonb;
    new_vals JSONB := '{}'::jsonb;
    field_name TEXT;
    impact_level VARCHAR;
    current_user_id VARCHAR := current_setting('app.current_user_id', true);
    current_user_email VARCHAR := current_setting('app.current_user_email', true);
    record_id_value INTEGER;
BEGIN
    -- Get record ID based on table
    IF TG_OP = 'DELETE' THEN
        record_id_value := CASE
            WHEN TG_TABLE_NAME = 'tenant_renewals' THEN OLD.id
            WHEN TG_TABLE_NAME = 'tenant_alerts' THEN OLD.id
            WHEN TG_TABLE_NAME = 'tenant_vendors' THEN OLD.id
            WHEN TG_TABLE_NAME = 'tenant_products' THEN OLD.id
            WHEN TG_TABLE_NAME = 'tenant_product_versions' THEN OLD.id
            ELSE 0
        END;
        old_vals := to_jsonb(OLD);
        impact_level := 'critical';
        
        INSERT INTO tenant_log (
            TableName, Operation, RecordID, UserID, UserEmail,
            OldValues, BusinessImpact, Metadata
        ) VALUES (
            TG_TABLE_NAME, TG_OP, record_id_value,
            current_user_id, current_user_email,
            old_vals, impact_level,
            jsonb_build_object('trigger_time', NOW())
        );
        
        RETURN OLD;
    END IF;
    
    IF TG_OP = 'INSERT' THEN
        record_id_value := CASE
            WHEN TG_TABLE_NAME = 'tenant_renewals' THEN NEW.id
            WHEN TG_TABLE_NAME = 'tenant_alerts' THEN NEW.id
            WHEN TG_TABLE_NAME = 'tenant_vendors' THEN NEW.id
            WHEN TG_TABLE_NAME = 'tenant_products' THEN NEW.id
            WHEN TG_TABLE_NAME = 'tenant_product_versions' THEN NEW.id
            ELSE 0
        END;
        new_vals := to_jsonb(NEW);
        impact_level := calculate_business_impact(TG_TABLE_NAME, TG_OP, '{}', '{}'::jsonb, new_vals);
        
        INSERT INTO tenant_log (
            TableName, Operation, RecordID, UserID, UserEmail,
            NewValues, BusinessImpact, Metadata
        ) VALUES (
            TG_TABLE_NAME, TG_OP, record_id_value,
            current_user_id, current_user_email,
            new_vals, impact_level,
            jsonb_build_object('trigger_time', NOW())
        );
        
        RETURN NEW;
    END IF;
    
    IF TG_OP = 'UPDATE' THEN
        record_id_value := CASE
            WHEN TG_TABLE_NAME = 'tenant_renewals' THEN NEW.id
            WHEN TG_TABLE_NAME = 'tenant_alerts' THEN NEW.id
            WHEN TG_TABLE_NAME = 'tenant_vendors' THEN NEW.id
            WHEN TG_TABLE_NAME = 'tenant_products' THEN NEW.id
            WHEN TG_TABLE_NAME = 'tenant_product_versions' THEN NEW.id
            ELSE 0
        END;
        
        -- Compare OLD and NEW to find changed fields
        FOR field_name IN SELECT jsonb_object_keys(to_jsonb(NEW)) LOOP
            IF to_jsonb(OLD) ->> field_name IS DISTINCT FROM to_jsonb(NEW) ->> field_name THEN
                changed_fields := array_append(changed_fields, field_name);
                old_vals := old_vals || jsonb_build_object(field_name, to_jsonb(OLD) ->> field_name);
                new_vals := new_vals || jsonb_build_object(field_name, to_jsonb(NEW) ->> field_name);
            END IF;
        END LOOP;
        
        -- Only log if there are actual changes
        IF array_length(changed_fields, 1) > 0 THEN
            impact_level := calculate_business_impact(TG_TABLE_NAME, TG_OP, changed_fields, old_vals, new_vals);
            
            INSERT INTO tenant_log (
                TableName, Operation, RecordID, UserID, UserEmail,
                ChangedFields, OldValues, NewValues, BusinessImpact, Metadata
            ) VALUES (
                TG_TABLE_NAME, TG_OP, record_id_value,
                current_user_id, current_user_email,
                changed_fields, old_vals, new_vals, impact_level,
                jsonb_build_object('trigger_time', NOW(), 'fields_changed', array_length(changed_fields, 1))
            );
        END IF;
        
        RETURN NEW;
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for all tables
CREATE TRIGGER trigger_log_renewals
    AFTER INSERT OR UPDATE OR DELETE ON "Renewals"
    FOR EACH ROW EXECUTE FUNCTION log_tenant_changes();

CREATE TRIGGER trigger_log_alerts
    AFTER INSERT OR UPDATE OR DELETE ON "Alerts"
    FOR EACH ROW EXECUTE FUNCTION log_tenant_changes();

CREATE TRIGGER trigger_log_vendors
    AFTER INSERT OR UPDATE OR DELETE ON tenant_vendors
    FOR EACH ROW EXECUTE FUNCTION log_tenant_changes();

CREATE TRIGGER trigger_log_products
    AFTER INSERT OR UPDATE OR DELETE ON tenant_products
    FOR EACH ROW EXECUTE FUNCTION log_tenant_changes();

CREATE TRIGGER trigger_log_product_versions
    AFTER INSERT OR UPDATE OR DELETE ON tenant_product_versions
    FOR EACH ROW EXECUTE FUNCTION log_tenant_changes();

-- =====================================================
-- STEP 8: Log setup completion
-- =====================================================
INSERT INTO
    tenant_log (
        TableName,
        Operation,
        RecordID,
        UserID,
        UserEmail,
        BusinessImpact,
        Metadata
    )
VALUES (
        'SYSTEM',
        'SYSTEM',
        0,
        'SYSTEM',
        '<EMAIL>',
        'low',
        jsonb_build_object(
            'event',
            'tenant_setup_completed',
            'schema',
            current_schema(),
            'setup_time',
            NOW(),
            'tenant_id',
:tenant_id
        )
    );

-- Reset search path
SET search_path TO public;

-- =====================================================
-- TENANT SETUP COMPLETE
-- =====================================================
-- New tenant has been successfully created!
--
-- Tenant Details:
-- - Name: :tenant_name
-- - Schema: :tenant_schema
-- - Subdomain: :tenant_subdomain
-- - Client Domain: :domain
--
-- Next steps:
-- 1. Configure application to use new tenant
-- 2. Set up user access and permissions
-- 3. Import initial data if needed
-- =====================================================
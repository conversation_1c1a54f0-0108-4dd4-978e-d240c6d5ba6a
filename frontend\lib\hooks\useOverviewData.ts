/**
 * Overview Data Hook
 * 
 * Manages overview/dashboard data including stats and recent activity
 */

'use client'

import { useState, useEffect, useCallback } from 'react'
import { useData } from './useData'
import { useTenant } from './useTenant'
import { OverviewStats, Renewal } from '@/lib/types'

export interface OverviewData {
  stats: OverviewStats
  recentRenewals: Renewal[]
  upcomingRenewals: Renewal[]
}

export interface UseOverviewDataResult {
  data: OverviewData | null
  isLoading: boolean
  error: string | null
  refetch: () => Promise<void>
}

export function useOverviewData(): UseOverviewDataResult {
  const { tenant } = useTenant()
  const [data, setData] = useState<OverviewData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Fetch overview stats
  const { 
    data: statsData, 
    loading: statsLoading, 
    error: statsError, 
    refetch: refetchStats 
  } = useData<OverviewStats>({
    endpoint: '/api/overview/stats',
    cache: { key: `overview-stats-${tenant?.id}`, ttl: 5 * 60 * 1000 }, // 5 minutes
    enabled: !!tenant
  })

  // Fetch recent renewals
  const { 
    data: recentRenewalsData, 
    loading: recentLoading, 
    error: recentError, 
    refetch: refetchRecent 
  } = useData<Renewal[]>({
    endpoint: '/api/overview/recent-renewals',
    cache: { key: `recent-renewals-${tenant?.id}`, ttl: 2 * 60 * 1000 }, // 2 minutes
    enabled: !!tenant
  })

  // Fetch upcoming renewals
  const { 
    data: upcomingRenewalsData, 
    loading: upcomingLoading, 
    error: upcomingError, 
    refetch: refetchUpcoming 
  } = useData<Renewal[]>({
    endpoint: '/api/overview/upcoming-renewals',
    cache: { key: `upcoming-renewals-${tenant?.id}`, ttl: 2 * 60 * 1000 }, // 2 minutes
    enabled: !!tenant
  })

  // Combine loading states
  useEffect(() => {
    setIsLoading(statsLoading || recentLoading || upcomingLoading)
  }, [statsLoading, recentLoading, upcomingLoading])

  // Combine error states
  useEffect(() => {
    const errors = [statsError, recentError, upcomingError].filter(Boolean)
    setError(errors.length > 0 ? errors[0] : null)
  }, [statsError, recentError, upcomingError])

  // Combine data when all loaded
  useEffect(() => {
    if (statsData && recentRenewalsData && upcomingRenewalsData) {
      const combinedData: OverviewData = {
        stats: statsData,
        recentRenewals: recentRenewalsData,
        upcomingRenewals: upcomingRenewalsData
      }
      setData(combinedData)
      console.log('Overview data loaded:', combinedData)
    } else if (!statsLoading && !recentLoading && !upcomingLoading) {
      // If loading is complete but we don't have all data, provide defaults
      const defaultData: OverviewData = {
        stats: statsData || {
          totalRenewals: 0,
          renewalsDue: 0,
          vendors: 0,
          annualSpend: '$0'
        },
        recentRenewals: recentRenewalsData || [],
        upcomingRenewals: upcomingRenewalsData || []
      }
      setData(defaultData)
      console.log('Overview data loaded with defaults:', defaultData)
    }
  }, [
    statsData, 
    recentRenewalsData, 
    upcomingRenewalsData, 
    statsLoading, 
    recentLoading, 
    upcomingLoading
  ])

  // Refetch all data
  const refetch = useCallback(async () => {
    await Promise.all([
      refetchStats(),
      refetchRecent(),
      refetchUpcoming()
    ])
  }, [refetchStats, refetchRecent, refetchUpcoming])

  return {
    data,
    isLoading,
    error,
    refetch
  }
}

export { useOverviewData as default }

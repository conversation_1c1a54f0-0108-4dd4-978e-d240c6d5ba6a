/**
 * Renewal Summary Card Component
 * 
 * Displays renewal summary information including status and cost details
 */

'use client'

import React from 'react'
import { Renewal } from '@/lib/hooks'

interface RenewalSummaryCardProps {
  renewal: Renewal
}

export default function RenewalSummaryCard({ renewal }: RenewalSummaryCardProps) {
  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency || 'CAD'
    }).format(amount)
  }

  const getDaysUntilRenewal = () => {
    if (!renewal.start_date) return null
    
    const start_date = new Date(renewal.start_date)
    const today = new Date()
    const diffTime = start_date.getTime() - today.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    return diffDays
  }

  return (
    <div className="renewal-summary-card bg-white rounded-lg border border-gray-200 p-6">
      <h2 className="text-lg font-semibold text-gray-900 mb-6">Renewal Summary</h2>
      
      <div className="space-y-4">
        {/* Status Summary */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="text-sm font-medium text-gray-700 mb-3">Status</h3>
          <p className="text-sm text-gray-600">
            {renewal.status?.toLowerCase() === 'active' ?
              `This renewal is currently active and ${getDaysUntilRenewal() !== null && getDaysUntilRenewal()! > 0 ?
                `will expire in ${getDaysUntilRenewal()} days` :
                'has expired'
              } on ${new Date(renewal.start_date || '').toLocaleDateString()}.` :
              `This renewal is ${renewal.status?.toLowerCase() || 'unknown'}.`
            }
          </p>
        </div>

        {/* Cost Summary */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="text-sm font-medium text-gray-700 mb-3">Cost</h3>
          <p className="text-sm text-gray-600">
            The annual cost for this renewal is{' '}
            <span className="font-semibold text-gray-900">
              {formatCurrency(renewal.cost || 0, renewal.currency || 'USD')}
            </span>
            {(renewal.license_count || 0) > 0 && (
              <span>
                {' '}for {renewal.license_count} license{(renewal.license_count || 0) !== 1 ? 's' : ''}
              </span>
            )}.
          </p>
        </div>
      </div>
    </div>
  )
}

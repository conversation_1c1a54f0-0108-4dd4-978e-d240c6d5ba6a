/**
 * Authentication Test Page
 * 
 * Simple page to test and debug authentication flow
 */

'use client'

import React, { useState, useEffect } from 'react'

export default function AuthTestPage() {
  const [status, setStatus] = useState('Initializing...')
  const [logs, setLogs] = useState<string[]>([])
  const [user, setUser] = useState<any>(null)
  const [session, setSession] = useState<any>(null)

  const addLog = (message: string) => {
    console.log(message)
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`])
  }

  const testAmplifyConfig = async () => {
    try {
      addLog('🔧 Testing Amplify configuration...')
      
      const { ensureAmplifyConfigured } = await import('@/lib/services/amplify-service')
      await ensureAmplifyConfigured()
      
      addLog('✅ Amplify configured successfully')
      return true
    } catch (error) {
      addLog(`❌ Amplify configuration failed: ${error}`)
      return false
    }
  }

  const testCurrentUser = async () => {
    try {
      addLog('👤 Testing getCurrentUser...')
      
      const { getCurrentUser } = await import('aws-amplify/auth')
      const currentUser = await getCurrentUser()
      
      setUser(currentUser)
      addLog(`✅ Current user found: ${currentUser.username}`)
      return currentUser
    } catch (error) {
      addLog(`ℹ️ No current user: ${error}`)
      return null
    }
  }

  const testSession = async () => {
    try {
      addLog('🎫 Testing fetchAuthSession...')
      
      const { fetchAuthSession } = await import('aws-amplify/auth')
      const authSession = await fetchAuthSession()
      
      setSession(authSession)
      addLog(`✅ Session retrieved: ${JSON.stringify({
        hasTokens: !!authSession.tokens,
        hasIdToken: !!authSession.tokens?.idToken,
        hasAccessToken: !!authSession.tokens?.accessToken
      })}`)
      return authSession
    } catch (error) {
      addLog(`❌ Session retrieval failed: ${error}`)
      return null
    }
  }

  const testSignIn = async () => {
    try {
      addLog('🔐 Testing signInWithRedirect...')
      setStatus('Redirecting to login...')
      
      const { signInWithRedirect } = await import('aws-amplify/auth')
      await signInWithRedirect()
      
      addLog('✅ Sign in redirect initiated')
    } catch (error) {
      addLog(`❌ Sign in failed: ${error}`)
      setStatus('Sign in failed')
    }
  }

  const testSignOut = async () => {
    try {
      addLog('🚪 Testing signOut...')
      setStatus('Signing out...')
      
      const { signOut } = await import('aws-amplify/auth')
      await signOut({ global: true })
      
      setUser(null)
      setSession(null)
      addLog('✅ Sign out successful')
      setStatus('Signed out')
    } catch (error) {
      addLog(`❌ Sign out failed: ${error}`)
      setStatus('Sign out failed')
    }
  }

  const runFullTest = async () => {
    setStatus('Running authentication tests...')
    setLogs([])
    
    // Test 1: Amplify Configuration
    const configOk = await testAmplifyConfig()
    if (!configOk) {
      setStatus('Configuration failed')
      return
    }
    
    // Test 2: Current User
    const currentUser = await testCurrentUser()
    
    // Test 3: Session
    await testSession()
    
    if (currentUser) {
      setStatus('Authenticated')
    } else {
      setStatus('Not authenticated')
    }
  }

  useEffect(() => {
    runFullTest()
  }, [])

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Authentication Test Page</h1>
      
      <div className="mb-6">
        <h2 className="text-lg font-semibold mb-2">Status: {status}</h2>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div className="bg-gray-50 p-4 rounded">
          <h3 className="font-semibold mb-2">Current User</h3>
          <pre className="text-sm bg-white p-2 rounded overflow-auto">
            {user ? JSON.stringify(user, null, 2) : 'No user'}
          </pre>
        </div>

        <div className="bg-gray-50 p-4 rounded">
          <h3 className="font-semibold mb-2">Session</h3>
          <pre className="text-sm bg-white p-2 rounded overflow-auto">
            {session ? JSON.stringify({
              hasTokens: !!session.tokens,
              hasIdToken: !!session.tokens?.idToken,
              hasAccessToken: !!session.tokens?.accessToken,
              credentials: !!session.credentials
            }, null, 2) : 'No session'}
          </pre>
        </div>
      </div>

      <div className="mb-6">
        <h3 className="font-semibold mb-2">Actions</h3>
        <div className="space-x-2">
          <button
            onClick={runFullTest}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Run Tests
          </button>
          <button
            onClick={testSignIn}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            Sign In
          </button>
          <button
            onClick={testSignOut}
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
          >
            Sign Out
          </button>
        </div>
      </div>

      <div className="bg-gray-50 p-4 rounded">
        <h3 className="font-semibold mb-2">Test Logs</h3>
        <div className="bg-white p-2 rounded max-h-64 overflow-auto">
          {logs.length === 0 ? (
            <p className="text-gray-500">No logs yet...</p>
          ) : (
            logs.map((log, index) => (
              <div key={index} className="text-sm font-mono mb-1">
                {log}
              </div>
            ))
          )}
        </div>
      </div>

      <div className="mt-6 text-sm text-gray-600">
        <p>This page helps debug authentication issues. Check the console for detailed logs.</p>
        <p>Navigate to <code>/auth-test</code> to access this page.</p>
      </div>
    </div>
  )
}

/**
 * Users API Route
 * 
 * This route demonstrates best practices for API implementation including:
 * - Secure authentication and authorization
 * - Input validation with Zod schemas
 * - Standardized error handling
 * - Proper database operations
 * - Rate limiting
 * - Security headers
 */

import { NextRequest } from 'next/server';
import {
  createSuccessResponse,
  createErrorResponse,
  ApiErrorCode,
  HttpStatus
} from '@/lib/api/response';
import { withAuth } from '@/lib/api/auth-middleware';
import {
  validateRequestBody,
  validateQueryParams,
  userCreateSchema,
  paginationSchema,
  UserCreateData
} from '@/lib/utils/validation';
import { executeQuery, executeQuerySingle } from '@/lib/database';

import { User } from '@/lib/types';

// GET /api/users - List users with pagination
export const GET = withAuth(async (request: NextRequest, session) => {

  // Validate query parameters
  const { searchParams } = new URL(request.url);
  const queryValidation = validateQueryParams(searchParams, paginationSchema);
  
  if (!queryValidation.success) {
    return queryValidation.response;
  }

  const {
    page = 1,
    limit = 50,
    sortBy,
    sortOrder = 'asc'
  } = queryValidation.data;

  // Calculate offset for pagination
  const offset = (page - 1) * limit;

  // Build query with proper sorting
  const allowedSortFields = ['email', 'name', 'created_on', 'last_login'];
  const sortField = allowedSortFields.includes(sortBy || '') ? sortBy : 'created_on';
  
  const query = `
    SELECT 
      id,
      email,
      name,
      given_name,
      family_name,
      roles,
      created_on,
      changed_on,
      last_login,
      COUNT(*) OVER() as total_count
    FROM users
    ORDER BY ${sortField} ${sortOrder.toUpperCase()}
    LIMIT $1 OFFSET $2
  `;

  const result = await executeQuery<User & { total_count: number }>(
    query, 
    [limit, offset],
    { timeout: 10000 }
  );

  if (!result.success) {
    return createErrorResponse(
      'Failed to fetch users',
      ApiErrorCode.DATABASE_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }

  const users = result.data || [];
  const totalCount = users.length > 0 ? parseInt(users[0].total_count.toString()) : 0;
  const totalPages = Math.ceil(totalCount / limit);

  return createSuccessResponse({
    users: users.map(({ total_count, ...user }) => user),
    pagination: {
      page,
      limit,
      totalCount,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1
    }
  }, 'Users retrieved successfully');
}, {
  requireAuth: true,
  requiredGroups: ['admin']
});

// POST /api/users - Create a new user
export const POST = withAuth(async (request: NextRequest, session) => {

  // Validate request body
  const bodyValidation = await validateRequestBody(request, userCreateSchema);
  
  if (!bodyValidation.success) {
    return bodyValidation.response;
  }

  const userData: UserCreateData = {
    ...bodyValidation.data,
    roles: bodyValidation.data.roles || []
  };

  // Check if user already exists
  const existingUserQuery = 'SELECT id FROM users WHERE email = $1';
  const existingUser = await executeQuerySingle(existingUserQuery, [userData.email]);

  if (existingUser.success && existingUser.data) {
    return createErrorResponse(
      'User with this email already exists',
      ApiErrorCode.VALIDATION_ERROR,
      HttpStatus.CONFLICT
    );
  }

  // Create new user
  const insertQuery = `
    INSERT INTO users (
      email, 
      name, 
      given_name, 
      family_name, 
      roles,
      created_on
    ) 
    VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP)
    RETURNING 
      id,
      email,
      name,
      given_name,
      family_name,
      roles,
      created_on
  `;

  const insertResult = await executeQuerySingle<User>(
    insertQuery,
    [
      userData.email,
      userData.name || null,
      userData.given_name || null,
      userData.family_name || null,
      JSON.stringify(userData.roles)
    ]
  );

  if (!insertResult.success) {
    return createErrorResponse(
      'Failed to create user',
      ApiErrorCode.DATABASE_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }

  // Log user creation for audit
  const currentUserEmail = session.email || 'unknown';
  console.log(`User created: ${userData.email} by ${currentUserEmail}`);

  return createSuccessResponse(
    insertResult.data,
    'User created successfully',
    HttpStatus.CREATED
  );
}, {
  requireAuth: true,
  requiredGroups: ['admin', 'user_manager']
});

// OPTIONS /api/users - CORS preflight
export async function OPTIONS() {
  // Get allowed origins from secure config
  const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'];

  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': allowedOrigins[0], // Use first allowed origin, not wildcard
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Allow-Credentials': 'true',
      'Access-Control-Max-Age': '86400',
    },
  });
}

/**
 * Database Health Check API Endpoint
 * 
 * Provides detailed database connectivity and schema information
 * for debugging database issues
 */

import { NextRequest } from 'next/server';
import { executeQuery } from '@/lib/database';
import { 
  createSuccessResponse, 
  createErrorResponse, 
  ApiErrorCode, 
  HttpStatus,
  withErrorHandling 
} from '@/lib/api/response';

export const GET = withErrorHandling(async (request: NextRequest) => {
  try {
    console.log('🔍 Starting database health check...');
    
    const healthCheck = {
      timestamp: new Date().toISOString(),
      database: {
        connected: false,
        version: null as string | null,
        error: null as string | null
      },
      schemas: {
        metadata: false,
        tenant_management: false,
        tenant_schemas: [] as string[]
      },
      tables: {
        metadata: [] as string[],
        tenant_management: [] as string[],
        tenant_schemas: {} as Record<string, string[]>
      },
      errors: [] as string[]
    };

    // Test basic database connection
    try {
      const versionResult = await executeQuery('SELECT version() as version');
      if (versionResult.success && versionResult.data && versionResult.data.length > 0) {
        healthCheck.database.connected = true;
        healthCheck.database.version = versionResult.data[0].version;
        console.log('✅ Database connection successful');
      }
    } catch (error) {
      healthCheck.database.error = error instanceof Error ? error.message : 'Unknown error';
      healthCheck.errors.push(`Database connection failed: ${healthCheck.database.error}`);
      console.error('❌ Database connection failed:', error);
    }

    // Check if schemas exist
    const schemasToCheck = ['metadata', 'tenant_management'];

    // Also check for tenant schemas dynamically
    try {
      const tenantSchemasResult = await executeQuery(`
        SELECT schema_name
        FROM information_schema.schemata
        WHERE schema_name LIKE 'tenant_%'
        ORDER BY schema_name
      `);

      if (tenantSchemasResult.success && tenantSchemasResult.data) {
        healthCheck.schemas.tenant_schemas = tenantSchemasResult.data.map(row => row.schema_name);
      }
    } catch (error) {
      healthCheck.errors.push(`Error checking tenant schemas: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    for (const schemaName of schemasToCheck) {
      try {
        const schemaQuery = `
          SELECT EXISTS (
            SELECT FROM information_schema.schemata 
            WHERE schema_name = $1
          ) as exists
        `;
        
        const schemaResult = await executeQuery(schemaQuery, [schemaName]);
        
        if (schemaResult.success && schemaResult.data && schemaResult.data.length > 0) {
          (healthCheck.schemas as any)[schemaName] = schemaResult.data[0].exists;
          
          if (schemaResult.data[0].exists) {
            console.log(`✅ Schema '${schemaName}' exists`);
            
            // Get tables in this schema
            const tablesQuery = `
              SELECT table_name 
              FROM information_schema.tables 
              WHERE table_schema = $1 
              ORDER BY table_name
            `;
            
            const tablesResult = await executeQuery(tablesQuery, [schemaName]);
            
            if (tablesResult.success && tablesResult.data) {
              (healthCheck.tables as any)[schemaName] = tablesResult.data.map(row => row.table_name);
              console.log(`📋 Found ${tablesResult.data.length} tables in '${schemaName}' schema`);
            }
          } else {
            console.log(`❌ Schema '${schemaName}' does not exist`);
            healthCheck.errors.push(`Schema '${schemaName}' does not exist`);
          }
        }
      } catch (error) {
        healthCheck.errors.push(`Error checking schema '${schemaName}': ${error instanceof Error ? error.message : 'Unknown error'}`);
        console.error(`❌ Error checking schema '${schemaName}':`, error);
      }
    }

    // Check specific critical tables (using consistent snake_case naming)
    const criticalTables = [
      { schema: 'metadata', table: 'global_currencies' },
      { schema: 'metadata', table: 'global_renewal_types' },
      { schema: 'tenant_management', table: 'tenants' },
      { schema: 'tenant_management', table: 'clients' },
      { schema: 'tenant_0000000000000001', table: 'tenant_renewals' },
      { schema: 'tenant_0000000000000001', table: 'tenant_alerts' }
    ];

    const tableStatus: Record<string, boolean> = {};
    
    for (const { schema, table } of criticalTables) {
      try {
        const tableQuery = `
          SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = $1 AND table_name = $2
          ) as exists
        `;
        
        const tableResult = await executeQuery(tableQuery, [schema, table]);
        
        if (tableResult.success && tableResult.data && tableResult.data.length > 0) {
          const exists = tableResult.data[0].exists;
          tableStatus[`${schema}.${table}`] = exists;
          
          if (exists) {
            console.log(`✅ Critical table '${schema}.${table}' exists`);
          } else {
            console.log(`❌ Critical table '${schema}.${table}' missing`);
            healthCheck.errors.push(`Critical table '${schema}.${table}' is missing`);
          }
        }
      } catch (error) {
        tableStatus[`${schema}.${table}`] = false;
        healthCheck.errors.push(`Error checking table '${schema}.${table}': ${error instanceof Error ? error.message : 'Unknown error'}`);
        console.error(`❌ Error checking table '${schema}.${table}':`, error);
      }
    }

    (healthCheck as any)['criticalTables'] = tableStatus;

    // Overall health assessment
    const isHealthy = healthCheck.database.connected &&
                     healthCheck.schemas.metadata &&
                     healthCheck.schemas.tenant_management &&
                     (healthCheck.schemas as any).tenant_0000000000000001 &&
                     healthCheck.errors.length === 0;

    console.log(`🏥 Database health check complete. Status: ${isHealthy ? 'HEALTHY' : 'UNHEALTHY'}`);
    
    if (isHealthy) {
      return createSuccessResponse(healthCheck, 'Database is healthy');
    } else {
      return createErrorResponse(
        'Database health check failed',
        ApiErrorCode.DATABASE_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR,
        healthCheck
      );
    }

  } catch (error) {
    console.error('❌ Database health check failed:', error);
    return createErrorResponse(
      'Database health check failed',
      ApiErrorCode.DATABASE_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR,
      { error: error instanceof Error ? error.message : 'Unknown error' }
    );
  }
});

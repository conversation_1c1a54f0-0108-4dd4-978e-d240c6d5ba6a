/**
 * withAuthenticator HOC
 * 
 * Amplify-style HOC for protecting components with authentication
 * Follows Amplify patterns while integrating with our custom auth system
 */

'use client'

import React, { ComponentType, useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/lib/hooks/useAuth'
import { LoadingPage } from '@/components/common/LoadingStates'

export interface WithAuthenticatorOptions {
  /**
   * Whether authentication is required
   */
  requireAuth?: boolean
  
  /**
   * Required user groups for access
   */
  requiredGroups?: string[]
  
  /**
   * Where to redirect if not authenticated
   */
  loginPath?: string
  
  /**
   * Where to redirect if insufficient permissions
   */
  unauthorizedPath?: string
  
  /**
   * Custom loading component
   */
  loadingComponent?: ComponentType
  
  /**
   * Custom unauthorized component
   */
  unauthorizedComponent?: ComponentType
  
  /**
   * Whether to show loading during auth check
   */
  showLoading?: boolean
}

export interface AuthenticatorProps {
  user: any
  signOut: () => Promise<void>
}

/**
 * Higher-Order Component that wraps components with authentication
 */
export function withAuthenticator<P extends object>(
  Component: ComponentType<P & AuthenticatorProps>,
  options: WithAuthenticatorOptions = {}
) {
  const {
    requireAuth = true,
    requiredGroups = [],
    loginPath = '/login',
    unauthorizedPath = '/unauthorized',
    loadingComponent: LoadingComponent = LoadingPage,
    unauthorizedComponent: UnauthorizedComponent,
    showLoading = true
  } = options

  return function AuthenticatedComponent(props: P) {
    const { user, isAuthenticated, loading: authLoading, logout } = useAuth()
    const router = useRouter()
    const [isChecking, setIsChecking] = useState(true)
    const [hasAccess, setHasAccess] = useState(false)

    useEffect(() => {
      const checkAccess = async () => {
        console.log('[withAuthenticator] Checking access...', {
          requireAuth,
          isAuthenticated,
          user: !!user,
          requiredGroups
        })

        // If auth is not required, allow access
        if (!requireAuth) {
          setHasAccess(true)
          setIsChecking(false)
          return
        }

        // If auth is loading, wait
        if (authLoading) {
          return
        }

        // If not authenticated and auth is required
        if (!isAuthenticated || !user) {
          console.log('[withAuthenticator] Not authenticated, redirecting to login')
          router.push(loginPath)
          return
        }

        // Check group requirements
        if (requiredGroups.length > 0) {
          const userGroups = user.groups || []
          const hasRequiredGroup = requiredGroups.some(group => 
            userGroups.includes(group)
          )

          if (!hasRequiredGroup) {
            console.log('[withAuthenticator] Insufficient permissions', {
              userGroups,
              requiredGroups
            })
            
            if (UnauthorizedComponent) {
              setHasAccess(false)
              setIsChecking(false)
              return
            }
            
            router.push(unauthorizedPath)
            return
          }
        }

        // All checks passed
        console.log('[withAuthenticator] Access granted')
        setHasAccess(true)
        setIsChecking(false)
      }

      checkAccess()
    }, [
      requireAuth,
      isAuthenticated,
      user,
      authLoading,
      requiredGroups,
      router,
      loginPath,
      unauthorizedPath
    ])

    // Show loading during auth check
    if ((authLoading || isChecking) && showLoading) {
      return <LoadingComponent title="Authenticating..." />
    }

    // Show unauthorized component if provided and user lacks access
    if (!hasAccess && UnauthorizedComponent) {
      return <UnauthorizedComponent />
    }

    // Don't render anything if still checking or no access
    if (isChecking || !hasAccess) {
      return null
    }

    // Render the wrapped component with auth props
    return (
      <Component
        {...props}
        user={user}
        signOut={logout}
      />
    )
  }
}

/**
 * Default export for compatibility with Amplify patterns
 */
export default withAuthenticator

/**
 * Convenience function for common authentication patterns
 */
export const requireAuth = (options?: Omit<WithAuthenticatorOptions, 'requireAuth'>) =>
  (Component: ComponentType<any>) =>
    withAuthenticator(Component, { ...options, requireAuth: true })

/**
 * Convenience function for admin-only components
 */
export const requireAdmin = (options?: Omit<WithAuthenticatorOptions, 'requiredGroups'>) =>
  (Component: ComponentType<any>) =>
    withAuthenticator(Component, { 
      ...options, 
      requiredGroups: ['admin', 'super-admin'],
      unauthorizedPath: '/overview'
    })

/**
 * Convenience function for super-admin-only components
 */
export const requireSuperAdmin = (options?: Omit<WithAuthenticatorOptions, 'requiredGroups'>) =>
  (Component: ComponentType<any>) =>
    withAuthenticator(Component, { 
      ...options, 
      requiredGroups: ['super-admin'],
      unauthorizedPath: '/overview'
    })

/**
 * Filter Presets API Endpoint
 * 
 * Manages saved filter presets
 * GET /api/filter-presets?table=X - Get all presets for a table
 * POST /api/filter-presets - Create new preset (handled by advanced-filters)
 */

import { createApiRoute } from '@/lib/api/route-factory';
import { AdvancedFilterService } from '@/lib/services/advanced-filter-service';
import { z } from 'zod';

const filterPresetsQuerySchema = z.object({
  table: z.string().optional()
});

// GET /api/filter-presets - Get saved filter presets
export const GET = createApiRoute('GET', {
  requireAuth: true,
  requireTenant: true,
  querySchema: filterPresetsQuerySchema,
  handler: async (context) => {
    const { session, tenant, query } = context;
    if (!session || !tenant || !query) {
      throw new Error('Session, tenant context, and query parameters are required');
    }
    const { table } = query;

    if (!table) {
      throw new Error('Table name is required');
    }

    // Get filter presets
    const presets = await AdvancedFilterService.getFilterPresets(
      table,
      session.email,
      tenant.tenant
    );

    return {
      presets,
      count: presets.length
    };
  }
});

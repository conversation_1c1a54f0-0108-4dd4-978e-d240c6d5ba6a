/**
 * Tenant Renewal Items API Endpoint
 * 
 * Provides CRUD operations for tenant-specific renewal items
 * GET /api/tenant-renewal-items?renewal_id=xxx - Returns items for a renewal
 * POST /api/tenant-renewal-items - Creates renewal items for a renewal
 */

import { NextRequest } from 'next/server';
import { TenantContext } from '@/lib/tenant/context';
import { executeTenantQuery, executeTenantQuerySingle } from '@/lib/tenant/database';
import {
  createSuccessResponse,
  createErrorResponse,
  ApiErrorCode,
  HttpStatus,
  withErrorHandling
} from '@/lib/api/response';
import { authenticateRequest } from '@/lib/api/auth-middleware';
import { getClientByEmailDomain } from '@/lib/tenant/clients';
import { z } from 'zod';

// Validation schema for creating renewal items
const createRenewalItemSchema = z.object({
  renewal_id: z.number().int().positive('Invalid renewal ID'),
  vendor_id: z.union([z.string(), z.number()]).transform(val => typeof val === 'string' ? parseInt(val) : val),
  product_id: z.union([z.string(), z.number()]).transform(val => typeof val === 'string' ? parseInt(val) : val),
  version_id: z.union([z.string(), z.number()]).transform(val => typeof val === 'string' ? parseInt(val) : val),
  license_count: z.number().int().positive('License count must be positive').default(1),
  unit_cost: z.number().min(0, 'Unit cost must be non-negative').default(0),
  total_cost: z.number().min(0, 'Total cost must be non-negative').default(0),
  cost_code: z.string().max(100).optional(),
  notes: z.string().optional(),
});

const createMultipleItemsSchema = z.object({
  renewal_id: z.number().int().positive('Invalid renewal ID'),
  items: z.array(createRenewalItemSchema.omit({ renewal_id: true })).min(1, 'At least one item is required')
});

// GET /api/tenant-renewal-items - Get items for a renewal
export const GET = withErrorHandling(async (request: NextRequest) => {
  console.log('[TENANT-RENEWAL-ITEMS-API] GET request received');

  // Verify authentication using Amplify
  try {
    const session = await fetchAuthSession();
    if (!session?.tokens?.idToken) {
      return createErrorResponse(
        'Authentication required',
        ApiErrorCode.UNAUTHORIZED,
        HttpStatus.UNAUTHORIZED
      );
    }
  } catch (error) {
    return createErrorResponse(
      'Authentication failed',
      ApiErrorCode.UNAUTHORIZED,
      HttpStatus.UNAUTHORIZED
    );
  }

  // Get user attributes for tenant context
  const userAttributes = await fetchUserAttributes();
  const userEmail = userAttributes.email;

  if (!userEmail) {
    return createErrorResponse(
      'User email not found',
      ApiErrorCode.UNAUTHORIZED,
      HttpStatus.UNAUTHORIZED
    );
  }

  // Get tenant context
  const clientResult = await getClientByEmailDomain(userEmail);
  if (!clientResult.success || !clientResult.client) {
    return createErrorResponse(
      clientResult.error || 'Failed to resolve tenant context',
      (clientResult.errorCode as ApiErrorCode) || ApiErrorCode.UNAUTHORIZED,
      HttpStatus.UNAUTHORIZED
    );
  }

  const tenant = clientResult.client;
    const { searchParams } = new URL(request.url);
    const renewalId = searchParams.get('renewal_id');

    if (!renewalId) {
      return createErrorResponse(
        'renewal_id parameter is required',
        ApiErrorCode.VALIDATION_ERROR,
        HttpStatus.BAD_REQUEST
      );
    }

  try {
      const query = `
        SELECT 
          ri.id,
          ri.renewal_id,
          ri.vendor_id,
          ri.product_id,
          ri.version_id,
          ri.license_count,
          ri.unit_cost,
          ri.total_cost,
          ri.cost_code,
          ri.notes,
          v.name as vendor_name,
          v.display_name as vendor_display_name,
          p.name as product_name,
          pv.version as version_name,
          ri.created_on,
          ri.changed_on
        FROM "${tenant.tenantSchema}".tenant_renewal_items ri
        JOIN "${tenant.tenantSchema}".tenant_vendors v ON ri.vendor_id = v.id
        JOIN "${tenant.tenantSchema}".tenant_products p ON ri.product_id = p.id
        JOIN "${tenant.tenantSchema}".tenant_product_versions pv ON ri.version_id = pv.id
        WHERE ri.renewal_id = $1
        ORDER BY ri.created_on ASC
      `;

      const result = await executeTenantQuery(query, [parseInt(renewalId)], tenant);

      if (!result.success) {
        console.error('[TENANT-RENEWAL-ITEMS-API] Database error:', result.error);
        return createErrorResponse(
          'Failed to fetch renewal items',
          ApiErrorCode.DATABASE_ERROR,
          HttpStatus.INTERNAL_SERVER_ERROR
        );
      }

      console.log(`[TENANT-RENEWAL-ITEMS-API] Found ${result.data?.length || 0} items for renewal ${renewalId}`);

      return createSuccessResponse(
        result.data || [],
        'Renewal items retrieved successfully'
      );

  } catch (error) {
    console.error('[TENANT-RENEWAL-ITEMS-API] Error:', error);
    return createErrorResponse(
      'Failed to fetch renewal items',
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
});

// POST /api/tenant-renewal-items - Create renewal items
export const POST = withErrorHandling(async (request: NextRequest) => {
  console.log('[TENANT-RENEWAL-ITEMS-API] POST request received');

  // Use unified authentication middleware
  const authResult = await authenticateRequest(request, {
    requireAuth: true,
    requiredGroups: []
  });

  if (!authResult.success) {
    return authResult.response;
  }

  const { session } = authResult;

  // Get user attributes for tenant context
  const userAttributes = await fetchUserAttributes();
  const userEmail = userAttributes.email;

  if (!userEmail) {
    return createErrorResponse(
      'User email not found',
      ApiErrorCode.UNAUTHORIZED,
      HttpStatus.UNAUTHORIZED
    );
  }

  // Get tenant context
  const clientResult = await getClientByEmailDomain(userEmail);
  if (!clientResult.success || !clientResult.client) {
    return createErrorResponse(
      clientResult.error || 'Failed to resolve tenant context',
      (clientResult.errorCode as ApiErrorCode) || ApiErrorCode.UNAUTHORIZED,
      HttpStatus.UNAUTHORIZED
    );
  }

  const tenant = clientResult.client;

  try {
      const body = await request.json();
      console.log('[TENANT-RENEWAL-ITEMS-API] Request body:', body);

      // Validate request data
      const validationResult = createMultipleItemsSchema.safeParse(body);
      if (!validationResult.success) {
        console.error('[TENANT-RENEWAL-ITEMS-API] Validation error:', validationResult.error);
        return createErrorResponse(
          'Invalid renewal items data',
          ApiErrorCode.VALIDATION_ERROR,
          HttpStatus.BAD_REQUEST,
          validationResult.error.errors
        );
      }

      const { renewal_id, items } = validationResult.data;

      // Verify renewal exists
      const renewalQuery = `
        SELECT id FROM "${tenant.tenantSchema}".tenant_renewals
        WHERE id = $1
      `;

      const renewalResult = await executeTenantQuerySingle(
        renewalQuery, 
        [renewal_id], 
        tenant
      );

      if (!renewalResult.success || !renewalResult.data) {
        return createErrorResponse(
          'Renewal not found',
          ApiErrorCode.NOT_FOUND,
          HttpStatus.NOT_FOUND
        );
      }

      // Create all items in a transaction-like manner
      const createdItems = [];

      for (const item of items) {
        // Verify vendor, product, and version exist and are related
        const verificationQuery = `
          SELECT 
            v.id as vendor_id,
            p.id as product_id,
            pv.id as version_id,
            v.name as vendor_name,
            p.name as product_name,
            pv.version as version_name
          FROM "${tenant.tenantSchema}".tenant_vendors v
          JOIN "${tenant.tenantSchema}".tenant_products p ON p.vendor_id = v.id
          JOIN "${tenant.tenantSchema}".tenant_product_versions pv ON pv.product_id = p.id
          WHERE v.id = $1 AND p.id = $2 AND pv.id = $3
        `;

        const verificationResult = await executeTenantQuerySingle(
          verificationQuery,
          [item.vendor_id, item.product_id, item.version_id],
          tenant
        );

        if (!verificationResult.success || !verificationResult.data) {
          return createErrorResponse(
            'Invalid vendor/product/version combination',
            ApiErrorCode.VALIDATION_ERROR,
            HttpStatus.BAD_REQUEST
          );
        }

        // Calculate total cost if not provided
        const totalCost = item.total_cost || (item.unit_cost * item.license_count);

        // Create the renewal item
        const insertQuery = `
          INSERT INTO "${tenant.tenantSchema}".tenant_renewal_items (
            renewal_id, vendor_id, product_id, version_id, license_count,
            unit_cost, total_cost, cost_code, notes,
            created_by, changed_by, created_on, changed_on
          ) VALUES (
            $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, 
            CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
          ) RETURNING id, renewal_id, vendor_id, product_id, version_id, 
                     license_count, unit_cost, total_cost, cost_code, notes, created_on
        `;

        const insertResult = await executeTenantQuerySingle(
          insertQuery,
          [
            renewal_id,
            item.vendor_id,
            item.product_id,
            item.version_id,
            item.license_count,
            item.unit_cost,
            totalCost,
            item.cost_code || null,
            item.notes || null,
            userAttributes.sub || 'unknown',
            userAttributes.sub || 'unknown'
          ],
          tenant
        );

        if (!insertResult.success || !insertResult.data) {
          console.error('[TENANT-RENEWAL-ITEMS-API] Insert error:', insertResult.error);
          return createErrorResponse(
            'Failed to create renewal item',
            ApiErrorCode.DATABASE_ERROR,
            HttpStatus.INTERNAL_SERVER_ERROR
          );
        }

        // Add the verification data for response
        createdItems.push({
          ...insertResult.data,
          vendor_name: verificationResult.data.vendor_name,
          product_name: verificationResult.data.product_name,
          version_name: verificationResult.data.version_name
        });
      }

      console.log('[TENANT-RENEWAL-ITEMS-API] Renewal items created successfully:', createdItems.length);

      return createSuccessResponse(
        createdItems,
        'Renewal items created successfully',
        HttpStatus.CREATED
      );

  } catch (error) {
    console.error('[TENANT-RENEWAL-ITEMS-API] Error:', error);
    return createErrorResponse(
      'Failed to create renewal items',
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
});

/**
 * Next.js Navigation Mock for Jest
 * 
 * Mocks Next.js 13+ navigation functionality for testing
 */

const mockNavigation = {
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    prefetch: jest.fn(),
    back: jest.fn(),
    forward: jest.fn(),
    refresh: jest.fn(),
  }),
  usePathname: () => '/',
  useSearchParams: () => new URLSearchParams(),
  useParams: () => ({}),
  redirect: jest.fn(),
  notFound: jest.fn(),
}

export const useRouter = mockNavigation.useRouter
export const usePathname = mockNavigation.usePathname
export const useSearchParams = mockNavigation.useSearchParams
export const useParams = mockNavigation.useParams
export const redirect = mockNavigation.redirect
export const notFound = mockNavigation.notFound
export default mockNavigation

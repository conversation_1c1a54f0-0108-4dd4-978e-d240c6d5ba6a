'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/lib/hooks/useAuth'

export default function SignOutPage() {
  const router = useRouter()
  const { signOut } = useAuth()

  useEffect(() => {
    async function handleSignOut() {
      try {
        await signOut()
        // Clear SSR/middleware idToken cookie
        try {
          await fetch('/api/auth/set-token', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ idToken: '' }),
            credentials: 'include',
          });
        } catch (err) {
          console.error('Failed to clear idToken cookie:', err);
        }
        router.push('/login')
      } catch (error) {
        console.error('Sign out error:', error)
        // Still redirect to login
        router.push('/login')
      }
    }

    handleSignOut()
  }, [router, signOut])

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <h1 className="text-2xl font-bold mb-4">Signing Out</h1>
        <p className="mb-4">Please wait while we sign you out...</p>
        <div className="w-16 h-16 border-t-4 border-blue-500 border-solid rounded-full animate-spin mx-auto"></div>
      </div>
    </div>
  )
}

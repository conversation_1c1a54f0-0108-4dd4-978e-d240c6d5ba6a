'use client'

import { useEffect } from 'react'
import { useAuth } from '@/lib/hooks/useAuth'

export default function SignOutPage() {
  const { logout } = useAuth()

  useEffect(() => {
    async function handleSignOut() {
      try {
        // The logout function now handles cookie clearing internally
        await logout()
      } catch (error) {
        console.error('Sign out error:', error)
        // Still redirect to login - logout function handles this
      }
    }

    handleSignOut()
  }, [logout])

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <h1 className="text-2xl font-bold mb-4">Signing Out</h1>
        <p className="mb-4">Please wait while we sign you out...</p>
        <div className="w-16 h-16 border-t-4 border-blue-500 border-solid rounded-full animate-spin mx-auto"></div>
      </div>
    </div>
  )
}

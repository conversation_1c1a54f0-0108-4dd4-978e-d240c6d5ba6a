/**
 * Unified Tenant Context Management
 * 
 * This module provides a consistent, reusable approach for tenant context resolution
 * across all API endpoints. It replaces the inconsistent usage of getClientByEmailDomain
 * and withTenant with a single, standardized pattern.
 */

import { NextResponse } from 'next/server';
import { AuthSession } from '@/lib/api/auth-middleware';
import { getClientByEmailDomain, ClientLookupResult } from '@/lib/tenant/clients';
import {
  createErrorResponse,
  ApiErrorCode,
  HttpStatus,
  ApiResponse
} from '@/lib/api/response';

// Import and re-export the canonical TenantContext interface
import type { TenantContext } from '@/lib/types';
export type { TenantContext };

// Result interface for tenant resolution
export interface TenantResolutionResult {
  success: boolean;
  tenant?: TenantContext;
  error?: string;
  response?: NextResponse<ApiResponse>;
}

/**
 * Resolve tenant context from authenticated session
 * This is the primary function that should be used across all API endpoints
 */
export async function resolveTenantContext(session: AuthSession): Promise<TenantResolutionResult> {
  const startTime = Date.now()

  try {
    console.log(`🔍 [TENANT] Resolving tenant context for: ${session.email}`)

    // Use the proven getClientByEmailDomain approach
    const clientResult: ClientLookupResult = await getClientByEmailDomain(session.email);

    const duration = Date.now() - startTime

    if (!clientResult.success) {
      console.log(`❌ [TENANT] Failed to resolve tenant (${duration}ms): ${clientResult.error}`)

      const statusCode = clientResult.errorCode === 'NOT_FOUND' ? HttpStatus.NOT_FOUND : HttpStatus.INTERNAL_SERVER_ERROR;
      const apiErrorCode = clientResult.errorCode === 'NOT_FOUND' ? ApiErrorCode.NOT_FOUND : ApiErrorCode.DATABASE_ERROR;

      return {
        success: false,
        error: clientResult.error || 'Failed to resolve tenant context',
        response: createErrorResponse(
          clientResult.error || 'Failed to fetch tenant information',
          apiErrorCode,
          statusCode
        )
      };
    }

    console.log(`✅ [TENANT] Resolved tenant (${duration}ms): ${clientResult.client!.clientName} (ID: ${clientResult.client!.clientId})`)

    return {
      success: true,
      tenant: clientResult.client!
    };

  } catch (error) {
    const duration = Date.now() - startTime
    console.error(`❌ [TENANT] Error resolving tenant context (${duration}ms):`, error);
    return {
      success: false,
      error: 'Internal error resolving tenant context',
      response: createErrorResponse(
        'Internal error resolving tenant context',
        ApiErrorCode.INTERNAL_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    };
  }
}

/**
 * Higher-order function to wrap API handlers with tenant context
 * This provides a consistent pattern for all API endpoints that need tenant context
 */
export function withTenantContext<T extends any[], R>(
  handler: (tenant: TenantContext, session: AuthSession, ...args: T) => Promise<R>
) {
  return async (session: AuthSession, ...args: T): Promise<R | Response> => {
    const tenantResult = await resolveTenantContext(session);

    if (!tenantResult.success) {
      return tenantResult.response!;
    }

    return handler(tenantResult.tenant!, session, ...args);
  };
}

/**
 * Utility function to get tenant context for use in components/hooks
 * This should be used in React components that need tenant information
 */
export async function getTenantContext(session: AuthSession): Promise<TenantContext | null> {
  const result = await resolveTenantContext(session);
  return result.success ? result.tenant! : null;
}

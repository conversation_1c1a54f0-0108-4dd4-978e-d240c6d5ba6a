/**
 * Renewal Details Card Component
 * 
 * Displays detailed renewal information in a card format
 */

'use client'

import React from 'react'
import { Renewal } from '@/lib/types'
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card'

interface RenewalDetailsCardProps {
  renewal: Renewal
}

export default function RenewalDetailsCard({ renewal }: RenewalDetailsCardProps) {
  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency || 'CAD'
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    if (!dateString) return 'Not specified'
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  return (
    <Card className="renewal-details-card">
      <CardHeader>
        <CardTitle>Renewal Details</CardTitle>
      </CardHeader>
      <CardContent>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Left Column */}
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Product
            </label>
            <p className="text-sm text-gray-900">{renewal.product_name || renewal.name}</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Version/Edition
            </label>
            <p className="text-sm text-gray-900">{renewal.version || 'Not specified'}</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Vendor
            </label>
            <p className="text-sm text-gray-900">{renewal.vendor}</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Type
            </label>
            <p className="text-sm text-gray-900">{renewal.type}</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Department
            </label>
            <p className="text-sm text-gray-900">{renewal.department || 'Unspecified Department'}</p>
          </div>
        </div>

        {/* Right Column */}
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Licensed Date
            </label>
            <p className="text-sm text-gray-900">{formatDate(renewal.licensed_date || '')}</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Start Date
            </label>
            <p className="text-sm text-gray-900">{formatDate(renewal.start_date || '')}</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Associated Users
            </label>
            <div className="text-sm text-gray-900">
              {renewal.associated_emails ? (
                <span className="text-blue-600">{renewal.associated_emails}</span>
              ) : (
                <p>No email addresses specified</p>
              )}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Reseller Information
            </label>
            <p className="text-sm text-gray-900">{renewal.reseller || 'Not specified'}</p>
          </div>
        </div>
      </div>

      {/* Full Width Fields */}
      <div className="mt-6 space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Currency
            </label>
            <p className="text-sm text-gray-900">{renewal.currency}</p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Cost
            </label>
            <p className="text-sm text-gray-900 font-semibold">
              {formatCurrency(renewal.cost || 0, renewal.currency || 'USD')}
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Cost Code
            </label>
            <p className="text-sm text-gray-900">{renewal.cost_code || 'Not specified'}</p>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            License Count
          </label>
          <p className="text-sm text-gray-900">
            {(renewal.license_count || 0) > 0 ? `${renewal.license_count} licenses` : 'Not specified'}
          </p>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Description
          </label>
          <p className="text-sm text-gray-900">
            {renewal.description || 'No description provided'}
          </p>
        </div>

        {renewal.notes && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Notes
            </label>
            <p className="text-sm text-gray-900">{renewal.notes}</p>
          </div>
        )}
      </div>
      </CardContent>
    </Card>
  )
}

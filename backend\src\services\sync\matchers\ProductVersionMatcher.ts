/**
 * Product Version Matching Algorithm
 * 
 * Implements sophisticated product version matching logic within product context:
 * 1. Exact version string match (95% confidence)
 * 2. Semantic version match (90% confidence)
 * 3. Release date + fuzzy version match (70-85% confidence)
 * 
 * Versions are only matched within the same product to ensure accuracy.
 */

import { Logger } from '../../Logger'
import { TenantProductVersion, GlobalProductVersion, ProductVersionMatch } from '../processors/ProductVersionSyncProcessor'

export class ProductVersionMatcher {
  private logger: Logger

  constructor(logger: Logger) {
    this.logger = logger
  }

  /**
   * Find potential matches for a tenant product version against global versions
   */
  async findMatches(tenantVersion: TenantProductVersion, globalVersions: GlobalProductVersion[]): Promise<ProductVersionMatch[]> {
    const matches: ProductVersionMatch[] = []

    for (const globalVersion of globalVersions) {
      // Try exact version match first (highest confidence)
      const exactMatch = this.matchByExactVersion(tenantVersion, globalVersion)
      if (exactMatch) {
        matches.push(exactMatch)
        continue // Exact match is definitive, skip other checks
      }

      // Try semantic version match
      const semanticMatch = this.matchBySemanticVersion(tenantVersion, globalVersion)
      if (semanticMatch) {
        matches.push(semanticMatch)
        continue // Semantic match is very strong, skip fuzzy matching
      }

      // Try fuzzy version + date match
      const fuzzyMatch = this.matchByFuzzyVersionAndDate(tenantVersion, globalVersion)
      if (fuzzyMatch) {
        matches.push(fuzzyMatch)
      }
    }

    // Sort by confidence descending
    matches.sort((a, b) => b.confidence - a.confidence)

    this.logger.debug(`Found ${matches.length} potential matches for version ${tenantVersion.version}`, {
      tenantVersionId: tenantVersion.id,
      matches: matches.map(m => ({ globalVersionId: m.globalVersionId, confidence: m.confidence, matchType: m.matchType }))
    })

    return matches
  }

  /**
   * Match by exact version string (95% confidence)
   */
  private matchByExactVersion(tenantVersion: TenantProductVersion, globalVersion: GlobalProductVersion): ProductVersionMatch | null {
    // Normalize version strings for comparison
    const tenantVersionNorm = this.normalizeVersion(tenantVersion.version)
    const globalVersionNorm = this.normalizeVersion(globalVersion.version)

    if (tenantVersionNorm === globalVersionNorm) {
      return {
        tenantVersionId: tenantVersion.id,
        globalVersionId: globalVersion.id,
        confidence: 95,
        matchType: 'exact_version',
        matchDetails: {
          tenantVersion: tenantVersion.version,
          globalVersion: globalVersion.version,
          normalizedMatch: tenantVersionNorm
        }
      }
    }

    return null
  }

  /**
   * Match by semantic version (90% confidence)
   */
  private matchBySemanticVersion(tenantVersion: TenantProductVersion, globalVersion: GlobalProductVersion): ProductVersionMatch | null {
    const tenantSemVer = this.parseSemanticVersion(tenantVersion.version)
    const globalSemVer = this.parseSemanticVersion(globalVersion.version)

    if (!tenantSemVer || !globalSemVer) {
      return null
    }

    // Check if semantic versions match
    if (tenantSemVer.major === globalSemVer.major &&
        tenantSemVer.minor === globalSemVer.minor &&
        tenantSemVer.patch === globalSemVer.patch) {
      
      return {
        tenantVersionId: tenantVersion.id,
        globalVersionId: globalVersion.id,
        confidence: 90,
        matchType: 'semantic_version',
        matchDetails: {
          tenantVersion: tenantVersion.version,
          globalVersion: globalVersion.version,
          tenantSemVer,
          globalSemVer
        }
      }
    }

    return null
  }

  /**
   * Match by fuzzy version + release date (70-85% confidence)
   */
  private matchByFuzzyVersionAndDate(tenantVersion: TenantProductVersion, globalVersion: GlobalProductVersion): ProductVersionMatch | null {
    // Calculate version similarity
    const versionSimilarity = this.calculateStringSimilarity(
      this.normalizeVersion(tenantVersion.version),
      this.normalizeVersion(globalVersion.version)
    )

    // Require at least 60% version similarity
    if (versionSimilarity < 0.6) {
      return null
    }

    // Calculate date similarity if both have release dates
    let dateSimilarity = 0
    let hasDateMatch = false

    if (tenantVersion.releaseDate && globalVersion.releaseDate) {
      dateSimilarity = this.calculateDateSimilarity(tenantVersion.releaseDate, globalVersion.releaseDate)
      hasDateMatch = true
    }

    // Calculate overall confidence
    let confidence = 0

    if (hasDateMatch) {
      // Version (70%) + Date (30%) weighting
      confidence = Math.round((versionSimilarity * 0.7 + dateSimilarity * 0.3) * 100)
    } else {
      // Version only, but reduce confidence due to lack of date verification
      confidence = Math.round(versionSimilarity * 75) // Max 75% without date
    }

    // Only return matches with at least 70% confidence
    if (confidence >= 70) {
      return {
        tenantVersionId: tenantVersion.id,
        globalVersionId: globalVersion.id,
        confidence: Math.min(confidence, 85), // Cap at 85% for fuzzy matches
        matchType: 'fuzzy_version_date',
        matchDetails: {
          versionSimilarity,
          dateSimilarity,
          hasDateMatch,
          tenantVersion: tenantVersion.version,
          globalVersion: globalVersion.version,
          tenantReleaseDate: tenantVersion.releaseDate,
          globalReleaseDate: globalVersion.releaseDate
        }
      }
    }

    return null
  }

  /**
   * Normalize version string for comparison
   */
  private normalizeVersion(version: string): string {
    return version.toLowerCase()
      .replace(/[^\w\d\.]/g, '')
      .replace(/^v/, '') // Remove leading 'v'
      .trim()
  }

  /**
   * Parse semantic version (major.minor.patch)
   */
  private parseSemanticVersion(version: string): { major: number; minor: number; patch: number } | null {
    const normalized = this.normalizeVersion(version)
    const match = normalized.match(/^(\d+)\.(\d+)\.(\d+)/)
    
    if (match) {
      return {
        major: parseInt(match[1], 10),
        minor: parseInt(match[2], 10),
        patch: parseInt(match[3], 10)
      }
    }
    
    return null
  }

  /**
   * Calculate string similarity using Levenshtein distance
   */
  private calculateStringSimilarity(str1: string, str2: string): number {
    if (str1 === str2) return 1.0
    if (str1.length === 0 || str2.length === 0) return 0.0

    const distance = this.levenshteinDistance(str1, str2)
    const maxLength = Math.max(str1.length, str2.length)
    
    return 1 - (distance / maxLength)
  }

  /**
   * Calculate date similarity (closer dates = higher similarity)
   */
  private calculateDateSimilarity(date1: Date, date2: Date): number {
    const diffMs = Math.abs(date1.getTime() - date2.getTime())
    const diffDays = diffMs / (1000 * 60 * 60 * 24)
    
    // Perfect match for same day
    if (diffDays === 0) return 1.0
    
    // High similarity for dates within 7 days
    if (diffDays <= 7) return 0.9
    
    // Good similarity for dates within 30 days
    if (diffDays <= 30) return 0.8
    
    // Moderate similarity for dates within 90 days
    if (diffDays <= 90) return 0.6
    
    // Low similarity for dates within 365 days
    if (diffDays <= 365) return 0.3
    
    // Very low similarity for dates more than a year apart
    return 0.1
  }

  /**
   * Calculate Levenshtein distance between two strings
   */
  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null))

    for (let i = 0; i <= str1.length; i++) {
      matrix[0][i] = i
    }

    for (let j = 0; j <= str2.length; j++) {
      matrix[j][0] = j
    }

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,     // deletion
          matrix[j - 1][i] + 1,     // insertion
          matrix[j - 1][i - 1] + indicator // substitution
        )
      }
    }

    return matrix[str2.length][str1.length]
  }
}

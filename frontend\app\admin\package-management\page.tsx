'use client'

import { useState } from 'react'
import { useData } from '@/lib/hooks'
import PageAccessGuard from '@/components/auth/PageAccessGuard'

interface AddonPackage {
  id: number
  name: string
  display_name: string
  description: string | null
  is_default: boolean
  status: string
  created_on: string
  changed_on: string
}

interface ClientAddonPackage {
  id: number
  client_id: string
  name: string
  domain: string
  package_id: number
  package_name: string
  package_display_name: string
  assigned_on: string
  assigned_by: string | null
  status: string
}

interface Client {
  client_id: string
  name: string
  domain: string
  status: string
}

export default function PackageManagementPage() {
  const [activeTab, setActiveTab] = useState<'packages' | 'assignments'>('packages')
  const [showAddPackageModal, setShowAddPackageModal] = useState(false)
  const [showAssignModal, setShowAssignModal] = useState(false)

  // Fetch addon packages
  const {
    data: packages = [],
    loading: packagesLoading,
    error: packagesError,
    refetch: refetchPackages
  } = useData<AddonPackage[]>({
    endpoint: '/api/addon-packages',
    cache: {
      key: 'addon-packages'
    }
  })

  // Fetch client package assignments
  const {
    data: assignments = [],
    loading: assignmentsLoading,
    error: assignmentsError,
    refetch: refetchAssignments
  } = useData<ClientAddonPackage[]>({
    endpoint: '/api/client-addon-packages',
    cache: {
      key: 'client-addon-packages'
    }
  })

  // Fetch clients for assignment dropdown
  const {
    data: clients = [],
    loading: clientsLoading
  } = useData<Client[]>({
    endpoint: '/api/clients',
    cache: {
      key: 'clients-for-packages'
    }
  })

  const handleCreatePackage = async (packageData: Partial<AddonPackage>) => {
    try {
      const response = await fetch('/api/addon-packages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(packageData)
      })

      if (response.ok) {
        refetchPackages()
        setShowAddPackageModal(false)
      } else {
        console.error('Failed to create package')
      }
    } catch (error) {
      console.error('Error creating package:', error)
    }
  }

  const handleAssignPackage = async (clientId: string, packageId: number) => {
    try {
      const response = await fetch('/api/client-addon-packages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          client_id: clientId,
          package_id: packageId
        })
      })

      if (response.ok) {
        refetchAssignments()
        setShowAssignModal(false)
      } else {
        console.error('Failed to assign package')
      }
    } catch (error) {
      console.error('Error assigning package:', error)
    }
  }

  const handleRemoveAssignment = async (clientId: string, packageId: number) => {
    if (!confirm('Are you sure you want to remove this package assignment?')) {
      return
    }

    try {
      const response = await fetch('/api/client-addon-packages', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          client_id: clientId,
          package_id: packageId
        })
      })

      if (response.ok) {
        refetchAssignments()
      } else {
        console.error('Failed to remove assignment')
      }
    } catch (error) {
      console.error('Error removing assignment:', error)
    }
  }

  return (
    <PageAccessGuard pageName="package-management" redirectTo="/overview">
      <div className="overview-container">
        {/* Header */}
        <div className="page-header">
          <div className="page-title-section">
            <h1 className="page-title">Package Management</h1>
            <p className="page-subtitle">Manage addon packages and client assignments (Super Admin Only)</p>
          </div>

          <div className="page-actions">
            {activeTab === 'packages' && (
              <button 
                className="btn-primary"
                onClick={() => setShowAddPackageModal(true)}
              >
                ➕ Add Package
              </button>
            )}
            {activeTab === 'assignments' && (
              <button 
                className="btn-primary"
                onClick={() => setShowAssignModal(true)}
              >
                🔗 Assign Package
              </button>
            )}
          </div>
        </div>

        {/* Tabs */}
        <div className="mb-6">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8">
              <button
                onClick={() => setActiveTab('packages')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'packages'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                📦 Addon Packages
              </button>
              <button
                onClick={() => setActiveTab('assignments')}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'assignments'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                🔗 Client Assignments
              </button>
            </nav>
          </div>
        </div>

        {/* Packages Tab */}
        {activeTab === 'packages' && (
          <div className="section-container">
            <div className="section-header">
              <h2 className="section-title">Addon Packages</h2>
              <p className="section-subtitle">Manage available addon packages</p>
            </div>

            {packagesLoading ? (
              <div className="text-center py-8">Loading packages...</div>
            ) : packagesError ? (
              <div className="text-center py-8 text-red-600">Error loading packages</div>
            ) : (
              <div className="card">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Package
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Name
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Default
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Status
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Created
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {packages?.map((pkg) => (
                        <tr key={pkg.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div>
                              <div className="text-sm font-medium text-gray-900">
                                {pkg.display_name}
                              </div>
                              <div className="text-sm text-gray-500">
                                {pkg.description}
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <code className="bg-gray-100 px-2 py-1 rounded text-xs">
                              {pkg.name}
                            </code>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            {pkg.is_default ? (
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Default
                              </span>
                            ) : (
                              <span className="text-sm text-gray-500">No</span>
                            )}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              pkg.status === 'A' 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {pkg.status === 'A' ? 'Active' : 'Inactive'}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {new Date(pkg.created_on).toLocaleDateString()}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
        )}

        {/* Assignments Tab */}
        {activeTab === 'assignments' && (
          <div className="section-container">
            <div className="section-header">
              <h2 className="section-title">Client Package Assignments</h2>
              <p className="section-subtitle">Manage which packages are assigned to which clients</p>
            </div>

            {assignmentsLoading ? (
              <div className="text-center py-8">Loading assignments...</div>
            ) : assignmentsError ? (
              <div className="text-center py-8 text-red-600">Error loading assignments</div>
            ) : (
              <div className="card">
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Client
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Package
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Assigned By
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Assigned On
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Actions
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {assignments?.map((assignment) => (
                        <tr key={assignment.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div>
                              <div className="text-sm font-medium text-gray-900">
                                {assignment.name}
                              </div>
                              <div className="text-sm text-gray-500">
                                {assignment.domain}
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div>
                              <div className="text-sm font-medium text-gray-900">
                                {assignment.package_display_name}
                              </div>
                              <div className="text-sm text-gray-500">
                                <code className="bg-gray-100 px-1 py-0.5 rounded text-xs">
                                  {assignment.package_name}
                                </code>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {assignment.assigned_by || 'System'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {new Date(assignment.assigned_on).toLocaleDateString()}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <button
                              onClick={() => handleRemoveAssignment(assignment.client_id, assignment.package_id)}
                              className="text-red-600 hover:text-red-900"
                            >
                              Remove
                            </button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </PageAccessGuard>
  )
}

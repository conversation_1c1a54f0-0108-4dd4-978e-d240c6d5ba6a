{"compilerOptions": {"target": "es2015", "downlevelIteration": true, "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./"], "@/components/*": ["./components/*"], "@/contexts/*": ["./contexts/*"], "@/lib/*": ["./lib/*"], "@/src/*": ["./src/*"], "@/styles/*": ["./styles/*"], "@/utils/*": ["./utils/*"], "@/hooks/*": ["./hooks/*"], "@/app/*": ["./app/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules", "__tests__/**/*"]}
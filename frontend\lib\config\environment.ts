/**
 * Environment Configuration
 * 
 * Centralized environment variable management with defaults and validation.
 * This file provides a single source of truth for all environment-dependent configuration.
 */

import { DATABASE, SESSION, ENVIRONMENT, TIMEOUTS } from '@/lib/constants/app-constants';

/**
 * Environment variable schema with defaults
 */
export const ENV_DEFAULTS = {
  // Node environment
  NODE_ENV: ENVIRONMENT.DEVELOPMENT,
  
  // Database configuration
  DB_HOST: DATABASE.DEFAULT_HOST,
  DB_PORT: DATABASE.DEFAULT_PORT.toString(),
  DB_NAME: DATABASE.DEFAULT_NAME,
  DB_USER: DATABASE.DEFAULT_USER,
  DB_PASSWORD: '',
  DATABASE_URL: '',
  DATABASE_SSL: 'false',
  USE_IAM_DB_AUTH: 'false',
  
  // Session configuration
  SESSION_COOKIE_MAX_AGE: SESSION.DEFAULT_MAX_AGE.toString(),
  TOKEN_REFRESH_THRESHOLD: SESSION.TOKEN_REFRESH_THRESHOLD.toString(),
  AUTO_REFRESH_TOKENS: 'true',
  
  // JWT configuration
  JWT_EXPIRATION: '3600', // 1 hour
  REFRESH_TOKEN_EXPIRATION: SESSION.DEFAULT_MAX_AGE.toString(),
  
  // AWS configuration (these should be provided via environment)
  NEXT_PUBLIC_AWS_REGION: '',
  NEXT_PUBLIC_AWS_USER_POOLS_ID: '',
  NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID: '',
  NEXT_PUBLIC_AWS_COGNITO_DOMAIN: '',
  NEXT_PUBLIC_REDIRECT_SIGN_IN: '',
  NEXT_PUBLIC_REDIRECT_SIGN_OUT: '',
  
  // Security configuration
  ENCRYPTION_ALGORITHM: 'aes-256-gcm',
  
  // Logging configuration
  NEXT_PUBLIC_LOG_LEVEL: ENVIRONMENT.DEVELOPMENT === process.env.NODE_ENV ? 'DEBUG' : 'ERROR',
  NEXT_PUBLIC_ENABLE_LOGGING: 'false',
  NEXT_PUBLIC_SHOW_LOGS_IN_PRODUCTION: 'false',
  NEXT_PUBLIC_LOG_CATEGORIES: '',
  
  // Performance configuration
  API_TIMEOUT: TIMEOUTS.API_REQUEST.toString(),
  DATABASE_TIMEOUT: TIMEOUTS.DATABASE_QUERY.toString(),
  
  // Feature flags
  ENABLE_DEBUG_MODE: 'false',
  ENABLE_PERFORMANCE_MONITORING: 'false',
  ENABLE_ERROR_TRACKING: 'true',
} as const;

/**
 * Get environment variable with fallback to default
 */
export function getEnvVar(key: keyof typeof ENV_DEFAULTS, fallback?: string): string {
  const value = process.env[key];
  if (value !== undefined && value !== '') {
    return value;
  }
  
  if (fallback !== undefined) {
    return fallback;
  }
  
  return ENV_DEFAULTS[key];
}

/**
 * Get environment variable as number with fallback
 */
export function getEnvNumber(key: keyof typeof ENV_DEFAULTS, fallback?: number): number {
  const value = getEnvVar(key);
  const parsed = parseInt(value, 10);
  
  if (isNaN(parsed)) {
    if (fallback !== undefined) {
      return fallback;
    }
    // Try to parse the default value
    const defaultValue = parseInt(ENV_DEFAULTS[key], 10);
    return isNaN(defaultValue) ? 0 : defaultValue;
  }
  
  return parsed;
}

/**
 * Get environment variable as boolean with fallback
 */
export function getEnvBoolean(key: keyof typeof ENV_DEFAULTS, fallback?: boolean): boolean {
  const value = getEnvVar(key).toLowerCase();
  
  if (value === 'true' || value === '1' || value === 'yes') {
    return true;
  }
  
  if (value === 'false' || value === '0' || value === 'no') {
    return false;
  }
  
  return fallback ?? false;
}

/**
 * Validate required environment variables
 */
export function validateRequiredEnvVars(requiredVars: (keyof typeof ENV_DEFAULTS)[]): string[] {
  const missing: string[] = [];
  
  for (const varName of requiredVars) {
    const value = process.env[varName];
    if (!value || value.trim() === '') {
      missing.push(varName);
    }
  }
  
  return missing;
}

/**
 * Get all environment variables with their current values (for debugging)
 * Only includes non-sensitive variables
 */
export function getEnvironmentInfo(): Record<string, string> {
  const nonSensitiveVars = [
    'NODE_ENV',
    'NEXT_PUBLIC_AWS_REGION',
    'DB_HOST',
    'DB_PORT',
    'DB_NAME',
    'DATABASE_SSL',
    'USE_IAM_DB_AUTH',
    'SESSION_COOKIE_MAX_AGE',
    'TOKEN_REFRESH_THRESHOLD',
    'AUTO_REFRESH_TOKENS',
    'JWT_EXPIRATION',
    'ENCRYPTION_ALGORITHM',
    'NEXT_PUBLIC_LOG_LEVEL',
    'NEXT_PUBLIC_ENABLE_LOGGING',
    'API_TIMEOUT',
    'DATABASE_TIMEOUT',
    'ENABLE_DEBUG_MODE',
    'ENABLE_PERFORMANCE_MONITORING',
    'ENABLE_ERROR_TRACKING',
  ] as const;
  
  const info: Record<string, string> = {};
  
  for (const varName of nonSensitiveVars) {
    info[varName] = getEnvVar(varName);
  }
  
  return info;
}

/**
 * Check if we're running in development mode
 */
export function isDevelopment(): boolean {
  return getEnvVar('NODE_ENV') === ENVIRONMENT.DEVELOPMENT;
}

/**
 * Check if we're running in production mode
 */
export function isProduction(): boolean {
  return getEnvVar('NODE_ENV') === ENVIRONMENT.PRODUCTION;
}

/**
 * Check if we're running in test mode
 */
export function isTest(): boolean {
  return getEnvVar('NODE_ENV') === ENVIRONMENT.TEST;
}

/**
 * Get database configuration from environment
 */
export function getDatabaseConfig() {
  return {
    host: getEnvVar('DB_HOST'),
    port: getEnvNumber('DB_PORT'),
    name: getEnvVar('DB_NAME'),
    user: getEnvVar('DB_USER'),
    password: getEnvVar('DB_PASSWORD'),
    url: getEnvVar('DATABASE_URL'),
    ssl: getEnvBoolean('DATABASE_SSL'),
    useIAM: getEnvBoolean('USE_IAM_DB_AUTH'),
  };
}

/**
 * Get session configuration from environment
 */
export function getSessionConfig() {
  return {
    maxAge: getEnvNumber('SESSION_COOKIE_MAX_AGE'),
    refreshThreshold: getEnvNumber('TOKEN_REFRESH_THRESHOLD'),
    autoRefresh: getEnvBoolean('AUTO_REFRESH_TOKENS'),
    jwtExpiration: getEnvNumber('JWT_EXPIRATION'),
    refreshTokenExpiration: getEnvNumber('REFRESH_TOKEN_EXPIRATION'),
  };
}

/**
 * Get AWS configuration from environment
 */
export function getAWSConfig() {
  return {
    region: getEnvVar('NEXT_PUBLIC_AWS_REGION'),
    userPoolId: getEnvVar('NEXT_PUBLIC_AWS_USER_POOLS_ID'),
    userPoolClientId: getEnvVar('NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID'),
    cognitoDomain: getEnvVar('NEXT_PUBLIC_AWS_COGNITO_DOMAIN'),
    redirectSignIn: getEnvVar('NEXT_PUBLIC_REDIRECT_SIGN_IN'),
    redirectSignOut: getEnvVar('NEXT_PUBLIC_REDIRECT_SIGN_OUT'),
  };
}

/**
 * Get logging configuration from environment
 */
export function getLoggingConfig() {
  return {
    level: getEnvVar('NEXT_PUBLIC_LOG_LEVEL'),
    enabled: getEnvBoolean('NEXT_PUBLIC_ENABLE_LOGGING'),
    showInProduction: getEnvBoolean('NEXT_PUBLIC_SHOW_LOGS_IN_PRODUCTION'),
    categories: getEnvVar('NEXT_PUBLIC_LOG_CATEGORIES').split(',').filter(Boolean),
  };
}

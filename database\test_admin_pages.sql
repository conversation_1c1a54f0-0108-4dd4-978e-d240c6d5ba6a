-- Test script for admin pages system
-- Run this after executing the admin_pages_system.sql migration

-- Test 1: Verify admin_pages table was created and populated
SELECT 'Test 1: Admin Pages Table' as test_name;
SELECT 
    id, 
    name, 
    header, 
    sidebar, 
    status, 
    display_order,
    route_path
FROM metadata.admin_pages 
ORDER BY display_order;

-- Test 2: Verify admin_pages_groups table was created and populated
SELECT 'Test 2: Admin Pages Groups Table' as test_name;
SELECT 
    p.name as page_name,
    p.header,
    pg.group_name
FROM metadata.admin_pages p
JOIN metadata.admin_pages_groups pg ON p.id = pg.page_id
ORDER BY p.display_order, pg.group_name;

-- Test 3: Verify sidebar pages query (what the API will return)
SELECT 'Test 3: Sidebar Pages for Admin User' as test_name;
SELECT DISTINCT 
    p.id,
    p.name,
    p.header,
    p.description,
    p.display_order,
    p.route_path
FROM metadata.admin_pages p
INNER JOIN metadata.admin_pages_groups pg ON p.id = pg.page_id
WHERE p.status = 'A'
    AND p.sidebar = true
    AND pg.group_name = 'admin'
ORDER BY p.display_order ASC, p.name ASC;

-- Test 4: Verify sidebar pages query for regular user
SELECT 'Test 4: Sidebar Pages for Regular User' as test_name;
SELECT DISTINCT 
    p.id,
    p.name,
    p.header,
    p.description,
    p.display_order,
    p.route_path
FROM metadata.admin_pages p
INNER JOIN metadata.admin_pages_groups pg ON p.id = pg.page_id
WHERE p.status = 'A'
    AND p.sidebar = true
    AND pg.group_name = 'user'
ORDER BY p.display_order ASC, p.name ASC;

-- Test 5: Verify indexes were created
SELECT 'Test 5: Verify Indexes' as test_name;
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE schemaname = 'metadata' 
    AND (tablename = 'admin_pages' OR tablename = 'admin_pages_groups')
ORDER BY tablename, indexname;

-- Test 6: Check table comments
SELECT 'Test 6: Table Comments' as test_name;
SELECT 
    schemaname,
    tablename,
    obj_description(oid) as table_comment
FROM pg_class c
JOIN pg_namespace n ON n.oid = c.relnamespace
WHERE n.nspname = 'metadata' 
    AND c.relname IN ('admin_pages', 'admin_pages_groups');

-- Test 7: Check column comments
SELECT 'Test 7: Column Comments' as test_name;
SELECT 
    c.table_schema,
    c.table_name,
    c.column_name,
    pgd.description
FROM information_schema.columns c
LEFT JOIN pg_catalog.pg_statio_all_tables st ON c.table_schema = st.schemaname AND c.table_name = st.relname
LEFT JOIN pg_catalog.pg_description pgd ON pgd.objoid = st.relid AND pgd.objsubid = c.ordinal_position
WHERE c.table_schema = 'metadata' 
    AND c.table_name IN ('admin_pages', 'admin_pages_groups')
    AND pgd.description IS NOT NULL
ORDER BY c.table_name, c.ordinal_position;

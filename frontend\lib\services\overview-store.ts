/**
 * Overview Data Store
 *
 * Global store for overview data to persist across navigation
 * This prevents data from being reset when navigating between pages
 */

'use client'

import { useState, useEffect } from 'react'
import { OverviewStats, Renewal } from '@/lib/types'

interface OverviewData {
  stats: OverviewStats
  recentRenewals: Renewal[]
  upcomingRenewals: Renewal[]
}

interface OverviewStore {
  data: Record<string, OverviewData> // keyed by tenantId
  lastFetch: Record<string, number> // keyed by tenantId
  isLoading: Record<string, boolean> // keyed by tenantId
  error: Record<string, string | null> // keyed by tenantId
}

// Default data
const defaultStats: OverviewStats = {
  totalRenewals: 0,
  renewalsDue: 0,
  vendors: 0,
  annualSpend: '$0'
}

const defaultRenewals: Renewal[] = []

const defaultData: OverviewData = {
  stats: defaultStats,
  recentRenewals: defaultRenewals,
  upcomingRenewals: defaultRenewals
}

// Global store
let store: OverviewStore = {
  data: {},
  lastFetch: {},
  isLoading: {},
  error: {}
}

// Store listeners for React updates
const listeners = new Set<() => void>()

// Notify all listeners of store changes
function notifyListeners() {
  listeners.forEach(listener => listener())
}

// Store API
export const overviewStore = {
  // Get current store state
  getState: () => store,

  // Get data for specific tenant
  getData: (tenantId: string): OverviewData => {
    return store.data[tenantId] || defaultData
  },

  // Set data for specific tenant
  setData: (tenantId: string, data: Partial<OverviewData>) => {
    store.data[tenantId] = {
      ...store.data[tenantId] || defaultData,
      ...data
    }
    store.lastFetch[tenantId] = Date.now()
    notifyListeners()
  },

  // Get loading state for specific tenant
  getLoading: (tenantId: string): boolean => {
    return store.isLoading[tenantId] || false
  },

  // Set loading state for specific tenant
  setLoading: (tenantId: string, isLoading: boolean) => {
    store.isLoading[tenantId] = isLoading
    console.log('⏳ [OVERVIEW-STORE] Loading state updated for tenant:', tenantId, isLoading)
    notifyListeners()
  },

  // Get error state for specific tenant
  getError: (tenantId: string): string | null => {
    return store.error[tenantId] || null
  },

  // Set error state for specific tenant
  setError: (tenantId: string, error: string | null) => {
    store.error[tenantId] = error
    console.log('❌ [OVERVIEW-STORE] Error state updated for tenant:', tenantId, error)
    notifyListeners()
  },

  // Check if data is stale (older than 5 minutes)
  isStale: (tenantId: string): boolean => {
    const lastFetch = store.lastFetch[tenantId]
    if (!lastFetch) return true
    
    const fiveMinutes = 5 * 60 * 1000
    const isStale = Date.now() - lastFetch > fiveMinutes
    console.log('🕐 [OVERVIEW-STORE] Data staleness check for tenant:', tenantId, 'isStale:', isStale)
    return isStale
  },

  // Check if we have valid data (not just defaults)
  hasValidData: (tenantId: string): boolean => {
    const data = store.data[tenantId]
    if (!data) return false

    const hasStats = data.stats.totalRenewals > 0 || data.stats.renewalsDue > 0 || data.stats.vendors > 0
    const hasRenewals = data.recentRenewals.length > 0 || data.upcomingRenewals.length > 0
    const hasValidData = hasStats || hasRenewals

    console.log('✅ [OVERVIEW-STORE] Valid data check for tenant:', tenantId, 'hasValidData:', hasValidData)
    return hasValidData
  },

  // Clear data for specific tenant
  clearData: (tenantId: string) => {
    delete store.data[tenantId]
    delete store.lastFetch[tenantId]
    delete store.isLoading[tenantId]
    delete store.error[tenantId]
    console.log('🗑️ [OVERVIEW-STORE] Data cleared for tenant:', tenantId)
    notifyListeners()
  },

  // Clear all data
  clearAll: () => {
    store = {
      data: {},
      lastFetch: {},
      isLoading: {},
      error: {}
    }
    console.log('🗑️ [OVERVIEW-STORE] All data cleared')
    notifyListeners()
  },

  // Subscribe to store changes
  subscribe: (listener: () => void) => {
    listeners.add(listener)
    return () => listeners.delete(listener)
  }
}

// React hook to use the overview store
export function useOverviewStore(tenantId: string | null) {
  const [, forceUpdate] = useState({})
  
  useEffect(() => {
    if (!tenantId) return

    const unsubscribe = overviewStore.subscribe(() => {
      forceUpdate({})
    })

    return () => {
      unsubscribe()
    }
  }, [tenantId])
  
  if (!tenantId) {
    return {
      data: defaultData,
      isLoading: false,
      error: null,
      hasValidData: false,
      isStale: true
    }
  }
  
  return {
    data: overviewStore.getData(tenantId),
    isLoading: overviewStore.getLoading(tenantId),
    error: overviewStore.getError(tenantId),
    hasValidData: overviewStore.hasValidData(tenantId),
    isStale: overviewStore.isStale(tenantId)
  }
}

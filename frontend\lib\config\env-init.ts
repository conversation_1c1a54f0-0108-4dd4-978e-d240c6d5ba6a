/**
 * Environment Initialization
 * 
 * This module initializes the environment configuration at application startup
 * and ensures all required variables are available with proper defaults.
 */

import { initializeEnvironment, validateEnvironment, getClientSafeConfig } from './env-validator'

/**
 * Initialize environment variables at application startup
 */
function initializeAppEnvironment(): void {
  try {
    // Initialize environment with defaults
    initializeEnvironment()
    
    // Validate the environment
    const validation = validateEnvironment()
    
    // In development, log the validation results
    if (process.env.NODE_ENV === 'development') {
      console.log('🔧 Environment initialization complete')
      
      if (validation.warnings.length > 0) {
        console.warn('⚠️  Environment warnings:')
        validation.warnings.forEach(warning => console.warn(`   - ${warning}`))
      }
      
      if (validation.errors.length > 0) {
        console.error('❌ Environment errors:')
        validation.errors.forEach(error => console.error(`   - ${error}`))
      }
      
      if (validation.isValid) {
        console.log('✅ Environment validation passed')
      } else {
        console.error('❌ Environment validation failed')
      }
    }
    
    // In production, only log critical errors
    if (process.env.NODE_ENV === 'production' && !validation.isValid) {
      console.error('Environment validation failed:', validation.errors)
    }
    
  } catch (error) {
    console.error('Failed to initialize environment:', error)
    
    // In production, this should cause the application to fail to start
    if (process.env.NODE_ENV === 'production') {
      throw error
    }
  }
}

/**
 * Get runtime configuration that's safe to use throughout the application
 */
export function getRuntimeConfig() {
  // Ensure environment is initialized
  if (!process.env._ENV_INITIALIZED) {
    initializeAppEnvironment()
    process.env._ENV_INITIALIZED = 'true'
  }
  
  return getClientSafeConfig()
}

/**
 * Initialize environment on module load for server-side code
 */
if (typeof window === 'undefined') {
  // Server-side initialization
  initializeAppEnvironment()
  process.env._ENV_INITIALIZED = 'true'
}

/**
 * Initialize environment on client-side when this module is imported
 */
if (typeof window !== 'undefined') {
  // Client-side initialization - only initialize once
  if (!window.__ENV_INITIALIZED) {
    initializeAppEnvironment()
    window.__ENV_INITIALIZED = true
  }
}

// Export the initialization function for manual use if needed
export { initializeAppEnvironment }

// Extend window interface for TypeScript
declare global {
  interface Window {
    __ENV_INITIALIZED?: boolean
  }
  
  namespace NodeJS {
    interface ProcessEnv {
      _ENV_INITIALIZED?: string
    }
  }
}

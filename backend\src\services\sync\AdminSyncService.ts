/**
 * Administrative Synchronization Service
 * 
 * Provides administrative APIs for managing the synchronization system:
 * 1. Conflict resolution and management
 * 2. System-wide sync operations
 * 3. Global data management
 * 4. Monitoring and reporting
 */

import { Pool, PoolClient } from 'pg'
import { Logger } from '../Logger'
import { SyncEngine } from './SyncEngine'

export interface SyncConflict {
  id: string
  batchId: string
  entityType: 'vendor' | 'product' | 'product_version'
  entityId: string
  conflictData: any
  status: 'pending' | 'resolved' | 'rejected'
  createdAt: Date
  resolvedAt?: Date
  resolvedBy?: string
  resolution?: any
}

export interface ConflictResolution {
  action: 'accept_match' | 'create_new' | 'reject'
  selectedMatchId?: string
  newGlobalData?: any
  reason?: string
}

export interface SystemStats {
  totalTenants: number
  totalGlobalVendors: number
  totalGlobalProducts: number
  totalGlobalVersions: number
  pendingConflicts: number
  activeSyncBatches: number
  syncSuccessRate: number
}

export class AdminSyncService {
  private db: Pool
  private logger: Logger
  private syncEngine: SyncEngine

  constructor(db: Pool, logger: Logger) {
    this.db = db
    this.logger = logger
    this.syncEngine = new SyncEngine(db, logger)
  }

  /**
   * Get all pending conflicts across all tenants
   */
  async getPendingConflicts(options: {
    limit?: number
    offset?: number
    entityType?: string
    tenantId?: string
  } = {}): Promise<{
    conflicts: SyncConflict[]
    total: number
  }> {
    const client = await this.db.connect()
    
    try {
      const { limit = 50, offset = 0, entityType, tenantId } = options
      
      let whereClause = "WHERE sc.status = 'pending'"
      const params: any[] = []
      let paramIndex = 1
      
      if (entityType) {
        whereClause += ` AND sc.entity_type = $${paramIndex}`
        params.push(entityType)
        paramIndex++
      }
      
      if (tenantId) {
        whereClause += ` AND sb.tenant_id = $${paramIndex}`
        params.push(tenantId)
        paramIndex++
      }
      
      const result = await client.query(`
        SELECT 
          sc.*,
          sb.tenant_id,
          sb.entity_type as batch_entity_type
        FROM metadata.sync_conflicts sc
        JOIN metadata.sync_batches sb ON sc.batch_id = sb.id
        ${whereClause}
        ORDER BY sc.created_on DESC
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `, [...params, limit, offset])
      
      const countResult = await client.query(`
        SELECT COUNT(*) as total
        FROM metadata.sync_conflicts sc
        JOIN metadata.sync_batches sb ON sc.batch_id = sb.id
        ${whereClause}
      `, params)
      
      return {
        conflicts: result.rows,
        total: parseInt(countResult.rows[0].total, 10)
      }
      
    } finally {
      client.release()
    }
  }

  /**
   * Get specific conflict details
   */
  async getConflict(conflictId: string): Promise<SyncConflict | null> {
    const client = await this.db.connect()
    
    try {
      const result = await client.query(`
        SELECT 
          sc.*,
          sb.tenant_id,
          sb.entity_type as batch_entity_type
        FROM metadata.sync_conflicts sc
        JOIN metadata.sync_batches sb ON sc.batch_id = sb.id
        WHERE sc.id = $1
      `, [conflictId])
      
      return result.rows[0] || null
      
    } finally {
      client.release()
    }
  }

  /**
   * Resolve a conflict
   */
  async resolveConflict(
    conflictId: string,
    resolution: ConflictResolution,
    resolvedBy: string
  ): Promise<{
    success: boolean
    message: string
  }> {
    const client = await this.db.connect()
    
    try {
      await client.query('BEGIN')
      
      // Get conflict details
      const conflict = await this.getConflict(conflictId)
      if (!conflict) {
        throw new Error('Conflict not found')
      }
      
      if (conflict.status !== 'pending') {
        throw new Error('Conflict already resolved')
      }
      
      let result: { success: boolean; message: string }
      
      switch (resolution.action) {
        case 'accept_match':
          result = await this.acceptMatch(client, conflict, resolution.selectedMatchId!, resolvedBy)
          break
          
        case 'create_new':
          result = await this.createNewGlobal(client, conflict, resolution.newGlobalData!, resolvedBy)
          break
          
        case 'reject':
          result = await this.rejectConflict(client, conflict, resolution.reason!, resolvedBy)
          break
          
        default:
          throw new Error('Invalid resolution action')
      }
      
      // Update conflict status
      await client.query(`
        UPDATE metadata.sync_conflicts 
        SET status = 'resolved',
            resolved_at = NOW(),
            resolved_by = $2,
            resolution = $3
        WHERE id = $1
      `, [conflictId, resolvedBy, JSON.stringify(resolution)])
      
      await client.query('COMMIT')
      
      this.logger.info(`Conflict ${conflictId} resolved`, {
        conflictId,
        action: resolution.action,
        resolvedBy
      })
      
      return result
      
    } catch (error) {
      await client.query('ROLLBACK')
      this.logger.error(`Failed to resolve conflict ${conflictId}`, { error, resolution, resolvedBy })
      throw error
    } finally {
      client.release()
    }
  }

  /**
   * Get system-wide synchronization statistics
   */
  async getSystemStats(): Promise<SystemStats> {
    const client = await this.db.connect()
    
    try {
      // Get basic counts
      const statsResult = await client.query(`
        SELECT 
          (SELECT COUNT(DISTINCT tenant_id) FROM metadata.sync_batches) as total_tenants,
          (SELECT COUNT(*) FROM metadata.global_vendors) as total_global_vendors,
          (SELECT COUNT(*) FROM metadata.global_products) as total_global_products,
          (SELECT COUNT(*) FROM metadata.global_product_versions) as total_global_versions,
          (SELECT COUNT(*) FROM metadata.sync_conflicts WHERE status = 'pending') as pending_conflicts,
          (SELECT COUNT(*) FROM metadata.sync_batches WHERE status IN ('pending', 'processing')) as active_sync_batches
      `)
      
      // Calculate success rate
      const successRateResult = await client.query(`
        SELECT 
          COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed,
          COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed
        FROM metadata.sync_batches
        WHERE created_on >= NOW() - INTERVAL '30 days'
      `)
      
      const stats = statsResult.rows[0]
      const successData = successRateResult.rows[0]
      
      const totalBatches = parseInt(successData.completed, 10) + parseInt(successData.failed, 10)
      const successRate = totalBatches > 0 
        ? (parseInt(successData.completed, 10) / totalBatches) * 100 
        : 100
      
      return {
        totalTenants: parseInt(stats.total_tenants, 10),
        totalGlobalVendors: parseInt(stats.total_global_vendors, 10),
        totalGlobalProducts: parseInt(stats.total_global_products, 10),
        totalGlobalVersions: parseInt(stats.total_global_versions, 10),
        pendingConflicts: parseInt(stats.pending_conflicts, 10),
        activeSyncBatches: parseInt(stats.active_sync_batches, 10),
        syncSuccessRate: Math.round(successRate * 100) / 100
      }
      
    } finally {
      client.release()
    }
  }

  /**
   * Trigger system-wide synchronization
   */
  async triggerSystemSync(options: {
    tenantIds?: string[]
    entityTypes?: ('vendor' | 'product' | 'product_version')[]
    dryRun?: boolean
  } = {}): Promise<{
    success: boolean
    message: string
    batchIds: string[]
  }> {
    const { tenantIds, entityTypes = ['vendor', 'product', 'product_version'], dryRun = false } = options
    
    try {
      const batchIds: string[] = []
      
      // Get tenant IDs to sync
      const tenantsToSync = tenantIds || await this.getAllTenantIds()
      
      this.logger.info(`Starting system-wide sync`, {
        tenantCount: tenantsToSync.length,
        entityTypes,
        dryRun
      })
      
      // Process each tenant
      for (const tenantId of tenantsToSync) {
        try {
          if (entityTypes.includes('vendor')) {
            const result = await this.syncEngine.syncVendors(tenantId, { dryRun })
            batchIds.push(result.batchId)
          }
          
          if (entityTypes.includes('product')) {
            const result = await this.syncEngine.syncProducts(tenantId, { dryRun })
            batchIds.push(result.batchId)
          }
          
          if (entityTypes.includes('product_version')) {
            const result = await this.syncEngine.syncProductVersions(tenantId, { dryRun })
            batchIds.push(result.batchId)
          }
          
        } catch (error) {
          this.logger.error(`Failed to sync tenant ${tenantId}`, { error })
        }
      }
      
      return {
        success: true,
        message: `System sync initiated for ${tenantsToSync.length} tenants`,
        batchIds
      }
      
    } catch (error) {
      this.logger.error('System sync failed', { error })
      return {
        success: false,
        message: error instanceof Error ? error.message : 'System sync failed',
        batchIds: []
      }
    }
  }

  /**
   * Accept a match from conflict resolution
   */
  private async acceptMatch(
    client: PoolClient,
    conflict: SyncConflict,
    selectedMatchId: string,
    resolvedBy: string
  ): Promise<{ success: boolean; message: string }> {
    const conflictData = conflict.conflictData
    const tenantEntity = conflictData.tenantVendor || conflictData.tenantProduct || conflictData.tenantVersion
    const potentialMatches = conflictData.potentialMatches || []

    const selectedMatch = potentialMatches.find((m: any) =>
      m.globalVendorId === selectedMatchId ||
      m.globalProductId === selectedMatchId ||
      m.globalVersionId === selectedMatchId
    )
    if (!selectedMatch) {
      throw new Error('Selected match not found')
    }

    // Create sync record based on entity type
    const tenantSchema = `tenant_${conflict.tenantId?.padStart(16, '0')}`

    switch (conflict.entityType) {
      case 'vendor':
        await client.query(`
          INSERT INTO ${tenantSchema}.tenant_vendor_sync (
            tenant_vendor_id, global_vendor_id, confidence, match_type,
            sync_status, created_on, changed_on
          ) VALUES ($1, $2, $3, $4, 'synced', NOW(), NOW())
        `, [tenantEntity.id, selectedMatch.globalVendorId, selectedMatch.confidence, selectedMatch.matchType])
        break

      case 'product':
        await client.query(`
          INSERT INTO ${tenantSchema}.tenant_product_sync (
            tenant_product_id, global_product_id, confidence, match_type,
            sync_status, created_on, changed_on
          ) VALUES ($1, $2, $3, $4, 'synced', NOW(), NOW())
        `, [tenantEntity.id, selectedMatch.globalProductId, selectedMatch.confidence, selectedMatch.matchType])
        break

      case 'product_version':
        await client.query(`
          INSERT INTO ${tenantSchema}.tenant_product_version_sync (
            tenant_version_id, global_version_id, confidence, match_type,
            sync_status, created_on, changed_on
          ) VALUES ($1, $2, $3, $4, 'synced', NOW(), NOW())
        `, [tenantEntity.id, selectedMatch.globalVersionId, selectedMatch.confidence, selectedMatch.matchType])
        break
    }

    return {
      success: true,
      message: `Match accepted and sync record created`
    }
  }

  /**
   * Create new global entity from conflict resolution
   */
  private async createNewGlobal(
    client: PoolClient,
    conflict: SyncConflict,
    newGlobalData: any,
    resolvedBy: string
  ): Promise<{ success: boolean; message: string }> {
    const conflictData = conflict.conflictData
    const tenantEntity = conflictData.tenantVendor || conflictData.tenantProduct || conflictData.tenantVersion

    let globalId: string
    const tenantSchema = `tenant_${conflict.tenantId?.padStart(16, '0')}`

    switch (conflict.entityType) {
      case 'vendor':
        const vendorResult = await client.query(`
          INSERT INTO metadata.global_vendors (
            name, tax_id, domain, address, city, state, country, website,
            confidence, source_count, last_updated
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, 100, 1, NOW())
          RETURNING id
        `, [
          newGlobalData.name || tenantEntity.name,
          newGlobalData.taxId || tenantEntity.taxId,
          newGlobalData.domain || tenantEntity.domain,
          newGlobalData.address || tenantEntity.address,
          newGlobalData.city || tenantEntity.city,
          newGlobalData.state || tenantEntity.state,
          newGlobalData.country || tenantEntity.country,
          newGlobalData.website || tenantEntity.website
        ])
        globalId = vendorResult.rows[0].id

        await client.query(`
          INSERT INTO ${tenantSchema}.tenant_vendor_sync (
            tenant_vendor_id, global_vendor_id, confidence, match_type,
            sync_status, created_on, changed_on
          ) VALUES ($1, $2, 100, 'manual_create', 'synced', NOW(), NOW())
        `, [tenantEntity.id, globalId])
        break

      default:
        throw new Error(`Create new global not implemented for ${conflict.entityType}`)
    }

    return {
      success: true,
      message: `New global ${conflict.entityType} created and linked`
    }
  }

  /**
   * Reject conflict
   */
  private async rejectConflict(
    client: PoolClient,
    conflict: SyncConflict,
    reason: string,
    resolvedBy: string
  ): Promise<{ success: boolean; message: string }> {
    // Mark the tenant entity as rejected for sync
    const conflictData = conflict.conflictData
    const tenantEntity = conflictData.tenantVendor || conflictData.tenantProduct || conflictData.tenantVersion
    const tenantSchema = `tenant_${conflict.tenantId?.padStart(16, '0')}`

    switch (conflict.entityType) {
      case 'vendor':
        await client.query(`
          INSERT INTO ${tenantSchema}.tenant_vendor_sync (
            tenant_vendor_id, global_vendor_id, confidence, match_type,
            sync_status, created_on, changed_on
          ) VALUES ($1, NULL, 0, 'rejected', 'rejected', NOW(), NOW())
        `, [tenantEntity.id])
        break

      default:
        throw new Error(`Reject not implemented for ${conflict.entityType}`)
    }

    return {
      success: true,
      message: `Conflict rejected: ${reason}`
    }
  }

  /**
   * Get all tenant IDs that have sync data
   */
  private async getAllTenantIds(): Promise<string[]> {
    const client = await this.db.connect()

    try {
      const result = await client.query(`
        SELECT DISTINCT tenant_id
        FROM metadata.sync_batches
        ORDER BY tenant_id
      `)

      return result.rows.map(row => row.tenant_id)

    } finally {
      client.release()
    }
  }
}

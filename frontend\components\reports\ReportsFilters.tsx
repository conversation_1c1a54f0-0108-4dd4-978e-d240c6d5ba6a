/**
 * Reports Filters Component
 * 
 * Filter controls for the reports page including vendor, date range, and type filters
 */

'use client'

import React from 'react'
import { Form } from '@/components/ui/Form'
import { Button } from '@/components/ui/Button'
import { BaseComponentProps } from '@/lib/types'
import { UseFiltersReturn, UseSearchReturn } from '@/lib/hooks'

interface FilterOptions {
  vendors: Array<{ label: string; value: string }>
  renewalTypes: Array<{ label: string; value: string }>
}

interface ReportsFiltersProps extends BaseComponentProps {
  filters: UseFiltersReturn
  filterOptions: FilterOptions
  search: UseSearchReturn
  onReset: () => void
}

export default function ReportsFilters({
  filters,
  filterOptions,
  search,
  onReset,
  className = '',
  'data-testid': testId
}: ReportsFiltersProps) {
  return (
    <div 
      className={`reports-filters ${className}`}
      data-testid={testId}
    >
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
        {/* Vendor Filter */}
        <Form.Field>
          <Form.Label htmlFor="vendor-filter">Vendor</Form.Label>
          <Form.Select
            id="vendor-filter"
            value={filters.getFilterValue('vendor') as string}
            onChange={(e) => filters.actions.setFilter('vendor', e.target.value)}
            data-testid="vendor-filter"
          >
            <option value="">All Vendors</option>
            {filterOptions.vendors.map((vendor) => (
              <option key={vendor.value} value={vendor.value}>
                {vendor.label}
              </option>
            ))}
          </Form.Select>
        </Form.Field>

        {/* Renewal From Date */}
        <Form.Field>
          <Form.Label htmlFor="renewal-from-filter">Renewal From</Form.Label>
          <Form.Input
            id="renewal-from-filter"
            type="date"
            value={filters.getFilterValue('renewalFrom') as string}
            onChange={(e) => filters.actions.setFilter('renewalFrom', e.target.value)}
            placeholder="Pick a date"
            data-testid="renewal-from-filter"
          />
        </Form.Field>

        {/* Renewal To Date */}
        <Form.Field>
          <Form.Label htmlFor="renewal-to-filter">Renewal To</Form.Label>
          <Form.Input
            id="renewal-to-filter"
            type="date"
            value={filters.getFilterValue('renewalTo') as string}
            onChange={(e) => filters.actions.setFilter('renewalTo', e.target.value)}
            placeholder="Pick a date"
            data-testid="renewal-to-filter"
          />
        </Form.Field>

        {/* Renewal Type Filter */}
        <Form.Field>
          <Form.Label htmlFor="renewal-type-filter">Renewal Type</Form.Label>
          <Form.Select
            id="renewal-type-filter"
            value={filters.getFilterValue('renewalType') as string}
            onChange={(e) => filters.actions.setFilter('renewalType', e.target.value)}
            data-testid="renewal-type-filter"
          >
            <option value="">All Types</option>
            {filterOptions.renewalTypes.map((type) => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </Form.Select>
        </Form.Field>
      </div>

      {/* Search and Actions Row */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-end">
        {/* Search Input */}
        <Form.Field className="flex-1 min-w-0">
          <Form.Label htmlFor="search-input">Search</Form.Label>
          <Form.Input
            id="search-input"
            placeholder="Search by name, vendor, product..."
            value={search.query}
            onChange={(e) => search.actions.setQuery(e.target.value)}
            data-testid="search-input"
          />
        </Form.Field>

        {/* Action Buttons */}
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={onReset}
            leftIcon={
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path 
                  d="M1 4v6h6M23 20v-6h-6" 
                  stroke="currentColor" 
                  strokeWidth="2" 
                  strokeLinecap="round" 
                  strokeLinejoin="round"
                />
                <path 
                  d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15" 
                  stroke="currentColor" 
                  strokeWidth="2" 
                  strokeLinecap="round" 
                  strokeLinejoin="round"
                />
              </svg>
            }
            data-testid="reset-filters-button"
          >
            Reset Filters
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              // Toggle visualization visibility - this would be handled by parent component
              console.log('Hide/Show Visualization')
            }}
            leftIcon={
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path 
                  d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" 
                  stroke="currentColor" 
                  strokeWidth="2"
                />
                <circle cx="12" cy="12" r="3" stroke="currentColor" strokeWidth="2"/>
              </svg>
            }
            data-testid="hide-visualization-button"
          >
            Hide Visualization
          </Button>
        </div>
      </div>

      {/* Active Filters Indicator */}
      {filters.hasActiveFilters && (
        <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center justify-between">
            <span className="text-sm text-blue-800">
              {filters.activeFilterCount} filter{filters.activeFilterCount !== 1 ? 's' : ''} active
            </span>
            <button
              onClick={() => filters.actions.clearAllFilters()}
              className="text-sm text-blue-600 hover:text-blue-800 font-medium"
              data-testid="clear-all-filters"
            >
              Clear all
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

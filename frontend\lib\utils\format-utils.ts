/**
 * Format Utilities
 *
 * Centralized formatting functions for consistent display
 */

import { Renewal } from '@/lib/types';
import { handleError } from './error-handler';

/**
 * Format renewal data for display
 */
export function formatRenewalData(renewal: any): Renewal {
  try {
    if (!renewal) {
      throw new Error('Renewal data is required');
    }

    return {
      id: renewal.id || renewal.renewal_id,
      name: renewal.name || renewal.renewal_name || '',
      vendor: renewal.vendor || renewal.vendor_name || '',
      vendor_id: renewal.vendor_id,
      status: renewal.status || 'active',
      due_date: renewal.due_date ? new Date(renewal.due_date) : undefined,
      start_date: renewal.start_date,
      annual_cost: renewal.annual_cost || renewal.cost,
      cost: renewal.cost || renewal.annual_cost,
      currency: renewal.currency || 'CAD',
      description: renewal.description || '',
      created_on: renewal.created_on ? new Date(renewal.created_on) : new Date(),
      changed_on: renewal.changed_on ? new Date(renewal.changed_on) : undefined
    };
  } catch (error) {
    handleError(error as Error, {
      component: 'format-utils',
      operation: 'formatRenewalData',
      metadata: { renewal }
    });

    // Return a safe default
    return {
      id: '',
      name: 'Invalid Renewal Data',
      vendor: '',
      vendor_id: undefined,
      status: 'inactive',
      due_date: undefined,
      start_date: undefined,
      annual_cost: 0,
      cost: 0,
      currency: 'CAD',
      description: 'Error formatting renewal data',
      created_on: new Date(),
      changed_on: undefined
    };
  }
}

/**
 * Format currency amount
 */
export function formatCurrency(
  amount: number | string,
  currency: string = 'CAD',
  locale: string = 'en-US'
): string {
  try {
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;

    if (isNaN(numAmount)) {
      return 'Invalid Amount';
    }

    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(numAmount);
  } catch (error) {
    handleError(error as Error, {
      component: 'format-utils',
      operation: 'formatCurrency',
      metadata: { amount, currency, locale }
    });

    return 'Format Error';
  }
}

/**
 * Format number with thousands separators
 */
export function formatNumber(
  value: number | string,
  locale: string = 'en-US'
): string {
  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  
  if (isNaN(numValue)) {
    return 'Invalid Number';
  }

  return new Intl.NumberFormat(locale).format(numValue);
}

/**
 * Format percentage
 */
export function formatPercentage(
  value: number | string,
  decimals: number = 1
): string {
  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  
  if (isNaN(numValue)) {
    return 'Invalid Percentage';
  }

  return `${numValue.toFixed(decimals)}%`;
}

/**
 * Format file size
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Format phone number
 */
export function formatPhoneNumber(phone: string): string {
  // Remove all non-digit characters
  const cleaned = phone.replace(/\D/g, '');
  
  // Check if it's a valid US phone number
  if (cleaned.length === 10) {
    return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
  }
  
  // Return original if not a standard format
  return phone;
}

/**
 * Truncate text with ellipsis
 */
export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) {
    return text;
  }
  
  return text.slice(0, maxLength - 3) + '...';
}

/**
 * Format name (capitalize first letter of each word)
 */
export function formatName(name: string): string {
  return name
    .toLowerCase()
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

/**
 * Format duration in milliseconds to human readable
 */
export function formatDuration(ms: number): string {
  if (ms < 1000) {
    return `${ms}ms`;
  }
  
  const seconds = Math.floor(ms / 1000);
  if (seconds < 60) {
    return `${seconds}s`;
  }
  
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  
  if (minutes < 60) {
    return remainingSeconds > 0 ? `${minutes}m ${remainingSeconds}s` : `${minutes}m`;
  }
  
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  
  return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
}

/**
 * Format currency display for dropdowns (ID + Name)
 */
export function formatCurrencyDisplay(currency: { id: string; name: string; symbol?: string }): string {
  return currency.symbol 
    ? `${currency.id} (${currency.symbol} ${currency.name})`
    : `${currency.id} (${currency.name})`;
}

/**
 * Format status badge text
 */
export function formatStatus(status: string): string {
  return status
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
}

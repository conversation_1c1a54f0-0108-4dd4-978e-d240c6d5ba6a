/**
 * Tenant Users Upsert API Endpoint
 * 
 * Upserts user data to the tenant-specific tenant_users table
 * POST /api/tenant-users/upsert - Upserts user data after login
 */

import { NextRequest } from 'next/server';
import { fetchAuthSession, fetchUserAttributes } from 'aws-amplify/auth';
import { resolveTenantContext } from '@/lib/tenant/context';
import { executeTenantQuery } from '@/lib/tenant/database';
import {
  createSuccessResponse,
  createErrorResponse,
  ApiErrorCode,
  HttpStatus,
  withErrorHandling
} from '@/lib/api/response';
import { z } from 'zod';
import type { AuthSession } from '@/lib/types';

// Validation schema for tenant user upsert
const tenantUserUpsertSchema = z.object({
  id: z.string().min(1, 'User ID is required'),
  email: z.string().email('Valid email is required'),
  given_name: z.string().optional(),
  family_name: z.string().optional(),
  last_logged: z.string().datetime('Valid datetime is required')
});

type TenantUserUpsertData = z.infer<typeof tenantUserUpsertSchema>;

// POST /api/tenant-users/upsert - Upsert tenant user data
export const POST = withErrorHandling(async (request: NextRequest) => {
  console.log('[TENANT-USERS-UPSERT] POST request received');

  // Parse and validate request body
  let requestData: TenantUserUpsertData;
  try {
    const body = await request.json();
    requestData = tenantUserUpsertSchema.parse(body);
  } catch (error) {
    console.error('[TENANT-USERS-UPSERT] Invalid request body:', error);
    return createErrorResponse(
      'Invalid request data',
      ApiErrorCode.VALIDATION_ERROR,
      HttpStatus.BAD_REQUEST
    );
  }

  // Get authentication session
  let session;
  try {
    session = await fetchAuthSession();
    if (!session?.tokens?.idToken) {
      return createErrorResponse(
        'Authentication required',
        ApiErrorCode.UNAUTHORIZED,
        HttpStatus.UNAUTHORIZED
      );
    }
  } catch (error) {
    console.error('[TENANT-USERS-UPSERT] Authentication error:', error);
    return createErrorResponse(
      'Authentication failed',
      ApiErrorCode.UNAUTHORIZED,
      HttpStatus.UNAUTHORIZED
    );
  }

  // Get user attributes for session object
  let userAttributes;
  try {
    userAttributes = await fetchUserAttributes();
  } catch (error) {
    console.error('[TENANT-USERS-UPSERT] Failed to fetch user attributes:', error);
    return createErrorResponse(
      'Failed to fetch user attributes',
      ApiErrorCode.UNAUTHORIZED,
      HttpStatus.UNAUTHORIZED
    );
  }

  // Create session object for tenant resolution
  const sessionObj: AuthSession = {
    userId: userAttributes.sub || '',
    email: userAttributes.email || '',
    roles: ['user'],
    groups: session.tokens?.idToken?.payload['cognito:groups'] as string[] || [],
    isAuthenticated: true,
    tokens: {
      idToken: session.tokens?.idToken?.toString() || '',
      accessToken: session.tokens?.accessToken?.toString() || ''
    }
  };

  // Get tenant context
  const tenantResult = await resolveTenantContext(sessionObj);
  if (!tenantResult.success) {
    return tenantResult.response!;
  }

  const tenant = tenantResult.tenant!;
  console.log(`[TENANT-USERS-UPSERT] Upserting user for tenant: ${tenant.tenantSchema}`);

  try {
    // Upsert user data using the actual database schema
    const upsertQuery = `
      INSERT INTO "${tenant.tenantSchema}".tenant_users (
        cognito_user_id, email, given_name, family_name, last_login
      ) VALUES ($1, $2, $3, $4, $5)
      ON CONFLICT (cognito_user_id)
      DO UPDATE SET
        email = EXCLUDED.email,
        given_name = EXCLUDED.given_name,
        family_name = EXCLUDED.family_name,
        last_login = EXCLUDED.last_login
      RETURNING cognito_user_id, email, given_name, family_name, last_login
    `;

    const upsertResult = await executeTenantQuery(
      upsertQuery,
      [
        requestData.id, // This is the Cognito user ID -> cognito_user_id
        requestData.email,
        requestData.given_name || null, // given_name -> given_name
        requestData.family_name || null, // family_name -> family_name
        requestData.last_logged // last_logged -> last_login
      ],
      tenant
    );

    if (!upsertResult.success) {
      console.error('[TENANT-USERS-UPSERT] Failed to upsert user:', upsertResult.error);
      return createErrorResponse(
        'Failed to upsert tenant user',
        ApiErrorCode.DATABASE_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }

    const upsertedUser = upsertResult.data?.[0];
    console.log(`[TENANT-USERS-UPSERT] Successfully upserted user: ${requestData.email}`);

    return createSuccessResponse({
      message: 'Tenant user upserted successfully',
      user: upsertedUser
    });

  } catch (error) {
    console.error('[TENANT-USERS-UPSERT] Unexpected error:', error);
    return createErrorResponse(
      'Internal server error',
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
});

#!/bin/bash

# RenewTrack Secrets Infrastructure Deployment Script
# This script deploys the secrets management infrastructure to AWS

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT="${ENVIRONMENT:-prod}"
APPLICATION_NAME="${APPLICATION_NAME:-renewtrack}"
AWS_REGION="${AWS_REGION:-ca-central-1}"
STACK_NAME="${APPLICATION_NAME}-secrets-${ENVIRONMENT}"

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}$1${NC}"
}

# Function to check if AWS CLI is installed and configured
check_aws_cli() {
    if ! command -v aws &> /dev/null; then
        print_error "AWS CLI is not installed. Please install it first."
        exit 1
    fi

    if ! aws sts get-caller-identity &> /dev/null; then
        print_error "AWS CLI is not configured. Please run 'aws configure' first."
        exit 1
    fi

    print_status "AWS CLI is configured"
}

# Function to validate required parameters
validate_parameters() {
    if [ -z "$DATABASE_HOST" ]; then
        print_error "DATABASE_HOST environment variable is required"
        exit 1
    fi

    if [ -z "$DATABASE_PASSWORD" ]; then
        print_error "DATABASE_PASSWORD environment variable is required"
        exit 1
    fi

    if [ -z "$JWT_SECRET" ]; then
        print_error "JWT_SECRET environment variable is required"
        exit 1
    fi

    if [ -z "$ENCRYPTION_KEY" ]; then
        print_error "ENCRYPTION_KEY environment variable is required"
        exit 1
    fi

    # Validate secret lengths
    if [ ${#JWT_SECRET} -lt 32 ]; then
        print_error "JWT_SECRET must be at least 32 characters long"
        exit 1
    fi

    if [ ${#ENCRYPTION_KEY} -lt 32 ]; then
        print_error "ENCRYPTION_KEY must be at least 32 characters long"
        exit 1
    fi

    print_status "All required parameters are valid"
}

# Function to generate secure random strings if not provided
generate_secrets() {
    if [ -z "$JWT_SECRET" ]; then
        JWT_SECRET=$(openssl rand -base64 48)
        print_status "Generated JWT_SECRET"
    fi

    if [ -z "$ENCRYPTION_KEY" ]; then
        ENCRYPTION_KEY=$(openssl rand -base64 48)
        print_status "Generated ENCRYPTION_KEY"
    fi
}

# Function to check if stack exists
stack_exists() {
    aws cloudformation describe-stacks --stack-name "$STACK_NAME" --region "$AWS_REGION" &> /dev/null
}

# Function to deploy CloudFormation stack
deploy_stack() {
    local template_file="../templates/secrets-infrastructure.yaml"
    
    if [ ! -f "$template_file" ]; then
        print_error "CloudFormation template not found: $template_file"
        exit 1
    fi

    print_status "Deploying secrets infrastructure stack: $STACK_NAME"

    local parameters=(
        "ParameterKey=Environment,ParameterValue=$ENVIRONMENT"
        "ParameterKey=ApplicationName,ParameterValue=$APPLICATION_NAME"
        "ParameterKey=DatabaseHost,ParameterValue=$DATABASE_HOST"
        "ParameterKey=DatabaseName,ParameterValue=${DATABASE_NAME:-renewtrack}"
        "ParameterKey=DatabaseUsername,ParameterValue=${DATABASE_USERNAME:-renewtrack_admin}"
        "ParameterKey=DatabasePassword,ParameterValue=$DATABASE_PASSWORD"
        "ParameterKey=UseIAMAuth,ParameterValue=${USE_IAM_AUTH:-true}"
        "ParameterKey=JWTSecret,ParameterValue=$JWT_SECRET"
        "ParameterKey=EncryptionKey,ParameterValue=$ENCRYPTION_KEY"
    )

    if stack_exists; then
        print_status "Stack exists, updating..."
        aws cloudformation update-stack \
            --stack-name "$STACK_NAME" \
            --template-body "file://$template_file" \
            --parameters "${parameters[@]}" \
            --capabilities CAPABILITY_NAMED_IAM \
            --region "$AWS_REGION"
        
        print_status "Waiting for stack update to complete..."
        aws cloudformation wait stack-update-complete \
            --stack-name "$STACK_NAME" \
            --region "$AWS_REGION"
    else
        print_status "Creating new stack..."
        aws cloudformation create-stack \
            --stack-name "$STACK_NAME" \
            --template-body "file://$template_file" \
            --parameters "${parameters[@]}" \
            --capabilities CAPABILITY_NAMED_IAM \
            --region "$AWS_REGION"
        
        print_status "Waiting for stack creation to complete..."
        aws cloudformation wait stack-create-complete \
            --stack-name "$STACK_NAME" \
            --region "$AWS_REGION"
    fi

    print_status "Stack deployment completed successfully"
}

# Function to get stack outputs
get_stack_outputs() {
    print_status "Retrieving stack outputs..."
    
    aws cloudformation describe-stacks \
        --stack-name "$STACK_NAME" \
        --region "$AWS_REGION" \
        --query 'Stacks[0].Outputs[*].[OutputKey,OutputValue]' \
        --output table
}

# Function to test secrets access
test_secrets_access() {
    print_status "Testing secrets access..."
    
    # Test Parameter Store access
    local test_param="/${APPLICATION_NAME}/database/host"
    if aws ssm get-parameter --name "$test_param" --region "$AWS_REGION" &> /dev/null; then
        print_status "✅ Parameter Store access successful"
    else
        print_warning "⚠️ Parameter Store access failed"
    fi
    
    # Test Secrets Manager access
    local test_secret="/${APPLICATION_NAME}/jwt-secret"
    if aws secretsmanager get-secret-value --secret-id "$test_secret" --region "$AWS_REGION" &> /dev/null; then
        print_status "✅ Secrets Manager access successful"
    else
        print_warning "⚠️ Secrets Manager access failed"
    fi
}

# Function to create environment file template
create_env_template() {
    local env_file=".env.${ENVIRONMENT}.template"
    
    print_status "Creating environment template: $env_file"
    
    cat > "$env_file" << EOF
# RenewTrack Environment Configuration Template
# Copy this file to .env.${ENVIRONMENT} and update values as needed

# Environment
NODE_ENV=${ENVIRONMENT}
NEXT_PUBLIC_ENVIRONMENT=${ENVIRONMENT}

# AWS Configuration
NEXT_PUBLIC_AWS_REGION=${AWS_REGION}
AWS_REGION=${AWS_REGION}

# Database Configuration (will be loaded from AWS secrets)
# These can be left empty if using AWS secrets management
DB_HOST=
DB_PORT=5432
DB_NAME=renewtrack
DB_USER=renewtrack_admin
DB_PASSWORD=
USE_IAM_DB_AUTH=true

# Application Configuration
# These will be loaded from AWS Parameter Store/Secrets Manager
JWT_SECRET=
ENCRYPTION_KEY=

# Secrets Management
USE_AWS_SECRETS=true
PARAMETER_STORE_PREFIX=/${APPLICATION_NAME}/
SECRETS_MANAGER_PREFIX=/${APPLICATION_NAME}/

# Security
ENABLE_RATE_LIMIT=true
ENABLE_CSRF=true
ENABLE_CORS=true

# Monitoring
LOG_LEVEL=info
ENABLE_METRICS=true
ENABLE_TRACING=true
EOF

    print_status "Environment template created: $env_file"
}

# Main execution
main() {
    print_header "RenewTrack Secrets Infrastructure Deployment"
    echo "=============================================="
    echo ""
    
    print_status "Environment: $ENVIRONMENT"
    print_status "Application: $APPLICATION_NAME"
    print_status "AWS Region: $AWS_REGION"
    print_status "Stack Name: $STACK_NAME"
    echo ""
    
    # Check prerequisites
    check_aws_cli
    
    # Generate secrets if not provided
    generate_secrets
    
    # Validate parameters
    validate_parameters
    
    # Deploy stack
    deploy_stack
    
    # Get outputs
    get_stack_outputs
    
    # Test access
    test_secrets_access
    
    # Create environment template
    create_env_template
    
    echo ""
    print_status "Deployment completed successfully!"
    echo ""
    print_status "Next steps:"
    echo "1. Update your application configuration to use AWS secrets"
    echo "2. Deploy your application with the IAM role: ${APPLICATION_NAME}-secrets-access-${ENVIRONMENT}"
    echo "3. Test the application with the new secrets management"
    echo ""
    print_warning "Remember to:"
    echo "- Update API keys in Secrets Manager: /${APPLICATION_NAME}/api-keys"
    echo "- Configure your deployment to use the IAM role for secrets access"
    echo "- Test all functionality after deployment"
}

# Run main function
main "$@"

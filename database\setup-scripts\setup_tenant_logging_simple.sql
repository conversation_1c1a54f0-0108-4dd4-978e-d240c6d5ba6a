-- Simple Tenant Logging Setup for tenant_0000000000000001
-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create the tenant_log table
CREATE TABLE IF NOT EXISTS "tenant_0000000000000001".tenant_log (
    -- Primary identification
    log_id BIGSERIAL PRIMARY KEY,
    log_uuid UUID DEFAULT uuid_generate_v4() UNIQUE NOT NULL,

-- Event classification
operation VARCHAR(10) NOT NULL CHECK (
    operation IN (
        'INSERT',
        'UPDATE',
        'DELETE',
        'SYSTEM'
    )
),
table_name VARCHAR(100) NOT NULL,
record_id UUID NOT NULL,

-- Timing information
timestamp TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,

-- User context
user_id UUID, user_email VARCHAR(255), session_id VARCHAR(100),

-- Request context
ip_address INET, user_agent TEXT, request_id VARCHAR(100),

-- Change details
old_values JSONB,
new_values JSONB,
changed_fields TEXT [] DEFAULT '{}',

-- Business context
business_impact VARCHAR(50) DEFAULT 'low' CHECK (
    business_impact IN (
        'low',
        'medium',
        'high',
        'critical'
    )
),
change_reason VARCHAR(255),

-- Metadata for analysis
metadata JSONB DEFAULT '{}', tags TEXT [] DEFAULT '{}',

-- Data integrity
checksum VARCHAR(64),

-- Retention and compliance
retention_days INTEGER DEFAULT 2555,
compliance_flags TEXT [] DEFAULT '{}',

-- Performance optimization
partition_date DATE GENERATED ALWAYS AS (DATE(timestamp)) STORED );

-- Create indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_tenant_log_timestamp ON "tenant_0000000000000001".tenant_log (timestamp DESC);

CREATE INDEX IF NOT EXISTS idx_tenant_log_table_operation ON "tenant_0000000000000001".tenant_log (table_name, operation);

CREATE INDEX IF NOT EXISTS idx_tenant_log_record_id ON "tenant_0000000000000001".tenant_log (record_id);

CREATE INDEX IF NOT EXISTS idx_tenant_log_user_id ON "tenant_0000000000000001".tenant_log (user_id);

CREATE INDEX IF NOT EXISTS idx_tenant_log_partition_date ON "tenant_0000000000000001".tenant_log (partition_date);

CREATE INDEX IF NOT EXISTS idx_tenant_log_table_record ON "tenant_0000000000000001".tenant_log (
    table_name,
    record_id,
    timestamp DESC
);

CREATE INDEX IF NOT EXISTS idx_tenant_log_user_time ON "tenant_0000000000000001".tenant_log (user_id, timestamp DESC);

CREATE INDEX IF NOT EXISTS idx_tenant_log_operation_time ON "tenant_0000000000000001".tenant_log (operation, timestamp DESC);

CREATE INDEX IF NOT EXISTS idx_tenant_log_old_values_gin ON "tenant_0000000000000001".tenant_log USING GIN (old_values);

CREATE INDEX IF NOT EXISTS idx_tenant_log_new_values_gin ON "tenant_0000000000000001".tenant_log USING GIN (new_values);

CREATE INDEX IF NOT EXISTS idx_tenant_log_metadata_gin ON "tenant_0000000000000001".tenant_log USING GIN (metadata);

CREATE INDEX IF NOT EXISTS idx_tenant_log_tags_gin ON "tenant_0000000000000001".tenant_log USING GIN (tags);

CREATE INDEX IF NOT EXISTS idx_tenant_log_compliance_gin ON "tenant_0000000000000001".tenant_log USING GIN (compliance_flags);

CREATE INDEX IF NOT EXISTS idx_tenant_log_changed_fields_gin ON "tenant_0000000000000001".tenant_log USING GIN (changed_fields);

CREATE INDEX IF NOT EXISTS idx_tenant_log_high_impact ON "tenant_0000000000000001".tenant_log (timestamp DESC)
WHERE
    business_impact IN ('high', 'critical');

CREATE INDEX IF NOT EXISTS idx_tenant_log_recent ON "tenant_0000000000000001".tenant_log (table_name, record_id)
WHERE
    timestamp > CURRENT_TIMESTAMP - INTERVAL '30 days';

-- Create utility functions
CREATE OR REPLACE FUNCTION "tenant_0000000000000001".calculate_business_impact(
    p_table_name VARCHAR(100),
    p_operation VARCHAR(10),
    p_old_values JSONB,
    p_new_values JSONB
) RETURNS VARCHAR(50) AS $$
BEGIN
    -- Critical impact scenarios
    IF p_table_name = 'Renewals' AND p_operation = 'DELETE' THEN
        RETURN 'critical';
    END IF;
    
    IF p_table_name = 'tenant_vendors' AND p_operation = 'DELETE' THEN
        RETURN 'critical';
    END IF;
    
    -- High impact scenarios
    IF p_table_name = 'Renewals' AND p_operation = 'UPDATE' AND 
       (p_old_values->>'Cost' != p_new_values->>'Cost' OR 
        p_old_values->>'start_date' != p_new_values->>'start_date') THEN
        RETURN 'high';
    END IF;
    
    IF p_table_name = 'tenant_vendors' AND p_operation = 'UPDATE' AND
       p_old_values->>'name' != p_new_values->>'name' THEN
        RETURN 'high';
    END IF;
    
    -- Medium impact scenarios
    IF p_operation = 'INSERT' THEN
        RETURN 'medium';
    END IF;
    
    IF p_operation = 'UPDATE' THEN
        RETURN 'medium';
    END IF;
    
    -- Default to low impact
    RETURN 'low';
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION "tenant_0000000000000001".extract_changed_fields(
    p_old_values JSONB,
    p_new_values JSONB
) RETURNS TEXT[] AS $$
DECLARE
    changed_fields TEXT[] := '{}';
    key TEXT;
BEGIN
    -- For INSERT operations, all fields are "changed"
    IF p_old_values IS NULL THEN
        SELECT array_agg(key) INTO changed_fields
        FROM jsonb_object_keys(p_new_values) AS key;
        RETURN changed_fields;
    END IF;
    
    -- For UPDATE operations, find actually changed fields
    FOR key IN SELECT jsonb_object_keys(p_new_values)
    LOOP
        IF p_old_values->key IS DISTINCT FROM p_new_values->key THEN
            changed_fields := array_append(changed_fields, key);
        END IF;
    END LOOP;
    
    RETURN changed_fields;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION "tenant_0000000000000001".generate_log_checksum(
    p_operation VARCHAR(10),
    p_table_name VARCHAR(100),
    p_record_id UUID,
    p_timestamp TIMESTAMPTZ,
    p_old_values JSONB,
    p_new_values JSONB
) RETURNS VARCHAR(64) AS $$
BEGIN
    RETURN encode(
        digest(
            p_operation || '|' || 
            p_table_name || '|' || 
            p_record_id::TEXT || '|' || 
            p_timestamp::TEXT || '|' || 
            COALESCE(p_old_values::TEXT, '') || '|' || 
            COALESCE(p_new_values::TEXT, ''),
            'sha256'
        ),
        'hex'
    );
END;
$$ LANGUAGE plpgsql;

-- Create the main logging trigger function
CREATE OR REPLACE FUNCTION "tenant_0000000000000001".tenant_log_trigger()
RETURNS TRIGGER AS $$
DECLARE
    current_user_id UUID;
    current_user_email VARCHAR(255);
    current_session_id VARCHAR(100);
    current_ip_address INET;
    current_user_agent TEXT;
    current_request_id VARCHAR(100);
    old_values_json JSONB;
    new_values_json JSONB;
    changed_fields_array TEXT[];
    business_impact_level VARCHAR(50);
    log_checksum VARCHAR(64);
    record_id_value UUID;
BEGIN
    -- Extract current context from session variables
    current_user_id := COALESCE(
        current_setting('app.current_user_id', true)::UUID,
        NULL
    );
    current_user_email := COALESCE(
        current_setting('app.current_user_email', true),
        NULL
    );
    current_session_id := COALESCE(
        current_setting('app.session_id', true),
        NULL
    );
    current_ip_address := COALESCE(
        current_setting('app.client_ip', true)::INET,
        NULL
    );
    current_user_agent := COALESCE(
        current_setting('app.user_agent', true),
        NULL
    );
    current_request_id := COALESCE(
        current_setting('app.request_id', true),
        NULL
    );

    -- Extract record ID based on table structure
    IF TG_TABLE_NAME = 'Renewals' THEN
        record_id_value := COALESCE(NEW."RenewalID"::UUID, OLD."RenewalID"::UUID);
    ELSIF TG_TABLE_NAME = 'tenant_vendors' THEN
        record_id_value := COALESCE(NEW.id, OLD.id);
    ELSIF TG_TABLE_NAME = 'tenant_products' THEN
        record_id_value := COALESCE(NEW.id, OLD.id);
    ELSIF TG_TABLE_NAME = 'tenant_product_versions' THEN
        record_id_value := COALESCE(NEW.id, OLD.id);
    ELSE
        -- Default assumption: table has 'id' field
        record_id_value := COALESCE(NEW.id, OLD.id);
    END IF;

    -- Prepare JSON values based on operation
    IF TG_OP = 'DELETE' THEN
        old_values_json := to_jsonb(OLD);
        new_values_json := NULL;
        changed_fields_array := array(SELECT jsonb_object_keys(old_values_json));
    ELSIF TG_OP = 'INSERT' THEN
        old_values_json := NULL;
        new_values_json := to_jsonb(NEW);
        changed_fields_array := "tenant_0000000000000001".extract_changed_fields(NULL, new_values_json);
    ELSE -- UPDATE
        old_values_json := to_jsonb(OLD);
        new_values_json := to_jsonb(NEW);
        changed_fields_array := "tenant_0000000000000001".extract_changed_fields(old_values_json, new_values_json);
    END IF;

    -- Calculate business impact
    business_impact_level := "tenant_0000000000000001".calculate_business_impact(
        TG_TABLE_NAME,
        TG_OP,
        old_values_json,
        new_values_json
    );

    -- Generate checksum
    log_checksum := "tenant_0000000000000001".generate_log_checksum(
        TG_OP,
        TG_TABLE_NAME,
        record_id_value,
        CURRENT_TIMESTAMP,
        old_values_json,
        new_values_json
    );

    -- Insert log record
    INSERT INTO "tenant_0000000000000001".tenant_log (
        operation,
        table_name,
        record_id,
        user_id,
        user_email,
        session_id,
        ip_address,
        user_agent,
        request_id,
        old_values,
        new_values,
        changed_fields,
        business_impact,
        metadata,
        checksum
    ) VALUES (
        TG_OP,
        TG_TABLE_NAME,
        record_id_value,
        current_user_id,
        current_user_email,
        current_session_id,
        current_ip_address,
        current_user_agent,
        current_request_id,
        old_values_json,
        new_values_json,
        changed_fields_array,
        business_impact_level,
        jsonb_build_object(
            'trigger_name', TG_NAME,
            'schema_name', TG_TABLE_SCHEMA,
            'when', TG_WHEN,
            'level', TG_LEVEL
        ),
        log_checksum
    );

    -- Return appropriate record
    IF TG_OP = 'DELETE' THEN
        RETURN OLD;
    ELSE
        RETURN NEW;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for all tables
DROP TRIGGER IF EXISTS tenant_log_renewals_trigger ON "tenant_0000000000000001"."Renewals";

CREATE TRIGGER tenant_log_renewals_trigger 
    AFTER INSERT OR UPDATE OR DELETE ON "tenant_0000000000000001"."Renewals" 
    FOR EACH ROW EXECUTE FUNCTION "tenant_0000000000000001".tenant_log_trigger();

DROP TRIGGER IF EXISTS tenant_log_vendors_trigger ON "tenant_0000000000000001".tenant_vendors;

CREATE TRIGGER tenant_log_vendors_trigger 
    AFTER INSERT OR UPDATE OR DELETE ON "tenant_0000000000000001".tenant_vendors 
    FOR EACH ROW EXECUTE FUNCTION "tenant_0000000000000001".tenant_log_trigger();

-- Log the setup completion
INSERT INTO
    "tenant_0000000000000001".tenant_log (
        operation,
        table_name,
        record_id,
        old_values,
        new_values,
        changed_fields,
        business_impact,
        metadata
    )
VALUES (
        'SYSTEM',
        'tenant_log',
        uuid_generate_v4 (),
        NULL,
        jsonb_build_object(
            'setup_completed',
            true,
            'schema',
            'tenant_0000000000000001'
        ),
        ARRAY['system_setup'],
        'medium',
        jsonb_build_object(
            'operation_type',
            'setup',
            'automated',
            true,
            'timestamp',
            CURRENT_TIMESTAMP,
            'version',
            '1.0'
        )
    );

-- Verify setup
SELECT 'Setup completed successfully!' as status;

SELECT COUNT(*) as log_entries
FROM "tenant_0000000000000001".tenant_log;
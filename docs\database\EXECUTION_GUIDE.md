# Database Setup Execution Guide

## Prerequisites

1. **PostgreSQL 12+** installed and running
2. **Database user** with CREATE privileges
3. **Command line access** to psql

## Quick Start

### Option 1: Complete Setup (Recommended for new installations)

```bash
# 1. Connect to PostgreSQL
psql -U postgres

# 2. Create database if it doesn't exist
CREATE DATABASE renewtrack;
\c renewtrack

# 3. Run complete setup
\i database/setup_complete_database.sql
```

### Option 2: Step-by-Step Setup

```bash
# 1. Connect to renewtrack database
psql -U postgres -d renewtrack

# 2. Run scripts in order
\i database/migrations/metadata_schema.sql
\i database/migrations/tenant_management_schema.sql
\i database/migrations/tenant_schema_standardized.sql
\i database/tenant-logging-setup.sql
```

## Verification Commands

After running the scripts, verify the installation:

```sql
-- Check all schemas
\dn

-- Check metadata tables
SELECT table_name FROM information_schema.tables
WHERE table_schema = 'metadata' ORDER BY table_name;

-- Check tenant management tables
SELECT table_name FROM information_schema.tables
WHERE table_schema = 'tenant_management' ORDER BY table_name;

-- Check tenant tables
SELECT table_name FROM information_schema.tables
WHERE table_schema = 'tenant_0000000000000001' ORDER BY table_name;

-- Verify sample data
SELECT COUNT(*) as renewal_count FROM "tenant_0000000000000001".tenant_renewals;
SELECT COUNT(*) as purchase_types FROM metadata.global_purchase_types;
SELECT COUNT(*) as renewal_types FROM metadata.global_renewal_types;
SELECT COUNT(*) as currencies FROM metadata.global_currencies;

-- Check foreign key relationships
SELECT
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_schema IN ('metadata', 'tenant_management', 'tenant_0000000000000001')
ORDER BY tc.table_name, kcu.column_name;
```

## Expected Results

### Schemas Created

- ✅ `metadata` - Reference data
- ✅ `tenant_management` - Tenant infrastructure
- ✅ `tenant_0000000000000001` - Default tenant schema

### Tables Created (Metadata Schema)

- ✅ `PurchaseTypes` (4 records)
- ✅ `RenewalTypes` (6 records)
- ✅ `Currencies` (10 records)
- ✅ `Departments` (10 records)
- ✅ `global_vendors`
- ✅ `global_products`
- ✅ `global_product_versions`

### Tables Created (Tenant Management)

- ✅ `tenants` (1 default record)
- ✅ `clients` (1 default record)
- ✅ `domains` (1 default record)

### Tables Created (Tenant Schema)

- ✅ `tenant_renewals` (5 sample records)
- ✅ `tenant_alerts`
- ✅ `tenant_vendors`
- ✅ `tenant_products`
- ✅ `tenant_product_versions`
- ✅ `tenant_log`

### Sync and Audit Tables

- ✅ `sync_batches`
- ✅ `sync_conflicts`
- ✅ `sync_jobs`
- ✅ `sync_metrics`
- ✅ `audit_log`

## Troubleshooting

### Common Issues

**1. Permission Denied**

```sql
-- Solution: Connect as superuser
psql -U postgres -d renewtrack
```

**2. Database doesn't exist**

```sql
-- Solution: Create database first
CREATE DATABASE renewtrack;
```

**3. File not found**

```bash
# Solution: Run from correct directory
cd C:\Users\<USER>\OneDrive\Documents\RenewTrack
psql -U postgres -d renewtrack
```

**4. Foreign key constraint errors**

```sql
-- Solution: Run scripts in correct order
-- 1. metadata_schema.sql (creates reference tables)
-- 2. tenant_management_schema.sql
-- 3. tenant_schema_standardized.sql (references metadata)
-- 4. tenant-logging-setup.sql
```

### Validation Queries

```sql
-- Check if all primary keys are integers
SELECT
    t.table_schema,
    t.table_name,
    c.column_name,
    c.data_type
FROM information_schema.tables t
JOIN information_schema.columns c ON t.table_name = c.table_name AND t.table_schema = c.table_schema
WHERE c.column_name LIKE '%ID'
    AND t.table_schema IN ('metadata', 'tenant_management', 'tenant_0000000000000001')
    AND c.data_type IN ('integer', 'bigint')
ORDER BY t.table_schema, t.table_name;

-- Check foreign key integrity
SELECT
    schemaname,
    tablename,
    attname,
    n_distinct,
    correlation
FROM pg_stats
WHERE schemaname IN ('metadata', 'tenant_management', 'tenant_0000000000000001')
    AND attname LIKE '%ID'
ORDER BY schemaname, tablename;
```

## Next Steps After Successful Setup

1. **✅ Database Schema Complete**
2. **🔄 Update Application Code**
   - Update TypeScript interfaces
   - Modify API endpoints
   - Update service layers
3. **🧪 Run Tests**
   - Unit tests for new schema
   - Integration tests
   - Performance tests
4. **🚀 Deploy**
   - Backup existing data
   - Run migration scripts
   - Verify application functionality

## Support

If you encounter any issues:

1. Check the error message carefully
2. Verify you're in the correct directory
3. Ensure PostgreSQL is running
4. Check user permissions
5. Review the troubleshooting section above

The standardized schema provides optimal performance with integer primary keys and proper foreign key relationships!

{"name": "saas-application", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "clean": "rimraf .next", "build": "npm run clean && next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "test:debug": "jest --detect<PERSON><PERSON><PERSON><PERSON><PERSON> --forceExit"}, "dependencies": {"@aws-sdk/client-secrets-manager": "^3.840.0", "@aws-sdk/client-ses": "^3.840.0", "@aws-sdk/client-ssm": "^3.840.0", "@aws-sdk/rds-signer": "^3.830.0", "aws-amplify": "^6.0.0", "dotenv": "^17.2.0", "isomorphic-dompurify": "^2.25.0", "jose": "^5.1.0", "next": "^15.4.1", "node-fetch": "^3.3.2", "pg": "^8.11.3", "react": "^18.3.1", "react-dom": "^18.2.0", "zod": "^3.22.4"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/node": "^20.19.0", "@types/pg": "^8.10.9", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.0", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "eslint-plugin-security": "^1.7.1", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.1", "jest-watch-typeahead": "^2.2.2", "null-loader": "^4.0.1", "postcss": "^8.4.0", "rimraf": "^5.0.1", "tailwindcss": "^3.3.0", "ts-jest": "^29.4.0", "typescript": "^5.0.0"}}
-- =====================================================
-- RenewTrack Licensing System Database Schema
-- =====================================================

-- Create licensing schema if it doesn't exist
CREATE SCHEMA IF NOT EXISTS licensing;

-- =====================================================
-- License Keys Table
-- =====================================================
CREATE TABLE IF NOT EXISTS licensing.license_keys (
    id SERIAL PRIMARY KEY,
    license_key VARCHAR(255) UNIQUE NOT NULL,
    license_type VARCHAR(50) NOT NULL DEFAULT 'client', -- 'client', 'trial', 'enterprise'
    max_users INTEGER DEFAULT 10,
    max_tenants INTEGER DEFAULT 1,
    features JSONB DEFAULT '{}', -- Feature flags as JSON
    valid_from TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    valid_to TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    usage_count INTEGER DEFAULT 0,
    max_usage INTEGER DEFAULT 1, -- How many times this key can be used

-- Metadata
generated_by VARCHAR(255), -- Super admin who generated the key
generated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
notes TEXT,

-- Audit fields
created_on TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    changed_on TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(255) DEFAULT 'system',
    changed_by VARCHAR(255) DEFAULT 'system'
);

-- =====================================================
-- Client License Activations Table
-- =====================================================
CREATE TABLE IF NOT EXISTS licensing.client_activations (
    id SERIAL PRIMARY KEY,
    client_id INTEGER NOT NULL,
    license_key_id INTEGER NOT NULL REFERENCES licensing.license_keys(id),
    license_key VARCHAR(255) NOT NULL,

-- Activation details
activated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
activated_by VARCHAR(255) NOT NULL, -- Admin who activated
activation_status VARCHAR(50) DEFAULT 'active', -- 'active', 'suspended', 'expired'

-- Usage tracking
user_count INTEGER DEFAULT 0,
tenant_count INTEGER DEFAULT 0,
last_usage TIMESTAMP WITH TIME ZONE,

-- Expiration
expires_at TIMESTAMP WITH TIME ZONE,

-- Audit fields
created_on TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
changed_on TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
created_by VARCHAR(255) DEFAULT 'system',
changed_by VARCHAR(255) DEFAULT 'system',

-- Constraints
UNIQUE(client_id), -- One license per client
    FOREIGN KEY (client_id) REFERENCES metadata.clients(client_id)
);

-- =====================================================
-- License Usage Logs Table
-- =====================================================
CREATE TABLE IF NOT EXISTS licensing.usage_logs (
    id SERIAL PRIMARY KEY,
    client_id INTEGER NOT NULL,
    license_key VARCHAR(255) NOT NULL,

-- Usage details
event_type VARCHAR(50) NOT NULL, -- 'login', 'user_added', 'feature_used'
user_email VARCHAR(255),
tenant_id VARCHAR(255),
feature_name VARCHAR(100),

-- Metadata
ip_address INET, user_agent TEXT, session_id VARCHAR(255),

-- Timestamp
logged_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

-- Indexes for performance
FOREIGN KEY (client_id) REFERENCES metadata.clients(client_id) );

-- =====================================================
-- License Features Table
-- =====================================================
CREATE TABLE IF NOT EXISTS licensing.license_features (
    id SERIAL PRIMARY KEY,
    feature_code VARCHAR(100) UNIQUE NOT NULL,
    feature_name VARCHAR(255) NOT NULL,
    description TEXT,
    is_premium BOOLEAN DEFAULT false,
    default_enabled BOOLEAN DEFAULT true,

-- Audit fields
created_on TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    changed_on TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    status CHAR(1) DEFAULT 'A' CHECK (status IN ('A', 'I'))
);

-- =====================================================
-- Indexes for Performance
-- =====================================================

-- License keys indexes
CREATE INDEX IF NOT EXISTS idx_license_keys_active ON licensing.license_keys (is_active, valid_to);

CREATE INDEX IF NOT EXISTS idx_license_keys_type ON licensing.license_keys (license_type);

CREATE INDEX IF NOT EXISTS idx_license_keys_generated_by ON licensing.license_keys (generated_by);

-- Client activations indexes
CREATE INDEX IF NOT EXISTS idx_client_activations_client ON licensing.client_activations (client_id);

CREATE INDEX IF NOT EXISTS idx_client_activations_status ON licensing.client_activations (activation_status);

CREATE INDEX IF NOT EXISTS idx_client_activations_expires ON licensing.client_activations (expires_at);

-- Usage logs indexes
CREATE INDEX IF NOT EXISTS idx_usage_logs_client ON licensing.usage_logs (client_id);

CREATE INDEX IF NOT EXISTS idx_usage_logs_event ON licensing.usage_logs (event_type);

CREATE INDEX IF NOT EXISTS idx_usage_logs_date ON licensing.usage_logs (logged_at);

-- License features indexes
CREATE INDEX IF NOT EXISTS idx_license_features_status ON licensing.license_features (status);

CREATE INDEX IF NOT EXISTS idx_license_features_premium ON licensing.license_features (is_premium);

-- =====================================================
-- Insert Default License Features
-- =====================================================
INSERT INTO
    licensing.license_features (
        feature_code,
        feature_name,
        description,
        is_premium,
        default_enabled
    )
VALUES (
        'renewals',
        'Renewals Management',
        'Core renewal tracking and management',
        false,
        true
    ),
    (
        'vendors',
        'Vendor Management',
        'Vendor and product management',
        false,
        true
    ),
    (
        'reports',
        'Basic Reports',
        'Standard reporting capabilities',
        false,
        true
    ),
    (
        'notifications',
        'Email Notifications',
        'Basic email notifications',
        false,
        true
    ),
    (
        'advanced_reports',
        'Advanced Reports',
        'Advanced analytics and custom reports',
        true,
        false
    ),
    (
        'api_access',
        'API Access',
        'REST API access for integrations',
        true,
        false
    ),
    (
        'bulk_import',
        'Bulk Import',
        'CSV and Excel import capabilities',
        true,
        false
    ),
    (
        'custom_fields',
        'Custom Fields',
        'Custom field definitions',
        true,
        false
    ),
    (
        'audit_logs',
        'Audit Logs',
        'Detailed audit trail and logging',
        true,
        false
    ),
    (
        'sso_integration',
        'SSO Integration',
        'Single Sign-On integration',
        true,
        false
    )
ON CONFLICT (feature_code) DO NOTHING;

-- =====================================================
-- Functions for License Management
-- =====================================================

-- Function to generate license key
CREATE OR REPLACE FUNCTION licensing.generate_license_key()
RETURNS VARCHAR(255) AS $$
DECLARE
    key_prefix VARCHAR(10) := 'RT-';
    key_suffix VARCHAR(245);
BEGIN
    -- Generate a random key with format: RT-XXXX-XXXX-XXXX-XXXX
    key_suffix := UPPER(
        SUBSTRING(MD5(RANDOM()::TEXT) FROM 1 FOR 4) || '-' ||
        SUBSTRING(MD5(RANDOM()::TEXT) FROM 1 FOR 4) || '-' ||
        SUBSTRING(MD5(RANDOM()::TEXT) FROM 1 FOR 4) || '-' ||
        SUBSTRING(MD5(RANDOM()::TEXT) FROM 1 FOR 4)
    );
    
    RETURN key_prefix || key_suffix;
END;
$$ LANGUAGE plpgsql;

-- Function to check license validity
CREATE OR REPLACE FUNCTION licensing.is_license_valid(p_client_id INTEGER)
RETURNS BOOLEAN AS $$
DECLARE
    activation_record RECORD;
BEGIN
    SELECT ca.*, lk.valid_to, lk.is_active
    INTO activation_record
    FROM licensing.client_activations ca
    JOIN licensing.license_keys lk ON ca.license_key_id = lk.id
    WHERE ca.client_id = p_client_id
    AND ca.activation_status = 'active';
    
    IF NOT FOUND THEN
        RETURN FALSE;
    END IF;
    
    -- Check if license key is active
    IF NOT activation_record.is_active THEN
        RETURN FALSE;
    END IF;
    
    -- Check if license has expired
    IF activation_record.valid_to IS NOT NULL AND activation_record.valid_to < CURRENT_TIMESTAMP THEN
        RETURN FALSE;
    END IF;
    
    -- Check if client activation has expired
    IF activation_record.expires_at IS NOT NULL AND activation_record.expires_at < CURRENT_TIMESTAMP THEN
        RETURN FALSE;
    END IF;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- Triggers for Audit Trail
-- =====================================================

-- Update changed_on timestamp trigger
CREATE OR REPLACE FUNCTION licensing.update_changed_on()
RETURNS TRIGGER AS $$
BEGIN
    NEW.changed_on = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply triggers to all licensing tables
CREATE TRIGGER tr_license_keys_changed_on
    BEFORE UPDATE ON licensing.license_keys
    FOR EACH ROW EXECUTE FUNCTION licensing.update_changed_on();

CREATE TRIGGER tr_client_activations_changed_on
    BEFORE UPDATE ON licensing.client_activations
    FOR EACH ROW EXECUTE FUNCTION licensing.update_changed_on();

CREATE TRIGGER tr_license_features_changed_on
    BEFORE UPDATE ON licensing.license_features
    FOR EACH ROW EXECUTE FUNCTION licensing.update_changed_on();

-- =====================================================
-- Permissions and Security
-- =====================================================

-- Grant permissions to application role (adjust as needed)
-- GRANT USAGE ON SCHEMA licensing TO renewtrack_app;
-- GRANT SELECT, INSERT, UPDATE ON ALL TABLES IN SCHEMA licensing TO renewtrack_app;
-- GRANT USAGE ON ALL SEQUENCES IN SCHEMA licensing TO renewtrack_app;

COMMENT ON SCHEMA licensing IS 'RenewTrack licensing system schema';

COMMENT ON TABLE licensing.license_keys IS 'Master table for license key generation and management';

COMMENT ON TABLE licensing.client_activations IS 'Client license activations and usage tracking';

COMMENT ON TABLE licensing.usage_logs IS 'Detailed usage logging for license compliance';

COMMENT ON TABLE licensing.license_features IS 'Available features and their premium status';
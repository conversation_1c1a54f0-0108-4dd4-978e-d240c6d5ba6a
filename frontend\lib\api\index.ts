/**
 * API Module
 *
 * Centralized exports for all API-related utilities including
 * response handling, client utilities, and route factory.
 */

// Core API response utilities
export {
  createSuccessResponse,
  createErrorResponse,
  handleValidationError,
  handleDatabaseError,
  createUnauthorizedResponse,
  createForbiddenResponse,
  createNotFoundResponse,
  createRateLimitResponse,
  createTenantNotFoundResponse,
  createTenantInactiveResponse,
  createTenantAccessDeniedResponse,
  createTenantSchemaErrorResponse,
  handleApiError,
  addSecurityHeaders
} from './response'

// API client utilities
export * from './client'

// Authentication middleware (server-only - import directly in API routes)
// export { withAuth, authenticateRequest } from './auth-middleware'

// Error handling middleware
export {
  withErrorHandling,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  ConflictError,
  RateLimitError,
  throwValidationError,
  throwAuthError,
  throwAuthzError,
  throwNotFoundError
} from './error-handling'

// Tenant middleware (server-only - import directly in API routes)
// export { withTenant, withTenantContext } from '@/lib/tenant'

// API route factory for eliminating boilerplate
export {
  createApiRoute,
  createGET,
  createPOST,
  createPUT,
  createDELETE,
  createPATCH
} from './route-factory'

// Validation utilities (re-exported for convenience)
export {
  validateRequestBody,
  validateQueryParams,
  validatePathParams
} from '@/lib/utils/validation'

// API types and interfaces
export type {
  ApiResponse,
  ApiErrorCode,
  HttpStatus
} from './response'

export type {
  AuthSession,
  AuthMiddlewareOptions
} from './auth-middleware'

// export type {
//   RouteConfig,
//   RouteContext
// } from './route-factory'

# RenewTrack Database Setup Script (PowerShell)
# This script sets up the complete RenewTrack database with standardized schema

Write-Host "=====================================================" -ForegroundColor Cyan
Write-Host "RenewTrack Database Setup Script" -ForegroundColor Cyan
Write-Host "=====================================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "This script will set up the complete RenewTrack database" -ForegroundColor Yellow
Write-Host "with standardized integer primary keys and proper relationships." -ForegroundColor Yellow
Write-Host ""

# Check if PostgreSQL is available
try {
    $psqlVersion = psql --version 2>$null
    Write-Host "PostgreSQL found: $psqlVersion" -ForegroundColor Green
} catch {
    Write-Host "ERROR: PostgreSQL psql command not found!" -ForegroundColor Red
    Write-Host "Please ensure PostgreSQL is installed and in your PATH." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Get database connection details
$dbUser = Read-Host "Enter PostgreSQL username (default: postgres)"
if ([string]::IsNullOrEmpty($dbUser)) { $dbUser = "postgres" }

$dbName = Read-Host "Enter database name (default: renewtrack)"
if ([string]::IsNullOrEmpty($dbName)) { $dbName = "renewtrack" }

Write-Host ""
Write-Host "Connecting to PostgreSQL as user: $dbUser" -ForegroundColor Cyan
Write-Host "Database: $dbName" -ForegroundColor Cyan
Write-Host ""

# Check if database exists
Write-Host "Checking if database exists..." -ForegroundColor Yellow
$dbExists = psql -U $dbUser -d postgres -c "SELECT 1 FROM pg_database WHERE datname='$dbName';" -t -A 2>$null

if ($dbExists -ne "1") {
    Write-Host "Database $dbName does not exist. Creating..." -ForegroundColor Yellow
    $createResult = psql -U $dbUser -d postgres -c "CREATE DATABASE $dbName;" 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-Host "ERROR: Failed to create database!" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
    Write-Host "Database created successfully." -ForegroundColor Green
} else {
    Write-Host "Database $dbName already exists." -ForegroundColor Green
}

Write-Host ""
Write-Host "=====================================================" -ForegroundColor Cyan
Write-Host "Running Database Setup Scripts" -ForegroundColor Cyan
Write-Host "=====================================================" -ForegroundColor Cyan

# Function to run SQL script with error handling
function Run-SQLScript {
    param(
        [string]$ScriptPath,
        [string]$Description
    )

    Write-Host "Running: $Description..." -ForegroundColor Yellow
    $result = psql -U $dbUser -d $dbName -f $ScriptPath 2>&1

    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ $Description completed successfully" -ForegroundColor Green
        return $true
    } else {
        Write-Host "✗ $Description failed" -ForegroundColor Red
        Write-Host "Error output: $result" -ForegroundColor Red
        return $false
    }
}
}

# Try complete setup first
$completeSetupSuccess = Run-SQLScript "../setup-scripts/setup_complete_database.sql" "Complete database setup"

if (-not $completeSetupSuccess) {
    Write-Host ""
    Write-Host "Complete setup failed. Trying individual scripts..." -ForegroundColor Yellow
    Write-Host ""
    
    # Run individual scripts
    $scripts = @(
        @{ Path = "../migrations/metadata_schema.sql"; Description = "Metadata schema creation" },
        @{ Path = "../migrations/tenant_management_schema.sql"; Description = "Tenant management schema creation" },
        @{ Path = "../migrations/tenant_schema_standardized.sql"; Description = "Tenant schema creation" },
        @{ Path = "../setup-scripts/tenant-logging-setup.sql"; Description = "Tenant logging setup" }
    )
    
    $allSuccess = $true
    foreach ($script in $scripts) {
        $success = Run-SQLScript $script.Path $script.Description
        if (-not $success) {
            $allSuccess = $false
            break
        }
    }
    
    if (-not $allSuccess) {
        Write-Host ""
        Write-Host "ERROR: Script execution failed!" -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit 1
    }
}

Write-Host ""
Write-Host "=====================================================" -ForegroundColor Cyan
Write-Host "Verifying Installation" -ForegroundColor Cyan
Write-Host "=====================================================" -ForegroundColor Cyan

# Verify schemas
Write-Host "Checking schemas..." -ForegroundColor Yellow
psql -U $dbUser -d $dbName -c "\dn"

Write-Host ""
Write-Host "Checking table counts..." -ForegroundColor Yellow

# Run verification queries separately to avoid syntax issues
psql -U $dbUser -d $dbName -c "SELECT 'PurchaseTypes' as table_name, COUNT(*) as records FROM metadata.\"PurchaseTypes\";"
psql -U $dbUser -d $dbName -c "SELECT 'RenewalTypes' as table_name, COUNT(*) as records FROM metadata.\"RenewalTypes\";"
psql -U $dbUser -d $dbName -c "SELECT 'Currencies' as table_name, COUNT(*) as records FROM metadata.\"Currencies\";"
# Note: Tenant-specific table verification should be done after tenant setup

Write-Host ""
Write-Host "=====================================================" -ForegroundColor Green
Write-Host "Setup Complete!" -ForegroundColor Green
Write-Host "=====================================================" -ForegroundColor Green
Write-Host ""
Write-Host "The RenewTrack database has been successfully set up with:" -ForegroundColor Green
Write-Host "✓ Metadata schema with reference data" -ForegroundColor Green
Write-Host "✓ Tenant management infrastructure" -ForegroundColor Green
Write-Host "✓ Default tenant schema with sample data" -ForegroundColor Green
Write-Host "✓ Comprehensive logging system" -ForegroundColor Green
Write-Host "✓ Integer primary keys and proper relationships" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Update application configuration to use the new schema" -ForegroundColor Yellow
Write-Host "2. Run application tests to verify functionality" -ForegroundColor Yellow
Write-Host "3. Import any existing data using migration scripts" -ForegroundColor Yellow
Write-Host ""
Write-Host "For detailed information, see: database/EXECUTION_GUIDE.md" -ForegroundColor Cyan
Write-Host ""
Read-Host "Press Enter to exit"

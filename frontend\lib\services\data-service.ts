/**
 * Universal Data Service - Consolidates Data Fetching Patterns
 * 
 * This service provides a unified approach to data fetching, caching, and state management,
 * eliminating the duplicate patterns found across multiple hooks and components.
 */

'use client';

import { BehaviorSubject, Observable, combineLatest } from 'rxjs';
import { map, distinctUntilChanged, shareReplay } from 'rxjs/operators';

// Types for data service
export interface DataConfig<T = any> {
  // Unique identifier for this data
  key: string;
  
  // Fetcher function
  fetcher: () => Promise<T>;
  
  // Cache configuration
  cache?: {
    ttl?: number; // Time to live in milliseconds
    staleWhileRevalidate?: boolean;
    maxAge?: number;
  };
  
  // Retry configuration
  retry?: {
    attempts?: number;
    delay?: number;
    backoff?: 'linear' | 'exponential';
  };
  
  // Dependencies that trigger refetch
  dependencies?: string[];
  
  // Transform function
  transform?: (data: any) => T;
  
  // Error handling
  onError?: (error: Error) => void;
  
  // Success callback
  onSuccess?: (data: T) => void;
}

export interface DataState<T = any> {
  data: T | null;
  isLoading: boolean;
  error: Error | null;
  lastFetched: number | null;
  isStale: boolean;
  isFetching: boolean;
}

export interface CacheEntry<T = any> {
  data: T;
  timestamp: number;
  ttl: number;
  key: string;
}

/**
 * Universal Data Service Class
 */
class DataService {
  private cache = new Map<string, CacheEntry>();
  private subjects = new Map<string, BehaviorSubject<DataState>>();
  private fetchPromises = new Map<string, Promise<any>>();
  private dependencies = new Map<string, Set<string>>();
  
  /**
   * Get or create a data stream for the given configuration
   */
  getDataStream<T>(config: DataConfig<T>): Observable<DataState<T>> {
    const { key } = config;
    
    if (!this.subjects.has(key)) {
      // Create initial state
      const initialState: DataState<T> = {
        data: null,
        isLoading: false,
        error: null,
        lastFetched: null,
        isStale: true,
        isFetching: false
      };
      
      const subject = new BehaviorSubject<DataState<T>>(initialState);
      this.subjects.set(key, subject);
      
      // Set up dependencies
      if (config.dependencies) {
        this.dependencies.set(key, new Set(config.dependencies));
      }
      
      // Check cache and load if available
      const cached = this.getFromCache<T>(key);
      if (cached) {
        const isStale = this.isCacheStale(cached, config.cache?.ttl);
        subject.next({
          ...initialState,
          data: cached.data,
          lastFetched: cached.timestamp,
          isStale
        });
        
        // If stale and staleWhileRevalidate is enabled, fetch in background
        if (isStale && config.cache?.staleWhileRevalidate) {
          this.fetchData(config);
        }
      }
    }
    
    return this.subjects.get(key)!.asObservable().pipe(
      distinctUntilChanged((a, b) => 
        a.data === b.data && 
        a.isLoading === b.isLoading && 
        a.error === b.error
      ),
      shareReplay(1)
    );
  }
  
  /**
   * Fetch data for the given configuration
   */
  async fetchData<T>(config: DataConfig<T>): Promise<T> {
    const { key, fetcher, retry = {}, transform, onError, onSuccess } = config;
    
    // Check if already fetching
    if (this.fetchPromises.has(key)) {
      return this.fetchPromises.get(key);
    }
    
    const subject = this.subjects.get(key);
    if (!subject) {
      throw new Error(`No subject found for key: ${key}`);
    }
    
    // Update loading state
    const currentState = subject.value;
    subject.next({
      ...currentState,
      isLoading: !currentState.data, // Don't show loading if we have cached data
      isFetching: true,
      error: null
    });
    
    const fetchPromise = this.executeWithRetry(fetcher, retry)
      .then(rawData => {
        // Transform data if needed
        const data = transform ? transform(rawData) : rawData;
        
        // Update cache
        this.setCache(key, data, config.cache?.ttl || 300000); // Default 5 minutes
        
        // Update state
        subject.next({
          data,
          isLoading: false,
          error: null,
          lastFetched: Date.now(),
          isStale: false,
          isFetching: false
        });
        
        // Call success callback
        if (onSuccess) {
          onSuccess(data);
        }
        
        return data;
      })
      .catch(error => {
        // Update error state
        subject.next({
          ...currentState,
          isLoading: false,
          error,
          isFetching: false
        });
        
        // Call error callback
        if (onError) {
          onError(error);
        }
        
        throw error;
      })
      .finally(() => {
        this.fetchPromises.delete(key);
      });
    
    this.fetchPromises.set(key, fetchPromise);
    return fetchPromise;
  }
  
  /**
   * Invalidate cache and refetch data
   */
  async invalidate(key: string): Promise<void> {
    this.cache.delete(key);
    
    const subject = this.subjects.get(key);
    if (subject) {
      const currentState = subject.value;
      subject.next({
        ...currentState,
        isStale: true
      });
    }
    
    // Invalidate dependent keys
    this.invalidateDependents(key);
  }
  
  /**
   * Invalidate multiple keys
   */
  async invalidateMultiple(keys: string[]): Promise<void> {
    await Promise.all(keys.map(key => this.invalidate(key)));
  }
  
  /**
   * Clear all cache
   */
  clearCache(): void {
    this.cache.clear();
    
    // Mark all subjects as stale
    this.subjects.forEach(subject => {
      const currentState = subject.value;
      subject.next({
        ...currentState,
        isStale: true
      });
    });
  }
  
  /**
   * Get cached data
   */
  getCachedData<T>(key: string): T | null {
    const cached = this.getFromCache<T>(key);
    return cached ? cached.data : null;
  }
  
  /**
   * Set data directly (for optimistic updates)
   */
  setData<T>(key: string, data: T): void {
    const subject = this.subjects.get(key);
    if (subject) {
      const currentState = subject.value;
      subject.next({
        ...currentState,
        data,
        lastFetched: Date.now(),
        isStale: false
      });
    }
    
    // Update cache
    this.setCache(key, data);
  }
  
  /**
   * Combine multiple data streams
   */
  combineData<T extends Record<string, any>>(
    configs: { [K in keyof T]: DataConfig<T[K]> }
  ): Observable<{ [K in keyof T]: DataState<T[K]> }> {
    const streams = Object.entries(configs).map(([key, config]) => 
      this.getDataStream(config as DataConfig).pipe(
        map(state => ({ [key]: state }))
      )
    );
    
    return combineLatest(streams).pipe(
      map(states => states.reduce((acc, state) => ({ ...acc, ...state }), {} as any))
    );
  }
  
  // Private methods
  private getFromCache<T>(key: string): CacheEntry<T> | null {
    return this.cache.get(key) || null;
  }
  
  private setCache<T>(key: string, data: T, ttl: number = 300000): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
      key
    });
  }
  
  private isCacheStale(entry: CacheEntry, ttl?: number): boolean {
    const effectiveTtl = ttl || entry.ttl;
    return Date.now() - entry.timestamp > effectiveTtl;
  }
  
  private async executeWithRetry<T>(
    fn: () => Promise<T>,
    config: { attempts?: number; delay?: number; backoff?: 'linear' | 'exponential' } = {}
  ): Promise<T> {
    const { attempts = 3, delay = 1000, backoff = 'exponential' } = config;
    
    let lastError: Error;
    
    for (let attempt = 1; attempt <= attempts; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error as Error;
        
        if (attempt === attempts) {
          throw lastError;
        }
        
        // Calculate delay
        const currentDelay = backoff === 'exponential' 
          ? delay * Math.pow(2, attempt - 1)
          : delay * attempt;
        
        await new Promise(resolve => setTimeout(resolve, currentDelay));
      }
    }
    
    throw lastError!;
  }
  
  private invalidateDependents(key: string): void {
    this.dependencies.forEach((deps, dependentKey) => {
      if (deps.has(key)) {
        this.invalidate(dependentKey);
      }
    });
  }
}

// Create singleton instance
export const dataService = new DataService();

// Export types (avoid conflicts with hooks/useData.ts)
export type {
  DataConfig as ServiceDataConfig,
  DataState as ServiceDataState,
  CacheEntry as ServiceCacheEntry
};

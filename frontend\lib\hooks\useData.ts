/**
 * Universal Data Fetching Hook
 * 
 * Provides consistent data fetching with caching, loading states, and error handling
 */

'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
import { TIME } from '@/lib/constants/app-constants'

export interface UseDataOptions {
  endpoint?: string
  cache?: {
    key: string
    ttl?: number
    tags?: string[]
  }
  params?: Record<string, any>
  enabled?: boolean
  retryAttempts?: number
  retryDelay?: number
  onSuccess?: (data: any) => void
  onError?: (error: any) => void
}

export interface UseDataResult<T> {
  data: T | null
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
  mutate: (newData: T | null) => void
}

// Simple in-memory cache
const dataCache = new Map<string, { data: any; timestamp: number; ttl: number }>()

function getCachedData(key: string): any | null {
  const cached = dataCache.get(key)
  if (!cached) return null
  
  const now = Date.now()
  if (now - cached.timestamp > cached.ttl) {
    dataCache.delete(key)
    return null
  }
  
  return cached.data
}

function setCachedData(key: string, data: any, ttl: number): void {
  dataCache.set(key, {
    data,
    timestamp: Date.now(),
    ttl
  })
}

/**
 * Universal data fetching hook
 */
export function useData<T = any>(options: UseDataOptions | string): UseDataResult<T> {
  // Handle string endpoint shorthand
  const config: UseDataOptions = typeof options === 'string' 
    ? { endpoint: options }
    : options

  const {
    endpoint,
    cache,
    params = {},
    enabled = true,
    retryAttempts = 1,
    retryDelay = 1000,
    onSuccess,
    onError
  } = config

  const [data, setData] = useState<T | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  const abortControllerRef = useRef<AbortController | null>(null)
  const retryCountRef = useRef(0)

  const fetchData = useCallback(async (isRetry = false) => {
    if (!endpoint || !enabled) return

    // Cancel previous request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }

    // Create new abort controller
    abortControllerRef.current = new AbortController()

    try {
      if (!isRetry) {
        setLoading(true)
        setError(null)
        
        // Check cache first
        if (cache?.key) {
          const cachedData = getCachedData(cache.key)
          if (cachedData) {
            setData(cachedData)
            setLoading(false)
            return
          }
        }
      }

      // Build URL with params
      const url = new URL(endpoint, window.location.origin)
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          url.searchParams.append(key, String(value))
        }
      })

      const response = await fetch(url.toString(), {
        signal: abortControllerRef.current.signal,
        headers: {
          'Content-Type': 'application/json',
        },
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      const responseData = result.data || result

      setData(responseData)
      
      // Cache the result
      if (cache?.key) {
        setCachedData(cache.key, responseData, cache.ttl || 5 * TIME.MINUTE)
      }

      retryCountRef.current = 0
      onSuccess?.(responseData)

    } catch (err: any) {
      if (err.name === 'AbortError') {
        return // Request was cancelled
      }

      const errorMessage = err.message || 'An error occurred'
      
      // Retry logic
      if (retryCountRef.current < retryAttempts) {
        retryCountRef.current++
        setTimeout(() => {
          fetchData(true)
        }, retryDelay)
        return
      }

      setError(errorMessage)
      onError?.(err)
    } finally {
      if (!isRetry) {
        setLoading(false)
      }
    }
  }, [endpoint, enabled, params, cache, retryAttempts, retryDelay, onSuccess, onError])

  const refetch = useCallback(async () => {
    retryCountRef.current = 0
    await fetchData()
  }, [fetchData])

  const mutate = useCallback((newData: T | null) => {
    setData(newData)
    if (cache?.key && newData) {
      setCachedData(cache.key, newData, cache.ttl || 5 * TIME.MINUTE)
    }
  }, [cache])

  useEffect(() => {
    fetchData()
    
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [fetchData])

  return {
    data,
    loading,
    error,
    refetch,
    mutate
  }
}

// Convenience hooks for specific HTTP methods
export function useGet<T = any>(endpoint: string, options?: Omit<UseDataOptions, 'endpoint'>) {
  return useData<T>({ endpoint, ...options })
}

export function usePost<T = any>(endpoint: string, options?: Omit<UseDataOptions, 'endpoint'>) {
  return useData<T>({ endpoint, ...options })
}

export function usePut<T = any>(endpoint: string, options?: Omit<UseDataOptions, 'endpoint'>) {
  return useData<T>({ endpoint, ...options })
}

export function useDelete<T = any>(endpoint: string, options?: Omit<UseDataOptions, 'endpoint'>) {
  return useData<T>({ endpoint, ...options })
}

/**
 * Tenant Management Hook
 * 
 * Handles tenant context and operations in a focused, reusable way.
 * Extracted from AppContext to follow single responsibility principle.
 */

'use client';

import { useState, useCallback, useEffect } from 'react';
import { TenantContext as TenantContextType } from '@/lib/types';
import { api } from '@/lib/api/client';

export interface TenantState {
  tenant: TenantContextType | null;
  tenantLoading: boolean;
  tenantError: string | null;
}

export interface TenantActions {
  refreshTenant: () => Promise<void>;
  clearTenant: () => void;
}

export function useTenant(userEmail?: string): TenantState & TenantActions {
  const [tenant, setTenant] = useState<TenantContextType | null>(null);
  const [tenantLoading, setTenantLoading] = useState(false);
  const [tenantError, setTenantError] = useState<string | null>(null);

  // Fetch tenant information based on user email
  const fetchTenant = useCallback(async () => {
    if (!userEmail) {
      console.log('🔍 [TENANT] No user email provided, skipping tenant fetch');
      return;
    }

    setTenantLoading(true);
    setTenantError(null);

    try {
      console.log('🔄 [TENANT] Fetching tenant context for user:', userEmail);
      
      const response = await api.get<{ client: TenantContextType }>('/clients/by-email', {
        body: { email: userEmail }
      });

      if (response.success && response.data?.client) {
        setTenant(response.data.client);
        console.log('✅ [TENANT] Tenant context loaded successfully:', response.data.client.clientName);
      } else {
        throw new Error('Invalid response format or no tenant found');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      console.error('❌ [TENANT] Error fetching tenant:', errorMessage);
      setTenantError(errorMessage);
      setTenant(null);
    } finally {
      setTenantLoading(false);
    }
  }, [userEmail]);

  // Refresh tenant information
  const refreshTenant = useCallback(async () => {
    await fetchTenant();
  }, [fetchTenant]);

  // Clear tenant information
  const clearTenant = useCallback(() => {
    setTenant(null);
    setTenantError(null);
    setTenantLoading(false);
  }, []);

  // Fetch tenant when user email changes
  useEffect(() => {
    if (userEmail) {
      fetchTenant();
    } else {
      clearTenant();
    }
  }, [userEmail, fetchTenant, clearTenant]);

  return {
    // State
    tenant,
    tenantLoading,
    tenantError,
    
    // Actions
    refreshTenant,
    clearTenant
  };
}

// Helper function to upsert tenant user data
export async function upsertTenantUser(user: any): Promise<void> {
  try {
    console.log('🔄 [TENANT] Upserting tenant user data:', {
      id: user.id,
      email: user.email,
      given_name: user.given_name,
      family_name: user.family_name,
      name: user.name,
      fullUserObject: user
    });

    // Add a server-side log by calling a simple API endpoint first
    try {
      await fetch('/api/debug-log', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          message: 'upsertTenantUser called',
          userEmail: user.email,
          userId: user.id
        })
      });
    } catch (debugError) {
      console.log('Debug log failed:', debugError);
    }

    const response = await api.post('/tenant-users/upsert', {
      body: {
        id: user.id, // Changed from user_id to id
        email: user.email,
        given_name: user.given_name,
        family_name: user.family_name,
        last_logged: new Date().toISOString() // API expects last_logged, maps to last_login in DB
      }
    });

    if (response.success) {
      console.log('✅ [TENANT] Tenant user data upserted successfully:', response.data);
    } else {
      console.error('❌ [TENANT] Upsert failed with response:', response);
      throw new Error(response.error || 'Failed to upsert tenant user');
    }
  } catch (error) {
    console.error('❌ [TENANT] Error upserting tenant user:', error);
    throw error;
  }
}

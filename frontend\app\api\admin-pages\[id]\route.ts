/**
 * Admin Pages Management API - Individual Page Operations
 * 
 * This endpoint handles CRUD operations for individual admin pages.
 * Only accessible to admin users.
 */

import { NextRequest } from 'next/server'
import { withAuth } from '@/lib/api/auth-middleware'
import { createSuccessResponse, createErrorResponse, ApiErrorCode, HttpStatus } from '@/lib/api/response'
import { databaseService } from '@/lib/services/database-service'

/**
 * GET /api/admin-pages/[id]
 * Get a specific admin page by ID
 */
export const GET = withAuth(async (
  request: NextRequest,
  session,
  { params }: { params: Promise<{ id: string }> }
) => {
  try {
    // Await params first
    const resolvedParams = await params

    const pageId = parseInt(resolvedParams.id)
    if (isNaN(pageId)) {
      return createErrorResponse('Invalid page ID', ApiErrorCode.VALIDATION_ERROR, HttpStatus.BAD_REQUEST)
    }

    // Get database connection
    const db = databaseService

    // Query the specific page with its groups
    const query = `
      SELECT 
        p.id,
        p.name,
        p.header,
        p.description,
        p.sidebar,
        p.status,
        p.display_order,
        p.icon_svg,
        p.route_path,
        ARRAY_AGG(DISTINCT pg.group_name) FILTER (WHERE pg.group_name IS NOT NULL) as groups
      FROM metadata.pages p
      LEFT JOIN metadata.page_groups pg ON p.id = pg.page_id
      WHERE p.id = $1
      GROUP BY p.id, p.name, p.header, p.description, p.sidebar, p.status, p.display_order, p.icon_svg, p.route_path
    `

    const result = await db.query(query, [pageId])
    
    if (result.rows.length === 0) {
      return createErrorResponse('Page not found', ApiErrorCode.NOT_FOUND, HttpStatus.NOT_FOUND)
    }

    const page = {
      ...result.rows[0],
      groups: result.rows[0].groups || []
    }

    return createSuccessResponse(page)

  } catch (error) {
    console.error('[ADMIN-PAGES] Error fetching page:', error)
    return createErrorResponse(
      'Failed to fetch page',
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR,
      error instanceof Error ? error.message : 'Unknown error'
    )
  }
}, {
  requireAuth: true,
  requiredGroups: ['admin']
})

/**
 * PATCH /api/admin-pages/[id]
 * Update a specific admin page
 */
export const PATCH = withAuth(async (
  request: NextRequest,
  session,
  { params }: { params: Promise<{ id: string }> }
) => {
  try {
    // Await params first
    const resolvedParams = await params
    // Only super-admin can modify page access and packages
    const isSuperAdmin = session.groups?.includes('super-admin') || false

    const pageId = parseInt(resolvedParams.id)
    if (isNaN(pageId)) {
      return createErrorResponse('Invalid page ID', ApiErrorCode.VALIDATION_ERROR, HttpStatus.BAD_REQUEST)
    }

    const body = await request.json()
    const {
      name,
      header,
      description,
      sidebar,
      status,
      display_order,
      icon_svg,
      route_path,
      groups
    } = body

    // Restrict certain operations to super-admin only
    if (!isSuperAdmin && (groups !== undefined || status !== undefined)) {
      return createErrorResponse('Super admin access required for group and status changes', ApiErrorCode.FORBIDDEN, HttpStatus.FORBIDDEN)
    }

    // Get database connection
    const db = databaseService

    // Build dynamic update query
    const updateFields: string[] = []
    const updateValues: any[] = []
    let paramIndex = 1

    if (name !== undefined) {
      updateFields.push(`name = $${paramIndex++}`)
      updateValues.push(name)
    }
    if (header !== undefined) {
      updateFields.push(`header = $${paramIndex++}`)
      updateValues.push(header)
    }
    if (description !== undefined) {
      updateFields.push(`description = $${paramIndex++}`)
      updateValues.push(description)
    }
    if (sidebar !== undefined) {
      updateFields.push(`sidebar = $${paramIndex++}`)
      updateValues.push(sidebar)
    }
    if (status !== undefined) {
      updateFields.push(`status = $${paramIndex++}`)
      updateValues.push(status)
    }
    if (display_order !== undefined) {
      updateFields.push(`display_order = $${paramIndex++}`)
      updateValues.push(display_order)
    }
    if (icon_svg !== undefined) {
      updateFields.push(`icon_svg = $${paramIndex++}`)
      updateValues.push(icon_svg)
    }
    if (route_path !== undefined) {
      updateFields.push(`route_path = $${paramIndex++}`)
      updateValues.push(route_path)
    }

    if (updateFields.length > 0) {
      updateFields.push(`changed_on = CURRENT_TIMESTAMP`)
      updateValues.push(pageId)

      const updateQuery = `
        UPDATE metadata.admin_pages 
        SET ${updateFields.join(', ')}
        WHERE id = $${paramIndex}
        RETURNING *
      `

      await db.query(updateQuery, updateValues)
    }

    // Update groups if provided
    if (groups !== undefined && Array.isArray(groups)) {
      // Delete existing groups
      await db.query('DELETE FROM metadata.page_groups WHERE page_id = $1', [pageId])
      
      // Insert new groups
      if (groups.length > 0) {
        const groupInserts = groups.map((group, index) => 
          `($1, $${index + 2})`
        ).join(', ')
        
        const insertGroupsQuery = `
          INSERT INTO metadata.page_groups (page_id, group_name)
          VALUES ${groupInserts}
        `
        
        await db.query(insertGroupsQuery, [pageId, ...groups])
      }
    }

    return createSuccessResponse({ message: 'Page updated successfully' })

  } catch (error) {
    console.error('[ADMIN-PAGES] Error updating page:', error)
    return createErrorResponse(
      'Failed to update page',
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR,
      error instanceof Error ? error.message : 'Unknown error'
    )
  }
}, {
  requireAuth: true,
  requiredGroups: ['admin', 'super-admin']
})

/**
 * DELETE /api/admin-pages/[id]
 * Delete a specific admin page
 */
export const DELETE = withAuth(async (
  request: NextRequest,
  session,
  { params }: { params: Promise<{ id: string }> }
) => {
  try {
    // Await params first
    const resolvedParams = await params

    // Only super-admin can delete pages
    if (!session.groups?.includes('super-admin')) {
      return createErrorResponse('Super admin access required for page deletion', ApiErrorCode.FORBIDDEN, HttpStatus.FORBIDDEN)
    }

    const pageId = parseInt(resolvedParams.id)
    if (isNaN(pageId)) {
      return createErrorResponse('Invalid page ID', ApiErrorCode.VALIDATION_ERROR, HttpStatus.BAD_REQUEST)
    }

    // Get database connection
    const db = databaseService

    // Delete the page (groups will be deleted automatically due to CASCADE)
    const deleteQuery = 'DELETE FROM metadata.pages WHERE id = $1 RETURNING *'
    const result = await db.query(deleteQuery, [pageId])

    if (result.rows.length === 0) {
      return createErrorResponse('Page not found', ApiErrorCode.NOT_FOUND, HttpStatus.NOT_FOUND)
    }

    return createSuccessResponse({ message: 'Page deleted successfully' })

  } catch (error) {
    console.error('[ADMIN-PAGES] Error deleting page:', error)
    return createErrorResponse(
      'Failed to delete page',
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR,
      error instanceof Error ? error.message : 'Unknown error'
    )
  }
}, {
  requireAuth: true,
  requiredGroups: ['super-admin']
})

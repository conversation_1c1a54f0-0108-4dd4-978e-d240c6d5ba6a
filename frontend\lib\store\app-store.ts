/**
 * Application State Store
 * 
 * Centralized state management using Zustand for better performance and simplicity.
 * Replaces the overcomplicated useAppState hook with a clean, type-safe solution.
 */

'use client'

import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'
import { User, TenantContext } from '@/lib/types'

// Define the application state interface
export interface AppState {
  // Authentication state
  user: User | null
  isAuthenticated: boolean
  authLoading: boolean

  // Tenant state
  tenant: TenantContext | null
  tenantLoading: boolean
  tenantError: string | null

  // UI state
  sidebarCollapsed: boolean
  theme: 'light' | 'dark'

  // Notifications
  notifications: AppNotification[]

  // Loading states
  globalLoading: boolean
  loadingStates: Record<string, boolean>
}

// Define notification interface
export interface AppNotification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message?: string
  duration?: number
  timestamp: Date
}

// Define the store actions
export interface AppActions {
  // Authentication actions
  setUser: (user: User | null) => void
  setAuthLoading: (loading: boolean) => void
  setAuthenticated: (authenticated: boolean) => void
  
  // Tenant actions
  setTenant: (tenant: TenantContext | null) => void
  setTenantLoading: (loading: boolean) => void
  setTenantError: (error: string | null) => void
  
  // UI actions
  toggleSidebar: () => void
  setSidebarCollapsed: (collapsed: boolean) => void
  setTheme: (theme: 'light' | 'dark') => void
  
  // Notification actions
  addNotification: (notification: Omit<AppNotification, 'id' | 'timestamp'>) => void
  removeNotification: (id: string) => void
  clearNotifications: () => void
  
  // Loading actions
  setGlobalLoading: (loading: boolean) => void
  setLoading: (key: string, loading: boolean) => void
  
  // Reset actions
  reset: () => void
}

// Initial state
const initialState: AppState = {
  user: null,
  isAuthenticated: false,
  authLoading: true,
  tenant: null,
  tenantLoading: false,
  tenantError: null,
  sidebarCollapsed: false,
  theme: 'light',
  notifications: [],
  globalLoading: false,
  loadingStates: {}
}

// Create the store
export const useAppStore = create<AppState & AppActions>()(
  subscribeWithSelector((set, get) => ({
    ...initialState,
    
    // Authentication actions
    setUser: (user) => set({ user }),
    setAuthLoading: (authLoading) => set({ authLoading }),
    setAuthenticated: (isAuthenticated) => set({ isAuthenticated }),
    
    // Tenant actions
    setTenant: (tenant) => set({ tenant }),
    setTenantLoading: (tenantLoading) => set({ tenantLoading }),
    setTenantError: (tenantError) => set({ tenantError }),
    
    // UI actions
    toggleSidebar: () => set((state) => ({ sidebarCollapsed: !state.sidebarCollapsed })),
    setSidebarCollapsed: (sidebarCollapsed) => set({ sidebarCollapsed }),
    setTheme: (theme) => set({ theme }),
    
    // Notification actions
    addNotification: (notification) => {
      const id = Math.random().toString(36).substr(2, 9)
      const newNotification: AppNotification = {
        ...notification,
        id,
        timestamp: new Date()
      }
      set((state) => ({
        notifications: [...state.notifications, newNotification]
      }))
      
      // Auto-remove notification after duration
      if (notification.duration !== 0) {
        const duration = notification.duration || 5000
        setTimeout(() => {
          get().removeNotification(id)
        }, duration)
      }
    },
    
    removeNotification: (id) => set((state) => ({
      notifications: state.notifications.filter(n => n.id !== id)
    })),
    
    clearNotifications: () => set({ notifications: [] }),
    
    // Loading actions
    setGlobalLoading: (globalLoading) => set({ globalLoading }),
    setLoading: (key, loading) => set((state) => ({
      loadingStates: {
        ...state.loadingStates,
        [key]: loading
      }
    })),
    
    // Reset action
    reset: () => set(initialState)
  }))
)

// Selector hooks for better performance
export const useAuthStore = () => useAppStore((state) => ({
  user: state.user,
  isAuthenticated: state.isAuthenticated,
  authLoading: state.authLoading,
  setUser: state.setUser,
  setAuthLoading: state.setAuthLoading,
  setAuthenticated: state.setAuthenticated
}))

export const useTenantStore = () => useAppStore((state) => ({
  tenant: state.tenant,
  tenantLoading: state.tenantLoading,
  tenantError: state.tenantError,
  setTenant: state.setTenant,
  setTenantLoading: state.setTenantLoading,
  setTenantError: state.setTenantError
}))

export const useUI = () => useAppStore((state) => ({
  sidebarCollapsed: state.sidebarCollapsed,
  theme: state.theme,
  toggleSidebar: state.toggleSidebar,
  setSidebarCollapsed: state.setSidebarCollapsed,
  setTheme: state.setTheme
}))

export const useNotifications = () => useAppStore((state) => ({
  notifications: state.notifications,
  addNotification: state.addNotification,
  removeNotification: state.removeNotification,
  clearNotifications: state.clearNotifications
}))

export const useLoading = () => useAppStore((state) => ({
  globalLoading: state.globalLoading,
  loadingStates: state.loadingStates,
  setGlobalLoading: state.setGlobalLoading,
  setLoading: state.setLoading
}))

// Utility functions
export const appStore = {
  getState: () => useAppStore.getState(),
  subscribe: useAppStore.subscribe
}

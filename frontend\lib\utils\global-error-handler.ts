/**
 * Global Error Handler
 * 
 * Catches unhandled errors and promise rejections to prevent them from
 * showing technical details to users.
 */

import { logger, LogCategory } from './logger';
import { handleError } from './error-handler';
import { UI } from '@/lib/constants/app-constants';

interface GlobalErrorConfig {
  enableLogging: boolean;
  enableReporting: boolean;
  showUserNotifications: boolean;
}

class GlobalErrorHandler {
  private config: GlobalErrorConfig;
  private isInitialized = false;

  constructor() {
    this.config = {
      enableLogging: process.env.NODE_ENV === 'development',
      enableReporting: process.env.NODE_ENV === 'production',
      showUserNotifications: true
    };
  }

  /**
   * Initialize global error handlers
   */
  initialize(): void {
    if (this.isInitialized || typeof window === 'undefined') {
      return;
    }

    // Handle unhandled JavaScript errors
    window.addEventListener('error', this.handleGlobalError.bind(this));

    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', this.handleUnhandledRejection.bind(this));

    // Handle React errors (if not caught by error boundaries)
    if (process.env.NODE_ENV === 'development') {
      const originalConsoleError = console.error;
      console.error = (...args: any[]) => {
        // Check if this is a React error
        if (args[0] && typeof args[0] === 'string' && args[0].includes('React')) {
          this.handleReactError(args);
        }
        originalConsoleError.apply(console, args);
      };
    }

    this.isInitialized = true;
    logger.debug(LogCategory.GENERAL, 'Global error handler initialized');
  }

  /**
   * Handle global JavaScript errors
   */
  private handleGlobalError(event: ErrorEvent): void {
    const error = event.error || new Error(event.message);

    // Handle Next.js 15 params read-only error gracefully
    if (error.message && error.message.includes('Cannot assign to read only property')) {
      // Silently handle this known issue
      event.preventDefault();
      return;
    }

    if (this.config.enableLogging) {
      logger.error(LogCategory.GENERAL, 'Unhandled global error', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: error.stack
      });
    }

    // Process error through unified handler
    handleError(error, {
      component: 'global-error-handler',
      operation: 'unhandled-error',
      metadata: {
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno
      }
    });

    // Show user-friendly notification
    if (this.config.showUserNotifications) {
      this.showUserNotification('An unexpected error occurred. Please refresh the page and try again.');
    }

    // Prevent the default browser error handling
    event.preventDefault();
  }

  /**
   * Handle unhandled promise rejections
   */
  private handleUnhandledRejection(event: PromiseRejectionEvent): void {
    const error = event.reason instanceof Error ? event.reason : new Error(String(event.reason));

    // Handle Next.js 15 params read-only error gracefully
    if (error.message && error.message.includes('Cannot assign to read only property')) {
      // Silently handle this known issue
      event.preventDefault();
      return;
    }

    if (this.config.enableLogging) {
      logger.error(LogCategory.GENERAL, 'Unhandled promise rejection', {
        reason: event.reason,
        stack: error.stack
      });
    }

    // Process error through unified handler
    handleError(error, {
      component: 'global-error-handler',
      operation: 'unhandled-promise-rejection',
      metadata: {
        reason: String(event.reason)
      }
    });

    // Show user-friendly notification
    if (this.config.showUserNotifications) {
      this.showUserNotification('A background operation failed. Please try again.');
    }

    // Prevent the default browser handling
    event.preventDefault();
  }

  /**
   * Handle React-specific errors
   */
  private handleReactError(args: any[]): void {
    if (this.config.enableLogging) {
      logger.error(LogCategory.UI, 'React error detected', { args });
    }

    // Don't show notifications for React errors as they should be handled by error boundaries
  }

  /**
   * Show user-friendly notification
   */
  private showUserNotification(message: string): void {
    // Try to use the app's notification system if available
    if (typeof window !== 'undefined' && (window as any).showNotification) {
      (window as any).showNotification({
        type: 'error',
        message,
        duration: UI.TOAST_DURATION
      });
      return;
    }

    // Fallback to browser notification (only in development)
    if (process.env.NODE_ENV === 'development') {
      // Create a simple toast notification
      const toast = document.createElement('div');
      toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #ef4444;
        color: white;
        padding: 12px 16px;
        border-radius: 6px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        z-index: 10000;
        max-width: 400px;
        font-family: system-ui, -apple-system, sans-serif;
        font-size: 14px;
      `;
      toast.textContent = message;
      
      document.body.appendChild(toast);
      
      // Remove after configured duration
      setTimeout(() => {
        if (toast.parentNode) {
          toast.parentNode.removeChild(toast);
        }
      }, UI.TOAST_DURATION);
    }
  }

  /**
   * Manually report an error
   */
  reportError(error: Error, context?: any): void {
    if (this.config.enableLogging) {
      logger.error(LogCategory.GENERAL, 'Manually reported error', { error: error.message, context });
    }

    handleError(error, {
      component: 'manual-report',
      operation: 'manual-error-report',
      metadata: context
    });
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<GlobalErrorConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }
}

// Export singleton instance
export const globalErrorHandler = new GlobalErrorHandler();

// Auto-initialize in browser environment
if (typeof window !== 'undefined') {
  // Initialize after DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      globalErrorHandler.initialize();
    });
  } else {
    globalErrorHandler.initialize();
  }
}

export type { GlobalErrorConfig };

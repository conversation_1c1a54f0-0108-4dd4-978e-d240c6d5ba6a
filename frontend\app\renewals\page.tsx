/**
 * Renewals Inventory Page
 * 
 * Main page for managing renewals inventory with table view, filtering, and actions
 */

'use client'

import { useState, useCallback, useMemo } from 'react'
import { useRenewals, RenewalsFilters as RenewalsFiltersType, usePost } from '@/lib/hooks'
// Note: usePerformanceMonitor moved to server-side only

// Components
import { PageHeader } from '@/components/ui'
import { usePageInfoByName } from '@/lib/hooks/usePageInfo'
import RenewalsTable from '@/components/renewals/RenewalsTable'
import RenewalsFiltersComponent from '@/components/renewals/RenewalsFilters'
import RenewalTimeline from '@/components/renewals/RenewalTimeline'
import AddRenewalModal, { RenewalFormData, AlertFormData } from '@/components/modals/AddRenewalModal'
import ImportCSVModal from '@/components/renewals/ImportCSVModal'
import CollapsibleSection from '@/components/ui/CollapsibleSection'

export default function RenewalsPage() {
  // Performance monitoring - removed for now
  // usePerformanceMonitor('RenewalsPage')

  // Get page info for the header
  const { pageInfo } = usePageInfoByName('renewals')

  // Context
  const {
    renewals,
    filterOptions,
    isLoading,
    fetchRenewals
  } = useRenewals()

  // Consolidated API hooks - will be updated with payload in handleSubmitRenewal

  // State
  const [filters, setFilters] = useState<RenewalsFiltersType>({
    search: '',
    vendor: '',
    type: '',
    status: ''
  })
  const [isAddRenewalModalOpen, setIsAddRenewalModalOpen] = useState(false)
  const [isImportCSVModalOpen, setIsImportCSVModalOpen] = useState(false)
  const [showArchivedSection, setShowArchivedSection] = useState(true)
  const [showTimelineSection, setShowTimelineSection] = useState(false)

  // Separate active and archived renewals
  const { activeRenewals, archivedRenewals } = useMemo(() => {
    const active = renewals.filter(renewal => renewal.status === 'active')
    const archived = renewals.filter(renewal =>
      renewal.status === 'inactive' ||
      renewal.status === 'expired'
    )

    return { activeRenewals: active, archivedRenewals: archived }
  }, [renewals])

  // Filter active renewals based on current filters
  const filteredActiveRenewals = useMemo(() => {
    return activeRenewals.filter(renewal => {
      const matchesSearch = !filters.search ||
        renewal.name.toLowerCase().includes(filters.search.toLowerCase()) ||
        renewal.product_name?.toLowerCase().includes(filters.search.toLowerCase()) ||
        renewal.vendor?.toLowerCase().includes(filters.search.toLowerCase())

      const matchesVendor = !filters.vendor || filters.vendor === 'All Vendors' || renewal.vendor === filters.vendor
      const matchesType = !filters.type || filters.type === 'All Renewal Types' || renewal.type === filters.type
      const matchesStatus = !filters.status || filters.status === 'All Statuses' || renewal.status === filters.status

      return matchesSearch && matchesVendor && matchesType && matchesStatus
    })
  }, [activeRenewals, filters])

  // Filter archived renewals based on current filters (excluding status filter)
  const filteredArchivedRenewals = useMemo(() => {
    return archivedRenewals.filter(renewal => {
      const matchesSearch = !filters.search ||
        renewal.name.toLowerCase().includes(filters.search.toLowerCase()) ||
        renewal.product_name?.toLowerCase().includes(filters.search.toLowerCase()) ||
        renewal.vendor?.toLowerCase().includes(filters.search.toLowerCase())

      const matchesVendor = !filters.vendor || filters.vendor === 'All Vendors' || renewal.vendor === filters.vendor
      const matchesType = !filters.type || filters.type === 'All Renewal Types' || renewal.type === filters.type

      return matchesSearch && matchesVendor && matchesType
    })
  }, [archivedRenewals, filters])

  // Event handlers
  const handleSearch = useCallback((query: string) => {
    setFilters(prev => ({ ...prev, search: query }))
  }, [])

  const handleFilterChange = useCallback((filterType: keyof RenewalsFiltersType, value: string) => {
    setFilters(prev => ({ ...prev, [filterType]: value }))
  }, [])

  const handleAddRenewal = useCallback(() => {
    setIsAddRenewalModalOpen(true)
  }, [])

  const handleCloseAddRenewalModal = useCallback(() => {
    setIsAddRenewalModalOpen(false)
  }, [])

  const handleSubmitRenewal = useCallback(async (renewalData: RenewalFormData, alertData: AlertFormData[]) => {
    try {
      console.log('Submitting renewal:', renewalData, alertData)

      // Transform renewal items to API format
      const renewalItems = renewalData.renewalItems.map(item => ({
        product_id: item.productId,
        version_id: item.versionId,
        license_count: item.licenseCount,
        unit_cost: (item as any).unitCost || 0,
        total_cost: item.totalCost,
        cost_code: item.costCode,
        notes: item.notes
      }))

      // Prepare API payload
      const apiPayload = {
        renewal_name: renewalData.renewalName,
        vendor_id: renewalData.vendorId,
        renewal_type_id: renewalData.renewalTypeId,
        department: renewalData.department,
        purchase_type_id: renewalData.purchaseTypeId,

        start_date: renewalData.start_date,
        associated_emails: (renewalData as any).associatedEmails,
        reseller: renewalData.reseller,
        currency_id: renewalData.currencyId,
        total_cost: renewalData.totalCost,
        cost_code: renewalData.costCode,
        description: renewalData.description,
        notes: renewalData.notes,
        renewal_items: renewalItems
      }

      // Create the renewal using consolidated data hook
      const createRenewal = usePost('/api/tenant-renewals', apiPayload)
      const result = await createRenewal.fetch()
      console.log('Renewal created successfully:', result)

      // Implementation note: Alert creation is handled separately via alert management API

      setIsAddRenewalModalOpen(false)

      // Refresh renewals list
      await fetchRenewals()

    } catch (error) {
      console.error('Failed to submit renewal:', error)
      throw error // Re-throw so the modal can handle the error
    }
  }, [fetchRenewals])

  const handleRenewalAction = useCallback((renewalId: string, action: string) => {
    console.log('Renewal action:', action, 'for renewal:', renewalId)
    // Actions are now handled by the RenewalActionsMenu component
  }, [])

  const handleImportCSV = useCallback(() => {
    setIsImportCSVModalOpen(true)
  }, [])

  const handleCloseImportCSVModal = useCallback(() => {
    setIsImportCSVModalOpen(false)
  }, [])

  const handleCSVImport = useCallback(async (file: File) => {
    try {
      console.log('Importing CSV file:', file.name)
      // TODO: Implement actual CSV import API call
      // For now, just close the modal
      setIsImportCSVModalOpen(false)
    } catch (error) {
      console.error('Failed to import CSV:', error)
      throw error
    }
  }, [])

  // Filter active renewals for the main table (only show active renewals in main table)
  const filteredMainRenewals = useMemo(() => {
    return activeRenewals.filter(renewal => {
      const matchesSearch = !filters.search ||
        renewal.name.toLowerCase().includes(filters.search.toLowerCase()) ||
        renewal.product_name?.toLowerCase().includes(filters.search.toLowerCase()) ||
        renewal.vendor?.toLowerCase().includes(filters.search.toLowerCase())

      const matchesVendor = !filters.vendor || filters.vendor === 'All Vendors' || renewal.vendor === filters.vendor
      const matchesType = !filters.type || filters.type === 'All Renewal Types' || renewal.type === filters.type

      return matchesSearch && matchesVendor && matchesType
    })
  }, [activeRenewals, filters])

  return (
    <div className="overview-container">
      {/* Page Header */}
      <PageHeader
        title={pageInfo?.header || 'Renewals Inventory'}
        subtitle={pageInfo?.description || 'Manage your subscriptions, maintenance, support and warranties'}
        searchPlaceholder="Search renewals..."
        searchValue={filters.search}
        onSearchChange={handleSearch}
        actions={[
          {
            label: 'Import CSV',
            onClick: handleImportCSV,
            variant: 'secondary',
            leftIcon: '📄'
          },
          {
            label: 'Add Renewal',
            onClick: handleAddRenewal,
            variant: 'primary',
            leftIcon: '+'
          }
        ]}
      />

      {/* Filters Section */}
      <div className="filters-section mb-6">
        <select
          value={filters.vendor}
          onChange={(e) => handleFilterChange('vendor', e.target.value)}
          className="filter-select"
        >
          <option value="">All Vendors</option>
          {filterOptions.vendors.map(vendor => (
            <option key={vendor} value={vendor}>{vendor}</option>
          ))}
        </select>
      </div>

      {/* Main Renewals Table - Shows only active renewals */}
      <div className="section-container mb-6">
        <div className="section-header">
          <h2 className="section-title">Active Renewals</h2>
          <p className="section-subtitle">Currently active subscriptions and renewals</p>
        </div>
        <RenewalsTable
          renewals={filteredMainRenewals}
          isLoading={isLoading}
          onRenewalAction={handleRenewalAction}
        />
      </div>

      {/* Archived Renewals Section */}
      <CollapsibleSection
        title="Archived Renewals"
        subtitle="View previously processed or cancelled renewals"
        count={filteredArchivedRenewals.length}
        icon="📁"
        isOpen={showArchivedSection}
        onToggle={() => setShowArchivedSection(!showArchivedSection)}
        className="mb-6"
      >
        <RenewalsTable
          renewals={filteredArchivedRenewals}
          isLoading={isLoading}
          onRenewalAction={handleRenewalAction}
        />
      </CollapsibleSection>

      {/* Renewal Timeline Section */}
      <CollapsibleSection
        title="Renewal Timeline"
        subtitle="Upcoming renewals for this month and next month"
        icon="📅"
        isOpen={showTimelineSection}
        onToggle={() => setShowTimelineSection(!showTimelineSection)}
        className="mb-6"
      >
        <RenewalTimeline
          renewals={renewals}
          isLoading={isLoading}
          maxEvents={15}
        />
      </CollapsibleSection>

      {/* Add Renewal Modal */}
      <AddRenewalModal
        isOpen={isAddRenewalModalOpen}
        onClose={handleCloseAddRenewalModal}
        onSubmit={handleSubmitRenewal}
      />

      {/* Import CSV Modal */}
      <ImportCSVModal
        isOpen={isImportCSVModalOpen}
        onClose={handleCloseImportCSVModal}
        onImport={handleCSVImport}
      />
    </div>
  )
}

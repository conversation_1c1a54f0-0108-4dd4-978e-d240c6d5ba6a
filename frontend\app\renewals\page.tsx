/**
 * Renewals Inventory Page
 * 
 * Main page for managing renewals inventory with table view, filtering, and actions
 */

'use client'

import { useState, useCallback, useMemo } from 'react'
import { useRenewals } from '@/lib/hooks'
import type { RenewalsFilters } from '@/lib/hooks/useRenewals'
import { Renewal } from '@/lib/types'

// Components
import { PageHeader } from '@/components/ui'
import { usePageInfoByName } from '@/lib/hooks/usePageInfo'
import RenewalsTable from '@/components/renewals/RenewalsTable'
import RenewalsFiltersComponent from '@/components/renewals/RenewalsFilters'
import RenewalTimeline from '@/components/renewals/RenewalTimeline'
import AddRenewalModal, { RenewalFormData, AlertFormData } from '@/components/modals/AddRenewalModal'
import ImportCSVModal from '@/components/renewals/ImportCSVModal'
import CollapsibleSection from '@/components/ui/CollapsibleSection'

export default function RenewalsPage() {
  // Performance monitoring - removed for now
  // usePerformanceMonitor('RenewalsPage')

  // Get page info for the header
  const { pageInfo } = usePageInfoByName('renewals')

  // Context
  const { renewals, filteredRenewals, filterOptions, isLoading, fetchRenewals, deleteRenewal, processRenewal, updateRenewal, createRenewal } = useRenewals()

  // Consolidated API hooks - will be updated with payload in handleSubmitRenewal
  // State
  const [filters, setFilters] = useState<RenewalsFilters>({
    search: '',
    vendor: 'All Vendors',
    type: 'All Renewal Types',
    status: 'all',
    dateFrom: '',
    dateTo: ''
  })

  const [isAddRenewalModalOpen, setIsAddRenewalModalOpen] = useState(false)
  const [isImportCSVModalOpen, setIsImportCSVModalOpen] = useState(false)
  const [showArchivedSection, setShowArchivedSection] = useState(true)
  const [showTimelineSection, setShowTimelineSection] = useState(false)

  // Separate filtered renewals into active and archived
  const activeRenewals = useMemo(() =>
    filteredRenewals?.filter((renewal: Renewal) => renewal.status === 'active') || [],
    [filteredRenewals]
  )

  const archivedRenewals = useMemo(() =>
    filteredRenewals?.filter((renewal: Renewal) =>
      renewal.status === 'inactive' ||
      renewal.status === 'expired'
    ) || [],
    [filteredRenewals]
  )

  // Event handlers
  const handleSearch = useCallback((query: string) => {
    setFilters(prev => ({ ...prev, search: query }))
  }, [])

  const handleFilterChange = useCallback((field: keyof RenewalsFilters, value: string) => {
    setFilters((prev: RenewalsFilters) => {
      const newFilters: RenewalsFilters = {
        ...prev,
        [field]: value
      }
      return newFilters
    })
  }, [])

  const handleAddRenewal = useCallback(() => {
    setIsAddRenewalModalOpen(true)
  }, [])

  const handleCloseAddRenewalModal = useCallback(() => {
    setIsAddRenewalModalOpen(false)
  }, [])

  const handleSubmitRenewal = useCallback(async (renewalData: RenewalFormData, alertData: AlertFormData[]) => {
    try {
      console.log('Submitting renewal:', renewalData, alertData)

      // Transform renewal items to API format
      const renewalItems = renewalData.renewalItems.map(item => ({
        product_id: item.productId,
        version_id: item.versionId,
        license_count: item.licenseCount,
        unit_cost: (item as any).unitCost || 0,
        total_cost: item.totalCost,
        cost_code: item.costCode,
        notes: item.notes
      }))

      // Prepare API payload
      const apiPayload: Omit<Renewal, 'id'> = {
        name: renewalData.renewalName,
        vendor: renewalData.vendorName,
        vendor_id: renewalData.vendorId,
        status: 'active',
        start_date: renewalData.start_date,
        description: renewalData.description,
        department: renewalData.department,
        reseller: renewalData.reseller,
        cost_code: renewalData.costCode,
        created_on: new Date(),
        changed_on: new Date(),
        version: 1
      }

      // Create the renewal using the useRenewals hook
      await createRenewal(apiPayload)
      console.log('Renewal created successfully')

      // Implementation note: Alert creation is handled separately via alert management API

      setIsAddRenewalModalOpen(false)

      // The renewals list will be automatically refreshed by the hook

    } catch (error) {
      console.error('Failed to submit renewal:', error)
      throw error // Re-throw so the modal can handle the error
    }
  }, [fetchRenewals])

  const handleRenewalAction = useCallback((renewalId: string, action: string) => {
    console.log('Renewal action:', action, 'for renewal:', renewalId)
    // Actions are now handled by the RenewalActionsMenu component
  }, [])

  const handleImportCSV = useCallback(() => {
    setIsImportCSVModalOpen(true)
  }, [])

  const handleCloseImportCSVModal = useCallback(() => {
    setIsImportCSVModalOpen(false)
  }, [])

  const handleCSVImport = useCallback(async (file: File) => {
    try {
      console.log('Importing CSV file:', file.name)
      // TODO: Implement actual CSV import API call
      // For now, just close the modal
      setIsImportCSVModalOpen(false)
    } catch (error) {
      console.error('Failed to import CSV:', error)
      throw error
    }
  }, [])

  return (
    <div className="overview-container">
      {/* Page Header */}
      <PageHeader
        title={pageInfo?.header || 'Renewals Inventory'}
        subtitle={pageInfo?.description || 'Manage your subscriptions, maintenance, support and warranties'}
        searchPlaceholder="Search renewals..."
        searchValue={filters.search}
        onSearchChange={handleSearch}
        actions={[
          {
            label: 'Import CSV',
            onClick: handleImportCSV,
            variant: 'secondary',
            leftIcon: '📄'
          },
          {
            label: 'Add Renewal',
            onClick: handleAddRenewal,
            variant: 'primary',
            leftIcon: '+'
          }
        ]}
      />

      {/* Filters Section */}
      <div className="filters-section mb-6">
        <select
          value={filters.vendor}
          onChange={(e) => handleFilterChange('vendor', e.target.value)}
          className="filter-select"
        >
          <option value="">All Vendors</option>
          {filterOptions.vendors.map((vendorOption) => (
            <option key={vendorOption.value} value={vendorOption.value}>{vendorOption.label}</option>
          ))}
        </select>
      </div>

      {/* Main Renewals Table - Shows only active renewals */}
      <div className="section-container mb-6">
        <div className="section-header">
          <h2 className="section-title">Active Renewals</h2>
          <p className="section-subtitle">Currently active subscriptions and renewals</p>
        </div>
        <RenewalsTable
          renewals={activeRenewals || []}
          isLoading={isLoading}
          onRenewalAction={handleRenewalAction}
        />
      </div>

      {/* Archived Renewals Section */}
      <CollapsibleSection
        title="Archived Renewals"
        subtitle="View previously processed or cancelled renewals"
        count={archivedRenewals.length}
        icon="📁"
        isOpen={showArchivedSection}
        onToggle={() => setShowArchivedSection(!showArchivedSection)}
        className="mb-6"
      >
        <RenewalsTable
          renewals={archivedRenewals || []}
          isLoading={isLoading}
          onRenewalAction={handleRenewalAction}
        />
      </CollapsibleSection>

      {/* Renewal Timeline Section */}
      <CollapsibleSection
        title="Renewal Timeline"
        subtitle="Upcoming renewals for this month and next month"
        icon="📅"
        isOpen={showTimelineSection}
        onToggle={() => setShowTimelineSection(!showTimelineSection)}
        className="mb-6"
      >
        <RenewalTimeline
          renewals={renewals}
          isLoading={isLoading}
          maxEvents={15}
        />
      </CollapsibleSection>

      {/* Add Renewal Modal */}
      <AddRenewalModal
        isOpen={isAddRenewalModalOpen}
        onClose={handleCloseAddRenewalModal}
        onSubmit={handleSubmitRenewal}
      />

      {/* Import CSV Modal */}
      <ImportCSVModal
        isOpen={isImportCSVModalOpen}
        onClose={handleCloseImportCSVModal}
        onImport={handleCSVImport}
      />
    </div>
  )
}

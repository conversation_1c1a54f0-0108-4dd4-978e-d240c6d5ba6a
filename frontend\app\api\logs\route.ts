/**
 * Logs Collection API Endpoint
 * 
 * Receives and processes log entries from the frontend
 */

import { createApiRoute } from '@/lib/api/route-factory'
import { LogEntry } from '@/lib/monitoring/logger'
import { handleError } from '@/lib/utils/error-handler'
import { z } from 'zod'

const logsSchema = z.object({
  logs: z.array(z.object({
    timestamp: z.string(),
    level: z.enum(['debug', 'info', 'warn', 'error']),
    message: z.string(),
    context: z.record(z.any()).optional(),
    error: z.any().optional()
  }))
})

// POST /api/logs - Receive log entries from frontend
export const POST = createApiRoute('POST', {
  requireAuth: false, // Allow unauthenticated logging for error reporting
  bodySchema: logsSchema,
  handler: async (context) => {
    if (!context.body) {
      throw new Error('Invalid request data. Please try again.');
    }
    const { logs } = context.body;

    // Process each log entry
    for (const logEntry of logs as LogEntry[]) {
      await processLogEntry(logEntry);
    }

    return {
      processed: logs.length,
      message: `Processed ${logs.length} log entries`
    };
  }
})

async function processLogEntry(logEntry: LogEntry): Promise<void> {
  try {
    // In development, just log to console
    if (process.env.NODE_ENV === 'development') {
      const { timestamp, level, message, context, error } = logEntry;
      const prefix = `[${timestamp}] [${level.toUpperCase()}]`;
      
      switch (level) {
        case 'debug':
          console.debug(prefix, message, context, error)
          break
        case 'info':
          console.info(prefix, message, context, error)
          break
        case 'warn':
          console.warn(prefix, message, context, error)
          break
        case 'error':
        case 'fatal':
          console.error(prefix, message, context, error)
          break
      }
    }
    
    // In production, logs are processed and can be collected by external services
    // Examples: Elasticsearch, CloudWatch, Datadog, etc.
    if (process.env.NODE_ENV === 'production') {
      // Production logs are handled by the logging infrastructure
      console.log('Production log entry processed:', logEntry.level, logEntry.message)
    }
    
    // Store critical errors in database for immediate attention
    if (logEntry.level === 'fatal' || logEntry.level === 'error') {
      await storeCriticalError(logEntry)
    }
    
  } catch (error) {
    handleError(error as Error, {
      component: 'log-processor',
      operation: 'processLogEntry',
      metadata: { logEntry }
    });
  }
}

async function storeCriticalError(logEntry: LogEntry): Promise<void> {
  try {
    // Implementation note: Critical errors would be stored in database for alerting
    // Currently logs to console - implement database storage when monitoring is added
    console.error('CRITICAL ERROR:', logEntry)
  } catch (error) {
    handleError(error as Error, {
      component: 'log-processor',
      operation: 'storeCriticalError',
      metadata: { logEntry }
    });
  }
}

'use client'

/**
 * License Management Page
 * 
 * Admin interface for activating licenses and viewing license consumption
 */

import React, { useState, useEffect } from 'react'
import { Button, Form, Modal, useToast } from '@/components/ui'
import { useAuth, useData } from '@/lib/hooks'

interface License {
  client_license_id: number
  license_key: string
  license_type: string
  max_renewals: number
  current_renewals: number
  usage_percentage: number
  activated_at: string
  expires_at: string
  status: string
  is_expired: boolean
  renewals_remaining: number
}

interface LicenseManagementData {
  licenses: License[]
  current_renewal_count: number
  license_compliance: {
    is_compliant: boolean
    total_allowed: number
    total_used: number
    warnings: string[]
  }
  tenant_info: {
    client_id: number
    name: string
    tenant_schema: string
  }
}

interface UsageHistory {
  usage_id: number
  renewal_count_change: number
  previous_count: number
  new_count: number
  changed_at: string
  changed_by: string
  notes?: string
  license_key: string
  license_type: string
}

export default function LicenseManagementPage() {
  const { user } = useAuth()
  const toast = useToast()
  
  const [activating, setActivating] = useState(false)
  const [showActivateDialog, setShowActivateDialog] = useState(false)
  const [licenseKey, setLicenseKey] = useState('')

  // Access control is handled by PageAccessGuard wrapper - no hardcoded checks needed

  // Use the unified data hook for license management data
  const {
    data: licenseData,
    loading,
    error: licenseError,
    refetch: refetchLicenseData
  } = useData({
    endpoint: '/api/license-management',
    cache: {
      key: 'license-management-data',
      ttl: 60000 // 1 minute cache
    },
    options: {
      enabled: true // Page access is controlled by routing, not data fetching
    }
  })

  // Use the unified data hook for usage history
  const {
    data: usageHistoryData,
    loading: usageLoading,
    refetch: refetchUsageHistory
  } = useData({
    endpoint: '/api/license-management/usage-history?limit=20',
    cache: {
      key: 'license-usage-history',
      ttl: 30000 // 30 seconds cache
    },
    options: {
      enabled: true // Page access is controlled by routing, not data fetching
    }
  })

  const usageHistory = usageHistoryData?.usage_history || []

  const handleActivateLicense = async () => {
    if (!licenseKey.trim()) {
      toast.error('Please enter a license key', 'Error')
      return
    }

    try {
      setActivating(true)
      
      const response = await fetch('/api/admin/client-licenses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          license_key: licenseKey.trim()
        })
      })
      
      const data = await response.json()
      
      if (data.success) {
        toast.success('License activated successfully', 'Success')
        
        setShowActivateDialog(false)
        setLicenseKey('')
        refetchLicenseData()
        refetchUsageHistory()
      } else {
        toast.error(data.message || 'Failed to activate license', 'Error')
      }
    } catch (error) {
      console.error('Error activating license:', error)
      toast.error('Failed to activate license', 'Error')
    } finally {
      setActivating(false)
    }
  }

  const getStatusIcon = (license: License) => {
    if (license.is_expired) return '❌'
    if (license.status === 'ACTIVE') return '✅'
    return '⏰'
  }

  const getUsageColor = (percentage: number) => {
    if (percentage >= 90) return 'text-red-600'
    if (percentage >= 75) return 'text-yellow-600'
    return 'text-green-600'
  }

  if (!isAdmin) {
    return (
      <div className="container">
        <div className="card">
          <p>You need admin privileges to access the License Management page.</p>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="container">
        <div className="text-center py-8">Loading license information...</div>
      </div>
    )
  }

  return (
    <div className="container">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">License Management</h1>
          <p className="text-secondary">
            Manage your licenses and monitor renewal usage
          </p>
        </div>

        <Button onClick={() => setShowActivateDialog(true)}>
          ➕ Activate License
        </Button>
      </div>

      {/* License Compliance Alert */}
      {licenseData?.license_compliance && !licenseData.license_compliance.is_compliant && (
        <div className="card" style={{ backgroundColor: '#fef2f2', borderColor: '#fecaca' }}>
          <div className="flex items-start gap-2">
            <span className="text-red-500">⚠️</span>
            <div>
              <div className="font-semibold text-red-800">License Compliance Issues:</div>
              {licenseData.license_compliance.warnings.map((warning: any, index: number) => (
                <div key={index} className="text-red-700">• {warning}</div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* License Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <div className="stat-card">
          <div className="stat-icon text-blue-500">🛡️</div>
          <h3>Active Licenses</h3>
          <p className="stat-value">
            {licenseData?.licenses?.filter((l: any) => l.status === 'ACTIVE' && !l.is_expired).length || 0}
          </p>
        </div>

        <div className="stat-card">
          <div className="stat-icon text-green-500">🔑</div>
          <h3>Total Renewals Allowed</h3>
          <p className="stat-value">
            {licenseData?.license_compliance?.total_allowed?.toLocaleString() || 0}
          </p>
        </div>

        <div className="stat-card">
          <div className="stat-icon text-orange-500">📈</div>
          <h3>Renewals Used</h3>
          <p className="stat-value">
            {licenseData?.current_renewal_count?.toLocaleString() || 0}
          </p>
        </div>

        <div className="stat-card">
          <div className="stat-icon">📊</div>
          <h3>Usage</h3>
          <p className={`stat-value ${getUsageColor(
            licenseData?.license_compliance?.total_allowed
              ? (licenseData.current_renewal_count / licenseData.license_compliance.total_allowed) * 100
              : 0
          )}`}>
            {licenseData?.license_compliance?.total_allowed
              ? Math.round((licenseData.current_renewal_count / licenseData.license_compliance.total_allowed) * 100)
              : 0}%
          </p>
        </div>
      </div>

      {/* License Details */}
      <div className="card">
        <div className="card-header">
          <h2>License Details</h2>
        </div>
        <div className="card-content">
          {licenseData?.licenses && licenseData.licenses.length > 0 ? (
            <div className="space-y-4">
              {licenseData.licenses.map((license: any) => (
                <div key={license.client_license_id} className="card p-4">
                  <div className="flex items-start justify-between">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <span>{getStatusIcon(license)}</span>
                        <code className="text-sm bg-gray-100 px-2 py-1 rounded">{license.license_key}</code>
                        <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-sm">
                          {license.license_type}
                        </span>
                        <span className={`px-2 py-1 rounded text-sm ${
                          license.is_expired
                            ? 'bg-red-100 text-red-800'
                            : 'bg-green-100 text-green-800'
                        }`}>
                          {license.status}
                        </span>
                      </div>

                      <div className="text-sm text-secondary">
                        Activated: {new Date(license.activated_at).toLocaleDateString()}
                        {license.expires_at && (
                          <> • Expires: {new Date(license.expires_at).toLocaleDateString()}</>
                        )}
                      </div>
                    </div>

                    <div className="text-right">
                      <div className="text-sm font-medium">
                        {license.current_renewals.toLocaleString()} / {license.max_renewals.toLocaleString()}
                      </div>
                      <div className="text-xs text-secondary">
                        {license.renewals_remaining.toLocaleString()} remaining
                      </div>
                    </div>
                  </div>

                  <div className="mt-3">
                    <div className="flex justify-between text-sm mb-1">
                      <span>Usage</span>
                      <span className={getUsageColor(license.usage_percentage)}>
                        {license.usage_percentage}%
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${
                          license.usage_percentage >= 90 ? 'bg-red-500' :
                          license.usage_percentage >= 75 ? 'bg-yellow-500' : 'bg-green-500'
                        }`}
                        style={{ width: `${license.usage_percentage}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-secondary">
              No active licenses found. Click "Activate License" to add a license key.
            </div>
          )}
        </div>
      </div>

      {/* Usage History */}
      <div className="card">
        <div className="card-header">
          <h2>Usage History</h2>
        </div>
        <div className="card-content">
          {usageHistory.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-2">Date</th>
                    <th className="text-left py-2">Change</th>
                    <th className="text-left py-2">Previous</th>
                    <th className="text-left py-2">New Count</th>
                    <th className="text-left py-2">License</th>
                    <th className="text-left py-2">Changed By</th>
                  </tr>
                </thead>
                <tbody>
                  {usageHistory.map((usage: any) => (
                    <tr key={usage.usage_id} className="border-b">
                      <td className="py-2">
                        {new Date(usage.changed_at).toLocaleDateString()}
                      </td>
                      <td className="py-2">
                        <span className={`px-2 py-1 rounded text-sm ${
                          usage.renewal_count_change > 0
                            ? 'bg-green-100 text-green-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {usage.renewal_count_change > 0 ? '+' : ''}{usage.renewal_count_change}
                        </span>
                      </td>
                      <td className="py-2">{usage.previous_count}</td>
                      <td className="py-2">{usage.new_count}</td>
                      <td className="py-2">
                        <code className="text-xs">{usage.license_key}</code>
                      </td>
                      <td className="py-2">{usage.changed_by}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-8 text-secondary">
              No usage history available.
            </div>
          )}
        </div>
      </div>

      {/* Activate License Modal */}
      <Modal
        isOpen={showActivateDialog}
        onClose={() => setShowActivateDialog(false)}
        title="Activate License Key"
        subtitle="Enter your license key to activate it for your organization"
        size="md"
      >
        <Form.Root onSubmit={(e) => { e.preventDefault(); handleActivateLicense(); }}>
          <Form.Field>
            <Form.Label htmlFor="license_key" required>License Key</Form.Label>
            <Form.Input
              id="license_key"
              placeholder="ABCD-1234-5678-9012"
              value={licenseKey}
              onChange={(e) => setLicenseKey(e.target.value)}
              style={{ fontFamily: 'monospace' }}
            />
          </Form.Field>

          <Form.Actions>
            <Button
              variant="outline"
              onClick={() => setShowActivateDialog(false)}
              type="button"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={!licenseKey.trim() || activating}
            >
              {activating ? 'Activating...' : 'Activate'}
            </Button>
          </Form.Actions>
        </Form.Root>
      </Modal>
    </div>
  )
}

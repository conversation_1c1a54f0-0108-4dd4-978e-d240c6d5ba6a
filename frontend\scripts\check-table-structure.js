#!/usr/bin/env node

/**
 * Check Table Structure Script
 * 
 * This script checks the actual column names in the tenant_renewals table
 * to determine which naming convention is being used.
 */

import pkg from 'pg';
const { Pool } = pkg;
import dotenv from 'dotenv';
dotenv.config({ path: '.env.local' });

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.DATABASE_SSL === 'true' ? { rejectUnauthorized: false } : false,
});

async function checkTableStructure() {
  console.log('🔍 Checking tenant_renewals table structure...\n');

  try {
    const client = await pool.connect();
    
    // Check tenant_renewals table structure
    console.log('1. Checking tenant_renewals table columns in tenant schemas...');
    const renewalsResult = await client.query(`
      SELECT column_name, data_type, is_nullable, column_default, table_schema
      FROM information_schema.columns
      WHERE table_schema LIKE 'tenant_%'
      AND table_name = 'tenant_renewals'
      ORDER BY table_schema, ordinal_position
    `);
    
    if (renewalsResult.rows.length === 0) {
      console.log('❌ tenant_renewals table not found');
      
      // Check if there's a Renewals table instead
      console.log('\n2. Checking for Renewals table (capitalized) in tenant schemas...');
      const renewalsCapResult = await client.query(`
        SELECT column_name, data_type, is_nullable, column_default, table_schema
        FROM information_schema.columns
        WHERE table_schema LIKE 'tenant_%'
        AND table_name = 'Renewals'
        ORDER BY table_schema, ordinal_position
      `);
      
      if (renewalsCapResult.rows.length > 0) {
        console.log('✅ Found Renewals table(s) in tenant schema(s)');
        console.log('📋 Renewals table columns:');
        renewalsCapResult.rows.forEach(row => {
          console.log(`   - ${row.table_schema}.${row.column_name} (${row.data_type})`);
        });
      } else {
        console.log('❌ No renewals table found');
      }
    } else {
      console.log('✅ Found tenant_renewals table (lowercase)');
      console.log('📋 tenant_renewals table columns:');
      renewalsResult.rows.forEach(row => {
        console.log(`   - ${row.column_name} (${row.data_type})`);
      });
    }
    
    // Check tenant_vendors table
    console.log('\n3. Checking tenant_vendors table...');
    const vendorsResult = await client.query(`
      SELECT column_name, data_type
      FROM information_schema.columns 
      WHERE table_schema = 'tenant_0000000000000001' 
      AND table_name = 'tenant_vendors'
      ORDER BY ordinal_position
    `);
    
    if (vendorsResult.rows.length > 0) {
      console.log('✅ Found tenant_vendors table');
      console.log('📋 tenant_vendors table columns:');
      vendorsResult.rows.forEach(row => {
        console.log(`   - ${row.column_name} (${row.data_type})`);
      });
    } else {
      console.log('❌ tenant_vendors table not found');
    }
    
    // Check all tables in tenant schema
    console.log('\n4. All tables in tenant_0000000000000001 schema:');
    const allTablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'tenant_0000000000000001'
      ORDER BY table_name
    `);
    
    allTablesResult.rows.forEach(row => {
      console.log(`   - ${row.table_name}`);
    });

    client.release();
    
    console.log('\n🎉 Table structure check completed!');

  } catch (error) {
    console.error('❌ Table structure check failed:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Run the check
checkTableStructure().catch(console.error);

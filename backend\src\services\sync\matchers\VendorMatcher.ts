/**
 * Vendor Matching Algorithm
 * 
 * Implements sophisticated vendor matching logic with multiple strategies:
 * 1. Exact Tax ID match (95% confidence)
 * 2. Domain + Name match (90% confidence)
 * 3. Fuzzy Name + Address match (70-85% confidence)
 * 
 * Uses Levenshtein distance and other string similarity algorithms
 * for fuzzy matching with configurable thresholds.
 */

import { Logger } from '../../Logger'
import { TenantVendor, GlobalVendor, VendorMatch } from '../processors/VendorSyncProcessor'

export class VendorMatcher {
  private logger: Logger

  constructor(logger: Logger) {
    this.logger = logger
  }

  /**
   * Find potential matches for a tenant vendor against global vendors
   */
  async findMatches(tenantVendor: TenantVendor, globalVendors: GlobalVendor[]): Promise<VendorMatch[]> {
    const matches: VendorMatch[] = []

    for (const globalVendor of globalVendors) {
      // Try exact tax ID match first (highest confidence)
      const taxIdMatch = this.matchByTaxId(tenantVendor, globalVendor)
      if (taxIdMatch) {
        matches.push(taxIdMatch)
        continue // Tax ID match is definitive, skip other checks
      }

      // Try domain + name match
      const domainNameMatch = this.matchByDomainAndName(tenantVendor, globalVendor)
      if (domainNameMatch) {
        matches.push(domainNameMatch)
        continue // Domain + name is very strong, skip fuzzy matching
      }

      // Try fuzzy name + address match
      const fuzzyMatch = this.matchByFuzzyNameAndAddress(tenantVendor, globalVendor)
      if (fuzzyMatch) {
        matches.push(fuzzyMatch)
      }
    }

    // Sort by confidence descending
    matches.sort((a, b) => b.confidence - a.confidence)

    this.logger.debug(`Found ${matches.length} potential matches for vendor ${tenantVendor.name}`, {
      tenantVendorId: tenantVendor.id,
      matches: matches.map(m => ({ globalVendorId: m.globalVendorId, confidence: m.confidence, matchType: m.matchType }))
    })

    return matches
  }

  /**
   * Match by exact tax ID (95% confidence)
   */
  private matchByTaxId(tenantVendor: TenantVendor, globalVendor: GlobalVendor): VendorMatch | null {
    if (!tenantVendor.taxId || !globalVendor.taxId) {
      return null
    }

    // Normalize tax IDs (remove spaces, hyphens, convert to uppercase)
    const tenantTaxId = this.normalizeTaxId(tenantVendor.taxId)
    const globalTaxId = this.normalizeTaxId(globalVendor.taxId)

    if (tenantTaxId === globalTaxId) {
      return {
        tenantVendorId: tenantVendor.id,
        globalVendorId: globalVendor.id,
        confidence: 95,
        matchType: 'tax_id',
        matchDetails: {
          tenantTaxId: tenantVendor.taxId,
          globalTaxId: globalVendor.taxId,
          normalizedMatch: tenantTaxId
        }
      }
    }

    return null
  }

  /**
   * Match by domain + name (90% confidence)
   */
  private matchByDomainAndName(tenantVendor: TenantVendor, globalVendor: GlobalVendor): VendorMatch | null {
    if (!tenantVendor.domain || !globalVendor.domain) {
      return null
    }

    // Normalize domains (remove www, convert to lowercase)
    const tenantDomain = this.normalizeDomain(tenantVendor.domain)
    const globalDomain = this.normalizeDomain(globalVendor.domain)

    if (tenantDomain !== globalDomain) {
      return null
    }

    // Check name similarity
    const nameSimilarity = this.calculateStringSimilarity(
      this.normalizeCompanyName(tenantVendor.name),
      this.normalizeCompanyName(globalVendor.name)
    )

    // Require at least 70% name similarity for domain match
    if (nameSimilarity >= 0.7) {
      return {
        tenantVendorId: tenantVendor.id,
        globalVendorId: globalVendor.id,
        confidence: 90,
        matchType: 'domain_name',
        matchDetails: {
          domain: tenantDomain,
          nameSimilarity,
          tenantName: tenantVendor.name,
          globalName: globalVendor.name
        }
      }
    }

    return null
  }

  /**
   * Match by fuzzy name + address (70-85% confidence)
   */
  private matchByFuzzyNameAndAddress(tenantVendor: TenantVendor, globalVendor: GlobalVendor): VendorMatch | null {
    // Calculate name similarity
    const nameSimilarity = this.calculateStringSimilarity(
      this.normalizeCompanyName(tenantVendor.name),
      this.normalizeCompanyName(globalVendor.name)
    )

    // Require at least 60% name similarity for fuzzy matching
    if (nameSimilarity < 0.6) {
      return null
    }

    // Calculate address similarity if both have addresses
    let addressSimilarity = 0
    let hasAddressMatch = false

    if (tenantVendor.address && globalVendor.address) {
      addressSimilarity = this.calculateAddressSimilarity(tenantVendor, globalVendor)
      hasAddressMatch = true
    }

    // Calculate overall confidence based on available data
    let confidence = 0

    if (hasAddressMatch) {
      // Name (60%) + Address (40%) weighting
      confidence = Math.round((nameSimilarity * 0.6 + addressSimilarity * 0.4) * 100)
    } else {
      // Name only, but reduce confidence due to lack of address verification
      confidence = Math.round(nameSimilarity * 80) // Max 80% without address
    }

    // Only return matches with at least 70% confidence
    if (confidence >= 70) {
      return {
        tenantVendorId: tenantVendor.id,
        globalVendorId: globalVendor.id,
        confidence: Math.min(confidence, 85), // Cap at 85% for fuzzy matches
        matchType: 'fuzzy_name_address',
        matchDetails: {
          nameSimilarity,
          addressSimilarity,
          hasAddressMatch,
          tenantName: tenantVendor.name,
          globalName: globalVendor.name,
          tenantAddress: tenantVendor.address,
          globalAddress: globalVendor.address
        }
      }
    }

    return null
  }

  /**
   * Normalize tax ID for comparison
   */
  private normalizeTaxId(taxId: string): string {
    return taxId.replace(/[\s\-]/g, '').toUpperCase()
  }

  /**
   * Normalize domain for comparison
   */
  private normalizeDomain(domain: string): string {
    return domain.toLowerCase()
      .replace(/^https?:\/\//, '')
      .replace(/^www\./, '')
      .replace(/\/$/, '')
      .split('/')[0] // Take only the domain part
  }

  /**
   * Normalize company name for comparison
   */
  private normalizeCompanyName(name: string): string {
    return name.toLowerCase()
      .replace(/\b(inc|corp|corporation|ltd|limited|llc|co|company)\b\.?/g, '')
      .replace(/[^\w\s]/g, '')
      .replace(/\s+/g, ' ')
      .trim()
  }

  /**
   * Calculate string similarity using Levenshtein distance
   */
  private calculateStringSimilarity(str1: string, str2: string): number {
    if (str1 === str2) return 1.0
    if (str1.length === 0 || str2.length === 0) return 0.0

    const distance = this.levenshteinDistance(str1, str2)
    const maxLength = Math.max(str1.length, str2.length)
    
    return 1 - (distance / maxLength)
  }

  /**
   * Calculate Levenshtein distance between two strings
   */
  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null))

    for (let i = 0; i <= str1.length; i++) {
      matrix[0][i] = i
    }

    for (let j = 0; j <= str2.length; j++) {
      matrix[j][0] = j
    }

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,     // deletion
          matrix[j - 1][i] + 1,     // insertion
          matrix[j - 1][i - 1] + indicator // substitution
        )
      }
    }

    return matrix[str2.length][str1.length]
  }

  /**
   * Calculate address similarity
   */
  private calculateAddressSimilarity(tenantVendor: TenantVendor, globalVendor: GlobalVendor): number {
    let totalSimilarity = 0
    let componentCount = 0

    // Compare address components
    if (tenantVendor.address && globalVendor.address) {
      totalSimilarity += this.calculateStringSimilarity(
        this.normalizeAddress(tenantVendor.address),
        this.normalizeAddress(globalVendor.address)
      )
      componentCount++
    }

    if (tenantVendor.city && globalVendor.city) {
      totalSimilarity += this.calculateStringSimilarity(
        tenantVendor.city.toLowerCase(),
        globalVendor.city.toLowerCase()
      )
      componentCount++
    }

    if (tenantVendor.state && globalVendor.state) {
      totalSimilarity += this.calculateStringSimilarity(
        tenantVendor.state.toLowerCase(),
        globalVendor.state.toLowerCase()
      )
      componentCount++
    }

    if (tenantVendor.country && globalVendor.country) {
      totalSimilarity += this.calculateStringSimilarity(
        tenantVendor.country.toLowerCase(),
        globalVendor.country.toLowerCase()
      )
      componentCount++
    }

    return componentCount > 0 ? totalSimilarity / componentCount : 0
  }

  /**
   * Normalize address for comparison
   */
  private normalizeAddress(address: string): string {
    return address.toLowerCase()
      .replace(/\b(street|st|avenue|ave|road|rd|drive|dr|lane|ln|boulevard|blvd)\b\.?/g, '')
      .replace(/[^\w\s]/g, '')
      .replace(/\s+/g, ' ')
      .trim()
  }
}

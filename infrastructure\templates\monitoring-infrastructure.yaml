AWSTemplateFormatVersion: '2010-09-09'
Description: 'RenewTrack Monitoring Infrastructure - CloudWatch, SNS, and Alerting'

Parameters:
  Environment:
    Type: String
    Default: 'prod'
    AllowedValues: ['dev', 'staging', 'prod']
    Description: 'Environment name'
  
  ApplicationName:
    Type: String
    Default: 'renewtrack'
    Description: 'Application name for resource naming'
  
  AlertEmail:
    Type: String
    Description: 'Email address for alerts'
    Default: '<EMAIL>'
  
  SlackWebhookUrl:
    Type: String
    Description: 'Slack webhook URL for notifications (optional)'
    Default: ''
    NoEcho: true

Conditions:
  HasSlackWebhook: !Not [!Equals [!Ref SlackWebhookUrl, '']]

Resources:
  # SNS Topics for different alert types
  CriticalAlertsTopic:
    Type: AWS::SNS::Topic
    Properties:
      TopicName: !Sub '${ApplicationName}-critical-alerts-${Environment}'
      DisplayName: !Sub '${ApplicationName} Critical Alerts'

  WarningAlertsTopic:
    Type: AWS::SNS::Topic
    Properties:
      TopicName: !Sub '${ApplicationName}-warning-alerts-${Environment}'
      DisplayName: !Sub '${ApplicationName} Warning Alerts'

  InfoAlertsTopic:
    Type: AWS::SNS::Topic
    Properties:
      TopicName: !Sub '${ApplicationName}-info-alerts-${Environment}'
      DisplayName: !Sub '${ApplicationName} Info Alerts'

  # Email subscriptions
  CriticalAlertsEmailSubscription:
    Type: AWS::SNS::Subscription
    Properties:
      TopicArn: !Ref CriticalAlertsTopic
      Protocol: email
      Endpoint: !Ref AlertEmail

  WarningAlertsEmailSubscription:
    Type: AWS::SNS::Subscription
    Properties:
      TopicArn: !Ref WarningAlertsTopic
      Protocol: email
      Endpoint: !Ref AlertEmail

  # Lambda function for Slack notifications
  SlackNotificationFunction:
    Type: AWS::Lambda::Function
    Condition: HasSlackWebhook
    Properties:
      FunctionName: !Sub '${ApplicationName}-slack-notifications-${Environment}'
      Runtime: python3.9
      Handler: index.lambda_handler
      Role: !GetAtt SlackNotificationRole.Arn
      Environment:
        Variables:
          SLACK_WEBHOOK_URL: !Ref SlackWebhookUrl
      Code:
        ZipFile: |
          import json
          import urllib3
          import os
          
          def lambda_handler(event, context):
              webhook_url = os.environ['SLACK_WEBHOOK_URL']
              
              for record in event['Records']:
                  message = json.loads(record['Sns']['Message'])
                  
                  # Parse CloudWatch alarm
                  alarm_name = message.get('AlarmName', 'Unknown')
                  new_state = message.get('NewStateValue', 'Unknown')
                  reason = message.get('NewStateReason', 'No reason provided')
                  
                  # Determine color based on state
                  color = {
                      'ALARM': 'danger',
                      'OK': 'good',
                      'INSUFFICIENT_DATA': 'warning'
                  }.get(new_state, 'warning')
                  
                  # Create Slack message
                  slack_message = {
                      'attachments': [{
                          'color': color,
                          'title': f'CloudWatch Alarm: {alarm_name}',
                          'text': reason,
                          'fields': [
                              {
                                  'title': 'State',
                                  'value': new_state,
                                  'short': True
                              },
                              {
                                  'title': 'Environment',
                                  'value': os.environ.get('ENVIRONMENT', 'Unknown'),
                                  'short': True
                              }
                          ],
                          'timestamp': message.get('StateChangeTime', '')
                      }]
                  }
                  
                  # Send to Slack
                  http = urllib3.PoolManager()
                  response = http.request(
                      'POST',
                      webhook_url,
                      body=json.dumps(slack_message),
                      headers={'Content-Type': 'application/json'}
                  )
              
              return {'statusCode': 200}

  SlackNotificationRole:
    Type: AWS::IAM::Role
    Condition: HasSlackWebhook
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole

  SlackNotificationSubscription:
    Type: AWS::SNS::Subscription
    Condition: HasSlackWebhook
    Properties:
      TopicArn: !Ref CriticalAlertsTopic
      Protocol: lambda
      Endpoint: !GetAtt SlackNotificationFunction.Arn

  SlackNotificationPermission:
    Type: AWS::Lambda::Permission
    Condition: HasSlackWebhook
    Properties:
      FunctionName: !Ref SlackNotificationFunction
      Action: lambda:InvokeFunction
      Principal: sns.amazonaws.com
      SourceArn: !Ref CriticalAlertsTopic

  # Application-specific CloudWatch Alarms
  ApplicationErrorRateAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub '${ApplicationName}-error-rate-${Environment}'
      AlarmDescription: 'High application error rate'
      MetricName: 4XXError
      Namespace: AWS/ApplicationELB
      Statistic: Sum
      Period: 300
      EvaluationPeriods: 2
      Threshold: 10
      ComparisonOperator: GreaterThanThreshold
      Dimensions:
        - Name: LoadBalancer
          Value: 
            Fn::ImportValue: !Sub '${ApplicationName}-alb-arn-${Environment}'
      AlarmActions:
        - !Ref CriticalAlertsTopic
      OKActions:
        - !Ref InfoAlertsTopic
      TreatMissingData: notBreaching

  ApplicationResponseTimeAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub '${ApplicationName}-response-time-${Environment}'
      AlarmDescription: 'High application response time'
      MetricName: TargetResponseTime
      Namespace: AWS/ApplicationELB
      Statistic: Average
      Period: 300
      EvaluationPeriods: 2
      Threshold: 2.0  # 2 seconds
      ComparisonOperator: GreaterThanThreshold
      Dimensions:
        - Name: LoadBalancer
          Value: 
            Fn::ImportValue: !Sub '${ApplicationName}-alb-arn-${Environment}'
      AlarmActions:
        - !Ref WarningAlertsTopic
      OKActions:
        - !Ref InfoAlertsTopic
      TreatMissingData: notBreaching

  UnhealthyHostsAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: !Sub '${ApplicationName}-unhealthy-hosts-${Environment}'
      AlarmDescription: 'Unhealthy application instances'
      MetricName: UnHealthyHostCount
      Namespace: AWS/ApplicationELB
      Statistic: Average
      Period: 300
      EvaluationPeriods: 1
      Threshold: 0
      ComparisonOperator: GreaterThanThreshold
      Dimensions:
        - Name: TargetGroup
          Value: 
            Fn::ImportValue: !Sub '${ApplicationName}-tg-arn-${Environment}'
      AlarmActions:
        - !Ref CriticalAlertsTopic
      OKActions:
        - !Ref InfoAlertsTopic
      TreatMissingData: notBreaching

  # Custom CloudWatch Dashboard
  ApplicationDashboard:
    Type: AWS::CloudWatch::Dashboard
    Properties:
      DashboardName: !Sub '${ApplicationName}-overview-${Environment}'
      DashboardBody: !Sub |
        {
          "widgets": [
            {
              "type": "metric",
              "x": 0,
              "y": 0,
              "width": 12,
              "height": 6,
              "properties": {
                "metrics": [
                  [ "AWS/ApplicationELB", "RequestCount", "LoadBalancer", "${ApplicationName}-alb-${Environment}" ],
                  [ ".", "TargetResponseTime", ".", "." ],
                  [ ".", "HTTPCode_Target_2XX_Count", ".", "." ],
                  [ ".", "HTTPCode_Target_4XX_Count", ".", "." ],
                  [ ".", "HTTPCode_Target_5XX_Count", ".", "." ]
                ],
                "view": "timeSeries",
                "stacked": false,
                "region": "${AWS::Region}",
                "title": "Application Load Balancer Metrics",
                "period": 300
              }
            },
            {
              "type": "metric",
              "x": 12,
              "y": 0,
              "width": 12,
              "height": 6,
              "properties": {
                "metrics": [
                  [ "AWS/EC2", "CPUUtilization", "AutoScalingGroupName", "${ApplicationName}-asg-${Environment}" ],
                  [ ".", "NetworkIn", ".", "." ],
                  [ ".", "NetworkOut", ".", "." ]
                ],
                "view": "timeSeries",
                "stacked": false,
                "region": "${AWS::Region}",
                "title": "EC2 Instance Metrics",
                "period": 300
              }
            },
            {
              "type": "metric",
              "x": 0,
              "y": 6,
              "width": 24,
              "height": 6,
              "properties": {
                "metrics": [
                  [ "AWS/RDS", "CPUUtilization", "DBInstanceIdentifier", "${ApplicationName}-db-${Environment}" ],
                  [ ".", "DatabaseConnections", ".", "." ],
                  [ ".", "ReadLatency", ".", "." ],
                  [ ".", "WriteLatency", ".", "." ]
                ],
                "view": "timeSeries",
                "stacked": false,
                "region": "${AWS::Region}",
                "title": "Database Metrics",
                "period": 300
              }
            }
          ]
        }

  # Log Groups
  ApplicationLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub '/aws/ec2/${ApplicationName}/application'
      RetentionInDays: 30

  SystemLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub '/aws/ec2/${ApplicationName}/system'
      RetentionInDays: 7

  # Custom Metrics for Application
  CustomMetricsNamespace:
    Type: AWS::CloudWatch::CompositeAlarm
    Properties:
      AlarmName: !Sub '${ApplicationName}-composite-health-${Environment}'
      AlarmDescription: 'Composite alarm for overall application health'
      AlarmRule: !Sub |
        ALARM("${ApplicationErrorRateAlarm}") OR 
        ALARM("${ApplicationResponseTimeAlarm}") OR 
        ALARM("${UnhealthyHostsAlarm}")
      AlarmActions:
        - !Ref CriticalAlertsTopic
      OKActions:
        - !Ref InfoAlertsTopic

  # EventBridge Rules for Auto Scaling Events
  AutoScalingEventRule:
    Type: AWS::Events::Rule
    Properties:
      Name: !Sub '${ApplicationName}-autoscaling-events-${Environment}'
      Description: 'Capture Auto Scaling events'
      EventPattern:
        source:
          - 'aws.autoscaling'
        detail-type:
          - 'EC2 Instance Launch Successful'
          - 'EC2 Instance Launch Unsuccessful'
          - 'EC2 Instance Terminate Successful'
          - 'EC2 Instance Terminate Unsuccessful'
        detail:
          AutoScalingGroupName:
            - Fn::ImportValue: !Sub '${ApplicationName}-asg-name-${Environment}'
      State: ENABLED
      Targets:
        - Arn: !Ref InfoAlertsTopic
          Id: 'AutoScalingNotifications'

  # Lambda function for custom metrics
  CustomMetricsFunction:
    Type: AWS::Lambda::Function
    Properties:
      FunctionName: !Sub '${ApplicationName}-custom-metrics-${Environment}'
      Runtime: python3.9
      Handler: index.lambda_handler
      Role: !GetAtt CustomMetricsRole.Arn
      Environment:
        Variables:
          APPLICATION_NAME: !Ref ApplicationName
          ENVIRONMENT: !Ref Environment
      Code:
        ZipFile: |
          import boto3
          import json
          import os
          from datetime import datetime
          
          def lambda_handler(event, context):
              cloudwatch = boto3.client('cloudwatch')
              
              # Example: Put custom business metrics
              # This would be called by your application to send custom metrics
              
              namespace = f"{os.environ['APPLICATION_NAME']}/{os.environ['ENVIRONMENT']}"
              
              # Example metrics (replace with actual business metrics)
              metrics = [
                  {
                      'MetricName': 'ActiveUsers',
                      'Value': 100,  # This would come from your application
                      'Unit': 'Count'
                  },
                  {
                      'MetricName': 'RenewalProcessingTime',
                      'Value': 1.5,  # This would come from your application
                      'Unit': 'Seconds'
                  }
              ]
              
              for metric in metrics:
                  cloudwatch.put_metric_data(
                      Namespace=namespace,
                      MetricData=[{
                          'MetricName': metric['MetricName'],
                          'Value': metric['Value'],
                          'Unit': metric['Unit'],
                          'Timestamp': datetime.utcnow()
                      }]
                  )
              
              return {'statusCode': 200}

  CustomMetricsRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: lambda.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
      Policies:
        - PolicyName: CloudWatchMetrics
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - cloudwatch:PutMetricData
                Resource: '*'

  # Schedule for custom metrics
  CustomMetricsSchedule:
    Type: AWS::Events::Rule
    Properties:
      Name: !Sub '${ApplicationName}-custom-metrics-schedule-${Environment}'
      Description: 'Schedule for custom metrics collection'
      ScheduleExpression: 'rate(5 minutes)'
      State: ENABLED
      Targets:
        - Arn: !GetAtt CustomMetricsFunction.Arn
          Id: 'CustomMetricsTarget'

  CustomMetricsPermission:
    Type: AWS::Lambda::Permission
    Properties:
      FunctionName: !Ref CustomMetricsFunction
      Action: lambda:InvokeFunction
      Principal: events.amazonaws.com
      SourceArn: !GetAtt CustomMetricsSchedule.Arn

Outputs:
  CriticalAlertsTopicArn:
    Description: 'ARN of the critical alerts SNS topic'
    Value: !Ref CriticalAlertsTopic
    Export:
      Name: !Sub '${ApplicationName}-critical-alerts-topic-${Environment}'

  WarningAlertsTopicArn:
    Description: 'ARN of the warning alerts SNS topic'
    Value: !Ref WarningAlertsTopic
    Export:
      Name: !Sub '${ApplicationName}-warning-alerts-topic-${Environment}'

  InfoAlertsTopicArn:
    Description: 'ARN of the info alerts SNS topic'
    Value: !Ref InfoAlertsTopic
    Export:
      Name: !Sub '${ApplicationName}-info-alerts-topic-${Environment}'

  DashboardURL:
    Description: 'CloudWatch Dashboard URL'
    Value: !Sub 'https://${AWS::Region}.console.aws.amazon.com/cloudwatch/home?region=${AWS::Region}#dashboards:name=${ApplicationName}-overview-${Environment}'

  ApplicationLogGroupName:
    Description: 'Application log group name'
    Value: !Ref ApplicationLogGroup
    Export:
      Name: !Sub '${ApplicationName}-app-log-group-${Environment}'

  SystemLogGroupName:
    Description: 'System log group name'
    Value: !Ref SystemLogGroup
    Export:
      Name: !Sub '${ApplicationName}-system-log-group-${Environment}'

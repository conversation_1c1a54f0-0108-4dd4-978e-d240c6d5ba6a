const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');

// Database configuration
const pool = new Pool({
  user: 'postgres',
  host: 'localhost',
  database: 'Renewtrack',
  password: 'admin',
  port: 5432,
});

async function setupLicensing() {
  const client = await pool.connect();
  
  try {
    console.log('Setting up licensing system...');
    
    // Read the SQL file
    const sqlPath = path.join(__dirname, '../../database/create-licensing-tables.sql');
    const sql = fs.readFileSync(sqlPath, 'utf8');
    
    // Execute the SQL
    await client.query(sql);
    
    console.log('✅ Licensing system setup completed successfully!');
    
    // Verify the setup
    const result = await client.query(`
      SELECT 
        c.name,
        cl.status,
        cl.activation_date,
        cl.expiry_date,
        lt.type_name,
        cl.max_renewals
      FROM metadata.admin_client_licenses cl
      JOIN metadata.clients c ON cl.client_id = c.id
      JOIN metadata.admin_license_types lt ON cl.license_type_id = lt.id
      WHERE c.name = 'RenewTrack'
    `);
    
    if (result.rows.length > 0) {
      console.log('✅ RenewTrack license verified:');
      console.log(result.rows[0]);
    } else {
      console.log('⚠️  No license found for RenewTrack');
    }
    
  } catch (error) {
    console.error('❌ Error setting up licensing system:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

setupLicensing();

/**
 * Unified Error Handler Utility
 * 
 * Provides consistent error handling patterns across the application
 */

import { ZodError } from 'zod';

export interface ErrorContext {
  component?: string;
  operation?: string;
  userId?: string;
  tenantId?: string;
  metadata?: Record<string, any>;
}

export interface ErrorInfo {
  type: 'validation' | 'network' | 'server' | 'auth' | 'permission' | 'notFound' | 'unknown';
  message: string;
  originalError: Error | string;
  context?: ErrorContext;
  timestamp: Date;
  stack?: string;
  code?: string | number;
}

/**
 * Categorize error type based on error characteristics
 */
export function categorizeError(error: Error | string): ErrorInfo['type'] {
  if (error instanceof ZodError) return 'validation';

  const message = typeof error === 'string' ? error : error.message;
  const lowerMessage = message.toLowerCase();

  // Database and configuration errors (critical)
  if (lowerMessage.includes('database') ||
      lowerMessage.includes('configuration') ||
      lowerMessage.includes('environment') ||
      lowerMessage.includes('econnrefused') ||
      lowerMessage.includes('pool') ||
      lowerMessage.includes('connection refused')) {
    return 'server'; // Treat as server error for user-friendly messaging
  }

  // Network errors
  if (lowerMessage.includes('fetch') ||
      lowerMessage.includes('network') ||
      lowerMessage.includes('connection') ||
      lowerMessage.includes('timeout')) {
    return 'network';
  }

  // Server errors
  if (lowerMessage.includes('server') ||
      lowerMessage.includes('internal') ||
      lowerMessage.includes('500') ||
      lowerMessage.includes('502') ||
      lowerMessage.includes('503')) {
    return 'server';
  }

  // Authentication errors
  if (lowerMessage.includes('auth') ||
      lowerMessage.includes('unauthorized') ||
      lowerMessage.includes('401') ||
      lowerMessage.includes('token') ||
      lowerMessage.includes('amplify') ||
      lowerMessage.includes('cognito') ||
      lowerMessage.includes('oauth') ||
      lowerMessage.includes('session') ||
      lowerMessage.includes('login')) {
    return 'auth';
  }

  // Permission errors
  if (lowerMessage.includes('permission') ||
      lowerMessage.includes('forbidden') ||
      lowerMessage.includes('403') ||
      lowerMessage.includes('access denied')) {
    return 'permission';
  }

  // Not found errors
  if (lowerMessage.includes('not found') ||
      lowerMessage.includes('404')) {
    return 'notFound';
  }

  // Validation errors
  if (lowerMessage.includes('validation') ||
      lowerMessage.includes('invalid') ||
      lowerMessage.includes('required') ||
      lowerMessage.includes('400')) {
    return 'validation';
  }

  return 'unknown';
}

/**
 * Create standardized error info object
 */
export function createErrorInfo(
  error: Error | string,
  context?: ErrorContext
): ErrorInfo {
  const originalError = error;
  const message = typeof error === 'string' ? error : error.message;
  const type = categorizeError(error);
  
  return {
    type,
    message,
    originalError,
    context,
    timestamp: new Date(),
    stack: error instanceof Error ? error.stack : undefined,
    code: error instanceof Error ? (error as any).code : undefined
  };
}

/**
 * Get user-friendly error message
 */
export function getUserFriendlyMessage(errorInfo: ErrorInfo): string {
  const { type, message } = errorInfo;

  // Check for specific database/configuration errors first
  const lowerMessage = message.toLowerCase();
  if (lowerMessage.includes('database configuration') ||
      lowerMessage.includes('environment') ||
      lowerMessage.includes('missing required database')) {
    return 'Application configuration error. Please contact your system administrator.';
  }

  if (lowerMessage.includes('econnrefused') ||
      lowerMessage.includes('connection refused') ||
      lowerMessage.includes('database initialization failed')) {
    return 'Unable to connect to the database. Please try again later or contact support.';
  }

  // In production, provide generic messages for security
  if (process.env.NODE_ENV === 'production') {
    switch (type) {
      case 'network':
        return 'Unable to connect to the server. Please check your internet connection and try again.';
      case 'server':
        return 'A server error occurred. Please try again later or contact support if this continues.';
      case 'auth':
        return 'Authentication failed. Please try signing in again.';
      case 'permission':
        return 'You do not have permission to perform this action.';
      case 'notFound':
        return 'The requested resource was not found.';
      case 'validation':
        return message; // Validation messages are usually safe to show
      default:
        return 'An unexpected error occurred. Please try again or contact support.';
    }
  }

  // In development, show more detailed error messages
  switch (type) {
    case 'server':
      return `Server Error: ${message}`;
    case 'network':
      return `Network Error: ${message}`;
    case 'auth':
      return `Authentication Error: ${message}`;
    default:
      return message;
  }
}

/**
 * Log error with context
 */
export function logError(errorInfo: ErrorInfo): void {
  const { type, message, context, timestamp, stack } = errorInfo;
  
  const logData = {
    type,
    message,
    context,
    timestamp: timestamp.toISOString(),
    stack: stack?.split('\n').slice(0, 10).join('\n') // Limit stack trace
  };
  
  // Use appropriate log level based on error type
  switch (type) {
    case 'server':
    case 'unknown':
      console.error('[ERROR]', logData);
      break;
    case 'network':
    case 'auth':
      console.warn('[WARNING]', logData);
      break;
    case 'validation':
    case 'permission':
    case 'notFound':
      console.info('[INFO]', logData);
      break;
    default:
      console.log('[LOG]', logData);
  }
}

/**
 * Handle error with consistent logging and processing
 */
export function handleError(
  error: Error | string,
  context?: ErrorContext
): ErrorInfo {
  const errorInfo = createErrorInfo(error, context);
  logError(errorInfo);
  
  // Send to error tracking service in production
  if (process.env.NODE_ENV === 'production') {
    // Note: Error tracking service integration will be added when monitoring is set up
    // trackError(errorInfo);
  }
  
  return errorInfo;
}

/**
 * Async error handler wrapper
 */
export function withErrorHandler<T extends any[], R>(
  fn: (...args: T) => Promise<R>,
  context?: ErrorContext
) {
  return async (...args: T): Promise<R> => {
    try {
      return await fn(...args);
    } catch (error) {
      const errorInfo = handleError(error as Error, context);
      throw errorInfo;
    }
  };
}

/**
 * Sync error handler wrapper
 */
export function withSyncErrorHandler<T extends any[], R>(
  fn: (...args: T) => R,
  context?: ErrorContext
) {
  return (...args: T): R => {
    try {
      return fn(...args);
    } catch (error) {
      const errorInfo = handleError(error as Error, context);
      throw errorInfo;
    }
  };
}

/**
 * React error handler for components
 */
export function useErrorHandler(context?: ErrorContext) {
  return (error: Error | string) => {
    return handleError(error, context);
  };
}

/**
 * Validation error handler for Zod schemas
 */
export function handleValidationError(
  error: ZodError,
  context?: ErrorContext
): ErrorInfo {
  const message = error.errors
    .map(err => `${err.path.join('.')}: ${err.message}`)
    .join(', ');
  
  return handleError(new Error(message), {
    ...context,
    operation: 'validation',
    metadata: { validationErrors: error.errors }
  });
}

/**
 * API error handler
 */
export function handleApiError(
  error: any,
  context?: ErrorContext
): ErrorInfo {
  // Handle different API error formats
  if (error?.response) {
    // Axios-style error
    const status = error.response.status;
    const message = error.response.data?.message || error.message;
    return handleError(new Error(`API Error ${status}: ${message}`), context);
  }
  
  if (error?.status) {
    // Fetch-style error
    const message = error.message || `HTTP ${error.status}`;
    return handleError(new Error(message), context);
  }
  
  // Generic error
  return handleError(error, context);
}

export default {
  categorizeError,
  createErrorInfo,
  getUserFriendlyMessage,
  logError,
  handleError,
  withErrorHandler,
  withSyncErrorHandler,
  useErrorHandler,
  handleValidationError,
  handleApiError
};

AWSTemplateFormatVersion: "2010-09-09"
Description: "RenewTrack Secrets Management Infrastructure - Parameter Store and Secrets Manager setup"

Parameters:
  Environment:
    Type: String
    Default: "prod"
    AllowedValues: ["dev", "staging", "prod"]
    Description: "Environment name"

  ApplicationName:
    Type: String
    Default: "renewtrack"
    Description: "Application name for resource naming"

  DatabaseHost:
    Type: String
    Description: "RDS database hostname"

  DatabaseName:
    Type: String
    Default: "renewtrack"
    Description: "Database name"

  DatabaseUsername:
    Type: String
    Default: "renewtrack_admin"
    Description: "Database username"

  DatabasePassword:
    Type: String
    NoEcho: true
    Description: "Database password (will be stored in Secrets Manager)"

  UseIAMAuth:
    Type: String
    Default: "true"
    AllowedValues: ["true", "false"]
    Description: "Whether to use IAM authentication for RDS"

  JWTSecret:
    Type: String
    NoEcho: true
    MinLength: 32
    Description: "JWT secret key (minimum 32 characters)"

  EncryptionKey:
    Type: String
    NoEcho: true
    MinLength: 32
    Description: "Application encryption key (minimum 32 characters)"

Resources:
  # KMS Key for encrypting secrets
  SecretsKMSKey:
    Type: AWS::KMS::Key
    Properties:
      Description: !Sub "${ApplicationName} Secrets Encryption Key"
      KeyPolicy:
        Version: "2012-10-17"
        Statement:
          - Sid: Enable IAM User Permissions
            Effect: Allow
            Principal:
              AWS: !Sub "arn:aws:iam::${AWS::AccountId}:root"
            Action: "kms:*"
            Resource: "*"
          - Sid: Allow use of the key for Parameter Store
            Effect: Allow
            Principal:
              Service: ssm.amazonaws.com
            Action:
              - kms:Encrypt
              - kms:Decrypt
              - kms:ReEncrypt*
              - kms:GenerateDataKey*
              - kms:DescribeKey
            Resource: "*"
          - Sid: Allow use of the key for Secrets Manager
            Effect: Allow
            Principal:
              Service: secretsmanager.amazonaws.com
            Action:
              - kms:Encrypt
              - kms:Decrypt
              - kms:ReEncrypt*
              - kms:GenerateDataKey*
              - kms:DescribeKey
            Resource: "*"

  SecretsKMSKeyAlias:
    Type: AWS::KMS::Alias
    Properties:
      AliasName: !Sub "alias/${ApplicationName}-secrets-${Environment}"
      TargetKeyId: !Ref SecretsKMSKey

  # Parameter Store Parameters
  DatabaseHostParameter:
    Type: AWS::SSM::Parameter
    Properties:
      Name: !Sub "/${ApplicationName}/database/host"
      Type: String
      Value: !Ref DatabaseHost
      Description: "Database hostname"

  DatabasePortParameter:
    Type: AWS::SSM::Parameter
    Properties:
      Name: !Sub "/${ApplicationName}/database/port"
      Type: String
      Value: "5432"
      Description: "Database port"

  DatabaseNameParameter:
    Type: AWS::SSM::Parameter
    Properties:
      Name: !Sub "/${ApplicationName}/database/name"
      Type: String
      Value: !Ref DatabaseName
      Description: "Database name"

  DatabaseUsernameParameter:
    Type: AWS::SSM::Parameter
    Properties:
      Name: !Sub "/${ApplicationName}/database/username"
      Type: String
      Value: !Ref DatabaseUsername
      Description: "Database username"

  DatabaseUseIAMParameter:
    Type: AWS::SSM::Parameter
    Properties:
      Name: !Sub "/${ApplicationName}/database/use-iam"
      Type: String
      Value: !Ref UseIAMAuth
      Description: "Whether to use IAM authentication"

  DatabaseSSLParameter:
    Type: AWS::SSM::Parameter
    Properties:
      Name: !Sub "/${ApplicationName}/database/ssl"
      Type: String
      Value: "true"
      Description: "Whether to use SSL for database connections"

  # Authentication Parameters
  TokenExpirationParameter:
    Type: AWS::SSM::Parameter
    Properties:
      Name: !Sub "/${ApplicationName}/auth/token-expiration"
      Type: String
      Value: "3600"
      Description: "JWT token expiration in seconds"

  RefreshTokenExpirationParameter:
    Type: AWS::SSM::Parameter
    Properties:
      Name: !Sub "/${ApplicationName}/auth/refresh-token-expiration"
      Type: String
      Value: "604800"
      Description: "Refresh token expiration in seconds"

  # Encryption Parameters
  EncryptionAlgorithmParameter:
    Type: AWS::SSM::Parameter
    Properties:
      Name: !Sub "/${ApplicationName}/encryption-algorithm"
      Type: String
      Value: "aes-256-gcm"
      Description: "Encryption algorithm"

  # Monitoring Parameters
  LogLevelParameter:
    Type: AWS::SSM::Parameter
    Properties:
      Name: !Sub "/${ApplicationName}/monitoring/log-level"
      Type: String
      Value: "info"
      Description: "Application log level"

  EnableMetricsParameter:
    Type: AWS::SSM::Parameter
    Properties:
      Name: !Sub "/${ApplicationName}/monitoring/enable-metrics"
      Type: String
      Value: "true"
      Description: "Whether to enable metrics collection"

  EnableTracingParameter:
    Type: AWS::SSM::Parameter
    Properties:
      Name: !Sub "/${ApplicationName}/monitoring/enable-tracing"
      Type: String
      Value: "true"
      Description: "Whether to enable request tracing"

  # Security Parameters
  EnableRateLimitParameter:
    Type: AWS::SSM::Parameter
    Properties:
      Name: !Sub "/${ApplicationName}/security/enable-rate-limit"
      Type: String
      Value: "true"
      Description: "Whether to enable rate limiting"

  MaxRequestsPerMinuteParameter:
    Type: AWS::SSM::Parameter
    Properties:
      Name: !Sub "/${ApplicationName}/security/max-requests-per-minute"
      Type: String
      Value: "100"
      Description: "Maximum requests per minute per user"

  EnableCSRFParameter:
    Type: AWS::SSM::Parameter
    Properties:
      Name: !Sub "/${ApplicationName}/security/enable-csrf"
      Type: String
      Value: "true"
      Description: "Whether to enable CSRF protection"

  EnableCORSParameter:
    Type: AWS::SSM::Parameter
    Properties:
      Name: !Sub "/${ApplicationName}/security/enable-cors"
      Type: String
      Value: "true"
      Description: "Whether to enable CORS"

  AllowedOriginsParameter:
    Type: AWS::SSM::Parameter
    Properties:
      Name: !Sub "/${ApplicationName}/security/allowed-origins"
      Type: String
      Value: "https://renewtrack.com,https://app.renewtrack.com"
      Description: "Allowed CORS origins"

  # Secrets Manager Secrets
  DatabasePasswordSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: !Sub "/${ApplicationName}/database/password"
      Description: "Database password"
      SecretString: !Ref DatabasePassword
      KmsKeyId: !Ref SecretsKMSKey

  JWTSecretSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: !Sub "/${ApplicationName}/jwt-secret"
      Description: "JWT signing secret"
      SecretString: !Ref JWTSecret
      KmsKeyId: !Ref SecretsKMSKey

  EncryptionKeySecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: !Sub "/${ApplicationName}/encryption-key"
      Description: "Application encryption key"
      SecretString: !Ref EncryptionKey
      KmsKeyId: !Ref SecretsKMSKey

  APIKeysSecret:
    Type: AWS::SecretsManager::Secret
    Properties:
      Name: !Sub "/${ApplicationName}/api-keys"
      Description: "External API keys"
      SecretString: '{"external_api_keys": "configure-during-deployment"}'
      KmsKeyId: !Ref SecretsKMSKey

  # IAM Role for Application to Access Secrets
  SecretsAccessRole:
    Type: AWS::IAM::Role
    Properties:
      RoleName: !Sub "${ApplicationName}-secrets-access-${Environment}"
      AssumeRolePolicyDocument:
        Version: "2012-10-17"
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - ec2.amazonaws.com
                - ecs-tasks.amazonaws.com
                - lambda.amazonaws.com
            Action: sts:AssumeRole
      Policies:
        - PolicyName: SecretsAccess
          PolicyDocument:
            Version: "2012-10-17"
            Statement:
              - Effect: Allow
                Action:
                  - ssm:GetParameter
                  - ssm:GetParameters
                  - ssm:GetParametersByPath
                Resource:
                  - !Sub "arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/${ApplicationName}/*"
              - Effect: Allow
                Action:
                  - secretsmanager:GetSecretValue
                Resource:
                  - !Ref DatabasePasswordSecret
                  - !Ref JWTSecretSecret
                  - !Ref EncryptionKeySecret
                  - !Ref APIKeysSecret
              - Effect: Allow
                Action:
                  - kms:Decrypt
                  - kms:DescribeKey
                Resource: !GetAtt SecretsKMSKey.Arn

  # Instance Profile for EC2 instances
  SecretsAccessInstanceProfile:
    Type: AWS::IAM::InstanceProfile
    Properties:
      Roles:
        - !Ref SecretsAccessRole

Outputs:
  SecretsKMSKeyId:
    Description: "KMS Key ID for secrets encryption"
    Value: !Ref SecretsKMSKey
    Export:
      Name: !Sub "${ApplicationName}-secrets-kms-key-${Environment}"

  SecretsKMSKeyArn:
    Description: "KMS Key ARN for secrets encryption"
    Value: !GetAtt SecretsKMSKey.Arn
    Export:
      Name: !Sub "${ApplicationName}-secrets-kms-key-arn-${Environment}"

  SecretsAccessRoleArn:
    Description: "IAM Role ARN for accessing secrets"
    Value: !GetAtt SecretsAccessRole.Arn
    Export:
      Name: !Sub "${ApplicationName}-secrets-access-role-${Environment}"

  SecretsAccessInstanceProfileArn:
    Description: "Instance Profile ARN for EC2 instances"
    Value: !GetAtt SecretsAccessInstanceProfile.Arn
    Export:
      Name: !Sub "${ApplicationName}-secrets-instance-profile-${Environment}"

  ParameterStorePrefix:
    Description: "Parameter Store prefix for application parameters"
    Value: !Sub "/${ApplicationName}/"
    Export:
      Name: !Sub "${ApplicationName}-parameter-prefix-${Environment}"

/**
 * Page Access Guard Component
 * 
 * Protects pages based on user group membership and database-driven access control
 */

'use client'

import React, { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/lib/hooks/useAuth'
import { LoadingPage } from '@/components/common/LoadingStates'

interface PageAccessGuardProps {
  children: React.ReactNode
  pageName: string
  redirectTo?: string
  fallback?: React.ReactNode
  showLoading?: boolean
}

export default function PageAccessGuard({
  children,
  pageName,
  redirectTo = '/overview',
  fallback,
  showLoading = true
}: PageAccessGuardProps) {
  const { user, isAuthenticated, isLoading: authLoading } = useAuth()
  const router = useRouter()
  const [hasAccess, setHasAccess] = useState<boolean | null>(null)
  const [isChecking, setIsChecking] = useState(true)

  useEffect(() => {
    const checkAccess = async () => {
      if (authLoading) return
      
      if (!isAuthenticated || !user) {
        router.push('/login')
        return
      }

      try {
        setIsChecking(true)
        
        // Check page access via API
        const response = await fetch(`/api/admin-pages/check-access?page=${encodeURIComponent(pageName)}`)
        
        if (response.ok) {
          const data = await response.json()
          const hasPageAccess = data.success && data.hasAccess
          
          setHasAccess(hasPageAccess)
          
          if (!hasPageAccess) {
            console.warn(`Access denied to page: ${pageName}`)
            if (redirectTo) {
              router.push(redirectTo)
            }
          }
        } else {
          console.error('Failed to check page access')
          setHasAccess(false)
          if (redirectTo) {
            router.push(redirectTo)
          }
        }
      } catch (error) {
        console.error('Error checking page access:', error)
        setHasAccess(false)
        if (redirectTo) {
          router.push(redirectTo)
        }
      } finally {
        setIsChecking(false)
      }
    }

    checkAccess()
  }, [pageName, isAuthenticated, user, authLoading, router, redirectTo])

  // Show loading while checking authentication or access
  if (authLoading || isChecking) {
    if (showLoading) {
      return <LoadingPage title="Checking access..." />
    }
    return null
  }

  // Show fallback if no access and fallback is provided
  if (hasAccess === false && fallback) {
    return <>{fallback}</>
  }

  // Show children if access is granted
  if (hasAccess === true) {
    return <>{children}</>
  }

  // Default: show nothing while redirecting
  return null
}

/**
 * Higher-Order Component for page access protection
 */
export function withPageAccess(pageName: string, redirectTo?: string) {
  return function <P extends object>(Component: React.ComponentType<P>) {
    return function PageAccessWrapper(props: P) {
      return (
        <PageAccessGuard pageName={pageName} redirectTo={redirectTo}>
          <Component {...props} />
        </PageAccessGuard>
      )
    }
  }
}

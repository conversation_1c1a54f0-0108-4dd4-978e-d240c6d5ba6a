/**
 * Authentication Utilities
 * 
 * Helper functions for authentication-related operations
 */

import { getPublicConfig } from '@/lib/config'

/**
 * Get the login URL for redirecting users to authentication
 */
export function getLoginUrl(): string {
  try {
    const config = getPublicConfig()
    
    // For OAuth flow, we redirect to the login page which will handle the OAuth redirect
    return '/login'
  } catch (error) {
    console.error('[AUTH-UTILS] Error getting login URL:', error)
    return '/login'
  }
}

/**
 * Get the logout URL for redirecting users after logout
 */
export function getLogoutUrl(): string {
  try {
    const config = getPublicConfig()
    return config.auth.redirectSignOut || '/login'
  } catch (error) {
    console.error('[AUTH-UTILS] Error getting logout URL:', error)
    return '/login'
  }
}

/**
 * Check if a user has required groups
 */
export function hasRequiredGroups(userGroups: string[] = [], requiredGroups: string[] = []): boolean {
  if (requiredGroups.length === 0) {
    return true
  }
  
  return requiredGroups.some(group => userGroups.includes(group))
}

/**
 * Check if user is admin (has admin or super-admin group)
 * Note: This function should be replaced with database-driven group checking
 * @deprecated Use database queries to check user groups instead
 */
export function isAdmin(userGroups: string[] = []): boolean {
  // This is kept for backward compatibility but should be replaced
  // with database-driven group checking
  return hasRequiredGroups(userGroups, ['admin', 'super-admin'])
}

/**
 * Check if user is super admin
 */
export function isSuperAdmin(userGroups: string[] = []): boolean {
  return hasRequiredGroups(userGroups, ['super-admin'])
}

/**
 * Extract user groups from Cognito user attributes
 */
export function extractUserGroups(user: any): string[] {
  try {
    // Try different possible locations for groups
    if (user?.signInUserSession?.idToken?.payload?.['cognito:groups']) {
      return user.signInUserSession.idToken.payload['cognito:groups']
    }
    
    if (user?.attributes?.['cognito:groups']) {
      return user.attributes['cognito:groups']
    }
    
    if (user?.['cognito:groups']) {
      return user['cognito:groups']
    }
    
    if (Array.isArray(user?.groups)) {
      return user.groups
    }
    
    return []
  } catch (error) {
    console.warn('[AUTH-UTILS] Error extracting user groups:', error)
    return []
  }
}

/**
 * Get user display name from Cognito attributes
 */
export function getUserDisplayName(user: any): string {
  try {
    // Try different possible locations for name
    const attributes = user?.attributes || user?.signInUserSession?.idToken?.payload || user
    
    if (attributes?.given_name && attributes?.family_name) {
      return `${attributes.given_name} ${attributes.family_name}`
    }
    
    if (attributes?.name) {
      return attributes.name
    }
    
    if (attributes?.email) {
      return attributes.email
    }
    
    if (user?.username) {
      return user.username
    }
    
    return 'User'
  } catch (error) {
    console.warn('[AUTH-UTILS] Error getting user display name:', error)
    return 'User'
  }
}

/**
 * Get user email from Cognito attributes
 */
export function getUserEmail(user: any): string | null {
  try {
    const attributes = user?.attributes || user?.signInUserSession?.idToken?.payload || user
    return attributes?.email || null
  } catch (error) {
    console.warn('[AUTH-UTILS] Error getting user email:', error)
    return null
  }
}

/**
 * Check if authentication is required for a given path
 */
export function isAuthRequiredForPath(path: string): boolean {
  const publicPaths = [
    '/login',
    '/signup',
    '/forgot-password',
    '/reset-password',
    '/callback',
    '/signout',
    '/auth-test'
  ]
  
  return !publicPaths.some(publicPath => path.startsWith(publicPath))
}

/**
 * Get redirect path after successful authentication
 */
export function getPostAuthRedirect(intendedPath?: string): string {
  // If there's an intended path, use it (but validate it's safe)
  if (intendedPath && isAuthRequiredForPath(intendedPath)) {
    return intendedPath
  }
  
  // Default to overview page
  return '/overview'
}

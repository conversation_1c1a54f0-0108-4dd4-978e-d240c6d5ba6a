/**
 * Input Validation Schemas and Utilities
 * 
 * This module provides comprehensive input validation using Zod schemas
 * for all API endpoints to ensure data integrity and security.
 */

import { z } from 'zod'
import { PAGINATION, VALIDATION } from '@/lib/constants/app-constants'
import { STATUS } from '@/lib/constants/app-constants';
import { NextRequest } from 'next/server';
import { handleValidationError } from '@/lib/api/response';

// Common validation patterns
const emailSchema = z.string().email('Invalid email format');
const uuidSchema = z.string().uuid('Invalid UUID format');
const positiveIntSchema = z.number().int().positive('Must be a positive integer');
const nonEmptyStringSchema = z.string().min(1, 'Cannot be empty');

// Pagination schema - handles string-to-number conversion for URL params
export const paginationSchema = z.object({
  page: z.coerce.number().int().min(1, 'Page must be at least 1').default(PAGINATION.DEFAULT_PAGE),
  limit: z.coerce.number().int().min(1, 'Limit must be at least 1').max(PAGINATION.MAX_PAGE_SIZE, `Limit cannot exceed ${PAGINATION.MAX_PAGE_SIZE}`).default(PAGINATION.DEFAULT_PAGE_SIZE),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

// Client validation schemas
export const clientCreateSchema = z.object({
  name: nonEmptyStringSchema.max(VALIDATION.MAX_NAME_LENGTH, `Name cannot exceed ${VALIDATION.MAX_NAME_LENGTH} characters`),
  domain: z.string().min(1, 'Domain is required').max(VALIDATION.MAX_NAME_LENGTH, `Domain cannot exceed ${VALIDATION.MAX_NAME_LENGTH} characters`),
  status: z.enum(['active', 'inactive', 'suspended']).default('active'),
  settings: z.record(z.any()).optional(),
});

export const clientUpdateSchema = z.object({
  name: z.string().max(255, 'Name cannot exceed 255 characters').optional(),
  domain: z.string().max(255, 'Domain cannot exceed 255 characters').optional(),
  status: z.enum(['active', 'inactive', 'suspended']).optional(),
  settings: z.record(z.any()).optional(),
});

// User validation schemas
export const userCreateSchema = z.object({
  email: emailSchema,
  name: z.string().max(255, 'Name cannot exceed 255 characters').optional(),
  given_name: z.string().max(255, 'Given name cannot exceed 255 characters').optional(),
  family_name: z.string().max(255, 'Family name cannot exceed 255 characters').optional(),
  roles: z.array(z.string()).default([]),
});

export const userUpdateSchema = z.object({
  name: z.string().max(255, 'Name cannot exceed 255 characters').optional(),
  given_name: z.string().max(255, 'Given name cannot exceed 255 characters').optional(),
  family_name: z.string().max(255, 'Family name cannot exceed 255 characters').optional(),
  roles: z.array(z.string()).optional(),
});

// User preferences validation
export const userPreferencesSchema = z.object({
  theme: z.enum(['light', 'dark', 'system']).optional(),
  notifications: z.object({
    email: z.boolean().optional(),
    push: z.boolean().optional(),
    sms: z.boolean().optional(),
  }).optional(),
  displayDensity: z.enum(['comfortable', 'compact']).optional(),
});

// Renewal validation schemas
export const renewalCreateSchema = z.object({
  name: nonEmptyStringSchema.max(255, 'Name cannot exceed 255 characters'),
  vendor: nonEmptyStringSchema.max(255, 'Vendor cannot exceed 255 characters'),
  status: z.enum(['active', 'inactive', 'pending', 'expired']).default('active'),
  dueDate: z.string().datetime('Invalid date format').optional(),
  annualCost: z.number().min(0, 'Annual cost cannot be negative').optional(),
  description: z.string().max(1000, 'Description cannot exceed 1000 characters').optional(),
});

export const renewalUpdateSchema = z.object({
  name: z.string().max(255, 'Name cannot exceed 255 characters').optional(),
  vendor: z.string().max(255, 'Vendor cannot exceed 255 characters').optional(),
  status: z.enum(['active', 'inactive', 'pending', 'expired']).optional(),
  dueDate: z.string().datetime('Invalid date format').optional(),
  annualCost: z.number().min(0, 'Annual cost cannot be negative').optional(),
  description: z.string().max(1000, 'Description cannot exceed 1000 characters').optional(),
});

// Query parameter validation
export const dashboardStatsQuerySchema = z.object({
  startDate: z.string().datetime('Invalid start date format').optional(),
  endDate: z.string().datetime('Invalid end date format').optional(),
  includeInactive: z.boolean().default(false),
});

export const renewalsQuerySchema = paginationSchema.extend({
  status: z.enum(['active', 'inactive', 'pending', 'expired']).optional(),
  vendor: z.string().optional(),
  search: z.string().max(255, 'Search term cannot exceed 255 characters').optional(),
  dueBefore: z.string().datetime('Invalid date format').optional(),
  dueAfter: z.string().datetime('Invalid date format').optional(),
});

// ID parameter validation
export const idParamSchema = z.object({
  id: uuidSchema,
});

export const clientIdParamSchema = z.object({
  clientId: z.string().min(1, 'Client ID is required'),
});

/**
 * Safely resolve params Promise in Next.js 15
 * This utility prevents "Cannot assign to read only property" errors
 */
export async function safeResolveParams<T>(
  params: Promise<T> | T
): Promise<T> {
  try {
    // If params is already resolved (synchronous), return it
    if (params && typeof params === 'object' && !('then' in params)) {
      return params as T;
    }

    // If params is a Promise, await it
    return await params;
  } catch (error) {
    console.error('Error resolving params:', error);
    throw new Error('Failed to resolve route parameters');
  }
}

// Validation utility functions
export async function validateRequestBody<T>(
  request: NextRequest,
  schema: z.ZodSchema<T>
): Promise<{ success: true; data: T } | { success: false; response: Response }> {
  try {
    const body = await request.json();
    const validatedData = schema.parse(body);
    return { success: true, data: validatedData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, response: handleValidationError(error) };
    }
    throw error;
  }
}

export function validateQueryParams<T>(
  searchParams: URLSearchParams,
  schema: z.ZodSchema<T>
): { success: true; data: T } | { success: false; response: Response } {
  try {
    // Create a new object to safely convert URLSearchParams
    // This prevents any potential read-only property issues
    const params: Record<string, any> = {};

    for (const [key, value] of searchParams.entries()) {
      // Handle boolean conversion
      if (value === 'true') {
        params[key] = true;
      } else if (value === 'false') {
        params[key] = false;
      } else if (value !== '') {
        // Let Zod handle type coercion (including numbers)
        params[key] = value;
      }
      // Skip empty values to let defaults apply
    }

    // Use safeParse to get defaults applied properly
    const result = schema.safeParse(params);
    if (!result.success) {
      return { success: false, response: handleValidationError(result.error) };
    }

    return { success: true, data: result.data };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, response: handleValidationError(error) };
    }
    throw error;
  }
}

export function validatePathParams<T>(
  params: Record<string, string | string[]>,
  schema: z.ZodSchema<T>
): { success: true; data: T } | { success: false; response: Response } {
  try {
    // Create a new object to avoid modifying the original params
    // This prevents "Cannot assign to read only property" errors in Next.js 15
    const normalizedParams: Record<string, string> = {};

    // Safely iterate over params without modifying the original object
    for (const [key, value] of Object.entries(params)) {
      normalizedParams[key] = Array.isArray(value) ? value[0] : value;
    }

    const validatedData = schema.parse(normalizedParams);
    return { success: true, data: validatedData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, response: handleValidationError(error) };
    }
    throw error;
  }
}

// Sanitization utilities
export function sanitizeString(input: string): string {
  return input.trim().replace(/[<>]/g, '');
}

export function sanitizeObject(obj: Record<string, any>): Record<string, any> {
  const sanitized: Record<string, any> = {};
  
  for (const [key, value] of Object.entries(obj)) {
    if (typeof value === 'string') {
      sanitized[key] = sanitizeString(value);
    } else if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      sanitized[key] = sanitizeObject(value);
    } else {
      sanitized[key] = value;
    }
  }
  
  return sanitized;
}

// Rate limiting validation
export const rateLimitSchema = z.object({
  maxRequests: z.number().int().min(1).max(1000).default(100),
  windowMs: z.number().int().min(1000).max(3600000).default(60000), // 1 second to 1 hour
});

// File upload validation
export const fileUploadSchema = z.object({
  filename: z.string().min(1, 'Filename is required').max(255, 'Filename too long'),
  mimetype: z.string().min(1, 'MIME type is required'),
  size: z.number().int().min(1, 'File size must be positive').max(10485760, 'File size cannot exceed 10MB'), // 10MB limit
});

// Export types for TypeScript
export type PaginationParams = z.infer<typeof paginationSchema>;
export type ClientCreateData = z.infer<typeof clientCreateSchema>;
export type ClientUpdateData = z.infer<typeof clientUpdateSchema>;
export type UserCreateData = z.infer<typeof userCreateSchema>;
export type UserUpdateData = z.infer<typeof userUpdateSchema>;
export type UserPreferencesData = z.infer<typeof userPreferencesSchema>;
export type RenewalCreateData = z.infer<typeof renewalCreateSchema>;
export type RenewalUpdateData = z.infer<typeof renewalUpdateSchema>;
export type DashboardStatsQuery = z.infer<typeof dashboardStatsQuerySchema>;
export type RenewalsQuery = z.infer<typeof renewalsQuerySchema>;
export type IdParam = z.infer<typeof idParamSchema>;
export type ClientIdParam = z.infer<typeof clientIdParamSchema>;

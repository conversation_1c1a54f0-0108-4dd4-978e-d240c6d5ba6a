/**
 * License Assignment API
 * 
 * Super Admin endpoints for assigning license keys to clients
 */

import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { databaseService } from '@/lib/services/database-service'
import { requireSuperAdmin } from '@/lib/api/auth-middleware'
import { createSuccessResponse, createErrorResponse, ApiErrorCode, HttpStatus } from '@/lib/api/response'

// Validation schema
const assignLicenseSchema = z.object({
  license_key: z.string().min(1),
  client_id: z.number().min(1),
  license_term_id: z.number().min(1)
})

/**
 * POST /api/admin/license-assignment
 * Assign a license key to a client with term (super admin only)
 */
export async function POST(request: NextRequest) {
  try {
    // Require super admin authentication
    const authResult = await requireSuperAdmin(request)
    if (!authResult.success) {
      return authResult.response
    }

    const body = await request.json()
    const validatedData = assignLicenseSchema.parse(body)

    // Use the database function to assign the license
    const query = `
      SELECT metadata.assign_license_to_client(
        $1::INTEGER,
        $2::VARCHAR(50),
        $3::INTEGER,
        $4::VARCHAR(255)
      ) as result
    `

    const result = await databaseService.query(query, [
      validatedData.client_id,
      validatedData.license_key,
      validatedData.license_term_id,
      authResult.session.email
    ])

    const assignmentResult = result.rows[0].result

    if (assignmentResult.success) {
      return createSuccessResponse({
        message: 'License assigned successfully',
        assignment: assignmentResult
      })
    } else {
      return createErrorResponse(assignmentResult.error, ApiErrorCode.VALIDATION_ERROR, HttpStatus.BAD_REQUEST)
    }

  } catch (error) {
    console.error('Error assigning license:', error)
    
    if (error instanceof z.ZodError) {
      return createErrorResponse('Invalid request data', ApiErrorCode.VALIDATION_ERROR, HttpStatus.BAD_REQUEST, error.errors)
    }

    return createErrorResponse('Failed to assign license', ApiErrorCode.INTERNAL_ERROR, HttpStatus.INTERNAL_SERVER_ERROR)
  }
}

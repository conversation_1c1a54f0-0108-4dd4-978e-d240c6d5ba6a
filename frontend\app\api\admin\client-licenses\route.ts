/**
 * Client License Management API
 * 
 * Admin endpoints for activating and managing client licenses
 */

import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import { databaseService } from '@/lib/services/database-service'
import { requireAdmin } from '@/lib/api/auth-middleware'
import { createSuccessResponse, createErrorResponse, ApiErrorCode, HttpStatus } from '@/lib/api/response'
import { resolveTenantContext } from '@/lib/tenant/context'

// Validation schemas
const activateLicenseSchema = z.object({
  license_key: z.string().min(1, 'License key is required'),
  license_term_id: z.number().min(1, 'License term is required')
})

const updateLicenseSchema = z.object({
  is_active: z.boolean().optional(),
  expiry_date: z.string().optional()
})

/**
 * GET /api/admin/client-licenses
 * Get client licenses for the current tenant (admin only)
 */
export async function GET(request: NextRequest) {
  try {
    // Require admin authentication
    const authResult = await requireAdmin(request)
    if (!authResult.success) {
      return authResult.response
    }

    // Get tenant context
    const tenantResult = await resolveTenantContext(authResult.session)
    if (!tenantResult.success) {
      return tenantResult.response!
    }
    const tenantContext = tenantResult.tenant!

    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const search = searchParams.get('search')

    // Use the client license status view for simplified querying
    let whereClause = 'client_id = $1'
    const params: any[] = [tenantContext.clientId]
    let paramIndex = 2

    if (status) {
      whereClause += ` AND status = $${paramIndex}`
      params.push(status)
      paramIndex++
    }

    if (search) {
      whereClause += ` AND (name ILIKE $${paramIndex} OR license_key ILIKE $${paramIndex})`
      params.push(`%${search}%`)
      paramIndex++
    }

    // Get client licenses using the view
    const query = `
      SELECT *
      FROM metadata.v_client_license_status
      WHERE ${whereClause}
      ORDER BY activation_date DESC
    `

    const result = await databaseService.query(query, params)

    return createSuccessResponse(result.rows)

  } catch (error) {
    console.error('Error fetching client licenses:', error)
    return createErrorResponse('Failed to fetch client licenses', ApiErrorCode.INTERNAL_ERROR, HttpStatus.INTERNAL_SERVER_ERROR)
  }
}

/**
 * POST /api/admin/client-licenses
 * Activate a license key for the current tenant (admin only)
 */
export async function POST(request: NextRequest) {
  try {
    // Require admin authentication
    const authResult = await requireAdmin(request)
    if (!authResult.success) {
      return authResult.response
    }

    // Get tenant context
    const tenantResult = await resolveTenantContext(authResult.session)
    if (!tenantResult.success) {
      return tenantResult.response!
    }
    const tenantContext = tenantResult.tenant!

    const body = await request.json()
    const validatedData = activateLicenseSchema.parse(body)

    // Use the database function to assign the license
    const query = `
      SELECT metadata.assign_license_to_client(
        $1::INTEGER,
        $2::VARCHAR(50),
        $3::INTEGER,
        $4::VARCHAR(255)
      ) as result
    `

    const result = await databaseService.query(query, [
      tenantContext.clientId,
      validatedData.license_key,
      validatedData.license_term_id,
      authResult.session.email
    ])

    const assignmentResult = result.rows[0].result

    if (!assignmentResult.success) {
      return createErrorResponse(assignmentResult.error, ApiErrorCode.VALIDATION_ERROR, HttpStatus.BAD_REQUEST)
    }

    return createSuccessResponse({
      message: 'License activated successfully',
      assignment: assignmentResult
    })

  } catch (error) {
    console.error('Error activating license:', error)
    
    if (error instanceof z.ZodError) {
      return createErrorResponse('Invalid request data', ApiErrorCode.VALIDATION_ERROR, HttpStatus.BAD_REQUEST, error.errors)
    }

    return createErrorResponse('Failed to activate license', ApiErrorCode.INTERNAL_ERROR, HttpStatus.INTERNAL_SERVER_ERROR)
  }
}

/**
 * PUT /api/admin/client-licenses/[id]
 * Update client license (admin only)
 */
export async function PUT(request: NextRequest) {
  try {
    // Require admin authentication
    const authResult = await requireAdmin(request)
    if (!authResult.success) {
      return authResult.response
    }

    // Get tenant context
    const tenantResult = await resolveTenantContext(authResult.session)
    if (!tenantResult.success) {
      return tenantResult.response!
    }
    const tenantContext = tenantResult.tenant!

    const { searchParams } = new URL(request.url)
    const clientLicenseId = searchParams.get('id')

    if (!clientLicenseId) {
      return createErrorResponse('Client license ID is required', ApiErrorCode.VALIDATION_ERROR, HttpStatus.BAD_REQUEST)
    }

    const body = await request.json()
    const validatedData = updateLicenseSchema.parse(body)

    // Build update query
    const updateFields = []
    const params = []
    let paramIndex = 1

    if (validatedData.is_active !== undefined) {
      updateFields.push(`is_active = $${paramIndex}`)
      params.push(validatedData.is_active)
      paramIndex++
    }

    if (validatedData.expiry_date !== undefined) {
      updateFields.push(`expiry_date = $${paramIndex}`)
      params.push(validatedData.expiry_date)
      paramIndex++
    }

    if (updateFields.length === 0) {
      return createErrorResponse('No fields to update', ApiErrorCode.VALIDATION_ERROR, HttpStatus.BAD_REQUEST)
    }

    params.push(clientLicenseId, tenantContext.clientId)

    const query = `
      UPDATE metadata.admin_client_licenses
      SET ${updateFields.join(', ')}, changed_on = CURRENT_TIMESTAMP
      WHERE client_license_id = $${paramIndex} AND client_id = $${paramIndex + 1}
      RETURNING
        client_license_id,
        license_key,
        license_type_id,
        license_term_id,
        activation_date,
        expiry_date,
        is_active
    `

    const result = await databaseService.query(query, params)

    if (result.rows.length === 0) {
      return createErrorResponse('Client license not found', ApiErrorCode.NOT_FOUND, HttpStatus.NOT_FOUND)
    }

    return createSuccessResponse({
      message: 'Client license updated successfully',
      license: result.rows[0]
    })

  } catch (error) {
    console.error('Error updating client license:', error)
    
    if (error instanceof z.ZodError) {
      return createErrorResponse('Invalid request data', ApiErrorCode.VALIDATION_ERROR, HttpStatus.BAD_REQUEST, error.errors)
    }

    return createErrorResponse('Failed to update client license', ApiErrorCode.INTERNAL_ERROR, HttpStatus.INTERNAL_SERVER_ERROR)
  }
}

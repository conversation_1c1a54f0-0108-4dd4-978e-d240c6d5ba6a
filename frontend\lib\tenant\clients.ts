import { executeQuery, executeQuery<PERSON>ingle } from '@/lib/database';
import { TenantContext } from '@/lib/types';

// Simple schema existence check
async function schemaExists(schemaName: string): Promise<boolean> {
  try {
    const result = await executeQuerySingle(`
      SELECT EXISTS (
        SELECT FROM information_schema.schemata
        WHERE schema_name = $1
      )
    `, [schemaName]);

    return result.success && result.data?.exists === true;
  } catch (error) {
    console.error('Error checking schema existence:', error);
    return false;
  }
}

// Client lookup result interface
export interface ClientLookupResult {
  success: boolean;
  client?: TenantContext;
  error?: string;
  errorCode?: 'NOT_FOUND' | 'MULTIPLE_FOUND' | 'DATABASE_ERROR' | 'INVALID_INPUT';
}

// Get tenant by email domain
export async function getTenantByDomain(domain: string) {
  const query = `
    SELECT
      c.tenant_id as tenant_id,
      c.name as tenant_name,
      c.tenant_id as schema_name,
      c.domain[1] as subdomain,
      c.status,
      c.created_on as created_on
    FROM metadata.clients c
    WHERE $1 = ANY(c.domain) AND c.status = 'A'
    LIMIT 1
  `;

  const result = await executeQuerySingle(query, [domain], { schema: 'metadata' });
  return result.success ? result.data : null;
}

// Get all clients (for super-admin use)
export async function getAllClients() {
  const query = `
    SELECT
      id,
      name as name,
      domain[1] as domain,
      status,
      created_on,
      changed_on
    FROM metadata.clients
    WHERE status = 'A'
    ORDER BY name ASC
  `;

  const result = await executeQuery(query, [], { schema: 'metadata' });

  if (!result.success) {
    throw new Error(`Failed to get all clients: ${result.error}`);
  }

  return result.data;
}

export async function getClientByDomain(domain: string) {
  if (!domain) {
    throw new Error('Domain parameter is required');
  }

  const query = `
    SELECT
      id,
      name,
      domain[1] as domain,
      status,
      created_on,
      changed_on
    FROM metadata.clients
    WHERE $1 = ANY(domain) AND status = 'A'
    LIMIT 1
  `;

  const result = await executeQuerySingle(query, [domain], { schema: 'metadata' });

  if (!result.success) {
    console.error('Error fetching client by domain:', {
      domain,
      error: result.error,
      errorCode: result.errorCode
    });
    throw new Error('Failed to retrieve client information');
  }

  if (!result.data) {
    return null;
  }

  // Parse JSON settings if needed
  const client = result.data;
  if (client.settings && typeof client.settings === 'string') {
    try {
      client.settings = JSON.parse(client.settings);
    } catch (parseError) {
      console.warn('Failed to parse client settings JSON:', parseError);
      client.settings = {};
    }
  }

  return client;
}

// Cache for client lookups to prevent repeated database calls
const clientLookupCache = new Map<string, { timestamp: number; result: ClientLookupResult }>();
const CLIENT_CACHE_TTL = 5 * 60 * 1000; // 5 minutes

// Get client by email domain with enhanced error handling
export async function getClientByEmailDomain(email: string): Promise<ClientLookupResult> {
  const startTime = Date.now()

  if (!email || typeof email !== 'string') {
    return {
      success: false,
      error: 'Invalid email provided',
      errorCode: 'INVALID_INPUT'
    };
  }

  // Check cache first
  const cached = clientLookupCache.get(email);
  if (cached && (Date.now() - cached.timestamp) < CLIENT_CACHE_TTL) {
    console.log(`🎯 [CLIENT] Using cached result for: ${email}`);
    return cached.result;
  }

  try {
    const domain = email.split('@')[1];
    if (!domain) {
      return {
        success: false,
        error: 'Invalid email format',
        errorCode: 'INVALID_INPUT'
      };
    }

    console.log(`🔍 [CLIENT] Looking up client for domain: ${domain}`)



    const query = `
      SELECT
        c.id,
        c.name,
        c.tenant_id,
        c.created_on
      FROM metadata.clients c
      WHERE $1 = ANY(c.domain) AND c.status = 'A'
      LIMIT 1
    `;

    const result = await executeQuery(query, [domain], { schema: 'metadata' });

    const duration = Date.now() - startTime

    if (!result.success) {
      console.log(`❌ [CLIENT] Database error (${duration}ms): ${result.error}`)

      const errorResult = {
        success: false,
        error: 'An error occurred, contact support',
        errorCode: 'DATABASE_ERROR' as const
      };

      // Cache the error result to prevent repeated failures
      clientLookupCache.set(email, {
        timestamp: Date.now(),
        result: errorResult
      });

      return errorResult;
    }

    if (!result.data || result.data.length === 0) {
      console.log(`❌ [CLIENT] No client found for domain (${duration}ms): ${domain}`)

      return {
        success: false,
        error: 'A client was not found, contact support',
        errorCode: 'NOT_FOUND'
      };
    }

    if (result.data.length > 1) {
      console.error(`❌ [CLIENT] Multiple clients found for domain (${duration}ms):`, domain, 'Count:', result.data.length);
      return {
        success: false,
        error: 'An error occurred, contact support',
        errorCode: 'MULTIPLE_FOUND'
      };
    }

    // Single client found - create tenant context
    const clientData = result.data[0];

    // Use the tenant_id from the database as the schema name
    const tenantSchema = clientData.tenant_id;

    console.log(`✅ [CLIENT] Found client (${duration}ms): ${clientData.name} (ID: ${clientData.id})`);

    const schemaReady = await schemaExists(tenantSchema);

    const tenantContext: TenantContext = {
      clientId: clientData.id.toString(),
      clientName: clientData.name,
      tenantId: clientData.tenant_id,
      tenantSchema: tenantSchema,
      schemaName: tenantSchema, // Alias for backward compatibility
      domains: [domain], // Use the domain that was used for lookup
      isActive: true, // Assume active since we found it
      settings: { schemaReady }, // Include schema readiness status
      createdAt: new Date(clientData.created_on),
      updatedAt: new Date() // Use current date since we don't have changed_on
    };

    const successResult = {
      success: true,
      client: tenantContext
    };

    // Cache the successful result
    clientLookupCache.set(email, {
      timestamp: Date.now(),
      result: successResult
    });

    return successResult;

  } catch (error) {
    const duration = Date.now() - startTime
    console.error(`❌ [CLIENT] Database error fetching client by email domain (${duration}ms):`, {
      email,
      error: error instanceof Error ? error.message : String(error)
    });

    const errorResult = {
      success: false,
      error: 'An error occurred, contact support',
      errorCode: 'DATABASE_ERROR' as const
    };

    // Cache the error result to prevent repeated failures
    clientLookupCache.set(email, {
      timestamp: Date.now(),
      result: errorResult
    });

    return errorResult;
  }
}

// Create a new client
export async function createClient(clientData: {
  name: string;
  domain: string;
  status?: string;
  settings?: Record<string, any>;
}) {
  const query = `
    INSERT INTO metadata.clients (
      name,
      domain,
      status
    )
    VALUES ($1, $2, $3)
    RETURNING
      id as id,
      name as name,
      domain[1] as domain,
      status,
      created_on,
      changed_on
  `;

  const values = [
    clientData.name,
    [clientData.domain], // Array for domain
    clientData.status || 'A'
  ];

  const result = await executeQuerySingle(query, values, { schema: 'metadata' });

  if (!result.success) {
    console.error('Error creating client:', result.error);
    throw new Error('Failed to create client');
  }

  return result.data;
}

// Update an existing client
export async function updateClient(
  clientId: string,
  updates: Partial<{
    name: string;
    domain: string;
    status: string;
    settings: Record<string, any>;
  }>
) {
  // Build dynamic update query
  const updateFields: string[] = [];
  const values: any[] = [];
  let paramIndex = 1;

  if (updates.name !== undefined) {
    updateFields.push(`name = $${paramIndex++}`);
    values.push(updates.name);
  }

  if (updates.domain !== undefined) {
    updateFields.push(`domain = $${paramIndex++}`);
    values.push([updates.domain]); // Array for domain
  }

  if (updates.status !== undefined) {
    updateFields.push(`status = $${paramIndex++}`);
    values.push(updates.status);
  }

  if (updateFields.length === 0) {
    return null; // Nothing to update
  }

  // Add id to values array
  values.push(clientId);

  const query = `
    UPDATE metadata.clients
    SET ${updateFields.join(', ')}, changed_on = CURRENT_TIMESTAMP
    WHERE id = $${paramIndex}
    RETURNING
      id,
      name as name,
      domain[1] as domain,
      status,
      created_on,
      changed_on
  `;

  const result = await executeQuerySingle(query, values, { schema: 'metadata' });

  if (!result.success) {
    console.error('Error updating client:', result.error);
    throw new Error('Failed to update client');
  }

  return result.data;
}

/**
 * Get tenant context by user ID
 * This function looks up which tenant a user belongs to by:
 * 1. Getting the user's email from Cognito user attributes
 * 2. Extracting the domain from the email
 * 3. Looking up the tenant by domain in the clients table
 */
export async function getTenantByUserId(userId: string): Promise<TenantContext | null> {
  try {
    console.log(`🔍 getTenantByUserId called for user: ${userId}`);

    // First, we need to get the user's email from their session/token
    // Since we don't have direct access to Cognito here, we'll need to get it from the session
    // This function should be called with the user's email already available

    // For now, we'll return null and let the calling code handle email-based lookup
    // The proper flow should be: session.email -> getTenantByEmailDomain
    console.log(`⚠️ getTenantByUserId: User ID lookup not implemented - use email-based lookup instead`);
    return null;

  } catch (error) {
    console.error('Error getting tenant by user ID:', error);
    return null;
  }
}

/**
 * Get tenant context by tenant ID
 */
export async function getTenantById(tenantId: string): Promise<TenantContext | null> {
  try {
    const query = `
      SELECT
        t.TenantID as tenant_id,
        t.TenantName as tenant_name,
        t.SchemaName as schema_name,
        t.Subdomain as subdomain,
        t.Status as status,
        t.CreatedAt as created_on,
        t.UpdatedAt as changed_on
      FROM tenant_management.tenants t
      WHERE t.TenantID = $1 AND t.Status = 'active'
    `;

    const result = await executeQuerySingle(query, [tenantId], { schema: 'tenant_management' });

    if (!result.success || !result.data) {
      console.log(`Tenant not found: ${tenantId}`);
      return null;
    }

    const tenantData = result.data;
    const schemaReady = await schemaExists(tenantData.schema_name);

    const tenantContext: TenantContext = {
      clientId: tenantData.tenant_id.toString(),
      clientName: tenantData.tenant_name,
      tenantId: tenantData.tenant_id.toString(),
      tenantSchema: tenantData.schema_name,
      schemaName: tenantData.schema_name, // Alias for backward compatibility
      domains: [], // Will be populated separately if needed
      isActive: tenantData.status === 'active',
      settings: { schemaReady },
      createdAt: new Date(tenantData.created_on),
      updatedAt: tenantData.changed_on ? new Date(tenantData.changed_on) : null
    };

    return tenantContext;

  } catch (error) {
    console.error('Error getting tenant by ID:', error);
    return null;
  }
}

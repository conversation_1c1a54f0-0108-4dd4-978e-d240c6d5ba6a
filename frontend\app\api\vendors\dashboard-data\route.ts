/**
 * Vendors Dashboard Data API
 * 
 * Provides vendor data for the vendor dashboard
 */

import { NextRequest } from 'next/server'
import { withAuth } from '@/lib/api/auth-middleware'
import { resolveTenantContext } from '@/lib/tenant/context'
import { executeQuery, schemaExists } from '@/lib/database'
import {
  createSuccessResponse,
  createErrorResponse,
  ApiErrorCode,
  HttpStatus
} from '@/lib/api/response'

interface VendorData {
  vendor: string
  totalSpend: number
  renewalCount: number
  avgSpend: number
  lastRenewal?: string
  upcomingRenewals?: number
  reliabilityScore?: number
  currency?: string
}

export const GET = withAuth(async (request: NextRequest, session) => {
  console.log('[VENDORS-DASHBOARD-API] GET request received')

  try {
    // Resolve tenant context
    const tenant = await resolveTenantContext(session.email)
    if (!tenant) {
      console.error('[VENDORS-DASHBOARD-API] Failed to resolve tenant context')
      return createErrorResponse(
        'Tenant context not found',
        ApiErrorCode.TENANT_NOT_FOUND,
        HttpStatus.BAD_REQUEST
      )
    }

    console.log(`[VENDORS-DASHBOARD-API] Resolved tenant: ${tenant.tenantId}`)

    // Check if tenant schema exists
    const schemaExistsResult = await schemaExists(tenant.tenantSchema)
    if (!schemaExistsResult) {
      console.log(`[VENDORS-DASHBOARD-API] Tenant schema ${tenant.tenantSchema} not ready yet`)
      return createSuccessResponse([], 'Vendor dashboard data retrieved successfully')
    }

    // Query vendor data from tenant schema
    const query = `
      SELECT 
        vendor,
        COUNT(*) as renewal_count,
        SUM(cost) as total_spend,
        AVG(cost) as avg_spend,
        MAX(renewal_date) as last_renewal,
        COUNT(CASE WHEN renewal_date > CURRENT_DATE THEN 1 END) as upcoming_renewals,
        'USD' as currency
      FROM "${tenant.tenantSchema}".tenant_renewals
      WHERE vendor IS NOT NULL AND vendor != ''
      GROUP BY vendor
      ORDER BY total_spend DESC
      LIMIT 50
    `

    console.log(`[VENDORS-DASHBOARD-API] Executing query for schema: ${tenant.tenantSchema}`)
    const result = await executeQuery(query)

    if (!result.success) {
      console.error('[VENDORS-DASHBOARD-API] Database query failed:', result.error)
      return createErrorResponse(
        'Failed to fetch vendor data',
        ApiErrorCode.INTERNAL_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }

    // Transform data
    const vendorData: VendorData[] = result.data.map((row: any) => ({
      vendor: row.vendor,
      totalSpend: parseFloat(row.total_spend) || 0,
      renewalCount: parseInt(row.renewal_count) || 0,
      avgSpend: parseFloat(row.avg_spend) || 0,
      lastRenewal: row.last_renewal,
      upcomingRenewals: parseInt(row.upcoming_renewals) || 0,
      reliabilityScore: Math.random() * 100, // TODO: Implement actual reliability scoring
      currency: row.currency || 'USD'
    }))

    console.log(`[VENDORS-DASHBOARD-API] Returning ${vendorData.length} vendor records`)
    return createSuccessResponse(vendorData, 'Vendor dashboard data retrieved successfully')

  } catch (error) {
    console.error('[VENDORS-DASHBOARD-API] Error:', error)
    return createErrorResponse(
      'Failed to fetch vendor dashboard data',
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    )
  }
}, {
  requireAuth: true
})

/**
 * Scheduled Alert Check API Endpoint
 * 
 * This endpoint is designed to be called by a scheduled job (cron, AWS Lambda, etc.)
 * to automatically check and send renewal alerts for all tenants
 * 
 * POST /api/cron/check-alerts
 */

import { NextRequest } from 'next/server';
import { NotificationService } from '@/lib/services/notification-service';
import { databaseService } from '@/lib/services/database-service';
import { TenantContext } from '@/lib/types';
import {
  createSuccessResponse,
  createErrorResponse,
  ApiErrorCode,
  HttpStatus,
  withErrorHandling
} from '@/lib/api/response';

interface TenantInfo {
  tenant_id: string;
  tenant_name: string;
  schema_name: string;
}

// POST /api/cron/check-alerts
export const POST = withErrorHandling(async (request: NextRequest) => {
  try {
    // Verify this is a scheduled job call (check for authorization header or API key)
    const authHeader = request.headers.get('authorization');
    const cronSecret = process.env.CRON_SECRET;
    
    if (!cronSecret || authHeader !== `Bearer ${cronSecret}`) {
      return createErrorResponse(
        'Unauthorized - Invalid cron secret',
        ApiErrorCode.UNAUTHORIZED,
        HttpStatus.UNAUTHORIZED
      );
    }

    console.log('Starting scheduled alert check for all tenants...');
    
    const results: Array<{
      tenantId: string;
      tenantName: string;
      success: boolean;
      error?: string;
      alertsSent?: number;
    }> = [];

    try {
      // Get all active tenants
      const tenantsQuery = `
        SELECT 
          tenant_id,
          tenant_name,
          schema_name
        FROM tenant_management.clients 
        WHERE status = 'A'
        ORDER BY tenant_name
      `;

      const tenantsResult = await databaseService.query(tenantsQuery);

      if (!tenantsResult.rows) {
        return createErrorResponse(
          'Failed to fetch tenants',
          ApiErrorCode.DATABASE_ERROR,
          HttpStatus.INTERNAL_SERVER_ERROR
        );
      }

      console.log(`Found ${tenantsResult.rows.length} active tenants`);

      // Process each tenant
      for (const tenant of tenantsResult.rows) {
        try {
          console.log(`Checking alerts for tenant: ${tenant.tenant_name} (${tenant.tenant_id})`);

          const tenantContext: TenantContext = {
            clientId: tenant.tenant_id,
            clientName: tenant.tenant_name,
            tenantId: tenant.tenant_id,
            tenantSchema: tenant.schema_name,
            schemaName: tenant.schema_name, // Alias for backward compatibility
            domains: [],
            isActive: true,
            settings: {
              schemaReady: true,
              features: {
                renewals: true,
                vendors: true,
                reports: true,
                notifications: true
              }
            },
            createdAt: new Date(tenant.created_on || new Date()),
            updatedAt: tenant.changed_on ? new Date(tenant.changed_on) : null
          };

          // Check and send alerts for this tenant
          await NotificationService.checkAndSendAlerts(tenantContext);

          results.push({
            tenantId: tenant.tenant_id,
            tenantName: tenant.tenant_name,
            success: true
          });

          console.log(`✅ Completed alert check for tenant: ${tenant.tenant_name}`);

        } catch (error) {
          console.error(`❌ Error checking alerts for tenant ${tenant.tenant_name}:`, error);
          
          results.push({
            tenantId: tenant.tenant_id,
            tenantName: tenant.tenant_name,
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      // Summary
      const successCount = results.filter(r => r.success).length;
      const failureCount = results.filter(r => !r.success).length;

      console.log(`Alert check completed: ${successCount} successful, ${failureCount} failed`);

      return createSuccessResponse({
        message: 'Scheduled alert check completed',
        timestamp: new Date().toISOString(),
        summary: {
          totalTenants: results.length,
          successful: successCount,
          failed: failureCount
        },
        results: results
      });

    } catch (error) {
      console.error('Error in scheduled alert check:', error);
      return createErrorResponse(
        'Failed to complete scheduled alert check',
        ApiErrorCode.INTERNAL_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }

  } catch (error) {
    console.error('Error in cron check-alerts endpoint:', error);
    return createErrorResponse(
      'Internal server error',
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
});

// GET method for health check
export const GET = withErrorHandling(async (request: NextRequest) => {
  return createSuccessResponse({
    message: 'Cron alert check endpoint is healthy',
    timestamp: new Date().toISOString()
  });
});

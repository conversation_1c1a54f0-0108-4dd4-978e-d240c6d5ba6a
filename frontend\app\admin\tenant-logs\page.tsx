'use client'

import React from 'react'
// import { TenantLogViewer } from '@/components/admin/TenantLogViewer'
import PageAccessGuard from '@/components/auth/PageAccessGuard'

function TenantLogsPage() {
  return (
    <div className="overview-container">
      {/* Header - Using same styling as overview page */}
      <div className="page-header">
        <div className="page-title-section">
          <h1 className="page-title">Tenant Logs</h1>
          <p className="page-subtitle">Monitor and analyze tenant activity and system events</p>
        </div>

        <div className="page-actions">
          <button className="btn-secondary">
            📊 Export Logs
          </button>
          <button className="btn-primary">
            🔄 Refresh
          </button>
        </div>
      </div>

      {/* Content Section */}
      <div className="section-container">
        <div className="section-header">
          <h2 className="section-title">Log Viewer</h2>
          <p className="section-subtitle">Real-time tenant activity monitoring</p>
        </div>

        <div className="card">
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🚧</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Feature Under Development</h3>
            <p className="text-gray-600">
              The tenant logs viewer is currently being developed. This will provide comprehensive
              monitoring and analysis of tenant activities, system events, and audit trails.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function TenantLogsPageWithAccess() {
  return (
    <PageAccessGuard pageName="tenant-logs" redirectTo="/overview">
      <TenantLogsPage />
    </PageAccessGuard>
  )
}

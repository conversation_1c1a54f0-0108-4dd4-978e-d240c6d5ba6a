/**
 * Database Query Builder - Eliminates Duplicate Query Patterns
 * 
 * This module provides reusable query builders for common database operations,
 * eliminating the duplicate SELECT, INSERT, UPDATE, DELETE patterns across the codebase.
 */

import { executeQuery, executeQ<PERSON>y<PERSON>ingle, DbResult } from '@/lib/database';
import { handleError } from '@/lib/utils/error-handler';

export interface QueryBuilderOptions {
  schema?: string;
  timeout?: number;
  validateTenantQuery?: boolean;
}

export interface SelectOptions extends QueryBuilderOptions {
  limit?: number;
  offset?: number;
  orderBy?: string;
  orderDirection?: 'ASC' | 'DESC';
}

export interface PaginationResult<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  hasMore: boolean;
}

/**
 * Generic SELECT query builder
 */
export class SelectQueryBuilder<T = any> {
  private tableName: string;
  private selectFields: string[] = ['*'];
  private whereConditions: string[] = [];
  private joinClauses: string[] = [];
  private groupByFields: string[] = [];
  private havingConditions: string[] = [];
  private orderByClause: string = '';
  private limitClause: string = '';
  private params: any[] = [];
  private paramIndex: number = 1;

  constructor(tableName: string) {
    this.tableName = tableName;
  }

  select(fields: string | string[]): this {
    this.selectFields = Array.isArray(fields) ? fields : [fields];
    return this;
  }

  where(condition: string, value?: any): this {
    this.whereConditions.push(condition);
    if (value !== undefined) {
      this.params.push(value);
      this.paramIndex++;
    }
    return this;
  }

  whereIn(field: string, values: any[]): this {
    if (values.length === 0) return this;
    
    const placeholders = values.map(() => `$${this.paramIndex++}`).join(', ');
    this.whereConditions.push(`${field} IN (${placeholders})`);
    this.params.push(...values);
    return this;
  }

  join(table: string, condition: string): this {
    this.joinClauses.push(`INNER JOIN ${table} ON ${condition}`);
    return this;
  }

  leftJoin(table: string, condition: string): this {
    this.joinClauses.push(`LEFT JOIN ${table} ON ${condition}`);
    return this;
  }

  groupBy(fields: string | string[]): this {
    this.groupByFields = Array.isArray(fields) ? fields : [fields];
    return this;
  }

  having(condition: string, value?: any): this {
    this.havingConditions.push(condition);
    if (value !== undefined) {
      this.params.push(value);
      this.paramIndex++;
    }
    return this;
  }

  orderBy(field: string, direction: 'ASC' | 'DESC' = 'ASC'): this {
    this.orderByClause = `ORDER BY ${field} ${direction}`;
    return this;
  }

  limit(count: number, offset?: number): this {
    this.limitClause = `LIMIT ${count}`;
    if (offset !== undefined) {
      this.limitClause += ` OFFSET ${offset}`;
    }
    return this;
  }

  build(): { query: string; params: any[] } {
    let query = `SELECT ${this.selectFields.join(', ')} FROM ${this.tableName}`;
    
    if (this.joinClauses.length > 0) {
      query += ` ${this.joinClauses.join(' ')}`;
    }
    
    if (this.whereConditions.length > 0) {
      query += ` WHERE ${this.whereConditions.join(' AND ')}`;
    }
    
    if (this.groupByFields.length > 0) {
      query += ` GROUP BY ${this.groupByFields.join(', ')}`;
    }
    
    if (this.havingConditions.length > 0) {
      query += ` HAVING ${this.havingConditions.join(' AND ')}`;
    }
    
    if (this.orderByClause) {
      query += ` ${this.orderByClause}`;
    }
    
    if (this.limitClause) {
      query += ` ${this.limitClause}`;
    }

    return { query, params: this.params };
  }

  async execute(options: SelectOptions = {}): Promise<DbResult<T[]>> {
    const { query, params } = this.build();
    return executeQuery<T>(query, params, options);
  }

  async executeOne(options: SelectOptions = {}): Promise<DbResult<T>> {
    const { query, params } = this.build();
    const result = await executeQuerySingle<T>(query, params, options);
    return result as DbResult<T>;
  }

  async executePaginated(
    page: number = 1, 
    limit: number = 20, 
    options: SelectOptions = {}
  ): Promise<DbResult<PaginationResult<T>>> {
    // First, get total count
    const countBuilder = new SelectQueryBuilder(this.tableName)
      .select('COUNT(*) as total');

    // Copy where conditions
    for (const condition of this.whereConditions) {
      countBuilder.whereConditions.push(condition);
    }

    // Copy parameters
    countBuilder.params.push(...this.params);

    // Copy join clauses
    countBuilder.joinClauses.push(...this.joinClauses);

    const countResult = await countBuilder.executeOne(options);
    if (!countResult.success) {
      return countResult as any;
    }

    const total = parseInt(countResult.data?.total || '0');
    const offset = (page - 1) * limit;

    // Then get paginated data
    this.limit(limit, offset);
    const dataResult = await this.execute(options);
    
    if (!dataResult.success) {
      return dataResult as any;
    }

    return {
      success: true,
      data: {
        data: dataResult.data || [],
        total,
        page,
        limit,
        hasMore: offset + limit < total
      }
    };
  }
}

/**
 * Generic INSERT query builder
 */
export class InsertQueryBuilder<T = any> {
  private tableName: string;
  private insertData: Record<string, any> = {};
  private returningFields: string[] = [];
  private onConflictClause: string = '';

  constructor(tableName: string) {
    this.tableName = tableName;
  }

  values(data: Record<string, any>): this {
    this.insertData = { ...this.insertData, ...data };
    return this;
  }

  returning(fields: string | string[]): this {
    this.returningFields = Array.isArray(fields) ? fields : [fields];
    return this;
  }

  onConflict(clause: string): this {
    this.onConflictClause = clause;
    return this;
  }

  build(): { query: string; params: any[] } {
    const fields = Object.keys(this.insertData);
    const values = Object.values(this.insertData);
    const placeholders = fields.map((_, index) => `$${index + 1}`);

    let query = `INSERT INTO ${this.tableName} (${fields.join(', ')}) VALUES (${placeholders.join(', ')})`;
    
    if (this.onConflictClause) {
      query += ` ${this.onConflictClause}`;
    }
    
    if (this.returningFields.length > 0) {
      query += ` RETURNING ${this.returningFields.join(', ')}`;
    }

    return { query, params: values };
  }

  async execute(options: QueryBuilderOptions = {}): Promise<DbResult<T>> {
    const { query, params } = this.build();
    const result = await executeQuerySingle<T>(query, params, options);
    return result as DbResult<T>;
  }
}

/**
 * Generic UPDATE query builder
 */
export class UpdateQueryBuilder<T = any> {
  private tableName: string;
  private updateData: Record<string, any> = {};
  private whereConditions: string[] = [];
  private returningFields: string[] = [];
  private params: any[] = [];
  private paramIndex: number = 1;

  constructor(tableName: string) {
    this.tableName = tableName;
  }

  set(data: Record<string, any>): this {
    this.updateData = { ...this.updateData, ...data };
    return this;
  }

  where(condition: string, value?: any): this {
    this.whereConditions.push(condition);
    if (value !== undefined) {
      this.params.push(value);
      this.paramIndex++;
    }
    return this;
  }

  returning(fields: string | string[]): this {
    this.returningFields = Array.isArray(fields) ? fields : [fields];
    return this;
  }

  build(): { query: string; params: any[] } {
    const updateFields = Object.keys(this.updateData);
    const updateValues = Object.values(this.updateData);
    
    // Build SET clause
    const setClause = updateFields.map((field, index) => 
      `${field} = $${index + 1}`
    ).join(', ');

    let query = `UPDATE ${this.tableName} SET ${setClause}`;
    let allParams = [...updateValues];

    // Add WHERE conditions
    if (this.whereConditions.length > 0) {
      // Adjust parameter indices for WHERE conditions
      const adjustedConditions = this.whereConditions.map(condition => {
        let adjustedCondition = condition;
        for (let i = this.params.length - 1; i >= 0; i--) {
          const oldParam = `$${i + 1}`;
          const newParam = `$${updateFields.length + i + 1}`;
          adjustedCondition = adjustedCondition.replace(oldParam, newParam);
        }
        return adjustedCondition;
      });
      
      query += ` WHERE ${adjustedConditions.join(' AND ')}`;
      allParams = [...allParams, ...this.params];
    }

    if (this.returningFields.length > 0) {
      query += ` RETURNING ${this.returningFields.join(', ')}`;
    }

    return { query, params: allParams };
  }

  async execute(options: QueryBuilderOptions = {}): Promise<DbResult<T>> {
    try {
      const { query, params } = this.build();
      const result = await executeQuerySingle<T>(query, params, options);
      return result as DbResult<T>;
    } catch (error) {
      handleError(error as Error, {
        component: 'query-builder',
        operation: 'execute',
        metadata: { tableName: this.tableName, options }
      });

      return {
        success: false,
        error: 'Query execution failed',
        data: null,
        rowCount: 0
      } as DbResult<T>;
    }
  }
}

/**
 * Convenience functions for common query patterns
 */
export function select<T = any>(tableName: string): SelectQueryBuilder<T> {
  return new SelectQueryBuilder<T>(tableName);
}

export function insert<T = any>(tableName: string): InsertQueryBuilder<T> {
  return new InsertQueryBuilder<T>(tableName);
}

export function update<T = any>(tableName: string): UpdateQueryBuilder<T> {
  return new UpdateQueryBuilder<T>(tableName);
}

/**
 * Common query patterns
 */
export async function findById<T = any>(
  tableName: string, 
  id: string | number, 
  options: SelectOptions = {}
): Promise<DbResult<T>> {
  return select<T>(tableName)
    .where('id = $1', id)
    .executeOne(options);
}

export async function findByField<T = any>(
  tableName: string, 
  field: string, 
  value: any, 
  options: SelectOptions = {}
): Promise<DbResult<T[]>> {
  return select<T>(tableName)
    .where(`${field} = $1`, value)
    .execute(options);
}

export async function findActive<T = any>(
  tableName: string, 
  options: SelectOptions = {}
): Promise<DbResult<T[]>> {
  return select<T>(tableName)
    .where('status = $1', 'A')
    .execute(options);
}

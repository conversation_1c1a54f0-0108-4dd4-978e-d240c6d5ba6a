/**
 * License Activations API
 * 
 * Admin endpoint for listing license activations
 * GET /api/admin/licenses/activations - List all license activations
 */

import { NextRequest } from 'next/server';
import { fetchAuthSession, fetchUserAttributes } from 'aws-amplify/auth';
import { executeQuery } from '@/lib/database';
import {
  createSuccessResponse,
  createErrorResponse,
  ApiErrorCode,
  HttpStatus,
  withErrorHandling
} from '@/lib/api/response';

interface LicenseActivation {
  id: number;
  client_id: number;
  license_key: string;
  activation_status: string;
  activated_at: string;
  activated_by: string;
  expires_at: string | null;
  name: string;
  license_type: string;
  max_users: number;
  max_tenants: number;
  user_count: number;
  tenant_count: number;
  last_usage: string | null;
}

/**
 * List all license activations (Admin only)
 */
export const GET = withErrorHandling(async (request: NextRequest) => {
  try {
    // Verify authentication
    const session = await fetchAuthSession();
    if (!session?.tokens?.idToken) {
      return createErrorResponse('Authentication required', ApiErrorCode.UNAUTHORIZED, HttpStatus.UNAUTHORIZED);
    }

    // Get user attributes to check admin status
    const attributes = await fetchUserAttributes();
    const userGroups = attributes['cognito:groups']?.split(',') || [];
    const adminGroups = ['admin', 'super-admin']

    if (!userGroups.some(group => adminGroups.includes(group.toLowerCase()))) {
      return createErrorResponse('Admin access required', ApiErrorCode.FORBIDDEN, HttpStatus.FORBIDDEN);
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100);
    const status = searchParams.get('status'); // 'active', 'suspended', 'expired', 'all'
    const clientId = searchParams.get('clientId');
    const offset = (page - 1) * limit;

    // Build query conditions
    let whereConditions = ['1=1'];
    let queryParams: any[] = [];
    let paramIndex = 1;

    if (status && status !== 'all') {
      if (status === 'expired') {
        whereConditions.push(`(ca.expires_at IS NOT NULL AND ca.expires_at <= CURRENT_TIMESTAMP)`);
      } else {
        whereConditions.push(`ca.activation_status = $${paramIndex}`);
        queryParams.push(status);
        paramIndex++;
      }
    }

    if (clientId) {
      whereConditions.push(`ca.client_id = $${paramIndex}`);
      queryParams.push(parseInt(clientId));
      paramIndex++;
    }

    // Get total count
    const countQuery = `
      SELECT COUNT(*) as total
      FROM licensing.client_activations ca
      JOIN licensing.license_keys lk ON ca.license_key_id = lk.id
      JOIN metadata.clients ac ON ca.client_id = ac.client_id
      WHERE ${whereConditions.join(' AND ')}
    `;

    const countResult = await executeQuery<{ total: number }>(countQuery, queryParams);
    const totalCount = countResult.data?.[0]?.total || 0;

    // Get license activations with pagination
    const activationsQuery = `
      SELECT 
        ca.id,
        ca.client_id,
        ca.license_key,
        ca.activation_status,
        ca.activated_at,
        ca.activated_by,
        ca.expires_at,
        ca.user_count,
        ca.tenant_count,
        ca.last_usage,
        ac.name,
        lk.license_type,
        lk.max_users,
        lk.max_tenants
      FROM licensing.client_activations ca
      JOIN licensing.license_keys lk ON ca.license_key_id = lk.id
      JOIN metadata.clients ac ON ca.client_id = ac.id
      WHERE ${whereConditions.join(' AND ')}
      ORDER BY ca.activated_at DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    queryParams.push(limit, offset);

    const result = await executeQuery<LicenseActivation>(activationsQuery, queryParams);

    if (!result.success) {
      return createErrorResponse('Failed to fetch license activations', ApiErrorCode.INTERNAL_ERROR, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    return createSuccessResponse({
      activations: result.data || [],
      pagination: {
        page,
        limit,
        totalCount,
        totalPages: Math.ceil(totalCount / limit),
        hasNext: page * limit < totalCount,
        hasPrev: page > 1
      }
    });

  } catch (error) {
    console.error('License activations listing error:', error);
    return createErrorResponse('Failed to fetch license activations', ApiErrorCode.INTERNAL_ERROR, HttpStatus.INTERNAL_SERVER_ERROR);
  }
});

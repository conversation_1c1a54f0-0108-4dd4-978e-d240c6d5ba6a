/**
 * Vendors Page - Redirect to Vendor Dashboard
 *
 * This page redirects to the new vendor dashboard to maintain consistency
 * with the database-driven navigation system.
 */

'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'

export default function VendorsPage() {
  const router = useRouter()

  useEffect(() => {
    // Redirect to vendor dashboard
    router.replace('/vendor-dashboard')
  }, [router])

  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Redirecting to Vendor Dashboard...</p>
      </div>
    </div>
  )
}



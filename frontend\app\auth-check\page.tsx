'use client'

import { useAuth } from '@/lib/hooks/useAuth'

export default function AuthCheck() {
  const { isAuthenticated, loading, user, logout, checkUser } = useAuth()

  // Add CSS animation for spinner
  if (typeof window !== 'undefined' && !document.querySelector('#spinner-styles')) {
    const style = document.createElement('style')
    style.id = 'spinner-styles'
    style.textContent = `
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    `
    document.head.appendChild(style)
  }

  if (loading) {
    return (
      <div style={{ minHeight: '100vh', display: 'flex', alignItems: 'center', justifyContent: 'center', backgroundColor: '#f9fafb' }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{
            width: '48px',
            height: '48px',
            border: '2px solid #e5e7eb',
            borderTop: '2px solid #3b82f6',
            borderRadius: '50%',
            animation: 'spin 1s linear infinite',
            margin: '0 auto 16px'
          }}></div>
          <p style={{ color: '#6b7280' }}>Checking authentication...</p>
        </div>
      </div>
    )
  }

  return (
    <div style={{ minHeight: '100vh', backgroundColor: '#f9fafb', padding: '32px' }}>
      <div style={{ maxWidth: '768px', margin: '0 auto' }}>
        <h1 style={{ fontSize: '30px', fontWeight: 'bold', marginBottom: '24px' }}>Authentication Status</h1>

        {/* Status */}
        <div style={{ backgroundColor: 'white', borderRadius: '8px', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', padding: '24px', marginBottom: '24px' }}>
          <h2 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '16px' }}>Status</h2>
          <div style={{
            padding: '16px',
            borderRadius: '6px',
            backgroundColor: isAuthenticated ? '#f0fdf4' : '#fef2f2',
            border: `1px solid ${isAuthenticated ? '#bbf7d0' : '#fecaca'}`
          }}>
            <div style={{
              fontWeight: '600',
              color: isAuthenticated ? '#166534' : '#991b1b'
            }}>
              {isAuthenticated ? '✅ Authenticated' : '❌ Not Authenticated'}
            </div>
          </div>
        </div>

        {/* User Info */}
        {isAuthenticated && user && (
          <div style={{ backgroundColor: 'white', borderRadius: '8px', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', padding: '24px', marginBottom: '24px' }}>
            <h2 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '16px' }}>User Information</h2>
            <div>
              <div style={{ marginBottom: '8px' }}><strong>Username:</strong> {user.username}</div>
              <div><strong>User ID:</strong> {user.userId}</div>
            </div>
          </div>
        )}

        {/* Actions */}
        <div style={{ backgroundColor: 'white', borderRadius: '8px', boxShadow: '0 1px 3px rgba(0,0,0,0.1)', padding: '24px' }}>
          <h2 style={{ fontSize: '20px', fontWeight: '600', marginBottom: '16px' }}>Actions</h2>
          <div style={{ display: 'flex', gap: '16px', flexWrap: 'wrap' }}>
            <button
              onClick={checkUser}
              style={{
                padding: '8px 16px',
                backgroundColor: '#6b7280',
                color: 'white',
                borderRadius: '6px',
                border: 'none',
                cursor: 'pointer'
              }}
            >
              Refresh Auth Status
            </button>

            {isAuthenticated ? (
              <>
                <button
                  onClick={logout}
                  style={{
                    padding: '8px 16px',
                    backgroundColor: '#dc2626',
                    color: 'white',
                    borderRadius: '6px',
                    border: 'none',
                    cursor: 'pointer'
                  }}
                >
                  Logout
                </button>
                <a
                  href="/overview"
                  style={{
                    padding: '8px 16px',
                    backgroundColor: '#2563eb',
                    color: 'white',
                    borderRadius: '6px',
                    textDecoration: 'none',
                    display: 'inline-block'
                  }}
                >
                  Go to Overview
                </a>
              </>
            ) : (
              <a
                href="/login"
                style={{
                  padding: '8px 16px',
                  backgroundColor: '#16a34a',
                  color: 'white',
                  borderRadius: '6px',
                  textDecoration: 'none',
                  display: 'inline-block'
                }}
              >
                Login
              </a>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

/**
 * Renewals Hook
 * 
 * Manages renewals data, filtering, and operations
 */

'use client'

import { useState, useEffect, useCallback, useMemo } from 'react'
import { useData } from './useData'
import { Renewal } from '@/lib/types'

export interface RenewalsFilters {
  search: string
  vendor: string
  type: string
  status: string
  dateFrom?: string
  dateTo?: string
}

export interface FilterOptions {
  vendors: Array<{ value: string; label: string }>
  types: Array<{ value: string; label: string }>
  statuses: Array<{ value: string; label: string }>
}

export interface UseRenewalsResult {
  renewals: Renewal[]
  filteredRenewals: Renewal[]
  filterOptions: FilterOptions
  isLoading: boolean
  error: string | null
  fetchRenewals: () => Promise<void>
  deleteRenewal: (id: string) => Promise<void>
  processRenewal: (id: string, data: any) => Promise<void>
  updateRenewal: (id: string, data: Partial<Renewal>) => Promise<void>
  createRenewal: (data: Omit<Renewal, 'id'>) => Promise<void>
}

export function useRenewals(filters?: RenewalsFilters): UseRenewalsResult {
  const [renewals, setRenewals] = useState<Renewal[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Fetch renewals data
  const { data: renewalsData, loading: dataLoading, error: dataError, refetch } = useData<Renewal[]>({
    endpoint: '/api/renewals',
    cache: { key: 'renewals', ttl: 5 * 60 * 1000 } // 5 minutes
  })

  // Update local state when data changes
  useEffect(() => {
    if (renewalsData) {
      setRenewals(renewalsData)
    }
    setIsLoading(dataLoading)
    setError(dataError)
  }, [renewalsData, dataLoading, dataError])

  // Filter renewals based on filters
  const filteredRenewals = useMemo(() => {
    if (!filters) return renewals

    return renewals.filter(renewal => {
      const matchesSearch = !filters.search || 
        renewal.product_name?.toLowerCase().includes(filters.search.toLowerCase()) ||
        renewal.vendor?.toLowerCase().includes(filters.search.toLowerCase())

      const matchesVendor = !filters.vendor || renewal.vendor === filters.vendor
      const matchesType = !filters.type || renewal.renewal_type === filters.type
      const matchesStatus = !filters.status || renewal.status === filters.status

      const matchesDateFrom = !filters.dateFrom || 
        new Date(renewal.renewal_date) >= new Date(filters.dateFrom)
      const matchesDateTo = !filters.dateTo || 
        new Date(renewal.renewal_date) <= new Date(filters.dateTo)

      return matchesSearch && matchesVendor && matchesType && matchesStatus && 
             matchesDateFrom && matchesDateTo
    })
  }, [renewals, filters])

  // Generate filter options from renewals data
  const filterOptions = useMemo((): FilterOptions => {
    const vendors = [...new Set(renewals.map(r => r.vendor).filter(Boolean))]
    const types = [...new Set(renewals.map(r => r.renewal_type).filter(Boolean))]
    const statuses = [...new Set(renewals.map(r => r.status).filter(Boolean))]

    return {
      vendors: vendors.map(v => ({ value: v, label: v })),
      types: types.map(t => ({ value: t, label: t })),
      statuses: statuses.map(s => ({ value: s, label: s }))
    }
  }, [renewals])

  // CRUD operations
  const fetchRenewals = useCallback(async () => {
    await refetch()
  }, [refetch])

  const deleteRenewal = useCallback(async (id: string) => {
    try {
      setIsLoading(true)
      const response = await fetch(`/api/renewals/${id}`, {
        method: 'DELETE',
        credentials: 'include'
      })

      if (!response.ok) {
        throw new Error('Failed to delete renewal')
      }

      // Update local state
      setRenewals(prev => prev.filter(r => r.id !== id))
      console.log('Renewal deleted successfully:', id)
    } catch (error) {
      console.error('Error deleting renewal:', error)
      setError(error instanceof Error ? error.message : 'Failed to delete renewal')
    } finally {
      setIsLoading(false)
    }
  }, [])

  const processRenewal = useCallback(async (id: string, data: any) => {
    try {
      setIsLoading(true)
      const response = await fetch(`/api/renewals/${id}/process`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
        credentials: 'include'
      })

      if (!response.ok) {
        throw new Error('Failed to process renewal')
      }

      const result = await response.json()
      
      // Update local state
      setRenewals(prev => prev.map(r => r.id === id ? { ...r, ...result.data } : r))
      console.log('Renewal processed successfully:', id)
    } catch (error) {
      console.error('Error processing renewal:', error)
      setError(error instanceof Error ? error.message : 'Failed to process renewal')
    } finally {
      setIsLoading(false)
    }
  }, [])

  const updateRenewal = useCallback(async (id: string, data: Partial<Renewal>) => {
    try {
      setIsLoading(true)
      const response = await fetch(`/api/renewals/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
        credentials: 'include'
      })

      if (!response.ok) {
        throw new Error('Failed to update renewal')
      }

      const result = await response.json()
      
      // Update local state
      setRenewals(prev => prev.map(r => r.id === id ? { ...r, ...result.data } : r))
      console.log('Renewal updated successfully:', id)
    } catch (error) {
      console.error('Error updating renewal:', error)
      setError(error instanceof Error ? error.message : 'Failed to update renewal')
    } finally {
      setIsLoading(false)
    }
  }, [])

  const createRenewal = useCallback(async (data: Omit<Renewal, 'id'>) => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/renewals', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
        credentials: 'include'
      })

      if (!response.ok) {
        throw new Error('Failed to create renewal')
      }

      const result = await response.json()
      
      // Update local state
      setRenewals(prev => [...prev, result.data])
      console.log('Renewal created successfully:', result.data.id)
    } catch (error) {
      console.error('Error creating renewal:', error)
      setError(error instanceof Error ? error.message : 'Failed to create renewal')
    } finally {
      setIsLoading(false)
    }
  }, [])

  return {
    renewals,
    filteredRenewals,
    filterOptions,
    isLoading,
    error,
    fetchRenewals,
    deleteRenewal,
    processRenewal,
    updateRenewal,
    createRenewal
  }
}

export { useRenewals as default }

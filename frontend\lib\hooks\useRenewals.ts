/**
 * Renewals Hook
 * 
 * Manages renewals data, filtering, and operations
 */

'use client'

import { useState, useEffect, useCallback, useMemo } from 'react'
import { useData } from './useData'
import { Renewal } from '@/lib/types'

export interface RenewalsFilters {
  search: string
  vendor: string
  type: string
  status: string
  dateFrom?: string
  dateTo?: string
}

export interface FilterOptions {
  vendors: { value: string; label: string }[]
  types: { value: string; label: string }[]
  statuses: { value: string; label: string }[]
}

export interface UseRenewalsResult {
  renewals: Renewal[]
  filteredRenewals: Renewal[]
  filterOptions: FilterOptions
  isLoading: boolean
  error: string | null
  fetchRenewals: () => Promise<void>
  deleteRenewal: (id: string) => Promise<void>
  processRenewal: (id: string, data: any) => Promise<void>
  updateRenewal: (id: string, data: Partial<Renewal>) => Promise<void>
  createRenewal: (data: Omit<Renewal, 'id'>) => Promise<void>
}

export function useRenewals(filters?: RenewalsFilters): UseRenewalsResult {
  const [error, setError] = useState<string | null>(null)

  // Fetch renewals data with filters
  const { data: renewals, loading: isLoading, error: dataError, refetch } = useData<Renewal[]>({
    endpoint: '/api/renewals',
    params: filters || {},
    cache: { key: 'renewals', ttl: 5 * 60 * 1000 }, // 5 minutes
  })

  // Generate filter options from renewals data
  const filterOptions = useMemo((): FilterOptions => {
    const vendors = ['All Vendors', ...new Set(renewals?.map(r => r.vendor).filter(Boolean) || [])]
    const types = ['All Renewal Types', ...new Set(renewals?.map(r => r.type).filter(Boolean) || [])]
    const statuses = ['All Statuses', ...new Set(renewals?.map(r => r.status).filter(Boolean) || [])]

    return {
      vendors: vendors.map((v) => ({ value: v as string, label: v as string })),
      types: types.map((t) => ({ value: t as string, label: t as string })),
      statuses: statuses.map((s) => ({ value: s as string, label: s as string }))
    }
  }, [renewals])

  // CRUD operations
  const fetchRenewals = useCallback(async () => {
    await refetch()
  }, [refetch])

  // Handle CRUD operations with proper error handling
  const handleCRUD = useCallback(async (operation: 'create' | 'update' | 'delete', id?: string, data?: any) => {
    try {
      setError(null)
      const response = await fetch(`${operation === 'delete' ? '/api/renewals/' : '/api/renewals'}`, {
        method: operation === 'delete' ? 'DELETE' : 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...(operation !== 'delete' && data),
          ...(operation === 'delete' && { id })
        })
      })

      if (!response.ok) {
        throw new Error('Failed to perform operation')
      }

      await refetch()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Operation failed')
    }
  }, [refetch])

  return {
    renewals: renewals || [],
    filteredRenewals: renewals || [],
    filterOptions,
    isLoading: isLoading || false,
    error,
    fetchRenewals,
    deleteRenewal: (id: string) => handleCRUD('delete', id),
    processRenewal: async (id: string, data: any) => {
      try {
        setError(null)
        const response = await fetch(`/api/renewals/${id}/process`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(data)
        })

        if (!response.ok) {
          throw new Error('Failed to process renewal')
        }

        await refetch()
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to process renewal')
      }
    },
    updateRenewal: (id: string, data: Partial<Renewal>) => handleCRUD('update', id, data),
    createRenewal: (data: Omit<Renewal, 'id'>) => handleCRUD('create', undefined, data)
  } as UseRenewalsResult
}

export { useRenewals as default }

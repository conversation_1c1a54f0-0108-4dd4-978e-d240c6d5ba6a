-- =====================================================
-- Missing License Database Functions
-- =====================================================

-- Function to get client license status (used by license-management API)
CREATE OR REPLACE FUNCTION metadata.get_client_license_status(p_client_id INTEGER)
RETURNS JSON AS $$
DECLARE
    license_record RECORD;
    result JSON;
BEGIN
    -- Get the active license for the client with all details
    SELECT 
        cl.client_license_id,
        cl.status,
        cl.activation_date,
        cl.expiry_date,
        cl.current_renewals,
        cl.max_renewals,
        lk.license_key,
        lt.type_name as license_type,
        lt.features,
        ac.name as client_name,
        cl.activated_by,
        -- Calculate usage percentage
        ROUND((cl.current_renewals::DECIMAL / cl.max_renewals::DECIMAL) * 100, 2) as usage_percentage,
        -- Calculate days until expiry
        CASE 
            WHEN cl.expiry_date IS NOT NULL THEN cl.expiry_date - CURRENT_DATE
            ELSE NULL
        END as days_until_expiry,
        -- Determine alert status
        CASE
            WHEN cl.expiry_date < CURRENT_DATE THEN 'EXPIRED'
            WHEN cl.expiry_date - CURRENT_DATE <= 30 THEN 'EXPIRING_SOON'
            WHEN cl.current_renewals >= cl.max_renewals * 0.9 THEN 'USAGE_HIGH'
            ELSE 'ACTIVE'
        END as alert_status
    INTO license_record
    FROM metadata.admin_client_licenses cl
    JOIN metadata.admin_license_keys lk ON cl.license_key_id = lk.license_key_id
    JOIN metadata.admin_license_types lt ON cl.license_type_id = lt.id
    JOIN metadata.clients ac ON cl.client_id = ac.id
    WHERE cl.client_id = p_client_id 
      AND cl.status = 'ACTIVE'
    ORDER BY cl.activation_date DESC
    LIMIT 1;

    -- Check if license exists
    IF license_record IS NULL THEN
        result := json_build_object(
            'has_license', false,
            'is_valid', false,
            'reason', 'No active license found for client',
            'client_id', p_client_id
        );
        RETURN result;
    END IF;

    -- Check if license is expired
    IF license_record.expiry_date IS NOT NULL AND license_record.expiry_date < CURRENT_DATE THEN
        -- Update status to expired
        UPDATE metadata.admin_client_licenses
        SET status = 'EXPIRED', changed_on = CURRENT_TIMESTAMP
        WHERE client_license_id = license_record.client_license_id;

        result := json_build_object(
            'has_license', true,
            'is_valid', false,
            'reason', 'License has expired',
            'client_id', p_client_id,
            'license_info', json_build_object(
                'license_type', license_record.license_type,
                'expiry_date', license_record.expiry_date,
                'client_name', license_record.client_name
            )
        );
        RETURN result;
    END IF;

    -- License is valid - return full status
    result := json_build_object(
        'has_license', true,
        'is_valid', true,
        'client_id', p_client_id,
        'license_info', json_build_object(
            'license_type', license_record.license_type,
            'activation_date', license_record.activation_date,
            'expiry_date', license_record.expiry_date,
            'current_renewals', license_record.current_renewals,
            'max_renewals', license_record.max_renewals,
            'usage_percentage', license_record.usage_percentage,
            'renewals_remaining', license_record.max_renewals - license_record.current_renewals,
            'days_until_expiry', license_record.days_until_expiry,
            'alert_status', license_record.alert_status,
            'features', license_record.features,
            'client_name', license_record.client_name,
            'license_key', license_record.license_key,
            'activated_by', license_record.activated_by
        )
    );

    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Function to update license renewal count (used by license-management API)
CREATE OR REPLACE FUNCTION metadata.update_license_renewal_count(
    p_client_id INTEGER,
    p_count_change INTEGER,
    p_changed_by VARCHAR(255) DEFAULT NULL
) RETURNS JSON AS $$
DECLARE
    v_client_license_id INTEGER;
    v_current_count INTEGER;
    v_max_renewals INTEGER;
    v_new_count INTEGER;
    result JSON;
BEGIN
    -- Get the active license for the client
    SELECT client_license_id, current_renewals, max_renewals
    INTO v_client_license_id, v_current_count, v_max_renewals
    FROM metadata.admin_client_licenses
    WHERE client_id = p_client_id
    AND status = 'ACTIVE'
    ORDER BY activation_date DESC
    LIMIT 1;

    -- Check if license exists
    IF v_client_license_id IS NULL THEN
        RETURN json_build_object(
            'success', false,
            'error', 'No active license found for client'
        );
    END IF;

    -- Calculate new count
    v_new_count := v_current_count + p_count_change;

    -- Validate new count
    IF v_new_count < 0 THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Cannot reduce renewal count below zero'
        );
    END IF;

    IF v_new_count > v_max_renewals THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Renewal count would exceed license limit',
            'current_count', v_current_count,
            'max_renewals', v_max_renewals,
            'attempted_count', v_new_count
        );
    END IF;

    -- Update the count
    UPDATE metadata.admin_client_licenses
    SET 
        current_renewals = v_new_count,
        changed_on = CURRENT_TIMESTAMP,
        changed_by = COALESCE(p_changed_by, 'system')
    WHERE client_license_id = v_client_license_id;

    -- Log the change
    INSERT INTO metadata.admin_license_usage_log (
        client_id,
        license_key_id,
        event_type,
        event_description,
        old_value,
        new_value,
        changed_by
    ) VALUES (
        p_client_id,
        (SELECT license_key_id FROM metadata.admin_client_licenses WHERE client_license_id = v_client_license_id),
        'renewal_count_update',
        'Renewal count updated: ' || v_current_count || ' -> ' || v_new_count,
        v_current_count,
        v_new_count,
        COALESCE(p_changed_by, 'system')
    );

    RETURN json_build_object(
        'success', true,
        'previous_count', v_current_count,
        'new_count', v_new_count,
        'count_change', p_count_change,
        'renewals_remaining', v_max_renewals - v_new_count,
        'usage_percentage', ROUND((v_new_count::DECIMAL / v_max_renewals::DECIMAL) * 100, 2)
    );
END;
$$ LANGUAGE plpgsql;

-- Create usage log table if it doesn't exist
CREATE TABLE IF NOT EXISTS metadata.admin_license_usage_log (
    log_id SERIAL PRIMARY KEY,
    client_id INTEGER NOT NULL,
    license_key_id INTEGER,
    event_type VARCHAR(50) NOT NULL,
    event_description TEXT,
    old_value INTEGER,
    new_value INTEGER,
    changed_by VARCHAR(255),
    logged_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (client_id) REFERENCES metadata.clients (id),
    FOREIGN KEY (license_key_id) REFERENCES metadata.admin_license_keys (license_key_id)
);

-- Grant execute permissions
GRANT
EXECUTE ON FUNCTION metadata.get_client_license_status (INTEGER) TO postgres;

GRANT
EXECUTE ON FUNCTION metadata.update_license_renewal_count (INTEGER, INTEGER, VARCHAR) TO postgres;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_license_usage_log_client_id ON metadata.admin_license_usage_log (client_id);

CREATE INDEX IF NOT EXISTS idx_license_usage_log_logged_at ON metadata.admin_license_usage_log (logged_at);

-- Test the functions
SELECT metadata.get_client_license_status (1) as test_status;

SELECT metadata.update_license_renewal_count (1, 1, 'test-user') as test_update;
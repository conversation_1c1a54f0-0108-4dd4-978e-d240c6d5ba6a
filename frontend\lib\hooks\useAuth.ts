'use client'

import { useEffect, useState } from 'react'
import { getCurrentUser, signOut } from 'aws-amplify/auth'
import { Hub } from 'aws-amplify/utils'
import { useRouter } from 'next/navigation'
import { AUTH_ENDPOINTS } from '@/lib/constants/api-endpoints'

export function useAuth() {
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    checkUser()

    // Set up Hub listener for auth events
    const hubListener = (data: any) => {
      const { payload } = data

      switch (payload.event) {
        case 'signedIn':
          checkUser()
          break
        case 'signedOut':
          setUser(null)
          break
        case 'tokenRefresh':
          checkUser()
          break
        case 'tokenRefresh_failure':
          setUser(null)
          break
      }
    }

    // Subscribe to auth events
    const unsubscribe = Hub.listen('auth', hubListener)

    return () => {
      unsubscribe()
    }
  }, [])

  const checkUser = async () => {
    try {
      // Ensure Amplify is configured first
      const { ensureAmplifyConfigured } = await import('@/lib/services/amplify-service')
      await ensureAmplifyConfigured()

      const currentUser = await getCurrentUser()
      setUser(currentUser)
    } catch (error) {
      setUser(null)
    } finally {
      setLoading(false)
    }
  }

  const logout = async () => {
    try {
      await signOut()
      setUser(null)

      // Clear SSR/middleware idToken cookie
      try {
        await fetch(AUTH_ENDPOINTS.SET_TOKEN, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ idToken: '' }),
          credentials: 'include',
        });
      } catch (err) {
        // Silently handle cookie clearing errors
      }

      router.push('/login')
    } catch (error) {
      // Still redirect to login even if signOut fails
      router.push('/login')
    }
  }

  return {
    user,
    loading,
    logout,
    checkUser, // Expose checkUser for manual refresh
    isAuthenticated: !!user,
  }
}

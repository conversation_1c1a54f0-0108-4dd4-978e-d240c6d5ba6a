'use client'

import { useEffect } from 'react'
import { getCurrentUser, signOut } from 'aws-amplify/auth'
import { Hub } from 'aws-amplify/utils'
import { useRouter } from 'next/navigation'
import { AUTH_ENDPOINTS } from '@/lib/constants/api-endpoints'
import { useAppStore } from '@/lib/store/app-store'

export function useAuth() {
  const { user, isAuthenticated, authLoading, setUser, setAuthLoading, setAuthenticated } = useAppStore((state) => ({
    user: state.user,
    isAuthenticated: state.isAuthenticated,
    authLoading: state.authLoading,
    setUser: state.setUser,
    setAuthLoading: state.setAuthLoading,
    setAuthenticated: state.setAuthenticated
  }))
  const router = useRouter()

  useEffect(() => {
    checkUser()

    // Set up Hub listener for auth events
    const hubListener = (data: any) => {
      const { payload } = data
      console.log('[useAuth] Hub event received:', payload.event, payload)

      switch (payload.event) {
        case 'signIn':
        case 'signedIn':
          console.log('[useAuth] User signed in')
          checkUser()
          break
        case 'signOut':
        case 'signedOut':
          console.log('[useAuth] User signed out')
          setUser(null)
          setAuthenticated(false)
          setAuthLoading(false)
          break
        case 'tokenRefresh':
          console.log('[useAuth] Token refreshed successfully')
          // Token refreshed successfully, sync to server
          checkUser()
          break
        case 'tokenRefresh_failure':
          console.log('[useAuth] Token refresh failed')
          // Token refresh failed, clear user and redirect to login
          setUser(null)
          setAuthenticated(false)
          setAuthLoading(false)
          router.push('/login')
          break
        case 'signInWithRedirect':
          console.log('[useAuth] Sign in with redirect completed')
          checkUser()
          break
        case 'signInWithRedirect_failure':
          console.log('[useAuth] Sign in with redirect failed')
          setUser(null)
          setAuthenticated(false)
          setAuthLoading(false)
          break
      }
    }

    // Subscribe to auth events
    const unsubscribe = Hub.listen('auth', hubListener)

    return () => {
      unsubscribe()
    }
  }, [router]) // Only router needs to be in dependencies, Zustand setters are stable

  const checkUser = async () => {
    try {
      // Ensure Amplify is configured first
      const { ensureAmplifyConfigured } = await import('@/lib/services/amplify-service')
      await ensureAmplifyConfigured()

      console.log('[useAuth] Checking authentication state...')

      // First try to get current user
      const currentUser = await getCurrentUser()
      console.log('[useAuth] Current user found:', {
        userId: currentUser.userId,
        username: currentUser.username
      })

      // Then get session for token sync
      const { fetchAuthSession } = await import('aws-amplify/auth')
      const session = await fetchAuthSession()

      console.log('[useAuth] Session retrieved:', {
        hasIdToken: !!session?.tokens?.idToken,
        hasAccessToken: !!session?.tokens?.accessToken
      })

      setUser(currentUser)
      setAuthenticated(true)
      setAuthLoading(false)

      // Sync token to server-side cookie for SSR/middleware
      if (session?.tokens?.idToken) {
        try {
          const response = await fetch(AUTH_ENDPOINTS.SET_TOKEN, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ idToken: session.tokens.idToken.toString() }),
            credentials: 'include',
          })

          if (!response.ok) {
            console.warn('[useAuth] Token sync failed:', response.status)
          } else {
            console.log('[useAuth] Token synced to server successfully')
          }
        } catch (syncError) {
          console.warn('[useAuth] Token sync error:', syncError)
        }
      } else {
        console.warn('[useAuth] No ID token found in session')
      }

    } catch (error) {
      console.log('[useAuth] Authentication check failed:', error)
      setUser(null)
      setAuthenticated(false)
      setAuthLoading(false)

      // Clear server-side cookie if client auth fails
      try {
        await fetch(AUTH_ENDPOINTS.SET_TOKEN, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ idToken: '' }),
          credentials: 'include',
        })
        console.log('[useAuth] Server token cleared')
      } catch (clearError) {
        console.warn('[useAuth] Failed to clear server token:', clearError)
      }
    }
  }

  const logout = async () => {
    try {
      console.log('[useAuth] Starting logout process...')

      // Sign out from Amplify with global sign out
      await signOut({ global: true })
      console.log('[useAuth] Amplify sign out completed')

      setUser(null)
      setAuthenticated(false)
      setAuthLoading(false)

      // Clear SSR/middleware idToken cookie
      try {
        await fetch(AUTH_ENDPOINTS.SET_TOKEN, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ idToken: '' }),
          credentials: 'include',
        });
        console.log('[useAuth] Server token cleared')
      } catch (err) {
        console.warn('[useAuth] Failed to clear server token:', err)
      }

      router.push('/login')
    } catch (error) {
      console.error('[useAuth] Logout error:', error)
      // Still redirect to login even if signOut fails
      setUser(null)
      setAuthenticated(false)
      setAuthLoading(false)
      router.push('/login')
    }
  }

  return {
    user,
    isAuthenticated,
    loading: authLoading,
    logout,
    checkUser, // Expose checkUser for manual refresh
    isAuthenticated: !!user,
  }
}

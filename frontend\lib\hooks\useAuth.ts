'use client'

import { useEffect, useState } from 'react'
import { getCurrentUser, signOut } from 'aws-amplify/auth'
import { Hub } from 'aws-amplify/utils'
import { useRouter } from 'next/navigation'
import { AUTH_ENDPOINTS } from '@/lib/constants/api-endpoints'

export function useAuth() {
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    checkUser()

    // Set up Hub listener for auth events
    const hubListener = (data: any) => {
      const { payload } = data
      console.log('[useAuth] Hub event received:', payload.event, payload)

      switch (payload.event) {
        case 'signedIn':
          console.log('[useAuth] User signed in')
          checkUser()
          break
        case 'signedOut':
          console.log('[useAuth] User signed out')
          setUser(null)
          break
        case 'tokenRefresh':
          console.log('[useAuth] Token refreshed successfully')
          // Token refreshed successfully, sync to server
          checkUser()
          break
        case 'tokenRefresh_failure':
          console.log('[useAuth] Token refresh failed')
          // Token refresh failed, clear user and redirect to login
          setUser(null)
          router.push('/login')
          break
      }
    }

    // Subscribe to auth events
    const unsubscribe = Hub.listen('auth', hubListener)

    return () => {
      unsubscribe()
    }
  }, [])

  const checkUser = async () => {
    try {
      // Ensure Amplify is configured first
      const { ensureAmplifyConfigured } = await import('@/lib/services/amplify-service')
      await ensureAmplifyConfigured()

      // Get current user and session in parallel
      const [currentUser, session] = await Promise.all([
        getCurrentUser(),
        import('aws-amplify/auth').then(({ fetchAuthSession }) => fetchAuthSession())
      ])

      console.log('[useAuth] User authenticated:', {
        userId: currentUser.userId,
        username: currentUser.username,
        hasSession: !!session?.tokens?.idToken
      })

      setUser(currentUser)

      // Sync token to server-side cookie for SSR/middleware
      if (session?.tokens?.idToken) {
        try {
          const response = await fetch(AUTH_ENDPOINTS.SET_TOKEN, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ idToken: session.tokens.idToken.toString() }),
            credentials: 'include',
          })

          if (!response.ok) {
            console.warn('[useAuth] Token sync failed:', response.status)
          } else {
            console.log('[useAuth] Token synced to server successfully')
          }
        } catch (syncError) {
          console.warn('[useAuth] Token sync error:', syncError)
        }
      }

    } catch (error) {
      console.log('[useAuth] Authentication failed:', error)
      setUser(null)

      // Clear server-side cookie if client auth fails
      try {
        await fetch(AUTH_ENDPOINTS.SET_TOKEN, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ idToken: '' }),
          credentials: 'include',
        })
        console.log('[useAuth] Server token cleared')
      } catch (clearError) {
        console.warn('[useAuth] Failed to clear server token:', clearError)
      }
    } finally {
      setLoading(false)
    }
  }

  const logout = async () => {
    try {
      await signOut()
      setUser(null)

      // Clear SSR/middleware idToken cookie
      try {
        await fetch(AUTH_ENDPOINTS.SET_TOKEN, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ idToken: '' }),
          credentials: 'include',
        });
      } catch (err) {
        // Silently handle cookie clearing errors
      }

      router.push('/login')
    } catch (error) {
      // Still redirect to login even if signOut fails
      router.push('/login')
    }
  }

  return {
    user,
    loading,
    logout,
    checkUser, // Expose checkUser for manual refresh
    isAuthenticated: !!user,
  }
}

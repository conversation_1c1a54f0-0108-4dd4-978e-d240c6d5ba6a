'use client'

import { useEffect, useState } from 'react'
import { getCurrentUser, signOut } from 'aws-amplify/auth'
import { Hub } from 'aws-amplify/utils'
import { useRouter } from 'next/navigation'

export function useAuth() {
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    checkUser()

    // Set up Hub listener for auth events
    const hubListener = (data: any) => {
      const { payload } = data
      console.log('🔄 [AUTH] Hub event:', payload.event)

      switch (payload.event) {
        case 'signedIn':
          console.log('✅ [AUTH] User signed in')
          checkUser()
          break
        case 'signedOut':
          console.log('🚪 [AUTH] User signed out')
          setUser(null)
          break
        case 'tokenRefresh':
          console.log('🔄 [AUTH] Token refreshed')
          checkUser()
          break
        case 'tokenRefresh_failure':
          console.log('❌ [AUTH] Token refresh failed')
          setUser(null)
          break
      }
    }

    // Subscribe to auth events
    const unsubscribe = Hub.listen('auth', hubListener)

    return () => {
      unsubscribe()
    }
  }, [])

  const checkUser = async () => {
    try {
      console.log('🔍 [AUTH] Checking current user...')

      // Ensure Amplify is configured first
      const { ensureAmplifyConfigured } = await import('@/lib/services/amplify-service')
      await ensureAmplifyConfigured()

      const currentUser = await getCurrentUser()
      console.log('✅ [AUTH] User found:', currentUser.username)
      setUser(currentUser)
    } catch (error) {
      console.log('❌ [AUTH] No authenticated user:', error.message)
      setUser(null)
    } finally {
      setLoading(false)
    }
  }

  const logout = async () => {
    try {
      await signOut()
      setUser(null)
      router.push('/login')
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  return {
    user,
    loading,
    logout,
    checkUser, // Expose checkUser for manual refresh
    isAuthenticated: !!user,
  }
}

'use client'

import { useEffect, useState } from 'react'
import { getCurrentUser, signOut } from 'aws-amplify/auth'
import { Hub } from 'aws-amplify/utils'
import { useRouter } from 'next/navigation'
import { AUTH_ENDPOINTS } from '@/lib/constants/api-endpoints'

export function useAuth() {
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    checkUser()

    // Set up Hub listener for auth events
    const hubListener = (data: any) => {
      const { payload } = data

      switch (payload.event) {
        case 'signedIn':
          checkUser()
          break
        case 'signedOut':
          setUser(null)
          break
        case 'tokenRefresh':
          // Token refreshed successfully, sync to server
          checkUser()
          break
        case 'tokenRefresh_failure':
          // Token refresh failed, clear user and redirect to login
          setUser(null)
          router.push('/login')
          break
      }
    }

    // Subscribe to auth events
    const unsubscribe = Hub.listen('auth', hubListener)

    return () => {
      unsubscribe()
    }
  }, [])

  const checkUser = async () => {
    try {
      // Ensure Amplify is configured first
      const { ensureAmplifyConfigured } = await import('@/lib/services/amplify-service')
      await ensureAmplifyConfigured()

      const currentUser = await getCurrentUser()
      setUser(currentUser)

      // Sync token to server-side cookie for SSR/middleware
      try {
        const { fetchAuthSession } = await import('aws-amplify/auth')
        const session = await fetchAuthSession()

        if (session?.tokens?.idToken) {
          await fetch(AUTH_ENDPOINTS.SET_TOKEN, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ idToken: session.tokens.idToken.toString() }),
            credentials: 'include',
          })
        }
      } catch (syncError) {
        // Token sync failed, but don't fail the entire auth check
        console.warn('Token sync failed:', syncError)
      }

    } catch (error) {
      setUser(null)

      // Clear server-side cookie if client auth fails
      try {
        await fetch(AUTH_ENDPOINTS.SET_TOKEN, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ idToken: '' }),
          credentials: 'include',
        })
      } catch (clearError) {
        // Silently handle cookie clearing errors
      }
    } finally {
      setLoading(false)
    }
  }

  const logout = async () => {
    try {
      await signOut()
      setUser(null)

      // Clear SSR/middleware idToken cookie
      try {
        await fetch(AUTH_ENDPOINTS.SET_TOKEN, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ idToken: '' }),
          credentials: 'include',
        });
      } catch (err) {
        // Silently handle cookie clearing errors
      }

      router.push('/login')
    } catch (error) {
      // Still redirect to login even if signOut fails
      router.push('/login')
    }
  }

  return {
    user,
    loading,
    logout,
    checkUser, // Expose checkUser for manual refresh
    isAuthenticated: !!user,
  }
}

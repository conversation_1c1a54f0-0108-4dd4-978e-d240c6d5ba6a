/**
 * Centralized Access Control Service
 * 
 * Provides database-driven access control for all authorization decisions.
 * Replaces all hardcoded group checks throughout the codebase.
 */

import { AuthSession } from '@/lib/types'
import { databaseService } from './database-service'

export interface AccessControlResult {
  hasAccess: boolean
  reason?: string
  metadata?: Record<string, any>
}

export interface PageAccessResult extends AccessControlResult {
  pages?: string[]
}

export interface LicenseExemptionResult extends AccessControlResult {
  isExempt: boolean
  exemptionType?: 'admin' | 'super-admin' | 'license-override'
}

/**
 * Centralized Access Control Service
 * All access control decisions should go through this service
 */
class AccessControlService {
  private static instance: AccessControlService
  private cache = new Map<string, { data: any; timestamp: number }>()
  private readonly CACHE_TTL = 5 * 60 * 1000 // 5 minutes

  static getInstance(): AccessControlService {
    if (!AccessControlService.instance) {
      AccessControlService.instance = new AccessControlService()
    }
    return AccessControlService.instance
  }

  /**
   * Check if user has access to a specific page
   */
  async hasPageAccess(session: AuthSession, pageName: string): Promise<AccessControlResult> {
    const cacheKey = `page_access_${session.userId}_${pageName}`
    const cached = this.getFromCache(cacheKey)
    if (cached) return cached

    try {
      const query = `
        SELECT COUNT(*) as count
        FROM metadata.pages p
        INNER JOIN metadata.page_groups pg ON p.id = pg.page_id
        WHERE p.name = $1
          AND p.status = 'A'
          AND pg.group_name = ANY($2)
      `
      
      const result = await databaseService.executeQuery(query, [pageName, session.groups])
      const hasAccess = result.rows[0]?.count > 0

      const accessResult: AccessControlResult = {
        hasAccess,
        reason: hasAccess ? 'User has required group membership' : 'User lacks required group membership',
        metadata: { pageName, userGroups: session.groups }
      }

      this.setCache(cacheKey, accessResult)
      return accessResult

    } catch (error) {
      console.error('Error checking page access:', error)
      return {
        hasAccess: false,
        reason: 'Database error during access check',
        metadata: { error: error instanceof Error ? error.message : 'Unknown error' }
      }
    }
  }

  /**
   * Get all pages accessible to user
   */
  async getUserAccessiblePages(session: AuthSession): Promise<PageAccessResult> {
    const cacheKey = `user_pages_${session.userId}`
    const cached = this.getFromCache(cacheKey)
    if (cached) return cached

    try {
      const query = `
        SELECT DISTINCT p.name, p.route_path, p.display_order
        FROM metadata.pages p
        INNER JOIN metadata.page_groups pg ON p.id = pg.page_id
        WHERE p.status = 'A'
          AND pg.group_name = ANY($1)
        ORDER BY p.display_order ASC, p.name ASC
      `
      
      const result = await databaseService.executeQuery(query, [session.groups])
      const pages = result.rows.map(row => row.name)

      const pageResult: PageAccessResult = {
        hasAccess: pages.length > 0,
        pages,
        reason: pages.length > 0 ? 'User has access to pages' : 'User has no accessible pages',
        metadata: { userGroups: session.groups, pageCount: pages.length }
      }

      this.setCache(cacheKey, pageResult)
      return pageResult

    } catch (error) {
      console.error('Error getting user accessible pages:', error)
      return {
        hasAccess: false,
        pages: [],
        reason: 'Database error during page access check',
        metadata: { error: error instanceof Error ? error.message : 'Unknown error' }
      }
    }
  }

  /**
   * Check if user is exempt from license validation
   * This replaces all hardcoded license exemption logic
   */
  async isLicenseExempt(session: AuthSession, context?: { path?: string; operation?: string }): Promise<LicenseExemptionResult> {
    const cacheKey = `license_exempt_${session.userId}_${context?.path || 'default'}`
    const cached = this.getFromCache(cacheKey)
    if (cached) return cached

    try {
      // Check if user has license exemption based on database configuration
      const query = `
        SELECT 
          le.exemption_type,
          le.scope,
          le.conditions
        FROM metadata.license_exemptions le
        INNER JOIN metadata.license_exemption_groups leg ON le.id = leg.exemption_id
        WHERE leg.group_name = ANY($1)
          AND le.status = 'A'
          AND (le.scope = 'global' OR le.scope = $2)
      `
      
      const result = await databaseService.executeQuery(query, [session.groups, context?.path || 'default'])
      
      const exemptionResult: LicenseExemptionResult = {
        hasAccess: true,
        isExempt: result.rows.length > 0,
        exemptionType: result.rows[0]?.exemption_type,
        reason: result.rows.length > 0 ? 'User has license exemption' : 'User requires license validation',
        metadata: { 
          userGroups: session.groups, 
          context,
          exemptions: result.rows 
        }
      }

      this.setCache(cacheKey, exemptionResult)
      return exemptionResult

    } catch (error) {
      console.error('Error checking license exemption:', error)
      // Default to requiring license validation on error
      return {
        hasAccess: true,
        isExempt: false,
        reason: 'Database error during license exemption check - defaulting to require license',
        metadata: { error: error instanceof Error ? error.message : 'Unknown error' }
      }
    }
  }

  // Note: Resource access control removed as it's not currently needed
  // The pages + pages_groups tables provide sufficient access control

  /**
   * Extract and normalize user groups from session
   */
  extractUserGroups(session: AuthSession): string[] {
    if (!session.groups) return []
    
    return Array.isArray(session.groups) 
      ? session.groups.filter(group => typeof group === 'string').map(group => group.toLowerCase().trim())
      : [session.groups].filter(group => typeof group === 'string').map(group => group.toLowerCase().trim())
  }

  /**
   * Clear cache for a specific user or all cache
   */
  clearCache(userId?: string): void {
    if (userId) {
      for (const key of this.cache.keys()) {
        if (key.includes(userId)) {
          this.cache.delete(key)
        }
      }
    } else {
      this.cache.clear()
    }
  }

  private getFromCache(key: string): any {
    const cached = this.cache.get(key)
    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
      return cached.data
    }
    this.cache.delete(key)
    return null
  }

  private setCache(key: string, data: any): void {
    this.cache.set(key, { data, timestamp: Date.now() })
  }
}

// Export singleton instance
export const accessControlService = AccessControlService.getInstance()

// Export convenience functions
export async function hasPageAccess(session: AuthSession, pageName: string): Promise<boolean> {
  const result = await accessControlService.hasPageAccess(session, pageName)
  return result.hasAccess
}

export async function getUserAccessiblePages(session: AuthSession): Promise<string[]> {
  const result = await accessControlService.getUserAccessiblePages(session)
  return result.pages || []
}

export async function isLicenseExempt(session: AuthSession, context?: { path?: string; operation?: string }): Promise<boolean> {
  const result = await accessControlService.isLicenseExempt(session, context)
  return result.isExempt
}

// Note: canAccessResource function removed as resource permissions are not currently needed

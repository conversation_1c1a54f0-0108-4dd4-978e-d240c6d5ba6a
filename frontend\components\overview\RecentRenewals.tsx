/**
 * Recent Renewals Component (Refactored with Design System)
 * 
 * Displays a list of recent renewals with filtering and pagination.
 * Now uses the unified Button component for consistency.
 */

'use client'

import React, { useState, useMemo } from 'react'
import { BaseComponentProps } from '@/lib/types'
import { Renewal } from '@/lib/types'
import { Button } from '@/components/ui/Button'
import { formatDate, getDaysUntilDate } from '@/lib/utils/date-utils'
import { formatCurrency } from '@/lib/utils/format-utils'

interface RecentRenewalsProps extends BaseComponentProps {
  renewals: Renewal[]
  isLoading?: boolean
  onRenewalClick?: (renewal: Renewal) => void
  maxItems?: number
  showViewAll?: boolean
  defaultDaysFilter?: number
}

const RecentRenewals = React.memo(function RecentRenewals({
  renewals = [],
  isLoading = false,
  onRenewalClick,
  maxItems = 5,
  showViewAll = true,
  defaultDaysFilter = 7,
  className = '',
  'data-testid': testId
}: RecentRenewalsProps) {
  const [showAll, setShowAll] = useState(false)
  const [daysFilter, setDaysFilter] = useState(defaultDaysFilter)

  // Filter renewals by days and sort
  const filteredRenewals = useMemo(() => {
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - daysFilter)

    // Filter by creation date within the time range
    const filtered = renewals.filter(renewal => {
      if (!renewal.created_on) return false
      const createdDate = new Date(renewal.created_on)
      return createdDate >= cutoffDate
    })

    // Sort by creation date (most recent first)
    const sorted = filtered.sort((a, b) => {
      const dateA = new Date(a.created_on || 0).getTime()
      const dateB = new Date(b.created_on || 0).getTime()
      return dateB - dateA
    })

    // Apply limit if not showing all
    return showAll ? sorted : sorted.slice(0, maxItems)
  }, [renewals, showAll, maxItems, daysFilter])

  const handleToggleShowAll = () => {
    setShowAll(!showAll)
  }

  const handleDaysFilterChange = (days: number) => {
    setDaysFilter(days)
    setShowAll(false) // Reset to limited view when filter changes
  }

  const handleRenewalClick = (renewal: Renewal) => {
    onRenewalClick?.(renewal)
  }

  if (isLoading) {
    return (
      <div className={`recent-renewals-container ${className}`} data-testid={testId}>
        <div className="section-header">
          <h2 className="section-title">Recent Renewals</h2>
        </div>
        <div className="renewals-list">
          {Array.from({ length: maxItems }).map((_, index) => (
            <div key={index} className="renewal-item loading">
              <div className="renewal-info">
                <div className="animate-pulse bg-gray-200 h-4 w-32 rounded mb-2"></div>
                <div className="animate-pulse bg-gray-200 h-3 w-24 rounded"></div>
              </div>
              <div className="renewal-meta">
                <div className="animate-pulse bg-gray-200 h-3 w-16 rounded mb-1"></div>
                <div className="animate-pulse bg-gray-200 h-3 w-20 rounded"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  if (renewals.length === 0) {
    return (
      <div className={`recent-renewals-container ${className}`} data-testid={testId}>
        <div className="section-header">
          <div className="flex items-center justify-between w-full">
            <h2 className="section-title">Recent Renewals</h2>

            {/* Time Filter Dropdown - positioned on the right */}
            <div className="flex items-center space-x-2 ml-auto">
              <span className="text-sm text-gray-600">Last:</span>
              <select
                value={daysFilter}
                onChange={(e) => handleDaysFilterChange(Number(e.target.value))}
                className="text-sm border border-gray-300 rounded px-2 py-1 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value={2}>2 days</option>
                <option value={7}>7 days</option>
                <option value={14}>14 days</option>
                <option value={30}>30 days</option>
              </select>
            </div>
          </div>
        </div>
        <div className="empty-state">
          <div className="empty-icon">📋</div>
          <h3>No renewals found</h3>
          <p>No renewals were added in the last {daysFilter} day{daysFilter !== 1 ? 's' : ''}.</p>
        </div>
      </div>
    )
  }

  // Handle case where renewals exist but none match the time filter
  if (filteredRenewals.length === 0) {
    return (
      <div className={`recent-renewals-container ${className}`} data-testid={testId}>
        <div className="section-header">
          <div className="flex items-center justify-between w-full">
            <h2 className="section-title">Recent Renewals</h2>

            {/* Time Filter Dropdown - positioned on the right */}
            <div className="flex items-center space-x-2 ml-auto">
              <span className="text-sm text-gray-600">Last:</span>
              <select
                value={daysFilter}
                onChange={(e) => handleDaysFilterChange(Number(e.target.value))}
                className="text-sm border border-gray-300 rounded px-2 py-1 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value={2}>2 days</option>
                <option value={7}>7 days</option>
                <option value={14}>14 days</option>
                <option value={30}>30 days</option>
              </select>
            </div>
          </div>

          <div className="section-meta">
            <span className="item-count">
              No renewals in last {daysFilter} day{daysFilter !== 1 ? 's' : ''}
            </span>
          </div>
        </div>
        <div className="empty-state">
          <div className="empty-icon">📄</div>
          <h3>No recent renewals</h3>
          <p>
            No renewals were processed in the last {daysFilter} day{daysFilter !== 1 ? 's' : ''}.
            Try expanding the time range or check if there are any completed renewals.
          </p>
          {daysFilter < 30 && (
            <button
              onClick={() => handleDaysFilterChange(30)}
              className="mt-3 text-sm text-blue-600 hover:text-blue-800"
            >
              Show last 30 days
            </button>
          )}
        </div>
      </div>
    )
  }

  return (
    <div className={`recent-renewals-container ${className}`} data-testid={testId}>
      <div className="section-header">
        <div className="flex items-center justify-between w-full">
          <h2 className="section-title">Recent Renewals</h2>

          {/* Time Filter Dropdown - positioned on the right */}
          <div className="flex items-center space-x-2 ml-auto">
            <span className="text-sm text-gray-600">Last:</span>
            <select
              value={daysFilter}
              onChange={(e) => handleDaysFilterChange(Number(e.target.value))}
              className="text-sm border border-gray-300 rounded px-2 py-1 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value={2}>2 days</option>
              <option value={7}>7 days</option>
              <option value={14}>14 days</option>
              <option value={30}>30 days</option>
            </select>
          </div>
        </div>

        <div className="section-meta">
          <span className="item-count">
            {filteredRenewals.length} renewal{filteredRenewals.length !== 1 ? 's' : ''} in last {daysFilter} day{daysFilter !== 1 ? 's' : ''}
          </span>
        </div>
      </div>

      <div className="renewals-list">
        {filteredRenewals.map((renewal) => (
          <div
            key={renewal.id}
            className="renewal-item"
            onClick={() => handleRenewalClick(renewal)}
            role="button"
            tabIndex={0}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault()
                handleRenewalClick(renewal)
              }
            }}
          >
            <div className="renewal-info">
              <h4 className="renewal-title">
                {renewal.vendor} - {renewal.product_name}
              </h4>
              <p className="renewal-description">
                {renewal.description || 'No description available'}
              </p>
            </div>
            <div className="renewal-meta">
              <span className="renewal-cost">
                {renewal.cost ? formatCurrency(renewal.cost, renewal.currency || 'CAD') : 'N/A'}
              </span>
              <span className="renewal-date">
                {renewal.start_date
                  ? formatDate(renewal.start_date, 'short')
                  : 'No date set'
                }
              </span>
              <span className={`renewal-status status-${renewal.status?.toLowerCase() || 'unknown'}`}>
                {renewal.status || 'Unknown'}
              </span>
            </div>
          </div>
        ))}
      </div>

      {showViewAll && renewals.length > maxItems && (
        <div className="section-footer">
          {!showAll ? (
            <div className="view-all-container">
              <Button
                variant="secondary"
                size="sm"
                onClick={handleToggleShowAll}
              >
                View all {filteredRenewals.length} renewals
              </Button>
            </div>
          ) : (
            <div className="view-all-container">
              <Button
                variant="secondary"
                size="sm"
                onClick={handleToggleShowAll}
              >
                Show less
              </Button>
            </div>
          )}
        </div>
      )}
    </div>
  )
})

export default RecentRenewals

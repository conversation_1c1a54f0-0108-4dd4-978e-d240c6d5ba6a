/**
 * Page Information Hook
 * 
 * Provides page metadata from the database for consistent header display
 */

'use client'

import { useState, useEffect } from 'react'
import { useData } from './useData'

export interface PageInfo {
  id: number
  name: string
  header: string
  description: string
  route_path: string
  display_order: number
  icon_svg: string | null
}

interface UsePageInfoResult {
  pageInfo: PageInfo | null
  isLoading: boolean
  error: string | null
}

/**
 * Hook to get page information by route path
 */
export function usePageInfo(routePath: string): UsePageInfoResult {
  const [pageInfo, setPageInfo] = useState<PageInfo | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchPageInfo = async () => {
      if (!routePath) {
        setIsLoading(false)
        return
      }

      try {
        setIsLoading(true)
        setError(null)

        const response = await fetch(`/api/admin-pages/by-route?route=${encodeURIComponent(routePath)}`)
        
        if (!response.ok) {
          throw new Error(`Failed to fetch page info: ${response.statusText}`)
        }

        const data = await response.json()
        
        if (data.success && data.data) {
          setPageInfo(data.data)
        } else {
          setError(data.error || 'Failed to load page information')
        }
      } catch (err) {
        console.error('Error fetching page info:', err)
        setError(err instanceof Error ? err.message : 'Failed to load page information')
      } finally {
        setIsLoading(false)
      }
    }

    fetchPageInfo()
  }, [routePath])

  return {
    pageInfo,
    isLoading,
    error
  }
}

/**
 * Hook to get page information by page name
 */
export function usePageInfoByName(pageName: string): UsePageInfoResult {
  const [pageInfo, setPageInfo] = useState<PageInfo | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchPageInfo = async () => {
      if (!pageName) {
        setIsLoading(false)
        return
      }

      try {
        setIsLoading(true)
        setError(null)

        const response = await fetch(`/api/admin-pages/by-name?name=${encodeURIComponent(pageName)}`)
        
        if (!response.ok) {
          throw new Error(`Failed to fetch page info: ${response.statusText}`)
        }

        const data = await response.json()
        
        if (data.success && data.data) {
          setPageInfo(data.data)
        } else {
          setError(data.error || 'Failed to load page information')
        }
      } catch (err) {
        console.error('Error fetching page info:', err)
        setError(err instanceof Error ? err.message : 'Failed to load page information')
      } finally {
        setIsLoading(false)
      }
    }

    fetchPageInfo()
  }, [pageName])

  return {
    pageInfo,
    isLoading,
    error
  }
}

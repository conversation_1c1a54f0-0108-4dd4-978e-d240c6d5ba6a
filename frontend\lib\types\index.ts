/**
 * Comprehensive Type Definitions and Type Guards
 * 
 * This module provides centralized type definitions and type guard functions
 * to ensure type safety throughout the application.
 */

// Base entity interface
export interface BaseEntity {
  id: string;
  created_on: Date;
  changed_on?: Date;
  version?: number; // For optimistic concurrency control
}

// User types
export interface User extends BaseEntity {
  email: string;
  name?: string;
  given_name?: string;
  family_name?: string;
  roles: string[];
  groups?: string[];
  status: 'active' | 'inactive' | 'suspended' | 'deleted';
  last_login?: Date;
  preferences?: UserPreferences;

  // Cognito-specific fields
  'cognito:groups'?: string | string[];
}

// Tenant-specific user interface
export interface TenantUser {
  id: string;
  email: string;
  given_name?: string;
  family_name?: string;
  last_logged?: Date;
  user_group?: string;
  cognito_user_id?: string;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  notifications: {
    email: boolean;
    push: boolean;
    sms: boolean;
  };
  displayDensity: 'comfortable' | 'compact';
  language?: string;
  timezone?: string;
  [key: string]: any;
}

// Client/Tenant types
export interface Client extends BaseEntity {
  name: string;
  domain: string;
  domains?: string[];
  status: 'active' | 'inactive' | 'suspended';
  settings: ClientSettings;
  industry_id?: string;
  tenant_id?: string;
  tenant_schema?: string;
}

export interface ClientSettings {
  schemaReady?: boolean;
  features?: {
    renewals?: boolean;
    vendors?: boolean;
    reports?: boolean;
    notifications?: boolean;
  };
  branding?: {
    logo?: string;
    primaryColor?: string;
    secondaryColor?: string;
  };
  [key: string]: any;
}

export interface TenantContext {
  clientId: string;
  clientName: string;
  tenantId: string;
  tenantSchema: string;
  schemaName: string; // Alias for tenantSchema for backward compatibility
  domains: string[];
  isActive: boolean;
  settings: ClientSettings;
  createdAt: Date;
  updatedAt?: Date | null;
}

// Renewal types - Unified interface for all renewal data across the application
export interface Renewal extends BaseEntity {
  name: string;
  vendor: string;
  vendor_id?: string;
  status: 'active' | 'inactive' | 'pending' | 'expired';
  due_date?: Date;
  start_date?: string; // For database and API compatibility
  annual_cost?: number;
  cost?: number; // For API compatibility
  currency?: string;
  description?: string;
  category?: string;
  priority?: 'low' | 'medium' | 'high' | 'critical';
  auto_renew?: boolean;
  notification_days?: number[];

  // Extended fields for detailed views
  type?: string;
  alerts?: number;
  license_count?: number;
  product_name?: string;
  product_version?: string; // Renamed to avoid conflict with BaseEntity.version
  purchase_type?: string;
  department?: string;
  licensed_date?: string;
  associated_emails?: string;
  reseller?: string;
  cost_code?: string;
  notes?: string;
}

// Vendor types
export interface Vendor extends BaseEntity {
  name: string;
  contact_email?: string;
  contact_phone?: string;
  website?: string;
  status: 'active' | 'inactive';
  address?: Address;
  notes?: string;
}

export interface Address {
  street?: string;
  city?: string;
  state?: string;
  postal_code?: string;
  country?: string;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  errorCode?: string;
  message?: string;
  timestamp: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    totalCount: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Authentication types
export interface AuthSession {
  userId: string;
  email: string;
  given_name?: string;
  family_name?: string;
  name?: string;
  roles: string[];
  groups: string[];
  isAuthenticated: boolean;
  sessionId?: string;
  tenantId?: string;
  tokens: {
    idToken: string;
    accessToken: string;
  };
  licenseInfo?: {
    isValid: boolean;
    licenseType: string;
    maxUsers: number;
    maxTenants: number;
    features: string[];
    expiresAt: Date | null;
    usageCount: number;
    clientName: string;
    activatedAt: Date;
    lastUsage: Date | null;
  };
}



// Database types - Unified database result interface
export interface DbResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  errorCode?: string;
  rowCount?: number;
  executionTime?: number;
  warnings?: string[];
}

// Specialized database result for single records
export interface DbSingleResult<T = any> extends Omit<DbResult<T[]>, 'data'> {
  data?: T;
}

// Specialized database result for multiple records
export interface DbMultipleResult<T = any> extends DbResult<T[]> {
  data?: T[];
}

export interface QueryOptions {
  timeout?: number;
  retries?: number;
  schema?: string;
}

// Form types
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'number' | 'date' | 'select' | 'textarea' | 'checkbox';
  required?: boolean;
  placeholder?: string;
  options?: { value: string; label: string }[];
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    custom?: (value: any) => string | null;
  };
}

export interface FormState<T = any> {
  values: T;
  errors: Record<string, string>;
  touched: Record<string, boolean>;
  isSubmitting: boolean;
  isValid: boolean;
}

// Component prop types
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
  'data-testid'?: string;
}

export interface LoadingState {
  isLoading: boolean;
  error?: string | null;
}

// Type Guards
export function isUser(obj: any): obj is User {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.id === 'string' &&
    typeof obj.email === 'string' &&
    Array.isArray(obj.roles) &&
    ['active', 'inactive', 'suspended', 'deleted'].includes(obj.status)
  );
}

export function isClient(obj: any): obj is Client {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.id === 'string' &&
    typeof obj.name === 'string' &&
    typeof obj.domain === 'string' &&
    ['active', 'inactive', 'suspended'].includes(obj.status)
  );
}

export function isTenantContext(obj: any): obj is TenantContext {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.clientId === 'string' &&
    typeof obj.clientName === 'string' &&
    typeof obj.tenantId === 'string' &&
    typeof obj.tenantSchema === 'string' &&
    Array.isArray(obj.domains) &&
    typeof obj.isActive === 'boolean'
  );
}

export function isRenewal(obj: any): obj is Renewal {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.id === 'string' &&
    typeof obj.name === 'string' &&
    typeof obj.vendor === 'string' &&
    ['active', 'inactive', 'pending', 'expired'].includes(obj.status)
  );
}

export function isApiResponse<T>(obj: any): obj is ApiResponse<T> {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.success === 'boolean' &&
    typeof obj.timestamp === 'string'
  );
}

export function isPaginatedResponse<T>(obj: any): obj is PaginatedResponse<T> {
  if (!isApiResponse(obj)) {
    return false;
  }

  if (!('pagination' in obj) || !obj.pagination || typeof obj.pagination !== 'object') {
    return false;
  }

  const pagination = obj.pagination;
  return (
    'page' in pagination &&
    'limit' in pagination &&
    'totalCount' in pagination &&
    typeof pagination.page === 'number' &&
    typeof pagination.limit === 'number' &&
    typeof pagination.totalCount === 'number'
  );
}

export function isAuthSession(obj: any): obj is AuthSession {
  return (
    obj &&
    typeof obj === 'object' &&
    typeof obj.isAuthenticated === 'boolean' &&
    typeof obj.userId === 'string' &&
    typeof obj.email === 'string' &&
    Array.isArray(obj.roles)
  );
}

// Utility types
export type Nullable<T> = T | null;
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

// Event types
export interface AppEvent<T = any> {
  type: string;
  payload: T;
  timestamp: Date;
  userId?: string;
  sessionId?: string;
}

// Error types
export interface AppError extends Error {
  code?: string;
  statusCode?: number;
  details?: any;
  timestamp: Date;
}

// Configuration types
export interface AppConfig {
  aws: {
    region: string;
    userPoolId: string;
    userPoolClientId: string;
    cognitoDomain: string;
  };
  auth: {
    redirectSignIn: string;
    redirectSignOut: string;
  };
  app: {
    environment: 'development' | 'production' | 'test';
    isDevelopment: boolean;
    isProduction: boolean;
  };
}

// Overview specific types
export interface OverviewStats {
  totalRenewals: number;
  renewalsDue: number;
  vendors: number;
  annualSpend: string;
}

// Note: All types are already exported as interfaces above

// Tenant Logging types
export interface TenantLogEntry {
  log_id: number
  log_uuid: string
  operation: 'INSERT' | 'UPDATE' | 'DELETE'
  table_name: string
  record_id: string
  timestamp: string
  user_id?: string
  user_email?: string
  session_id?: string
  ip_address?: string
  user_agent?: string
  request_id?: string
  old_values?: Record<string, any>
  new_values?: Record<string, any>
  changed_fields: string[]
  business_impact: 'low' | 'medium' | 'high' | 'critical'
  change_reason?: string
  metadata: Record<string, any>
  tags: string[]
  checksum: string
  retention_days: number
  compliance_flags: string[]
  partition_date: string
}

export interface TenantActivitySummary {
  table_name: string
  operation: string
  count: number
  unique_users: number
  high_impact_count: number
}

export interface UserActivitySummary {
  user_id: string
  user_email: string
  total_actions: number
  tables_affected: string[]
  high_impact_actions: number
  last_activity: string
}

export interface RecordHistory {
  log_id: number
  operation: string
  timestamp: string
  user_email: string
  changed_fields: string[]
  old_values: Record<string, any>
  new_values: Record<string, any>
  business_impact: string
}

export interface ComplianceReport {
  compliance_flag: string
  total_events: number
  high_impact_events: number
  unique_users: number
  tables_affected: string[]
  date_range: string
}

export interface TenantLogFilters {
  table?: string
  operation?: string
  user_id?: string
  record_id?: string
  start_date?: string
  end_date?: string
  business_impact?: string
  limit?: number
  offset?: number
  report_type?: 'activity' | 'user' | 'compliance'
}

export interface TenantLogSearchCriteria {
  field_name: string
  old_value?: string
  new_value?: string
  start_date?: string
  end_date?: string
  limit?: number
}

// Re-export type utilities
export * from './utils';

/**
 * Unified Tenant Service
 * 
 * Provides a single, coherent interface for all tenant operations.
 * Eliminates inconsistencies between different tenant patterns in the codebase.
 */

import { NextRequest } from 'next/server';
import { AuthSession, TenantContext } from '@/lib/types';
import { getTenantByDomain, getTenantByUserId } from '@/lib/tenant/clients';
import { createErrorResponse, ApiErrorCode, HttpStatus } from '@/lib/api/response';
import { validateClientLicense } from '@/lib/services/license-service';

// Unified tenant resolution result
export interface TenantResult {
  success: boolean;
  tenant?: TenantContext;
  error?: string;
  errorCode?: ApiErrorCode;
}

// Unified tenant session interface
export interface UnifiedTenantSession extends AuthSession {
  tenant: TenantContext;
  tenantId: string;
  tenantSchema: string;
}

/**
 * Core tenant service class
 */
export class UnifiedTenantService {
  private static instance: UnifiedTenantService;

  public static getInstance(): UnifiedTenantService {
    if (!UnifiedTenantService.instance) {
      UnifiedTenantService.instance = new UnifiedTenantService();
    }
    return UnifiedTenantService.instance;
  }

  /**
   * Resolve tenant context from request and session
   */
  async resolveTenant(request: NextRequest, session: AuthSession): Promise<TenantResult> {
    try {
      // Strategy 1: Domain-based tenant resolution
      const host = request.headers.get('host');
      if (host) {
        const domain = host.split(':')[0]; // Remove port if present
        const tenant = await getTenantByDomain(domain);
        if (tenant) {
          console.log(`✅ Tenant resolved by domain: ${domain} -> ${tenant.clientName}`);
          return await this.validateTenantAccess(tenant, session);
        }
      }

      // Strategy 2: User-based tenant resolution
      if (session.userId) {
        const tenant = await getTenantByUserId(session.userId);
        if (tenant) {
          console.log(`✅ Tenant resolved by user: ${session.userId} -> ${tenant.clientName}`);
          return await this.validateTenantAccess(tenant, session);
        }
      }

      // Strategy 3: Header-based tenant resolution (for API clients)
      const tenantHeader = request.headers.get('X-Tenant-ID');
      if (tenantHeader) {
        console.log(`⚠️ Header-based tenant resolution not implemented: ${tenantHeader}`);
      }

      console.warn('❌ No tenant context could be resolved');
      return {
        success: false,
        error: 'No tenant context available',
        errorCode: ApiErrorCode.TENANT_NOT_FOUND
      };

    } catch (error) {
      console.error('Error resolving tenant context:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Tenant resolution failed',
        errorCode: ApiErrorCode.INTERNAL_ERROR
      };
    }
  }

  /**
   * Validate tenant access for the given session
   */
  private async validateTenantAccess(tenant: TenantContext, session: AuthSession): Promise<TenantResult> {
    // Check if tenant is active
    if (!tenant.isActive) {
      return {
        success: false,
        error: 'Tenant is currently inactive',
        errorCode: ApiErrorCode.TENANT_INACTIVE
      };
    }

    // Validate license if required
    try {
      const clientId = parseInt(tenant.clientId);
      const licenseValidation = await validateClientLicense(clientId);
      if (!licenseValidation.isValid) {
        return {
          success: false,
          error: 'Invalid or expired license',
          errorCode: ApiErrorCode.FORBIDDEN
        };
      }
    } catch (error) {
      console.warn('License validation failed:', error);
      // Continue without license validation for now
    }

    return {
      success: true,
      tenant
    };
  }

  /**
   * Create a unified tenant session
   */
  createTenantSession(session: AuthSession, tenant: TenantContext): UnifiedTenantSession {
    return {
      ...session,
      tenant,
      tenantId: tenant.tenantId,
      tenantSchema: tenant.tenantSchema
    };
  }

  /**
   * Validate tenant schema access
   */
  validateSchemaAccess(tenantSession: UnifiedTenantSession, requestedSchema: string): boolean {
    return tenantSession.tenantSchema === requestedSchema;
  }

  /**
   * Get tenant-specific database connection string
   */
  getTenantConnectionString(tenant: TenantContext): string {
    // This would be implemented based on your database setup
    return `********************************/db?search_path=${tenant.tenantSchema}`;
  }

  /**
   * Check if user has access to tenant
   */
  async hasUserAccess(userId: string, tenantId: string): Promise<boolean> {
    try {
      // This would implement your specific access control logic
      // For now, we'll use the existing tenant resolution
      const tenant = await getTenantByUserId(userId);
      return tenant?.tenantId === tenantId;
    } catch (error) {
      console.error('Error checking user access:', error);
      return false;
    }
  }
}

// Export singleton instance
export const unifiedTenantService = UnifiedTenantService.getInstance();

// Export convenience functions
export async function resolveTenantFromRequest(
  request: NextRequest, 
  session: AuthSession
): Promise<TenantResult> {
  return unifiedTenantService.resolveTenant(request, session);
}

export function createTenantSession(
  session: AuthSession, 
  tenant: TenantContext
): UnifiedTenantSession {
  return unifiedTenantService.createTenantSession(session, tenant);
}

/**
 * Admin API endpoint to test master data management tables
 */

import { NextRequest } from 'next/server';
import { executeQuery } from '@/lib/database';
import { 
  createSuccessResponse, 
  createErrorResponse, 
  ApiErrorCode, 
  HttpStatus,
  withErrorHandling 
} from '@/lib/api/response';

export const GET = withErrorHandling(async (request: NextRequest) => {
  try {
    console.log('Testing master data tables...');
    
    // Test global vendors
    const vendorsResult = await executeQuery(`
      SELECT 
        id, 
        canonical_name, 
        legal_name, 
        domain, 
        country_code, 
        status,
        confidence_score,
        created_on
      FROM metadata.global_vendors 
      WHERE status = 'active'
      ORDER BY canonical_name
      LIMIT 10
    `);
    
    if (!vendorsResult.success) {
      throw new Error(`Failed to query global vendors: ${vendorsResult.error}`);
    }
    
    // Test global products
    const productsResult = await executeQuery(`
      SELECT 
        p.id,
        p.canonical_name,
        p.product_category,
        p.product_type,
        v.canonical_name as vendor_name,
        p.status,
        p.created_on
      FROM metadata.global_products p
      JOIN metadata.global_vendors v ON p.vendor_id = v.id
      WHERE p.status = 'active'
      ORDER BY p.canonical_name
      LIMIT 10
    `);
    
    if (!productsResult.success) {
      throw new Error(`Failed to query global products: ${productsResult.error}`);
    }
    
    // Test global product versions
    const versionsResult = await executeQuery(`
      SELECT 
        pv.id,
        pv.version_number,
        pv.release_date,
        p.canonical_name as product_name,
        v.canonical_name as vendor_name,
        pv.status,
        pv.created_on
      FROM metadata.global_product_versions pv
      JOIN metadata.global_products p ON pv.product_id = p.id
      JOIN metadata.global_vendors v ON p.vendor_id = v.id
      WHERE pv.status = 'active'
      ORDER BY p.canonical_name, pv.version_number
      LIMIT 10
    `);
    
    if (!versionsResult.success) {
      throw new Error(`Failed to query global product versions: ${versionsResult.error}`);
    }
    
    // Test tenant vendors
    const tenantVendorsResult = await executeQuery(`
      SELECT 
        tv.id,
        tv.name,
        tv.display_name,
        tv.contact_email,
        tv.sync_status,
        tv.sync_confidence,
        gv.canonical_name as global_vendor_name,
        tv.created_on
      FROM "tenant_0000000000000001".tenant_vendors tv
      LEFT JOIN metadata.global_vendors gv ON tv.global_vendor_id = gv.id
      ORDER BY tv.name
      LIMIT 10
    `);
    
    if (!tenantVendorsResult.success) {
      throw new Error(`Failed to query tenant vendors: ${tenantVendorsResult.error}`);
    }
    
    // Test tenant products
    const tenantProductsResult = await executeQuery(`
      SELECT 
        tp.id,
        tp.name,
        tp.description,
        tp.category,
        tv.name as vendor_name,
        tp.sync_status,
        tp.sync_confidence,
        gp.canonical_name as global_product_name,
        tp.created_on
      FROM "tenant_0000000000000001".tenant_products tp
      JOIN "tenant_0000000000000001".tenant_vendors tv ON tp.vendor_id = tv.id
      LEFT JOIN metadata.global_products gp ON tp.global_product_id = gp.id
      ORDER BY tp.name
      LIMIT 10
    `);
    
    if (!tenantProductsResult.success) {
      throw new Error(`Failed to query tenant products: ${tenantProductsResult.error}`);
    }
    
    // Test tenant product versions
    const tenantVersionsResult = await executeQuery(`
      SELECT 
        tpv.id,
        tpv.version,
        tpv.notes,
        tpv.is_current,
        tp.name as product_name,
        tv.name as vendor_name,
        tpv.sync_status,
        tpv.sync_confidence,
        gpv.version_number as global_version,
        tpv.created_on
      FROM "tenant_0000000000000001".tenant_product_versions tpv
      JOIN "tenant_0000000000000001".tenant_products tp ON tpv.product_id = tp.id
      JOIN "tenant_0000000000000001".tenant_vendors tv ON tp.vendor_id = tv.id
      LEFT JOIN metadata.global_product_versions gpv ON tpv.global_product_version_id = gpv.id
      ORDER BY tp.name, tpv.version
      LIMIT 10
    `);
    
    if (!tenantVersionsResult.success) {
      throw new Error(`Failed to query tenant product versions: ${tenantVersionsResult.error}`);
    }
    
    // Test synchronization query - find unmatched tenant vendors
    const unmatchedVendorsResult = await executeQuery(`
      SELECT 
        tv.id,
        tv.name,
        tv.display_name,
        tv.sync_status,
        tv.sync_confidence
      FROM "tenant_0000000000000001".tenant_vendors tv
      WHERE tv.sync_status = 'pending' OR tv.global_vendor_id IS NULL
      ORDER BY tv.name
      LIMIT 5
    `);
    
    if (!unmatchedVendorsResult.success) {
      throw new Error(`Failed to query unmatched vendors: ${unmatchedVendorsResult.error}`);
    }
    
    console.log('✅ All master data table tests passed!');
    
    const testResults = {
      globalVendors: {
        count: vendorsResult.data?.length || 0,
        data: vendorsResult.data || []
      },
      globalProducts: {
        count: productsResult.data?.length || 0,
        data: productsResult.data || []
      },
      globalProductVersions: {
        count: versionsResult.data?.length || 0,
        data: versionsResult.data || []
      },
      tenantVendors: {
        count: tenantVendorsResult.data?.length || 0,
        data: tenantVendorsResult.data || []
      },
      tenantProducts: {
        count: tenantProductsResult.data?.length || 0,
        data: tenantProductsResult.data || []
      },
      tenantProductVersions: {
        count: tenantVersionsResult.data?.length || 0,
        data: tenantVersionsResult.data || []
      },
      unmatchedVendors: {
        count: unmatchedVendorsResult.data?.length || 0,
        data: unmatchedVendorsResult.data || []
      }
    };
    
    return createSuccessResponse(
      testResults,
      'Master data management tables tested successfully'
    );
    
  } catch (error) {
    console.error('❌ Error testing master data tables:', error);
    return createErrorResponse(
      `Failed to test master data tables: ${error}`,
      ApiErrorCode.DATABASE_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
});

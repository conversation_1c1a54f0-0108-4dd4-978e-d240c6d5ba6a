/**
 * Renewal History Card Component
 * 
 * Displays renewal history and timeline of changes
 */

'use client'

import React, { useState } from 'react'
import { Renewal } from '@/lib/types'

interface RenewalHistoryCardProps {
  renewal: Renewal
}

interface HistoryEntry {
  id: string
  date: string
  action: string
  description: string
  user?: string
  details?: string
}

export default function RenewalHistoryCard({ renewal }: RenewalHistoryCardProps) {
  const [showAllHistory, setShowAllHistory] = useState(false)

  // Mock history data - in real implementation, this would come from API
  const historyEntries: HistoryEntry[] = [
    {
      id: '1',
      date: new Date().toISOString(),
      action: 'Viewed',
      description: 'Renewal details viewed',
      user: 'Current User'
    },
    {
      id: '2',
      date: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
      action: 'Updated',
      description: 'Cost updated from $95,000 to $100,000',
      user: 'Admin User',
      details: 'Annual cost adjustment due to license increase'
    },
    {
      id: '3',
      date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
      action: 'Alert Added',
      description: 'Email alert configured for 30 days before renewal',
      user: 'Admin User'
    },
    {
      id: '4',
      date: renewal.created_on?.toISOString() || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
      action: 'Created',
      description: 'Renewal record created',
      user: 'System'
    }
  ]

  const displayedEntries = showAllHistory ? historyEntries : historyEntries.slice(0, 3)

  const getActionIcon = (action: string) => {
    switch (action.toLowerCase()) {
      case 'created':
        return (
          <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
          </div>
        )
      case 'updated':
        return (
          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
          </div>
        )
      case 'viewed':
        return (
          <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
          </div>
        )
      case 'alert added':
        return (
          <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5zM9 7H4l5-5v5zm6 10V7a1 1 0 00-1-1H6a1 1 0 00-1 1v10a1 1 0 001 1h8a1 1 0 001-1z" />
            </svg>
          </div>
        )
      default:
        return (
          <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
            <svg className="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
        )
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffTime = now.getTime() - date.getTime()
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays === 0) {
      return 'Today'
    } else if (diffDays === 1) {
      return 'Yesterday'
    } else if (diffDays < 7) {
      return `${diffDays} days ago`
    } else {
      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric'
      })
    }
  }

  return (
    <div className="renewal-history-card bg-white rounded-lg border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-lg font-semibold text-gray-900">Historical Changes</h2>
        {historyEntries.length > 3 && (
          <button
            onClick={() => setShowAllHistory(!showAllHistory)}
            className="text-sm text-blue-600 hover:text-blue-700 transition-colors"
          >
            {showAllHistory ? 'Show Less' : 'View All'}
          </button>
        )}
      </div>

      {historyEntries.length === 0 ? (
        <div className="text-center py-8">
          <div className="text-gray-400 text-4xl mb-2">📋</div>
          <p className="text-gray-500 text-sm">No history records available yet</p>
          <p className="text-gray-400 text-xs mt-1">
            Changes to this renewal will appear here
          </p>
        </div>
      ) : (
        <div className="space-y-4">
          {displayedEntries.map((entry, index) => (
            <div key={entry.id} className="flex items-start space-x-3">
              {getActionIcon(entry.action)}
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium text-gray-900">
                    {entry.description}
                  </p>
                  <span className="text-xs text-gray-500">
                    {formatDate(entry.date)}
                  </span>
                </div>
                
                {entry.details && (
                  <p className="text-xs text-gray-600 mt-1">
                    {entry.details}
                  </p>
                )}
                
                {entry.user && (
                  <p className="text-xs text-gray-500 mt-1">
                    by {entry.user}
                  </p>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

/**
 * Advanced Rate Limiting System
 * 
 * Provides sophisticated rate limiting with tiered limits based on user roles,
 * endpoint sensitivity, and adaptive behavior patterns.
 */

import { NextRequest } from 'next/server'
import { AuthSession } from '@/lib/types'

// Rate limit configuration interfaces
interface RateLimitConfig {
  requests: number
  windowMs: number
  burstAllowance?: number
  skipSuccessfulRequests?: boolean
  skipFailedRequests?: boolean
}

interface TieredRateLimits {
  guest: RateLimitConfig
  user: RateLimitConfig
  premium: RateLimitConfig
  admin: RateLimitConfig
  super_admin: RateLimitConfig
}

interface EndpointSensitivity {
  level: 'low' | 'medium' | 'high' | 'critical'
  multiplier: number
}

interface RateLimitEntry {
  count: number
  resetTime: number
  violations: number
  lastViolation?: number
  burstUsed: number
  successfulRequests: number
  failedRequests: number
}

interface RateLimitResult {
  allowed: boolean
  remaining: number
  resetTime: number
  retryAfter?: number
  reason?: string
}

// Default rate limit configurations by user tier
const DEFAULT_RATE_LIMITS: TieredRateLimits = {
  guest: {
    requests: 10,
    windowMs: 60000, // 1 minute
    burstAllowance: 2
  },
  user: {
    requests: 100,
    windowMs: 60000, // 1 minute
    burstAllowance: 10
  },
  premium: {
    requests: 500,
    windowMs: 60000, // 1 minute
    burstAllowance: 50
  },
  admin: {
    requests: 1000,
    windowMs: 60000, // 1 minute
    burstAllowance: 100
  },
  super_admin: {
    requests: 5000,
    windowMs: 60000, // 1 minute
    burstAllowance: 500
  }
}

// Endpoint sensitivity configurations
const ENDPOINT_SENSITIVITY: Record<string, EndpointSensitivity> = {
  // Authentication endpoints - high sensitivity
  '/api/auth/login': { level: 'high', multiplier: 0.2 },
  '/api/auth/register': { level: 'high', multiplier: 0.1 },
  '/api/auth/reset-password': { level: 'critical', multiplier: 0.05 },
  
  // User management - medium to high sensitivity
  '/api/users': { level: 'medium', multiplier: 0.5 },
  '/api/users/[id]': { level: 'medium', multiplier: 0.7 },
  
  // Data modification - medium sensitivity
  '/api/renewals': { level: 'medium', multiplier: 0.8 },
  '/api/clients': { level: 'medium', multiplier: 0.6 },
  
  // Read operations - low sensitivity
  '/api/dashboard': { level: 'low', multiplier: 1.0 },
  '/api/metadata': { level: 'low', multiplier: 1.2 },
  
  // Admin operations - critical sensitivity
  '/api/admin': { level: 'critical', multiplier: 0.1 },
  '/api/admin/sync': { level: 'critical', multiplier: 0.05 }
}

class AdvancedRateLimiter {
  private static instance: AdvancedRateLimiter
  private rateLimitStore = new Map<string, RateLimitEntry>()
  private cleanupInterval: NodeJS.Timeout

  private constructor() {
    // Clean up expired entries every 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanup()
    }, 5 * 60 * 1000)
  }

  static getInstance(): AdvancedRateLimiter {
    if (!AdvancedRateLimiter.instance) {
      AdvancedRateLimiter.instance = new AdvancedRateLimiter()
    }
    return AdvancedRateLimiter.instance
  }

  /**
   * Check rate limit for a request
   */
  checkRateLimit(
    identifier: string,
    userTier: keyof TieredRateLimits,
    endpoint: string,
    isSuccess?: boolean
  ): RateLimitResult {
    const now = Date.now()
    
    // Get base configuration for user tier
    const baseConfig = DEFAULT_RATE_LIMITS[userTier]
    
    // Apply endpoint sensitivity multiplier
    const sensitivity = this.getEndpointSensitivity(endpoint)
    const adjustedConfig: RateLimitConfig = {
      ...baseConfig,
      requests: Math.floor(baseConfig.requests * sensitivity.multiplier),
      burstAllowance: Math.floor((baseConfig.burstAllowance || 0) * sensitivity.multiplier)
    }

    // Get or create rate limit entry
    let entry = this.rateLimitStore.get(identifier)
    
    if (!entry || now >= entry.resetTime) {
      // Create new entry or reset expired entry
      entry = {
        count: 0,
        resetTime: now + adjustedConfig.windowMs,
        violations: entry?.violations || 0,
        lastViolation: entry?.lastViolation,
        burstUsed: 0,
        successfulRequests: 0,
        failedRequests: 0
      }
      this.rateLimitStore.set(identifier, entry)
    }

    // Update request statistics
    if (isSuccess === true) {
      entry.successfulRequests++
    } else if (isSuccess === false) {
      entry.failedRequests++
    }

    // Check if request should be counted
    const shouldCount = this.shouldCountRequest(adjustedConfig, isSuccess)
    
    if (!shouldCount) {
      return {
        allowed: true,
        remaining: Math.max(0, adjustedConfig.requests - entry.count),
        resetTime: entry.resetTime
      }
    }

    // Check burst allowance first
    const burstLimit = adjustedConfig.burstAllowance || 0
    const regularLimit = adjustedConfig.requests
    const totalLimit = regularLimit + burstLimit

    // Check if we're within regular limits
    if (entry.count < regularLimit) {
      entry.count++
      return {
        allowed: true,
        remaining: Math.max(0, regularLimit - entry.count),
        resetTime: entry.resetTime
      }
    }

    // Check if we can use burst allowance
    if (entry.count < totalLimit && entry.burstUsed < burstLimit) {
      entry.count++
      entry.burstUsed++
      return {
        allowed: true,
        remaining: 0, // No regular remaining, using burst
        resetTime: entry.resetTime
      }
    }

    // Rate limit exceeded
    entry.violations++
    entry.lastViolation = now

    // Calculate retry after time (exponential backoff for repeated violations)
    const baseRetryAfter = Math.ceil((entry.resetTime - now) / 1000)
    const violationMultiplier = Math.min(entry.violations, 10) // Cap at 10x
    const retryAfter = baseRetryAfter * violationMultiplier

    return {
      allowed: false,
      remaining: 0,
      resetTime: entry.resetTime,
      retryAfter,
      reason: `Rate limit exceeded. ${entry.violations} violations.`
    }
  }

  /**
   * Get endpoint sensitivity configuration
   */
  private getEndpointSensitivity(endpoint: string): EndpointSensitivity {
    // Try exact match first
    if (ENDPOINT_SENSITIVITY[endpoint]) {
      return ENDPOINT_SENSITIVITY[endpoint]
    }

    // Try pattern matching for dynamic routes
    for (const [pattern, sensitivity] of Object.entries(ENDPOINT_SENSITIVITY)) {
      if (this.matchesPattern(endpoint, pattern)) {
        return sensitivity
      }
    }

    // Default to medium sensitivity
    return { level: 'medium', multiplier: 1.0 }
  }

  /**
   * Check if endpoint matches a pattern (simple pattern matching)
   */
  private matchesPattern(endpoint: string, pattern: string): boolean {
    // Convert pattern like '/api/users/[id]' to regex
    const regexPattern = pattern
      .replace(/\[.*?\]/g, '[^/]+') // Replace [id] with [^/]+
      .replace(/\//g, '\\/') // Escape forward slashes
    
    const regex = new RegExp(`^${regexPattern}$`)
    return regex.test(endpoint)
  }

  /**
   * Determine if request should be counted based on configuration
   */
  private shouldCountRequest(config: RateLimitConfig, isSuccess?: boolean): boolean {
    if (config.skipSuccessfulRequests && isSuccess === true) {
      return false
    }
    
    if (config.skipFailedRequests && isSuccess === false) {
      return false
    }
    
    return true
  }

  /**
   * Get user tier from session
   */
  getUserTier(session?: AuthSession): keyof TieredRateLimits {
    if (!session) {
      return 'guest'
    }

    const roles = session.roles || []
    
    if (roles.includes('super_admin')) {
      return 'super_admin'
    }
    
    if (roles.includes('admin')) {
      return 'admin'
    }
    
    if (roles.includes('premium') || roles.includes('pro')) {
      return 'premium'
    }
    
    return 'user'
  }

  /**
   * Get rate limit identifier
   */
  getRateLimitIdentifier(request: NextRequest, session?: AuthSession): string {
    if (session) {
      return `user:${session.userId}`
    }
    
    // Get IP from various headers
    const forwarded = request.headers.get('x-forwarded-for')
    const realIp = request.headers.get('x-real-ip')
    const ip = forwarded?.split(',')[0] || realIp || 'unknown'
    
    return `ip:${ip}`
  }

  /**
   * Clean up expired entries
   */
  private cleanup(): void {
    const now = Date.now()
    
    for (const [key, entry] of this.rateLimitStore.entries()) {
      // Remove entries that are expired and haven't had violations recently
      if (now >= entry.resetTime && (!entry.lastViolation || now - entry.lastViolation > 24 * 60 * 60 * 1000)) {
        this.rateLimitStore.delete(key)
      }
    }
  }

  /**
   * Get rate limit statistics
   */
  getStats(): {
    totalEntries: number
    activeEntries: number
    violationsInLast24h: number
  } {
    const now = Date.now()
    const last24h = now - 24 * 60 * 60 * 1000
    
    let activeEntries = 0
    let violationsInLast24h = 0
    
    for (const entry of this.rateLimitStore.values()) {
      if (now < entry.resetTime) {
        activeEntries++
      }
      
      if (entry.lastViolation && entry.lastViolation > last24h) {
        violationsInLast24h++
      }
    }
    
    return {
      totalEntries: this.rateLimitStore.size,
      activeEntries,
      violationsInLast24h
    }
  }

  /**
   * Destroy the rate limiter (cleanup)
   */
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
    }
    this.rateLimitStore.clear()
  }
}

// Export singleton instance
export const advancedRateLimiter = AdvancedRateLimiter.getInstance()

// Export types and utilities
export type { RateLimitResult, TieredRateLimits, EndpointSensitivity }
export { AdvancedRateLimiter }

import { NextRequest } from 'next/server';
import { withAuth } from '@/lib/api/auth-middleware';
import { resolveTenantContext, TenantContext } from '@/lib/tenant/context';
import { executeQuery, schemaExists } from '@/lib/database';
import {
  createSuccessResponse,
  createErrorResponse,
  ApiErrorCode,
  HttpStatus
} from '@/lib/api/response';

// Overview stats interface
interface OverviewStats {
  totalRenewals: number;
  renewalsDue: number;
  vendors: number;
  annualSpend: string;
}

export const GET = withAuth(async (request: NextRequest, session) => {
  console.log('[DASHBOARD-STATS-API] GET request received');

  try {
    // Resolve tenant context using session
    const tenantResult = await resolveTenantContext(session);
    if (!tenantResult.success) {
      return createErrorResponse(
        'Failed to resolve tenant context',
        ApiErrorCode.DATABASE_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }

    const tenant = tenantResult.tenant;
    if (!tenant) {
      return createErrorResponse(
        'Tenant not found',
        ApiErrorCode.TENANT_NOT_FOUND,
        HttpStatus.NOT_FOUND
      );
    }

    console.log(`📊 [STATS] Tenant context:`, {
      clientId: tenant.clientId,
      clientName: tenant.clientName,
      tenantSchema: tenant.tenantSchema,
      domains: tenant.domains
    });

    // Check if tenant schema exists
    const schemaReady = await schemaExists(tenant.tenantSchema);
    console.log(`📊 [STATS] Schema ${tenant.tenantSchema} ready:`, schemaReady);

  // Let's also check what schemas actually exist
  const allSchemasResult = await executeQuery(
    'SELECT schema_name FROM information_schema.schemata WHERE schema_name LIKE $1',
    ['tenant_%']
  );
  console.log(`📊 [STATS] Available tenant schemas:`, allSchemasResult.data?.map(s => s.schema_name));

  let stats: OverviewStats = {
    totalRenewals: 0,
    renewalsDue: 0,
    vendors: 0,
    annualSpend: '$0'
  };

  if (schemaReady) {
    // Query tenant schema for actual data using correct table and column names
    const queries = [
      {
        query: `SELECT COUNT(*) as total_renewals FROM "${tenant.tenantSchema}".tenant_renewals WHERE is_deleted = false`,
        key: 'totalRenewals'
      },
      {
        query: `SELECT COUNT(*) as renewals_due FROM "${tenant.tenantSchema}".tenant_renewals WHERE is_deleted = false AND expiry_date <= CURRENT_DATE + INTERVAL '30 days'`,
        key: 'renewalsDue'
      },
      {
        query: `SELECT COUNT(DISTINCT vendor_id) as vendors FROM "${tenant.tenantSchema}".tenant_renewals WHERE is_deleted = false AND vendor_id IS NOT NULL`,
        key: 'vendors'
      },
      {
        query: `SELECT COALESCE(SUM(ri.total_cost), 0) as annual_spend FROM "${tenant.tenantSchema}".tenant_renewals r LEFT JOIN "${tenant.tenantSchema}".tenant_renewal_items ri ON r.id = ri.renewal_id WHERE r.is_deleted = false`,
        key: 'annualSpend'
      }
    ];

    // Execute queries with proper error handling
    for (const { query, key } of queries) {
      console.log(`📊 [STATS] Executing query for ${key}:`, query);
      const result = await executeQuery(query, [], { schema: tenant.tenantSchema });
      console.log(`📊 [STATS] Query result for ${key}:`, result);

      if (result.success && result.data && result.data.length > 0) {
        const value = result.data[0];
        console.log(`📊 [STATS] Raw value for ${key}:`, value);
        switch (key) {
          case 'totalRenewals':
            stats.totalRenewals = parseInt(value.total_renewals || '0');
            break;
          case 'renewalsDue':
            stats.renewalsDue = parseInt(value.renewals_due || '0');
            break;
          case 'vendors':
            stats.vendors = parseInt(value.vendors || '0');
            break;
          case 'annualSpend':
            const amount = parseFloat(value.annual_spend || '0');
            stats.annualSpend = `$${amount.toLocaleString()}`;
            break;
        }
      } else {
        console.log(`📊 [STATS] Query failed or no data for ${key}:`, result);
      }
    }
  } else {
    console.log(`📊 [STATS] Tenant schema ${tenant.tenantSchema} not ready yet, returning zeros`);
    // Return zeros when schema doesn't exist - no mock data
    stats = {
      totalRenewals: 0,
      renewalsDue: 0,
      vendors: 0,
      annualSpend: '$0'
    };
  }

  console.log(`📊 [STATS] Final stats being returned:`, stats);
  return createSuccessResponse(stats, 'Dashboard statistics retrieved successfully');
  } catch (error) {
    console.error('[DASHBOARD-STATS-API] Error:', error);
    return createErrorResponse(
      'Failed to fetch dashboard statistics',
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
}, {
  requireAuth: true
});

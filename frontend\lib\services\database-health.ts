/**
 * Database Health Check and Initialization Service
 * 
 * Provides comprehensive database health monitoring and initialization
 */

import { databaseService } from './database-service'
import { handleError, getUserFriendlyMessage } from '@/lib/utils/error-handler'
import { validateDatabaseConfig } from '@/lib/config/validation'

export interface DatabaseHealthStatus {
  isHealthy: boolean
  isConnected: boolean
  connectionCount: number
  lastChecked: Date
  errors: string[]
  warnings: string[]
  responseTime?: number
}

export interface DatabaseInitializationResult {
  success: boolean
  healthStatus: DatabaseHealthStatus
  error?: string
}

/**
 * Perform comprehensive database health check
 */
export async function checkDatabaseHealth(): Promise<DatabaseHealthStatus> {
  const startTime = Date.now()
  const errors: string[] = []
  const warnings: string[] = []
  let isConnected = false
  let connectionCount = 0

  try {
    console.log('🔍 [DB-HEALTH] Starting database health check...')

    // First, validate configuration
    const configValidation = validateDatabaseConfig()
    if (!configValidation.isValid) {
      errors.push(...configValidation.errors)
      warnings.push(...configValidation.warnings)
      
      return {
        isHealthy: false,
        isConnected: false,
        connectionCount: 0,
        lastChecked: new Date(),
        errors,
        warnings
      }
    }

    // Check if database service is ready
    if (!databaseService.isReady()) {
      console.log('🔄 [DB-HEALTH] Database service not ready, initializing...')
      await databaseService.initialize()
    }

    // Test basic connectivity
    const client = await databaseService.getClient()
    
    try {
      // Simple connectivity test
      const result = await client.query('SELECT 1 as health_check, NOW() as server_time')
      
      if (result.rows.length > 0) {
        isConnected = true
        console.log('✅ [DB-HEALTH] Basic connectivity test passed')
      } else {
        errors.push('Database query returned no results')
      }
    } finally {
      client.release()
    }

    // Get connection pool status
    const poolStatus = databaseService.getPoolStatus()
    connectionCount = poolStatus.totalConnections

    // Test schema access
    try {
      const schemaClient = await databaseService.getClient()
      try {
        await schemaClient.query('SELECT schema_name FROM information_schema.schemata WHERE schema_name = $1', ['metadata'])
        console.log('✅ [DB-HEALTH] Schema access test passed')
      } catch (schemaError) {
        warnings.push('Could not access metadata schema')
        console.warn('⚠️ [DB-HEALTH] Schema access warning:', schemaError)
      } finally {
        schemaClient.release()
      }
    } catch (schemaTestError) {
      warnings.push('Schema connectivity test failed')
    }

    const responseTime = Date.now() - startTime
    const isHealthy = isConnected && errors.length === 0

    console.log(`${isHealthy ? '✅' : '❌'} [DB-HEALTH] Health check completed in ${responseTime}ms`)

    return {
      isHealthy,
      isConnected,
      connectionCount,
      lastChecked: new Date(),
      errors,
      warnings,
      responseTime
    }

  } catch (error) {
    console.error('❌ [DB-HEALTH] Health check failed:', error)
    
    const errorInfo = handleError(error as Error, {
      component: 'database',
      operation: 'checkDatabaseHealth'
    })

    errors.push(getUserFriendlyMessage(errorInfo))

    return {
      isHealthy: false,
      isConnected: false,
      connectionCount: 0,
      lastChecked: new Date(),
      errors,
      warnings,
      responseTime: Date.now() - startTime
    }
  }
}

/**
 * Initialize database with health checks
 */
export async function initializeDatabase(): Promise<DatabaseInitializationResult> {
  try {
    console.log('🚀 [DB-INIT] Starting database initialization...')

    // Perform initial health check
    const healthStatus = await checkDatabaseHealth()

    if (!healthStatus.isHealthy) {
      console.error('❌ [DB-INIT] Database health check failed')
      return {
        success: false,
        healthStatus,
        error: healthStatus.errors.join('; ')
      }
    }

    console.log('✅ [DB-INIT] Database initialization completed successfully')

    return {
      success: true,
      healthStatus
    }

  } catch (error) {
    console.error('❌ [DB-INIT] Database initialization failed:', error)
    
    const errorInfo = handleError(error as Error, {
      component: 'database',
      operation: 'initializeDatabase'
    })

    // Create a failed health status
    const failedHealthStatus: DatabaseHealthStatus = {
      isHealthy: false,
      isConnected: false,
      connectionCount: 0,
      lastChecked: new Date(),
      errors: [getUserFriendlyMessage(errorInfo)],
      warnings: []
    }

    return {
      success: false,
      healthStatus: failedHealthStatus,
      error: getUserFriendlyMessage(errorInfo)
    }
  }
}

/**
 * Periodic health check function
 */
export function startPeriodicHealthCheck(intervalMs: number = 60000): () => void {
  console.log(`🔄 [DB-HEALTH] Starting periodic health checks every ${intervalMs}ms`)
  
  const intervalId = setInterval(async () => {
    try {
      const health = await checkDatabaseHealth()
      
      if (!health.isHealthy) {
        console.warn('⚠️ [DB-HEALTH] Periodic health check detected issues:', health.errors)
      } else {
        console.log(`✅ [DB-HEALTH] Periodic health check passed (${health.responseTime}ms)`)
      }
    } catch (error) {
      console.error('❌ [DB-HEALTH] Periodic health check failed:', error)
    }
  }, intervalMs)

  // Return cleanup function
  return () => {
    console.log('🛑 [DB-HEALTH] Stopping periodic health checks')
    clearInterval(intervalId)
  }
}

/**
 * Get database status for monitoring/debugging
 */
export async function getDatabaseStatus(): Promise<{
  service: {
    isReady: boolean
    poolStatus: any
  }
  health: DatabaseHealthStatus
}> {
  const health = await checkDatabaseHealth()
  
  return {
    service: {
      isReady: databaseService.isReady(),
      poolStatus: databaseService.getPoolStatus()
    },
    health
  }
}

export default {
  checkDatabaseHealth,
  initializeDatabase,
  startPeriodicHealthCheck,
  getDatabaseStatus
}

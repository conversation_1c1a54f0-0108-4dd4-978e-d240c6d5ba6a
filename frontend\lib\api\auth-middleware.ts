/**
 * Unified Authentication Middleware
 * 
 * Provides consistent authentication and authorization for API routes
 */

import { NextRequest } from 'next/server'
import { cookies } from 'next/headers'
import { jwtDecode } from 'jwt-decode'
import { createErrorResponse, ApiErrorCode, HttpStatus } from './response'

export interface AuthSession {
  userId: string
  email: string
  name?: string
  given_name?: string
  family_name?: string
  groups: string[]
  exp: number
  iat: number
}

export interface AuthMiddlewareOptions {
  requireAuth?: boolean
  requiredGroups?: string[]
  allowExpired?: boolean
}

export interface AuthResult {
  success: boolean
  session?: AuthSession
  response?: Response
}

/**
 * Unified authentication middleware for API routes
 */
export async function authenticateRequest(
  request: NextRequest,
  options: AuthMiddlewareOptions = {}
): Promise<AuthResult> {
  const {
    requireAuth = true,
    requiredGroups = [],
    allowExpired = false
  } = options

  try {
    // Get token from cookie
    const cookieStore = cookies()
    const idToken = cookieStore.get('idToken')?.value

    if (!idToken) {
      if (!requireAuth) {
        return { success: true }
      }
      
      return {
        success: false,
        response: createErrorResponse(
          'Authentication required',
          ApiErrorCode.UNAUTHORIZED,
          HttpStatus.UNAUTHORIZED
        )
      }
    }

    // Decode and validate JWT
    let decodedToken: any
    try {
      decodedToken = jwtDecode(idToken)
    } catch (error) {
      return {
        success: false,
        response: createErrorResponse(
          'Invalid token format',
          ApiErrorCode.UNAUTHORIZED,
          HttpStatus.UNAUTHORIZED
        )
      }
    }

    // Check token expiration
    const now = Math.floor(Date.now() / 1000)
    if (!allowExpired && decodedToken.exp < now) {
      return {
        success: false,
        response: createErrorResponse(
          'Token expired',
          ApiErrorCode.UNAUTHORIZED,
          HttpStatus.UNAUTHORIZED
        )
      }
    }

    // Validate required token fields
    if (!decodedToken.sub && !decodedToken['cognito:username']) {
      return {
        success: false,
        response: createErrorResponse(
          'Invalid token: missing user identifier',
          ApiErrorCode.UNAUTHORIZED,
          HttpStatus.UNAUTHORIZED
        )
      }
    }

    if (!decodedToken.email) {
      return {
        success: false,
        response: createErrorResponse(
          'Invalid token: missing email',
          ApiErrorCode.UNAUTHORIZED,
          HttpStatus.UNAUTHORIZED
        )
      }
    }

    // Extract session information
    const session: AuthSession = {
      userId: decodedToken.sub || decodedToken['cognito:username'],
      email: decodedToken.email,
      name: decodedToken.name,
      given_name: decodedToken.given_name,
      family_name: decodedToken.family_name,
      groups: decodedToken['cognito:groups'] || [],
      exp: decodedToken.exp,
      iat: decodedToken.iat
    }

    // Check required groups
    if (requiredGroups.length > 0) {
      const userGroups = session.groups.map(g => g.toLowerCase())
      const hasRequiredGroup = requiredGroups.some(group => 
        userGroups.includes(group.toLowerCase())
      )

      if (!hasRequiredGroup) {
        return {
          success: false,
          response: createErrorResponse(
            'Insufficient permissions',
            ApiErrorCode.FORBIDDEN,
            HttpStatus.FORBIDDEN
          )
        }
      }
    }

    return {
      success: true,
      session
    }

  } catch (error) {
    console.error('Authentication middleware error:', error)
    return {
      success: false,
      response: createErrorResponse(
        'Authentication failed',
        ApiErrorCode.INTERNAL_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }
  }
}

/**
 * Higher-order function for protecting API routes
 */
export function withAuth(
  handler: (request: NextRequest, session: AuthSession) => Promise<Response>,
  options: AuthMiddlewareOptions = {}
) {
  return async (request: NextRequest) => {
    const authResult = await authenticateRequest(request, options)
    
    if (!authResult.success) {
      return authResult.response!
    }

    return handler(request, authResult.session!)
  }
}

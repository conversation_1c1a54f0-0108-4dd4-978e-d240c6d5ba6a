/**
 * Reports Page
 * 
 * Main reports dashboard showing various analytics and data visualizations
 */

'use client'

import React, { useState, useEffect } from 'react'
import { PageHeader } from '@/components/ui/PageHeader'
import { ReportsFilters } from '@/components/reports/ReportsFilters'
import { ReportsTable } from '@/components/reports/ReportsTable'
import { ReportsVisualization } from '@/components/reports/ReportsVisualization'
import { LoadingPage } from '@/components/common/LoadingStates'
import { ErrorBoundary } from '@/components/common/ErrorBoundary'
import { useData } from '@/lib/hooks/useData'

interface ReportsPageData {
  reports: any[]
  analytics: any
  filters: any[]
}

export default function ReportsPage() {
  const [activeTab, setActiveTab] = useState<'table' | 'charts'>('table')
  const [filters, setFilters] = useState({})

  const { data, loading, error, refetch } = useData<ReportsPageData>('/api/reports', {
    params: filters
  })

  if (loading) {
    return <LoadingPage title="Loading Reports..." />
  }

  if (error) {
    return (
      <div className="p-6">
        <PageHeader 
          title="Reports"
          subtitle="Analytics and data insights"
        />
        <div className="mt-6 text-center">
          <p className="text-red-600 mb-4">Failed to load reports data</p>
          <button 
            onClick={() => refetch()}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  return (
    <ErrorBoundary level="page">
      <div className="space-y-6">
        <PageHeader 
          title="Reports"
          subtitle="Analytics and data insights"
        />

        <ReportsFilters 
          onFiltersChange={setFilters}
          availableFilters={data?.filters || []}
        />

        <div className="bg-white rounded-lg shadow">
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex">
              <button
                onClick={() => setActiveTab('table')}
                className={`py-2 px-4 border-b-2 font-medium text-sm ${
                  activeTab === 'table'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Data Table
              </button>
              <button
                onClick={() => setActiveTab('charts')}
                className={`py-2 px-4 border-b-2 font-medium text-sm ${
                  activeTab === 'charts'
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                Visualizations
              </button>
            </nav>
          </div>

          <div className="p-6">
            {activeTab === 'table' ? (
              <ReportsTable 
                data={data?.reports || []}
                onRefresh={refetch}
              />
            ) : (
              <ReportsVisualization 
                data={data?.analytics || {}}
                onRefresh={refetch}
              />
            )}
          </div>
        </div>
      </div>
    </ErrorBoundary>
  )
}

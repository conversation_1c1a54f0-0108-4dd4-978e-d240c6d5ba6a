/**
 * Vendor Spend Chart Component
 * 
 * Displays vendor spending distribution in various chart formats
 */

'use client'

import React, { useMemo } from 'react'
import { BaseComponentProps } from '@/lib/types'

interface VendorSpendData {
  vendor: string
  totalSpend: number
  renewalCount: number
  avgSpend: number
  currency?: string
}

interface VendorSpendChartProps extends BaseComponentProps {
  data: VendorSpendData[]
  chartType?: 'bar' | 'pie' | 'donut'
  showTop?: number
  currency?: string
  onVendorClick?: (vendor: string) => void
}

export default function VendorSpendChart({
  data,
  chartType = 'bar',
  showTop = 10,
  currency = 'USD',
  onVendorClick,
  className = '',
  'data-testid': testId
}: VendorSpendChartProps) {
  // Process and sort data
  const chartData = useMemo(() => {
    if (!data.length) return []
    
    return data
      .sort((a, b) => b.totalSpend - a.totalSpend)
      .slice(0, showTop)
  }, [data, showTop])

  const totalSpend = useMemo(() => {
    return chartData.reduce((sum, item) => sum + item.totalSpend, 0)
  }, [chartData])

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  const colors = [
    'bg-blue-500', 'bg-green-500', 'bg-yellow-500', 'bg-red-500', 'bg-purple-500',
    'bg-indigo-500', 'bg-pink-500', 'bg-gray-500', 'bg-orange-500', 'bg-teal-500'
  ]

  if (!chartData.length) {
    return (
      <div className={`bg-gray-50 border border-gray-200 rounded-lg p-8 text-center ${className}`} data-testid={testId}>
        <div className="text-4xl mb-2">📊</div>
        <p className="text-gray-600">No vendor spending data available</p>
        <p className="text-sm text-gray-500 mt-1">Add renewals with vendor information to see spending distribution</p>
      </div>
    )
  }

  return (
    <div className={`bg-white border rounded-lg p-6 ${className}`} data-testid={testId}>
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-lg font-semibold text-gray-900">
          Vendor Spending Distribution
        </h3>
        <div className="text-sm text-gray-600">
          Total: {formatCurrency(totalSpend)}
        </div>
      </div>

      {chartType === 'bar' && (
        <div className="space-y-3">
          {chartData.map((item, index) => {
            const percentage = totalSpend > 0 ? (item.totalSpend / totalSpend) * 100 : 0
            const maxBarWidth = Math.max(percentage, 5) // Minimum 5% width for visibility
            
            return (
              <div 
                key={item.vendor} 
                className={`group ${onVendorClick ? 'cursor-pointer hover:bg-gray-50' : ''} p-3 rounded-lg transition-colors`}
                onClick={() => onVendorClick?.(item.vendor)}
              >
                <div className="flex justify-between items-center mb-2">
                  <span className="font-medium text-gray-900 truncate" title={item.vendor}>
                    {item.vendor}
                  </span>
                  <div className="text-right">
                    <div className="font-semibold text-gray-900">
                      {formatCurrency(item.totalSpend)}
                    </div>
                    <div className="text-xs text-gray-500">
                      {item.renewalCount} renewal{item.renewalCount !== 1 ? 's' : ''}
                    </div>
                  </div>
                </div>
                
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className={`h-2 rounded-full ${colors[index % colors.length]} transition-all duration-300`}
                    style={{ width: `${maxBarWidth}%` }}
                  />
                </div>
                
                <div className="flex justify-between items-center mt-1 text-xs text-gray-500">
                  <span>{percentage.toFixed(1)}% of total</span>
                  <span>Avg: {formatCurrency(item.avgSpend)}</span>
                </div>
              </div>
            )
          })}
        </div>
      )}

      {(chartType === 'pie' || chartType === 'donut') && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Chart Legend */}
          <div className="space-y-2">
            {chartData.map((item, index) => {
              const percentage = totalSpend > 0 ? (item.totalSpend / totalSpend) * 100 : 0
              
              return (
                <div 
                  key={item.vendor}
                  className={`flex items-center gap-3 p-2 rounded-lg ${onVendorClick ? 'cursor-pointer hover:bg-gray-50' : ''} transition-colors`}
                  onClick={() => onVendorClick?.(item.vendor)}
                >
                  <div className={`w-4 h-4 rounded-full ${colors[index % colors.length]}`} />
                  <div className="flex-1 min-w-0">
                    <div className="font-medium text-sm truncate" title={item.vendor}>
                      {item.vendor}
                    </div>
                    <div className="text-xs text-gray-500">
                      {formatCurrency(item.totalSpend)} ({percentage.toFixed(1)}%)
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
          
          {/* Simple Pie Chart Representation */}
          <div className="flex items-center justify-center">
            <div className="text-center">
              <div className="text-6xl mb-2">📊</div>
              <div className="text-sm text-gray-600">
                {chartData.length} vendor{chartData.length !== 1 ? 's' : ''}
              </div>
              <div className="text-lg font-semibold text-gray-900">
                {formatCurrency(totalSpend)}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

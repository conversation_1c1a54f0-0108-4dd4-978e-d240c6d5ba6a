/**
 * Next.js Router Mock for Jest
 * 
 * Mocks Next.js router functionality for testing
 */

const mockRouter = {
  push: jest.fn(() => Promise.resolve()),
  replace: jest.fn(() => Promise.resolve()),
  prefetch: jest.fn(() => Promise.resolve()),
  back: jest.fn(),
  reload: jest.fn(),
  route: '/',
  pathname: '/',
  query: {},
  asPath: '/',
  basePath: '',
  isLocaleDomain: false,
  isReady: true,
  isPreview: false,
  isFallback: false,
  events: {
    on: jest.fn(),
    off: jest.fn(),
    emit: jest.fn(),
  },
}

export const useRouter = () => mockRouter
export const withRouter = (Component) => Component
export const Router = mockRouter
export default { useRouter, withRouter, Router }

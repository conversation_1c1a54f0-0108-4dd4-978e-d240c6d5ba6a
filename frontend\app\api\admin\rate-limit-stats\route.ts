/**
 * Rate Limit Statistics API Endpoint
 * 
 * Provides rate limiting statistics for monitoring and debugging
 */

import { createApiRoute } from '@/lib/api/route-factory'
import { advancedRateLimiter } from '@/lib/security/rate-limiting'

// GET /api/admin/rate-limit-stats - Get rate limiting statistics
export const GET = createApiRoute('GET', {
  requireAuth: true,
  requireRoles: ['admin'],
  handler: async (context) => {
    // Get rate limiting statistics
    const stats = advancedRateLimiter.getStats()

    return {
      stats,
      message: 'Rate limit statistics retrieved successfully'
    }
  }
})

import { NextRequest } from 'next/server';
import { withAuth } from '@/lib/api/auth-middleware';
import { getClientByEmailDomain } from '@/lib/tenant/clients';
import { fetchUserAttributes } from 'aws-amplify/auth';
import { TenantContext } from '@/lib/tenant/context';
import {
  createSuccessResponse,
  createErrorResponse,
  ApiErrorCode,
  HttpStatus
} from '@/lib/api/response';
import { executeQuery, schemaExists } from '@/lib/database';
import { Renewal } from '@/lib/types';

// GET /api/renewals - Get renewals for tenant
export const GET = withAuth(async (request: NextRequest, session) => {
  console.log(`🔍 GET /api/renewals - Starting request`);

  // Get user attributes for tenant context
  const userAttributes = await fetchUserAttributes();
  const userEmail = userAttributes.email;

  if (!userEmail) {
    return createErrorResponse(
      'User email not found',
      ApiErrorCode.UNAUTHORIZED,
      HttpStatus.UNAUTHORIZED
    );
  }

  const clientResult = await getClientByEmailDomain(userEmail);

  if (!clientResult.success) {
    return createErrorResponse(
      'Failed to fetch tenant information',
      ApiErrorCode.DATABASE_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }

  const tenant = clientResult.client!;

  // Check if tenant schema exists
  const schemaReady = await schemaExists(tenant.tenantSchema);

  if (!schemaReady) {
    console.log(`⚠️ Tenant schema ${tenant.tenantSchema} not ready yet`);
    return createSuccessResponse([], 'Tenant schema not ready, returning empty results');
  }

  try {
    // Query tenant schema for renewals data with joins to get type and currency names
    const renewalsQuery = `
      SELECT
        r.id,
        r.name,
        COALESCE(v.name, 'Unknown') as vendor,
        COALESCE(rt.name, 'Unknown') as type,
        r.expiry_date as start_date,
        COALESCE(r.status, 'A') as status,
        r.description,
        (SELECT COUNT(*) FROM "${tenant.tenantSchema}".tenant_alerts a WHERE a.renewal_id = r.id AND a.is_active = true) as alerts
      FROM "${tenant.tenantSchema}".tenant_renewals r
      LEFT JOIN "${tenant.tenantSchema}".tenant_vendors v ON r.vendor_id = v.id
      LEFT JOIN metadata.global_renewal_types rt ON r.renewal_type_id = rt.id
      WHERE r.is_deleted = false
      ORDER BY r.expiry_date ASC, r.name ASC
    `;

    const result = await executeQuery(renewalsQuery, [], { schema: tenant.tenantSchema });

    if (!result.success) {
      console.error(`❌ Failed to fetch renewals:`, result.error);
      return createErrorResponse(
        'Failed to fetch renewals data',
        ApiErrorCode.DATABASE_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }

    // Format the results
    const renewals: Renewal[] = (result.data || []).map((row: any) => ({
      id: row.id.toString(),
      name: row.name,
      product_name: row.product_name || '',
      version: row.version || '',
      vendor: row.vendor || '',
      type: row.type,
      start_date: row.start_date ? new Date(row.start_date).toISOString().split('T')[0] : '',
      cost: parseFloat(row.cost) || 0,
      currency: row.currency,
      status: row.status as 'active' | 'inactive' | 'pending' | 'expired',
      alerts: parseInt(row.alerts) || 0,
      license_count: parseInt(row.license_count) || 0,
      description: row.description || '',
      created_on: row.created_on ? new Date(row.created_on) : new Date(),
      changed_on: row.changed_on ? new Date(row.changed_on) : undefined
    }));

    console.log(`✅ Retrieved ${renewals.length} renewals for tenant ${tenant.clientName}`);

    // Return renewals array directly for compatibility with RenewalsContext
    return createSuccessResponse(renewals, 'Renewals retrieved successfully');

  } catch (error) {
    console.error(`❌ Error fetching renewals:`, error);
    return createErrorResponse(
      'Failed to fetch renewals data',
      ApiErrorCode.DATABASE_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }
}, {
  requireAuth: true
});

// POST /api/renewals - Get filter options from renewals data
export const POST = withAuth(async (request: NextRequest, session) => {
  console.log(`🔍 POST /api/renewals - Starting request`);

  // Get user attributes for tenant context
  const userAttributes = await fetchUserAttributes();
  const userEmail = userAttributes.email;

  if (!userEmail) {
    return createErrorResponse(
      'User email not found',
      ApiErrorCode.UNAUTHORIZED,
      HttpStatus.UNAUTHORIZED
    );
  }

  const clientResult = await getClientByEmailDomain(userEmail);

  if (!clientResult.success) {
    return createErrorResponse(
      'Failed to fetch tenant information',
      ApiErrorCode.DATABASE_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    );
  }

  const tenant = clientResult.client!;

  // Check if tenant schema exists
  const schemaReady = await schemaExists(tenant.tenantSchema);

  if (!schemaReady) {
    console.log(`⚠️ Tenant schema ${tenant.tenantSchema} not ready yet`);
    return createSuccessResponse({
      vendors: [],
      types: [],
      statuses: []
    }, 'Tenant schema not ready');
  }

    try {
    // Get unique filter values
    const filtersQuery = `
      SELECT
        ARRAY_AGG(DISTINCT v.name) FILTER (WHERE v.name IS NOT NULL) as vendors,
        ARRAY_AGG(DISTINCT rt.name) FILTER (WHERE rt.name IS NOT NULL) as types,
        ARRAY_AGG(DISTINCT r.status) FILTER (WHERE r.status IS NOT NULL) as statuses
      FROM "${tenant.tenantSchema}".tenant_renewals r
      LEFT JOIN "${tenant.tenantSchema}".tenant_vendors v ON r.vendor_id = v.id
      LEFT JOIN metadata.global_renewal_types rt ON r.renewal_type_id = rt.id
      WHERE r.is_deleted = false
    `;

    const result = await executeQuery(filtersQuery, [], { schema: tenant.tenantSchema });

    if (!result.success || !result.data || result.data.length === 0) {
      console.log(`⚠️ No filter data available for tenant ${tenant.clientName}`);
      return createSuccessResponse({
        vendors: [],
        types: [],
        statuses: []
      }, 'No filter data available');
    }

    const filterData = result.data[0];

    console.log(`✅ Retrieved filter options for tenant ${tenant.clientName}`);

    return createSuccessResponse({
      vendors: filterData.vendors || [],
      types: filterData.types || [],
      statuses: filterData.statuses || []
    }, 'Filter options retrieved successfully');

    } catch (error) {
      console.error(`❌ Error fetching filter options:`, error);
      return createErrorResponse(
        'Failed to fetch filter options',
        ApiErrorCode.DATABASE_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
}, {
  requireAuth: true
});

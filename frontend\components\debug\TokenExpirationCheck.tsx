/**
 * Token Expiration Check Component
 * 
 * Displays current token expiration settings and verifies they meet the 7-day requirement
 */

'use client';

import React, { useState, useEffect } from 'react';
import { fetchAuthSession } from 'aws-amplify/auth';
import { secureConfig } from '@/lib/security/secure-config';
import { SESSION } from '@/lib/constants/app-constants';

interface TokenInfo {
  accessToken?: {
    payload: any;
    exp: number;
    iat: number;
    timeToExpiry: number;
  };
  idToken?: {
    payload: any;
    exp: number;
    iat: number;
    timeToExpiry: number;
  };
  refreshToken?: {
    exp?: number;
    iat?: number;
    timeToExpiry?: number;
  };
}

interface ConfigInfo {
  tokenExpiration: number;
  refreshTokenExpiration: number;
  sessionCookieMaxAge: number;
}

export function TokenExpirationCheck() {
  const [tokenInfo, setTokenInfo] = useState<TokenInfo | null>(null);
  const [configInfo, setConfigInfo] = useState<ConfigInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    checkTokenExpiration();
  }, []);

  const checkTokenExpiration = async () => {
    try {
      setLoading(true);
      setError(null);

      // Get current session tokens
      const session = await fetchAuthSession();
      
      if (session?.tokens) {
        const now = Math.floor(Date.now() / 1000);
        const tokenData: TokenInfo = {};

        // Parse access token
        if (session.tokens.accessToken) {
          const accessPayload = JSON.parse(atob(session.tokens.accessToken.toString().split('.')[1]));
          tokenData.accessToken = {
            payload: accessPayload,
            exp: accessPayload.exp,
            iat: accessPayload.iat,
            timeToExpiry: accessPayload.exp - now
          };
        }

        // Parse ID token
        if (session.tokens.idToken) {
          const idPayload = JSON.parse(atob(session.tokens.idToken.toString().split('.')[1]));
          tokenData.idToken = {
            payload: idPayload,
            exp: idPayload.exp,
            iat: idPayload.iat,
            timeToExpiry: idPayload.exp - now
          };
        }

        setTokenInfo(tokenData);
      }

      // Get configuration settings
      try {
        const config = await secureConfig.getAuthConfig();
        const sessionMaxAge = parseInt(process.env.SESSION_COOKIE_MAX_AGE || SESSION.DEFAULT_MAX_AGE.toString());
        
        setConfigInfo({
          tokenExpiration: config.tokenExpiration,
          refreshTokenExpiration: config.refreshTokenExpiration,
          sessionCookieMaxAge: sessionMaxAge
        });
      } catch (configError) {
        console.warn('Could not load secure config:', configError);
        // Use fallback values
        setConfigInfo({
          tokenExpiration: 3600, // 1 hour
          refreshTokenExpiration: SESSION.DEFAULT_MAX_AGE, // 7 days
          sessionCookieMaxAge: parseInt(process.env.SESSION_COOKIE_MAX_AGE || SESSION.DEFAULT_MAX_AGE.toString())
        });
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  const formatDuration = (seconds: number): string => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    const parts = [];
    if (days > 0) parts.push(`${days}d`);
    if (hours > 0) parts.push(`${hours}h`);
    if (minutes > 0) parts.push(`${minutes}m`);
    
    return parts.join(' ') || `${seconds}s`;
  };

  const formatTimestamp = (timestamp: number): string => {
    return new Date(timestamp * 1000).toLocaleString();
  };

  const isSevenDays = (seconds: number): boolean => {
    return Math.abs(seconds - 604800) < 3600; // Within 1 hour of 7 days
  };

  if (loading) {
    return (
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-center">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
          <span className="text-sm text-blue-700">Checking token expiration...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <h3 className="text-sm font-semibold text-red-700 mb-2">Token Check Error</h3>
        <p className="text-sm text-red-600">{error}</p>
        <button 
          onClick={checkTokenExpiration}
          className="mt-2 text-sm text-red-700 underline hover:no-underline"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Configuration Settings */}
      {configInfo && (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h3 className="text-sm font-semibold text-gray-700 mb-3">Configuration Settings</h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Access Token Expiration:</span>
              <span className="font-mono">{formatDuration(configInfo.tokenExpiration)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Refresh Token Expiration:</span>
              <div className="flex items-center">
                <span className="font-mono mr-2">{formatDuration(configInfo.refreshTokenExpiration)}</span>
                {isSevenDays(configInfo.refreshTokenExpiration) ? (
                  <span className="text-green-600 text-xs">✅ 7 days</span>
                ) : (
                  <span className="text-orange-600 text-xs">⚠️ Not 7 days</span>
                )}
              </div>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Session Cookie Max Age:</span>
              <div className="flex items-center">
                <span className="font-mono mr-2">{formatDuration(configInfo.sessionCookieMaxAge)}</span>
                {isSevenDays(configInfo.sessionCookieMaxAge) ? (
                  <span className="text-green-600 text-xs">✅ 7 days</span>
                ) : (
                  <span className="text-orange-600 text-xs">⚠️ Not 7 days</span>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Current Token Information */}
      {tokenInfo && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="text-sm font-semibold text-blue-700 mb-3">Current Session Tokens</h3>
          
          {tokenInfo.accessToken && (
            <div className="mb-3">
              <h4 className="text-xs font-semibold text-blue-600 mb-1">Access Token</h4>
              <div className="text-xs space-y-1">
                <div className="flex justify-between">
                  <span>Issued:</span>
                  <span className="font-mono">{formatTimestamp(tokenInfo.accessToken.iat)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Expires:</span>
                  <span className="font-mono">{formatTimestamp(tokenInfo.accessToken.exp)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Time to Expiry:</span>
                  <span className={`font-mono ${tokenInfo.accessToken.timeToExpiry < 300 ? 'text-red-600' : 'text-blue-600'}`}>
                    {formatDuration(Math.max(0, tokenInfo.accessToken.timeToExpiry))}
                  </span>
                </div>
              </div>
            </div>
          )}

          {tokenInfo.idToken && (
            <div>
              <h4 className="text-xs font-semibold text-blue-600 mb-1">ID Token</h4>
              <div className="text-xs space-y-1">
                <div className="flex justify-between">
                  <span>Issued:</span>
                  <span className="font-mono">{formatTimestamp(tokenInfo.idToken.iat)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Expires:</span>
                  <span className="font-mono">{formatTimestamp(tokenInfo.idToken.exp)}</span>
                </div>
                <div className="flex justify-between">
                  <span>Time to Expiry:</span>
                  <span className={`font-mono ${tokenInfo.idToken.timeToExpiry < 300 ? 'text-red-600' : 'text-blue-600'}`}>
                    {formatDuration(Math.max(0, tokenInfo.idToken.timeToExpiry))}
                  </span>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Refresh Button */}
      <button 
        onClick={checkTokenExpiration}
        className="text-sm text-blue-600 underline hover:no-underline"
      >
        Refresh Token Information
      </button>
    </div>
  );
}

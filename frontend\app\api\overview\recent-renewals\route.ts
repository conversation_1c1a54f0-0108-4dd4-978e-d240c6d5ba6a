/**
 * Overview Recent Renewals API
 * 
 * Provides recent renewals data for the overview dashboard
 */

import { NextRequest } from 'next/server'
import { withAuth } from '@/lib/api/auth-middleware'
import { resolveTenantContext } from '@/lib/tenant/context'
import { executeQuery, schemaExists } from '@/lib/database'
import {
  createSuccessResponse,
  createErrorResponse,
  ApiErrorCode,
  HttpStatus
} from '@/lib/api/response'

export const GET = withAuth(async (request: NextRequest, session) => {
  console.log('[OVERVIEW-RECENT-RENEWALS-API] GET request received')

  try {
    // Resolve tenant context
    const tenant = await resolveTenantContext(session.email)
    if (!tenant) {
      console.error('[OVERVIEW-RECENT-RENEWALS-API] Failed to resolve tenant context')
      return createErrorResponse(
        'Tenant context not found',
        ApiErrorCode.TENANT_NOT_FOUND,
        HttpStatus.BAD_REQUEST
      )
    }

    console.log(`[OVERVIEW-RECENT-RENEWALS-API] Resolved tenant: ${tenant.tenantId}`)

    // Check if tenant schema exists
    const schemaExistsResult = await schemaExists(tenant.tenantSchema)
    if (!schemaExistsResult) {
      console.log(`[OVERVIEW-RECENT-RENEWALS-API] Tenant schema ${tenant.tenantSchema} not ready yet`)
      return createSuccessResponse([], 'Recent renewals retrieved successfully')
    }

    // Query recent renewals from tenant schema
    const query = `
      SELECT 
        id,
        product_name,
        vendor,
        cost,
        renewal_date,
        renewal_type,
        status,
        created_on
      FROM "${tenant.tenantSchema}".tenant_renewals
      WHERE vendor IS NOT NULL AND vendor != ''
      ORDER BY created_on DESC
      LIMIT 10
    `

    console.log(`[OVERVIEW-RECENT-RENEWALS-API] Executing query for schema: ${tenant.tenantSchema}`)
    const result = await executeQuery(query)

    if (!result.success) {
      console.error('[OVERVIEW-RECENT-RENEWALS-API] Database query failed:', result.error)
      return createErrorResponse(
        'Failed to fetch recent renewals',
        ApiErrorCode.INTERNAL_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }

    // Transform data
    const renewals = result.data.map((row: any) => ({
      id: row.id,
      product_name: row.product_name,
      vendor: row.vendor,
      cost: parseFloat(row.cost) || 0,
      renewal_date: row.renewal_date,
      renewal_type: row.renewal_type,
      status: row.status,
      created_on: row.created_on
    }))

    console.log(`[OVERVIEW-RECENT-RENEWALS-API] Returning ${renewals.length} recent renewals`)
    return createSuccessResponse(renewals, 'Recent renewals retrieved successfully')

  } catch (error) {
    console.error('[OVERVIEW-RECENT-RENEWALS-API] Error:', error)
    return createErrorResponse(
      'Failed to fetch recent renewals',
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    )
  }
}, {
  requireAuth: true
})

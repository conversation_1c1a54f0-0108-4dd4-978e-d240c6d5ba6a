/**
 * Jest Global Setup
 * 
 * Runs once before all tests start.
 * Used for global test environment setup.
 */

export default async () => {
  console.log('🧪 Setting up test environment...')
  
  // Set test environment variables
  process.env.NODE_ENV = 'test'
  process.env.TZ = 'UTC'
  
  // Mock AWS configuration
  process.env.AWS_REGION = 'ca-central-1'
  process.env.AWS_ACCESS_KEY_ID = 'test-access-key'
  process.env.AWS_SECRET_ACCESS_KEY = 'test-secret-key'
  
  // Mock database configuration
  process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test_renewtrack'
  process.env.DATABASE_HOST = 'localhost'
  process.env.DATABASE_PORT = '5432'
  process.env.DATABASE_NAME = 'test_renewtrack'
  process.env.DATABASE_USER = 'test'
  process.env.DATABASE_PASSWORD = 'test'
  
  // Mock JWT configuration
  process.env.JWT_SECRET = 'test-jwt-secret-key-for-testing-only'
  process.env.JWT_EXPIRES_IN = '1h'
  
  // Mock Cognito configuration
  process.env.NEXT_PUBLIC_USER_POOL_ID = 'ca-central-1_testpool'
  process.env.NEXT_PUBLIC_USER_POOL_CLIENT_ID = 'test-client-id'
  process.env.NEXT_PUBLIC_COGNITO_DOMAIN = 'test.auth.renewtrack.com'
  
  // Mock API configuration
  process.env.API_BASE_URL = 'http://localhost:3000/api'
  process.env.NEXT_PUBLIC_API_URL = 'http://localhost:3000/api'
  
  // Disable console warnings for tests
  const originalWarn = console.warn
  console.warn = (...args) => {
    if (
      typeof args[0] === 'string' &&
      (args[0].includes('Warning:') || 
       args[0].includes('deprecated') ||
       args[0].includes('componentWillReceiveProps'))
    ) {
      return
    }
    originalWarn.call(console, ...args)
  }
  
  console.log('✅ Test environment setup complete')
}

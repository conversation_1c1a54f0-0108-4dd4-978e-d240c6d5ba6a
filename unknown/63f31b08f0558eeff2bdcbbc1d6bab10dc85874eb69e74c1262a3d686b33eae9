/**
 * Jest Global Teardown
 * 
 * Runs once after all tests complete.
 * Used for global test environment cleanup.
 */

export default async () => {
  console.log('🧹 Cleaning up test environment...')
  
  // Clean up any global resources
  // This could include closing database connections,
  // cleaning up temporary files, etc.
  
  // Reset environment variables
  delete process.env.NODE_ENV
  delete process.env.TZ
  delete process.env.AWS_REGION
  delete process.env.AWS_ACCESS_KEY_ID
  delete process.env.AWS_SECRET_ACCESS_KEY
  delete process.env.DATABASE_URL
  delete process.env.JWT_SECRET
  
  console.log('✅ Test environment cleanup complete')
}

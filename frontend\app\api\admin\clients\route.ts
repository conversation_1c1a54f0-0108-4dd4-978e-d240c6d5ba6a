/**
 * Clients API
 * 
 * Endpoints for managing clients
 */

import { NextRequest, NextResponse } from 'next/server'
import { databaseService } from '@/lib/services/database-service'
import { requireSuperAdmin } from '@/lib/api/auth-middleware'
import { createSuccessResponse, createErrorResponse, ApiErrorCode, HttpStatus } from '@/lib/api/response'
import { STATUS } from '@/lib/constants/app-constants'

/**
 * GET /api/admin/clients
 * Get all clients
 */
export async function GET(request: NextRequest) {
  try {
    // Require super admin authentication
    const authResult = await requireSuperAdmin(request)
    if (!authResult.success) {
      return authResult.response
    }

    const query = `
      SELECT 
        client_id,
        name,
        email_domain,
        status,
        created_on
      FROM metadata.clients
      WHERE status = '${STATUS.ACTIVE}'
      ORDER BY name ASC
    `

    const result = await databaseService.query(query)

    return createSuccessResponse(result.rows)

  } catch (error) {
    console.error('Error fetching clients:', error)
    return createErrorResponse('Failed to fetch clients', ApiErrorCode.INTERNAL_ERROR, HttpStatus.INTERNAL_SERVER_ERROR)
  }
}

# Naming Conventions Guide

## 📋 **Standardized Naming Patterns**

This document establishes consistent naming conventions across the RenewTrack codebase to improve maintainability and developer experience.

## 🗂️ **File and Directory Naming**

### **Frontend Files**

- **Components**: `PascalCase.tsx` (e.g., `AddRenewalModal.tsx`)
- **Pages**: `kebab-case/page.tsx` (e.g., `renewal-details/page.tsx`)
- **API Routes**: `kebab-case/route.ts` (e.g., `tenant-renewals/route.ts`)
- **Utilities**: `kebab-case.ts` (e.g., `tenant-context.ts`)
- **Types**: `kebab-case.ts` (e.g., `api-types.ts`)

### **Backend Files**

- **Services**: `kebab-case.ts` (e.g., `vendor-matcher.ts`)
- **Models**: `PascalCase.ts` (e.g., `TenantModel.ts`)
- **Utilities**: `kebab-case.ts` (e.g., `database-utils.ts`)

### **Database Files**

- **Migration Scripts**: `snake_case.sql` (e.g., `tenant_schema_standardized.sql`)
- **Setup Scripts**: `snake_case.sql` (e.g., `setup_new_tenant.sql`)

## 🔗 **API Endpoint Naming**

### **REST Endpoints**

```
GET    /api/tenant-renewals          # List renewals
POST   /api/tenant-renewals          # Create renewal
GET    /api/tenant-renewals/[id]     # Get specific renewal
PUT    /api/tenant-renewals/[id]     # Update renewal
DELETE /api/tenant-renewals/[id]     # Delete renewal
```

### **Query Parameters**

- Use `camelCase`: `?startDate=2024-01-01&endDate=2024-12-31`
- Boolean flags: `?includeArchived=true`
- Pagination: `?page=1&limit=20`

### **Response Fields**

- Use `camelCase` for JSON responses
- Consistent field names across endpoints
- Use descriptive names: `start_date` not `date`

## 🗄️ **Database Naming**

### **Schema Names**

- **Global schemas**: `snake_case` (e.g., `metadata`, `tenant_management`)
- **Tenant schemas**: `tenant_[id]` (e.g., `tenant_0000000000000001`)

### **Table Names**

- **Global tables**: `snake_case` (e.g., `global_currencies`, `global_renewal_types`)
- **Tenant tables**: `tenant_[entity]` (e.g., `tenant_renewals`, `tenant_vendors`)

### **Column Names**

- Use `snake_case` for all database columns
- Primary keys: `[table]_id` (e.g., `renewal_id`, `vendor_id`)
- Foreign keys: `[referenced_table]_id` (e.g., `client_id`, `vendor_id`)
- Timestamps: `created_on`, `changed_on`
- Status fields: `is_active`, `is_deleted`

## 💻 **Code Naming**

### **Variables and Functions**

- **JavaScript/TypeScript**: `camelCase`
- **Constants**: `UPPER_SNAKE_CASE`
- **Private methods**: `_camelCase` (with underscore prefix)

### **React Components**

- **Component names**: `PascalCase`
- **Props interfaces**: `[ComponentName]Props`
- **Hook names**: `use[DescriptiveName]`

### **Types and Interfaces**

- **Interfaces**: `PascalCase` (e.g., `RenewalFormData`)
- **Type aliases**: `PascalCase` (e.g., `ApiResponse`)
- **Enums**: `PascalCase` with `UPPER_CASE` values

## 🔧 **Configuration and Environment**

### **Environment Variables**

- Use `UPPER_SNAKE_CASE`
- Group by purpose: `DB_HOST`, `AUTH_SECRET`, `API_BASE_URL`

### **Configuration Keys**

- Use `camelCase` in JavaScript objects
- Use `snake_case` in YAML/JSON config files

## 📦 **Package and Module Naming**

### **NPM Packages**

- Use `kebab-case` for package names
- Scope packages: `@renewtrack/[package-name]`

### **Import Aliases**

- Use `@/` for root imports
- Descriptive aliases: `@/components`, `@/lib`, `@/types`

## 🎯 **Consistency Rules**

### **DO**

✅ Use consistent naming within the same context
✅ Choose descriptive names over short abbreviations
✅ Follow established patterns in the codebase
✅ Use standard conventions for the technology stack

### **DON'T**

❌ Mix naming conventions within the same file
❌ Use abbreviations unless they're widely understood
❌ Create new patterns when existing ones work
❌ Use misleading or ambiguous names

## 🔍 **Examples**

### **Good Examples**

```typescript
// Component
interface AddRenewalModalProps {
  isOpen: boolean;
  onClose: () => void;
}

// API Response
interface RenewalApiResponse {
  renewalId: string;
  start_date: string;
  vendorName: string;
}

// Database Query
const query = `
  SELECT renewal_id, start_date, vendor_name
  FROM tenant_renewals
  WHERE is_active = true
`;
```

### **Bad Examples**

```typescript
// Mixed conventions
interface addRenewalModal_Props {
  IsOpen: boolean;
  on_close: () => void;
}

// Inconsistent response
interface renewal_api_response {
  RenewalID: string;
  start_date: string;
  VendorName: string;
}
```

## 🚀 **Migration Strategy**

When updating existing code to follow these conventions:

1. **Prioritize**: Focus on public APIs and interfaces first
2. **Batch changes**: Group related naming updates together
3. **Test thoroughly**: Ensure all references are updated
4. **Document changes**: Update related documentation
5. **Gradual adoption**: Don't break existing functionality

---

_This guide should be followed for all new code and referenced when refactoring existing code._

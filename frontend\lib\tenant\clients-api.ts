/**
 * Client-Safe API Functions
 * 
 * Browser-safe versions of client/tenant functions that use API routes
 * instead of direct database access to avoid Node.js module issues.
 */

import { TenantContext } from '@/lib/types';

// Client lookup result interface
export interface ClientLookupResult {
  success: boolean;
  client?: TenantContext;
  error?: string;
  errorCode?: 'NOT_FOUND' | 'MULTIPLE_FOUND' | 'DATABASE_ERROR' | 'INVALID_INPUT';
}

/**
 * Get tenant by user's email domain (server-side version)
 */
export async function getTenantByUserId(userId: string, userEmail?: string): Promise<TenantContext | null> {
  try {
    if (!userEmail) {
      console.error('User email is required to fetch tenant');
      return null;
    }

    console.log(`🔍 [TENANT-API] Getting tenant for user: ${userId}, email: ${userEmail}`);

    // Use the server-side function directly instead of making a fetch call
    const { getClientByEmailDomain } = await import('@/lib/tenant/clients');
    const result = await getClientByEmailDomain(userEmail);

    if (!result.success) {
      console.error(`❌ [TENANT-API] Failed to get tenant: ${result.error}`);
      return null;
    }

    console.log(`✅ [TENANT-API] Found tenant: ${result.client!.clientName}`);
    return result.client!;
  } catch (error) {
    console.error('❌ [TENANT-API] Error getting tenant by user ID:', error);
    return null;
  }
}

/**
 * Get tenant by domain (client-safe version)
 */
export async function getTenantByDomain(domain: string): Promise<TenantContext | null> {
  try {
    const response = await fetch(`/api/tenants/by-domain/${encodeURIComponent(domain)}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      console.error('Failed to fetch tenant by domain:', response.statusText);
      return null;
    }

    const data = await response.json();
    return data.tenant || null;
  } catch (error) {
    console.error('Error getting tenant by domain:', error);
    return null;
  }
}

/**
 * Get client by email domain (client-safe version)
 */
export async function getClientByEmailDomain(email: string): Promise<ClientLookupResult> {
  if (!email || typeof email !== 'string') {
    return {
      success: false,
      error: 'Invalid email provided',
      errorCode: 'INVALID_INPUT'
    };
  }

  try {
    const response = await fetch('/api/clients/by-email', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email }),
    });

    if (!response.ok) {
      return {
        success: false,
        error: 'An error occurred, contact support',
        errorCode: 'DATABASE_ERROR'
      };
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error getting client by email domain:', error);
    return {
      success: false,
      error: 'An error occurred, contact support',
      errorCode: 'DATABASE_ERROR'
    };
  }
}

/**
 * Get client by domain (client-safe version)
 */
export async function getClientByDomain(domain: string): Promise<any> {
  if (!domain) {
    throw new Error('Domain parameter is required');
  }

  try {
    const response = await fetch(`/api/clients/by-domain/${encodeURIComponent(domain)}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to retrieve client information');
    }

    const data = await response.json();
    return data.client || null;
  } catch (error) {
    console.error('Error fetching client by domain:', error);
    throw new Error('Failed to retrieve client information');
  }
}

/**
 * Get tenant by ID (client-safe version)
 */
export async function getTenantById(tenantId: string): Promise<TenantContext | null> {
  try {
    const response = await fetch(`/api/tenants/${tenantId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      console.error('Failed to fetch tenant by ID:', response.statusText);
      return null;
    }

    const data = await response.json();
    return data.tenant || null;
  } catch (error) {
    console.error('Error getting tenant by ID:', error);
    return null;
  }
}

// Export types (avoid conflicts with clients.ts)
export type { ClientLookupResult as ClientApiLookupResult };

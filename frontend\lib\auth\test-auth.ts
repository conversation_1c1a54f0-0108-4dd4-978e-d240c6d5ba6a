/**
 * Authentication Test Utility
 * 
 * Simple utility to test authentication configuration
 */

export async function testAuthConfiguration() {
  try {
    console.log('[AUTH-TEST] Starting authentication configuration test...')
    
    // Test 1: Amplify Configuration
    const { ensureAmplifyConfigured } = await import('@/lib/services/amplify-service')
    await ensureAmplifyConfigured()
    console.log('[AUTH-TEST] ✅ Amplify configuration successful')
    
    // Test 2: Environment Variables
    const requiredEnvVars = [
      'NEXT_PUBLIC_AWS_REGION',
      'NEXT_PUBLIC_AWS_USER_POOLS_ID',
      'NEXT_PUBLIC_AWS_USER_POOLS_WEB_CLIENT_ID',
      'NEXT_PUBLIC_AWS_COGNITO_DOMAIN',
      'NEXT_PUBLIC_REDIRECT_SIGN_IN',
      'NEXT_PUBLIC_REDIRECT_SIGN_OUT'
    ]
    
    const missingVars = requiredEnvVars.filter(varName => !process.env[varName])
    if (missingVars.length > 0) {
      console.error('[AUTH-TEST] ❌ Missing environment variables:', missingVars)
      return false
    }
    console.log('[AUTH-TEST] ✅ All required environment variables present')
    
    // Test 3: Auth Session Check
    try {
      const { fetchAuthSession } = await import('aws-amplify/auth')
      const session = await fetchAuthSession()
      console.log('[AUTH-TEST] ✅ Auth session check successful:', {
        hasTokens: !!session.tokens,
        hasIdToken: !!session.tokens?.idToken,
        hasAccessToken: !!session.tokens?.accessToken
      })
    } catch (sessionError) {
      console.log('[AUTH-TEST] ℹ️ No active session (expected for unauthenticated users)')
    }
    
    // Test 4: Current User Check
    try {
      const { getCurrentUser } = await import('aws-amplify/auth')
      const user = await getCurrentUser()
      console.log('[AUTH-TEST] ✅ Current user found:', {
        userId: user.userId,
        username: user.username
      })
      return true
    } catch (userError) {
      console.log('[AUTH-TEST] ℹ️ No current user (expected for unauthenticated users)')
    }
    
    console.log('[AUTH-TEST] ✅ Authentication configuration test completed successfully')
    return true
    
  } catch (error) {
    console.error('[AUTH-TEST] ❌ Authentication configuration test failed:', error)
    return false
  }
}

// Auto-run test in development
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  // Run test after a short delay to ensure everything is loaded
  setTimeout(() => {
    testAuthConfiguration()
  }, 1000)
}

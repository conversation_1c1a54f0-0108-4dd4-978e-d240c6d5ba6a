/**
 * Authenticated Layout Component
 * 
 * Reusable layout component that handles authentication logic and loading states.
 * Eliminates redundancy between different layout components.
 */

'use client'

import { useEffect, useState, ReactNode } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/lib/hooks/useAuth'
import { LicenseValidator } from '@/components/license/LicenseValidator'
import { getLoginUrl } from '@/lib/auth'
import Sidebar from './Sidebar'
import { LoadingPage } from '@/components/common/LoadingStates'

interface AuthenticatedLayoutProps {
  children: ReactNode
  requireAuth?: boolean
  requireAdmin?: boolean
  redirectTo?: string
  loadingMessage?: string
  className?: string
  sidebarComponent?: ReactNode
  showSidebar?: boolean
}

// Removed custom LoadingState - using unified LoadingPage component

export default function AuthenticatedLayout({
  children,
  requireAuth = true,
  requireAdmin = false,
  redirectTo = '/login',
  loadingMessage = 'Loading...',
  className = '',
  sidebarComponent,
  showSidebar = true
}: AuthenticatedLayoutProps) {
  const { isAuthenticated, isLoading, user } = useAuth()
  const router = useRouter()
  const [isRedirecting, setIsRedirecting] = useState(false)
  const [hasInitialized, setHasInitialized] = useState(false)

  // Check if user has admin privileges
  const isAdmin = user?.groups?.includes('admin') || user?.groups?.includes('super-admin') || false

  useEffect(() => {
    // Mark as initialized once auth check is complete
    if (!isLoading && !hasInitialized) {
      setHasInitialized(true)
    }

    // Handle OAuth callback - give extra time for processing
    const urlParams = new URLSearchParams(window.location.search)
    const hasOAuthCode = urlParams.has('code')
    
    if (hasOAuthCode && !hasInitialized) {
      console.log('🔄 [AUTH-LAYOUT] OAuth callback detected, waiting for processing...')
      return
    }

    // Only redirect if auth check is complete and requirements aren't met
    if (!isLoading && !isRedirecting && hasInitialized) {
      if (requireAuth && !isAuthenticated) {
        setIsRedirecting(true)
        window.location.href = getLoginUrl()
      } else if (requireAdmin && !isAdmin) {
        setIsRedirecting(true)
        router.push('/')
      } else if (isAuthenticated && (requireAdmin ? isAdmin : true)) {
        // Clean up OAuth parameters from URL
        if (hasOAuthCode) {
          const cleanUrl = window.location.pathname
          window.history.replaceState({}, document.title, cleanUrl)
        }
      }
    }
  }, [isLoading, isAuthenticated, isAdmin, router, isRedirecting, hasInitialized, requireAuth, requireAdmin, redirectTo])

  // Show loading state during initial auth check or when redirecting
  if ((isLoading && !hasInitialized) || isRedirecting) {
    return (
      <LoadingPage
        title={isRedirecting ? 'Redirecting...' : loadingMessage}
        subtitle={isRedirecting ? 'Please wait...' : 'Checking authentication...'}
      />
    )
  }

  // Don't render if auth requirements aren't met (will redirect via useEffect)
  if (requireAuth && !isAuthenticated && hasInitialized) {
    return null
  }

  if (requireAdmin && !isAdmin && hasInitialized) {
    return null
  }

  // Render the authenticated layout
  const layoutClasses = `flex h-screen ${className}`.trim()
  const sidebarToRender = sidebarComponent || <Sidebar />

  return (
    <LicenseValidator required={!requireAdmin} exemptForAdmins={true}>
      <div className={layoutClasses}>
        {showSidebar && sidebarToRender}
        <main className="flex-1 overflow-auto">
          {children}
        </main>
      </div>
    </LicenseValidator>
  )
}

// Convenience components for common use cases
export function UserLayout({ children, ...props }: Omit<AuthenticatedLayoutProps, 'requireAuth'>) {
  return (
    <AuthenticatedLayout requireAuth={true} {...props}>
      {children}
    </AuthenticatedLayout>
  )
}

export function AdminLayout({ children, ...props }: Omit<AuthenticatedLayoutProps, 'requireAuth' | 'requireAdmin'>) {
  return (
    <AuthenticatedLayout requireAuth={true} requireAdmin={true} {...props}>
      {children}
    </AuthenticatedLayout>
  )
}

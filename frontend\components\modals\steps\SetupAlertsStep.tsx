/**
 * Setup Alerts Step Component (Refactored with Design System)
 *
 * Second step of the Add Renewal modal - configures renewal alerts.
 * Now uses the unified Form and Button components for consistency.
 */

'use client'

import React, { useCallback } from 'react'
import { AlertFormData } from '@/components/modals/AddRenewalModal'
import { Form } from '@/components/ui/Form'
import { Button } from '@/components/ui/Button'

interface SetupAlertsStepProps {
  data: AlertFormData[]
  onChange: (data: AlertFormData[]) => void
}

const SetupAlertsStep: React.FC<SetupAlertsStepProps> = ({
  data,
  onChange
}) => {
  // Handle alert field changes
  const handleAlertChange = useCallback((index: number, field: keyof AlertFormData, value: any) => {
    const updatedAlerts = [...data]
    updatedAlerts[index] = {
      ...updatedAlerts[index],
      [field]: value
    }
    onChange(updatedAlerts)
  }, [data, onChange])

  // Handle email recipients change
  const handleEmailRecipientsChange = useCallback((index: number, emailsString: string) => {
    const emails = emailsString.split(',').map(email => email.trim()).filter(email => email)
    handleAlertChange(index, 'emailRecipients', emails)
  }, [handleAlertChange])

  // Add new alert
  const handleAddAlert = useCallback(() => {
    const newAlert: AlertFormData = {
      daysBeforeRenewal: 30,
      emailRecipients: [],
      customMessage: '',
      enabled: true
    }
    onChange([...data, newAlert])
  }, [data, onChange])

  // Remove alert
  const handleRemoveAlert = useCallback((index: number) => {
    if (data.length > 1) {
      const updatedAlerts = data.filter((_, i) => i !== index)
      onChange(updatedAlerts)
    }
  }, [data, onChange])

  return (
    <div className="setup-alerts-step">
      {data.map((alert, index) => (
        <div key={index} className="alert-config">
          {/* Alert Header */}
          <div className="alert-header">
            <div className="alert-icon">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/>
                <line x1="12" y1="9" x2="12" y2="13"/>
                <line x1="12" y1="17" x2="12.01" y2="17"/>
              </svg>
            </div>
            <div className="alert-title">
              <h3>Set Up Renewal Alert</h3>
              <p>Create an alert to be notified before the Start Date for test.</p>
            </div>
            {data.length > 1 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleRemoveAlert(index)}
                aria-label="Remove alert"
                style={{
                  position: 'absolute',
                  top: '0',
                  right: '0',
                  padding: '4px',
                  minWidth: 'auto',
                  height: 'auto'
                }}
              >
                ×
              </Button>
            )}
          </div>

          {/* Days Before Renewal */}
          <Form.Field>
            <Form.Label htmlFor={`daysBeforeRenewal-${index}`}>
              Days Before Renewal
            </Form.Label>
            <Form.Input
              id={`daysBeforeRenewal-${index}`}
              type="number"
              min="1"
              max="365"
              value={alert.daysBeforeRenewal}
              onChange={(e) => handleAlertChange(index, 'daysBeforeRenewal', parseInt(e.target.value) || 30)}
            />
            <Form.Help>
              How many days before the Start Date to receive alerts
            </Form.Help>
          </Form.Field>

          {/* Email Recipients */}
          <Form.Field>
            <Form.Label htmlFor={`emailRecipients-${index}`}>
              Email Recipients
            </Form.Label>
            <Form.Textarea
              id={`emailRecipients-${index}`}
              placeholder="Enter email addresses separated by commas"
              rows={3}
              value={alert.emailRecipients.join(', ')}
              onChange={(e) => handleEmailRecipientsChange(index, e.target.value)}
            />
            <Form.Help>
              Email addresses that should receive alerts (leave empty to use the associated emails)
            </Form.Help>
          </Form.Field>

          {/* Custom Message */}
          <Form.Field>
            <Form.Label htmlFor={`customMessage-${index}`}>
              Custom Message (Optional)
            </Form.Label>
            <Form.Textarea
              id={`customMessage-${index}`}
              placeholder="Add a custom message to include in the alert"
              rows={4}
              value={alert.customMessage}
              onChange={(e) => handleAlertChange(index, 'customMessage', e.target.value)}
            />
          </Form.Field>

          {/* Enable Alert Checkbox */}
          <Form.Field>
            <label style={{
              display: 'flex',
              alignItems: 'flex-start',
              gap: '12px',
              cursor: 'pointer'
            }}>
              <input
                type="checkbox"
                checked={alert.enabled}
                onChange={(e) => handleAlertChange(index, 'enabled', e.target.checked)}
                style={{
                  width: '18px',
                  height: '18px',
                  marginTop: '2px'
                }}
              />
              <div>
                <strong style={{ display: 'block', marginBottom: '4px' }}>
                  Enable this alert
                </strong>
                <span style={{
                  fontSize: '12px',
                  color: 'var(--color-text-secondary)'
                }}>
                  Uncheck to create the alert but keep it disabled
                </span>
              </div>
            </label>
          </Form.Field>
        </div>
      ))}

      {/* Add Another Alert Button */}
      {data.length < 5 && (
        <div style={{
          textAlign: 'center',
          padding: '20px',
          border: '2px dashed var(--color-border-primary)',
          borderRadius: 'var(--border-radius-md)',
          marginBottom: '24px'
        }}>
          <Button
            variant="outline"
            onClick={handleAddAlert}
            leftIcon={
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <circle cx="12" cy="12" r="10"/>
                <line x1="12" y1="8" x2="12" y2="16"/>
                <line x1="8" y1="12" x2="16" y2="12"/>
              </svg>
            }
          >
            Add Another Alert
          </Button>
          <p style={{
            fontSize: '12px',
            color: 'var(--color-text-secondary)',
            margin: '8px 0 0 0'
          }}>
            {5 - data.length} more alerts available
          </p>
        </div>
      )}


    </div>
  )
}

export default SetupAlertsStep

/**
 * Overview Stats API
 * 
 * Provides overview statistics for the dashboard
 */

import { NextRequest } from 'next/server'
import { withAuth } from '@/lib/api/auth-middleware'
import { resolveTenantContext } from '@/lib/tenant/context'
import { executeQuery, schemaExists } from '@/lib/database'
import {
  createSuccessResponse,
  createErrorResponse,
  ApiErrorCode,
  HttpStatus
} from '@/lib/api/response'

interface OverviewStats {
  totalRenewals: number
  renewalsDue: number
  vendors: number
  annualSpend: string
}

export const GET = withAuth(async (request: NextRequest, session) => {
  console.log('[OVERVIEW-STATS-API] GET request received')

  try {
    // Resolve tenant context
    const tenant = await resolveTenantContext(session.email)
    if (!tenant) {
      console.error('[OVERVIEW-STATS-API] Failed to resolve tenant context')
      return createErrorResponse(
        'Tenant context not found',
        ApiErrorCode.TENANT_NOT_FOUND,
        HttpStatus.BAD_REQUEST
      )
    }

    console.log(`[OVERVIEW-STATS-API] Resolved tenant: ${tenant.tenantId}`)

    // Check if tenant schema exists
    const schemaExistsResult = await schemaExists(tenant.tenantSchema)
    if (!schemaExistsResult) {
      console.log(`[OVERVIEW-STATS-API] Tenant schema ${tenant.tenantSchema} not ready yet`)
      
      // Return default stats
      const defaultStats: OverviewStats = {
        totalRenewals: 0,
        renewalsDue: 0,
        vendors: 0,
        annualSpend: '$0'
      }
      
      return createSuccessResponse(defaultStats, 'Overview statistics retrieved successfully')
    }

    // Query overview stats from tenant schema
    const query = `
      SELECT 
        COUNT(*) as total_renewals,
        COUNT(CASE WHEN renewal_date <= CURRENT_DATE + INTERVAL '30 days' THEN 1 END) as renewals_due,
        COUNT(DISTINCT vendor) as vendors,
        COALESCE(SUM(cost), 0) as annual_spend
      FROM "${tenant.tenantSchema}".tenant_renewals
      WHERE vendor IS NOT NULL AND vendor != ''
    `

    console.log(`[OVERVIEW-STATS-API] Executing query for schema: ${tenant.tenantSchema}`)
    const result = await executeQuery(query)

    if (!result.success) {
      console.error('[OVERVIEW-STATS-API] Database query failed:', result.error)
      return createErrorResponse(
        'Failed to fetch overview statistics',
        ApiErrorCode.INTERNAL_ERROR,
        HttpStatus.INTERNAL_SERVER_ERROR
      )
    }

    // Transform data
    const row = result.data[0] || {}
    const stats: OverviewStats = {
      totalRenewals: parseInt(row.total_renewals) || 0,
      renewalsDue: parseInt(row.renewals_due) || 0,
      vendors: parseInt(row.vendors) || 0,
      annualSpend: `$${(parseFloat(row.annual_spend) || 0).toLocaleString()}`
    }

    console.log(`[OVERVIEW-STATS-API] Returning stats:`, stats)
    return createSuccessResponse(stats, 'Overview statistics retrieved successfully')

  } catch (error) {
    console.error('[OVERVIEW-STATS-API] Error:', error)
    return createErrorResponse(
      'Failed to fetch overview statistics',
      ApiErrorCode.INTERNAL_ERROR,
      HttpStatus.INTERNAL_SERVER_ERROR
    )
  }
}, {
  requireAuth: true
})

-- Migration: Create sync metrics table for monitoring
-- This table stores custom metrics and monitoring data

-- Create sync_metrics table in metadata schema
CREATE TABLE IF NOT EXISTS metadata.sync_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid (),
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(15, 4) NOT NULL,
    tags JSONB,
    recorded_at TIMESTAMP
    WITH
        TIME ZONE NOT NULL DEFAULT NOW(),
        created_on TIMESTAMP
    WITH
        TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_sync_metrics_name_recorded ON metadata.sync_metrics (metric_name, recorded_at DESC);

CREATE INDEX IF NOT EXISTS idx_sync_metrics_recorded_at ON metadata.sync_metrics (recorded_at DESC);

CREATE INDEX IF NOT EXISTS idx_sync_metrics_tags ON metadata.sync_metrics USING GIN (tags);

-- Add comments for documentation
COMMENT ON
TABLE metadata.sync_metrics IS 'Custom metrics and monitoring data for sync operations';

COMMENT ON COLUMN metadata.sync_metrics.metric_name IS 'Name of the metric being recorded';

COMMENT ON COLUMN metadata.sync_metrics.metric_value IS 'Numeric value of the metric';

COMMENT ON COLUMN metadata.sync_metrics.tags IS 'Additional metadata tags for the metric';

COMMENT ON COLUMN metadata.sync_metrics.recorded_at IS 'When the metric was recorded';

-- Grant permissions
GRANT
SELECT,
INSERT
,
UPDATE,
DELETE ON metadata.sync_metrics TO renewtrack_app;

-- Create materialized view for performance metrics aggregation
CREATE MATERIALIZED VIEW IF NOT EXISTS metadata.sync_performance_summary AS
SELECT
    DATE_TRUNC ('hour', sb.created_on) as hour_bucket,
    sb.entity_type,
    COUNT(*) as batch_count,
    AVG(sb.total_records) as avg_batch_size,
    AVG(
        EXTRACT(
            EPOCH
            FROM (
                    sb.completed_at - sb.started_at
                )
        )
    ) as avg_processing_seconds,
    SUM(sb.processed_records) as total_processed,
    SUM(sb.matched_records) as total_matched,
    SUM(sb.conflict_records) as total_conflicts,
    COUNT(
        CASE
            WHEN sb.status = 'completed' THEN 1
        END
    ) as completed_batches,
    COUNT(
        CASE
            WHEN sb.status = 'failed' THEN 1
        END
    ) as failed_batches
FROM metadata.sync_batches sb
WHERE
    sb.created_on >= NOW() - INTERVAL '30 days'
GROUP BY
    DATE_TRUNC ('hour', sb.created_on),
    sb.entity_type;

-- Create index on materialized view
CREATE INDEX IF NOT EXISTS idx_sync_performance_summary_hour_entity ON metadata.sync_performance_summary (hour_bucket DESC, entity_type);

-- Add comments for materialized view
COMMENT ON MATERIALIZED VIEW metadata.sync_performance_summary IS 'Hourly aggregated performance metrics for sync operations';

-- Grant permissions on materialized view
GRANT SELECT ON metadata.sync_performance_summary TO renewtrack_app;

-- Create function to refresh the materialized view
CREATE OR REPLACE FUNCTION metadata.refresh_sync_performance_summary()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY metadata.sync_performance_summary;
END;
$$ LANGUAGE plpgsql;

-- Grant execute permission on function
GRANT
EXECUTE ON FUNCTION metadata.refresh_sync_performance_summary () TO renewtrack_app;
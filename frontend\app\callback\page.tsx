'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'

export default function CallbackPage() {
  const router = useRouter()
  const [status, setStatus] = useState('Processing authentication...')

  useEffect(() => {
    const handleCallback = async () => {
      console.log('🔄 [CALLBACK] Starting OAuth callback processing...')
      setStatus('Processing authentication callback...')

      try {
        // Configure Amplify first
        const { ensureAmplifyConfigured } = await import('@/lib/services/amplify-service')
        await ensureAmplifyConfigured()
        console.log('✅ [CALLBACK] Amplify configured')

        // Import Hub to listen for auth events
        const { Hub } = await import('aws-amplify/utils')

        // Set up a promise that resolves when we get a signedIn event
        const authPromise = new Promise((resolve, reject) => {
          const timeout = setTimeout(() => {
            reject(new Error('Authentication timeout'))
          }, 10000) // 10 second timeout

          const hubListener = (data: any) => {
            const { payload } = data
            console.log('🔄 [CALLBACK] Hub event:', payload.event)

            if (payload.event === 'signedIn') {
              console.log('✅ [CALLBACK] Authentication successful!')
              clearTimeout(timeout)
              resolve(payload.data)
            } else if (payload.event === 'signIn_failure') {
              console.log('❌ [CALLBACK] Authentication failed!')
              clearTimeout(timeout)
              reject(new Error('Authentication failed'))
            }
          }

          Hub.listen('auth', hubListener)
        })

        console.log('⏳ [CALLBACK] Waiting for authentication to complete...')
        setStatus('Completing authentication...')

        // Wait for either success or timeout
        await authPromise
        
        // === Set idToken cookie for SSR/middleware ===
        try {
          const { fetchAuthSession } = await import('aws-amplify/auth');
          const session = await fetchAuthSession();
          const idToken = session?.tokens?.idToken?.toString();
          if (idToken) {
            await fetch('/api/auth/set-token', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ idToken }),
              credentials: 'include',
            });
          }
        } catch (err) {
          console.error('[CALLBACK] Failed to set idToken cookie:', err);
        }
        // === End set idToken cookie ===


        console.log('🔄 [CALLBACK] Authentication complete, redirecting...')
        setStatus('Authentication successful! Redirecting...')

        // Small delay to show success message
        await new Promise(resolve => setTimeout(resolve, 1000))

        // Redirect to auth check to verify authentication
        router.push('/auth-check')

      } catch (error: any) {
        console.error('❌ [CALLBACK] Error:', error)
        setStatus('Authentication failed. Redirecting to login...')
        setTimeout(() => {
          router.push('/login')
        }, 2000)
      }
    }

    handleCallback()
  }, [router])

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">{status}</p>
      </div>
    </div>
  )
}

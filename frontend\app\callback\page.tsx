'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { AUTH_ENDPOINTS } from '@/lib/constants/api-endpoints'

export default function CallbackPage() {
  const router = useRouter()
  const [status, setStatus] = useState('Processing authentication...')

  useEffect(() => {
    const handleCallback = async () => {
      setStatus('Processing authentication callback...')

      try {
        // Configure Amplify first
        const { ensureAmplifyConfigured } = await import('@/lib/services/amplify-service')
        await ensureAmplifyConfigured()

        setStatus('Processing authentication...')

        // Check if we have URL parameters indicating OAuth callback
        const urlParams = new URLSearchParams(window.location.search)
        const code = urlParams.get('code')
        const state = urlParams.get('state')

        console.log('[CALLBACK] OAuth callback detected:', { code: !!code, state: !!state })

        if (code) {
          // This is an OAuth callback, let Amplify handle it
          console.log('[CALLBACK] Processing OAuth callback...')

          // Import getCurrentUser to trigger OAuth processing
          const { getCurrentUser } = await import('aws-amplify/auth')

          // Try to get current user - this will process the OAuth callback
          const user = await getCurrentUser()
          console.log('[CALLBACK] OAuth processing successful:', user)

          setStatus('Authentication successful!')
        } else {
          // No OAuth code, check if user is already authenticated
          const { getCurrentUser } = await import('aws-amplify/auth')
          const user = await getCurrentUser()
          console.log('[CALLBACK] User already authenticated:', user)
        }
        
        // === Set idToken cookie for SSR/middleware ===
        try {
          const { fetchAuthSession } = await import('aws-amplify/auth');
          const session = await fetchAuthSession();
          const idToken = session?.tokens?.idToken?.toString();
          if (idToken) {
            await fetch(AUTH_ENDPOINTS.SET_TOKEN, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ idToken }),
              credentials: 'include',
            });
          }
        } catch (err) {
          // Silently handle cookie setting errors - not critical for auth flow
        }
        // === End set idToken cookie ===

        setStatus('Authentication successful! Redirecting...')

        // Small delay to show success message
        await new Promise(resolve => setTimeout(resolve, 1000))

        // Redirect to auth check to verify authentication
        router.push('/auth-check')

      } catch (error: any) {
        setStatus('Authentication failed. Redirecting to login...')
        setTimeout(() => {
          router.push('/login')
        }, 2000)
      }
    }

    handleCallback()
  }, [router])

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">{status}</p>
      </div>
    </div>
  )
}

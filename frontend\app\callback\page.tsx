'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { AUTH_ENDPOINTS } from '@/lib/constants/api-endpoints'

export default function CallbackPage() {
  const router = useRouter()
  const [status, setStatus] = useState('Processing authentication...')

  useEffect(() => {
    const handleCallback = async () => {
      setStatus('Processing authentication callback...')

      try {
        // Configure Amplify first
        const { ensureAmplifyConfigured } = await import('@/lib/services/amplify-service')
        await ensureAmplifyConfigured()

        // Import Hub to listen for auth events
        const { Hub } = await import('aws-amplify/utils')

        // Set up a promise that resolves when we get a signedIn event
        const authPromise = new Promise((resolve, reject) => {
          const timeout = setTimeout(() => {
            reject(new Error('Authentication timeout'))
          }, 10000) // 10 second timeout

          const hubListener = (data: any) => {
            const { payload } = data

            if (payload.event === 'signedIn') {
              clearTimeout(timeout)
              resolve(payload.data)
            } else if (payload.event === 'signIn_failure') {
              clearTimeout(timeout)
              reject(new Error('Authentication failed'))
            }
          }

          Hub.listen('auth', hubListener)
        })

        setStatus('Completing authentication...')

        // Wait for either success or timeout
        await authPromise
        
        // === Set idToken cookie for SSR/middleware ===
        try {
          const { fetchAuthSession } = await import('aws-amplify/auth');
          const session = await fetchAuthSession();
          const idToken = session?.tokens?.idToken?.toString();
          if (idToken) {
            await fetch(AUTH_ENDPOINTS.SET_TOKEN, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ idToken }),
              credentials: 'include',
            });
          }
        } catch (err) {
          // Silently handle cookie setting errors - not critical for auth flow
        }
        // === End set idToken cookie ===

        setStatus('Authentication successful! Redirecting...')

        // Small delay to show success message
        await new Promise(resolve => setTimeout(resolve, 1000))

        // Redirect to auth check to verify authentication
        router.push('/auth-check')

      } catch (error: any) {
        setStatus('Authentication failed. Redirecting to login...')
        setTimeout(() => {
          router.push('/login')
        }, 2000)
      }
    }

    handleCallback()
  }, [router])

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">{status}</p>
      </div>
    </div>
  )
}

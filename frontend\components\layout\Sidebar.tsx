'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useAppState } from '@/lib/hooks/useAppState'
import { useSidebarPages } from '@/lib/hooks/useSidebarPages'


export default function Sidebar() {
  const pathname = usePathname()

  // Get user information from authentication state
  const { user, auth } = useAppState({
    subscriptions: ['auth'],
    autoAuth: false,
    autoTenant: false
  })

  // Use the centralized sidebar pages hook with caching
  const { pages, isLoading, error } = useSidebarPages()

  // Use pages from the hook
  const displayPages = pages



  // Get display name from Cognito attributes only - no fallbacks
  const getDisplayName = (): string => {
    // Try to get name from auth session first (more reliable for Cognito attributes)
    const session = auth?.session;



    // Only use actual Cognito attributes
    if (session?.given_name && session?.family_name) {
      return `${session.given_name} ${session.family_name}`;
    }

    // If no Cognito attributes available, show generic fallback
    return '';
  }

  const handleSignOut = async () => {
    // Clear auth cookie
    document.cookie = 'idToken=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT'
    // Redirect to root which will handle Cognito redirect
    window.location.href = '/'
  }

  // Render SVG icon from database
  const renderIcon = (iconSvg: string | null) => {
    if (!iconSvg) {
      // No icon available - return empty
      return null;
    }

    // Parse and render the SVG paths
    return <g dangerouslySetInnerHTML={{ __html: iconSvg }} />
  }

  if (isLoading) {
    return (
      <div className="sidebar">
        <div className="sidebar-header">
          <h2 className="sidebar-title">RenewTrack</h2>
        </div>
        <div className="flex items-center justify-center py-4 text-gray-500">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-400 mr-2"></div>
          <span className="text-sm">Loading navigation...</span>
        </div>
      </div>
    )
  }

  return (
    <div className="sidebar">
      <div className="sidebar-header">
        <h2 className="sidebar-title">RenewTrack</h2>
      </div>

      {error && (
        <div className="px-4 py-2 mb-4 text-sm text-red-600 bg-red-50 border border-red-200 rounded">
          <p className="font-medium">Navigation Error</p>
          <p>{error?.message || String(error)}</p>
        </div>
      )}

      <nav className="sidebar-nav">
        {displayPages.length === 0 ? (
          <div className="px-4 py-2 text-sm text-gray-500">
            <p>No navigation pages available</p>
          </div>
        ) : (
          displayPages.map((page) => {
          const isActive = pathname === page.route_path

          return (
            <Link
              key={page.id}
              href={page.route_path}
              className={`sidebar-nav-item ${isActive ? 'active' : ''}`}
              title={page.description || page.header}
            >
              <svg
                className="sidebar-nav-icon"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                {renderIcon(page.icon_svg)}
              </svg>
              <span className="sidebar-nav-text">{page.header}</span>
            </Link>
          )
        }))}
      </nav>

      <div className="sidebar-footer">
        <div className="sidebar-user">
          <div className="user-avatar">
            <span className="user-avatar-text">
              {getDisplayName() ? getDisplayName().charAt(0).toUpperCase() : '?'}
            </span>
          </div>
          <div className="sidebar-user-info">
            <div className="sidebar-user-name">{getDisplayName() || 'Name not available'}</div>
            <div className="sidebar-user-email">{user?.email || 'Email not available'}</div>
          </div>
          <button
            onClick={handleSignOut}
            className="sidebar-signout-btn"
            title="Sign out"
            aria-label="Sign out of your account"
          >
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>
  )
}
